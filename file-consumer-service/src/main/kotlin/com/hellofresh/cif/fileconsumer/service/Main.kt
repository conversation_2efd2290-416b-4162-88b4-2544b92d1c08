package com.hellofresh.cif.fileconsumer.service

import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.featureflags.StatsigFactory
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.fileconsumer.service.listeners.StockUploadQueueListener
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.shutdown.shutdownHook
import com.hellofresh.cif.sqs.SQSClientBuilder
import software.amazon.awssdk.services.sqs.SqsClient

private fun getParallelismConfig() = ConfigurationLoader.getIntegerOrDefault("parallelism", 1)
private val sqsClient: SqsClient = SQSClientBuilder.getSqsClient()

private const val HTTP_PORT = 8081

fun main() {
    val parallelism = getParallelismConfig()
    val meterRegistry = createMeterRegistry()
    val masterDslContext = DBConfiguration.jooqMasterDslContext(parallelism, meterRegistry)
    val statsigFeatureFlagClient = getStatsigFeatureFlagClient(ConfigurationLoader)

    StatusServer.run(
        meterRegistry,
        HTTP_PORT,
    )

    val sqsUrl = ConfigurationLoader.getStringOrFail("aws.sqs.url")

    StockUploadQueueListener(
        meterRegistry = meterRegistry,
        dslContext = masterDslContext,
        statsigFeatureFlagClient = statsigFeatureFlagClient,
        sqsClient = sqsClient,
        sqsUrl = sqsUrl,
    ).start()
}

private fun getStatsigFeatureFlagClient(configLoader: ConfigurationLoader): StatsigFeatureFlagClient =
    with(configLoader) {
        StatsigFactory.build(
            ::shutdownHook,
            sdkKey = getStringOrFail("HF_STATSIG_SDK_KEY"),
            userId = getStringOrFail("application.name"),
            isOffline = getStringIfPresent("statsig.offline")?.toBoolean() ?: false,
            hfTier = getStringOrFail("HF_TIER"),
        )
    }
