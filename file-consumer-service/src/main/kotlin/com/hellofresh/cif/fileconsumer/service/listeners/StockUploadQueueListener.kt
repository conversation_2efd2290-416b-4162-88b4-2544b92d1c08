package com.hellofresh.cif.fileconsumer.service.listeners

import com.hellofresh.cif.business.stockupdate.repository.StockUpdateRepositoryImpl
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.fileconsumer.service.service.StockUpdateFileProcessorService
import com.hellofresh.cif.fileconsumer.service.service.StockUpdateService
import com.hellofresh.cif.inventory.InventoryActivityRepositoryImpl
import com.hellofresh.cif.inventory.InventoryRepositoryImpl
import com.hellofresh.cif.inventory.InventoryService
import com.hellofresh.cif.inventory.LiveInventoryRepositoryImpl
import com.hellofresh.cif.inventory.StockUpdateRepositoryImpl as InventoryStockUpdateRepositoryImpl
import com.hellofresh.cif.inventory.StockUpdateService as InventoryStockUpdateService
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.s3.S3EventMessageParser
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.sqs.SQSListener
import com.hellofresh.cif.sqs.SQSMessageProxy
import com.hellofresh.com.cif.business.fileupload.repository.FileUploadRepositoryImpl
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import software.amazon.awssdk.services.sqs.SqsClient

class StockUploadQueueListener(
    private val meterRegistry: HelloFreshMeterRegistry,
    private val dslContext: MetricsDSLContext,
    private val statsigFeatureFlagClient: StatsigFeatureFlagClient,
    private val sqsClient: SqsClient,
    private val sqsUrl: String,
) {

    fun start() {
        // inventory stock update service
        val inventoryRepository = InventoryRepositoryImpl(dslContext)
        val liveInventoryRepository = LiveInventoryRepositoryImpl(dslContext)
        val inventoryActivityRepository = InventoryActivityRepositoryImpl(dslContext)
        val stockUpdateRepository = InventoryStockUpdateRepositoryImpl(dslContext)

        val inventoryService = InventoryService(
            inventoryRepository = inventoryRepository,
            liveInventoryRepository = liveInventoryRepository,
            inventoryActivityRepository = inventoryActivityRepository,
            statsig = statsigFeatureFlagClient,
        )

        val inventoryStockUpdateService = InventoryStockUpdateService(
            inventoryService = inventoryService,
            stockUpdateRepository = stockUpdateRepository,
        )
        val skuSpecificationService = SkuSpecificationService(meterRegistry)
        val dcConfigService = DcConfigService(meterRegistry)
        val stockUpdateRepositoryImpl = StockUpdateRepositoryImpl()

        // stock update service
        val stockUpdateService = StockUpdateService(
            inventoryStockUpdateService = inventoryStockUpdateService,
            skuSpecificationService = skuSpecificationService,
            dcConfigService = dcConfigService,
            stockUpdateRepositoryImpl = stockUpdateRepositoryImpl,
        )

        val fileUploadRepositoryImpl = FileUploadRepositoryImpl()

        val proxy = SQSMessageProxy(
            processor = StockUpdateFileProcessorService(
                metricsDSLContext = dslContext,
                dcConfigService = dcConfigService,
                s3Importer = S3Importer(),
                stockUpdateService = stockUpdateService,
                fileUploadRepositoryImpl = fileUploadRepositoryImpl,
            ),
            s3EventMessageParser = S3EventMessageParser(),
        )

        val sqsListener = shutdownNeeded { SQSListener(proxy, sqsClient, sqsUrl) }

        CoroutineScope(Dispatchers.IO).launch {
            sqsListener.run()
        }
    }
}
