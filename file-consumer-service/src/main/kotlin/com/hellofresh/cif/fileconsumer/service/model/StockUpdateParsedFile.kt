package com.hellofresh.cif.fileconsumer.service.model

import com.hellofresh.cif.fileconsumer.model.ParsedFile
import org.apache.commons.csv.CSVRecord

/**
 * Represents a parsed file containing stock update information.
 * This class implements the ParsedFile interface to work with the file consumer framework.
 */
class StockUpdateParsedFile(records: List<CSVRecord>) : ParsedFile {
    override val columns = Companion.columns
    override val data = data(records)
    override val pKey: List<String> = listOf(SKU_CODE_HEADER, DC_CODE_HEADER, DATE_HEADER)
    override val numericColumnDataTypes: Set<String> = Companion.numericColumnDataTypes
    override val allowedBlank: Set<String> = Companion.allowedBlank

    companion object {
        const val SKU_CODE_HEADER = "skuCode"
        const val DC_CODE_HEADER = "dcCode"
        const val DATE_HEADER = "date"
        const val QTY_HEADER = "stockUpdate"
        const val REASON_HEADER = "reason"
        const val REASON_DETAILS_HEADER = "reasonDetails"

        val columns: List<String> = listOf(
            SKU_CODE_HEADER,
            DC_CODE_HEADER,
            DATE_HEADER,
            QTY_HEADER,
            REASON_HEADER,
            REASON_DETAILS_HEADER,
        )

        val numericColumnDataTypes = setOf(
            QTY_HEADER,
        )

        val allowedBlank = setOf(
            REASON_DETAILS_HEADER,
        )
    }
}
