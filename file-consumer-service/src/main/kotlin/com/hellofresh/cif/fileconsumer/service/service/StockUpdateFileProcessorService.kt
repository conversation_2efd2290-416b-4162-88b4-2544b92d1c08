package com.hellofresh.cif.fileconsumer.service.service

import com.hellofresh.cif.business.file_upload.schema.enums.FileType
import com.hellofresh.cif.business.file_upload.schema.enums.FileUploadStatus
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.fileconsumer.ProcessFile
import com.hellofresh.cif.fileconsumer.model.FileTypeDescriptor
import com.hellofresh.cif.fileconsumer.model.ProcessFileResult
import com.hellofresh.cif.fileconsumer.model.ViolationHandlersConfig
import com.hellofresh.cif.fileconsumer.service.model.StockUpdateCSVRow
import com.hellofresh.cif.fileconsumer.service.model.StockUpdateParsedFile
import com.hellofresh.cif.fileconsumer.violations.NegativeNumbersViolationHandler
import com.hellofresh.cif.fileconsumer.violations.ViolationHandlersPerFile
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.s3.AUTHOR_EMAIL_METADATA
import com.hellofresh.cif.s3.AUTHOR_NAME_METADATA
import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.sqs.MessageHandlerServiceInterface
import com.hellofresh.com.cif.business.fileupload.model.FileUpload as FileUploadModel
import com.hellofresh.com.cif.business.fileupload.repository.FileUploadRepositoryImpl
import io.ktor.server.plugins.NotFoundException
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.DSLContext

class StockUpdateFileProcessorService(
    private val metricsDSLContext: MetricsDSLContext,
    private val dcConfigService: DcConfigService,
    private val s3Importer: S3Importer,
    private val stockUpdateService: StockUpdateService,
    private val fileUploadRepositoryImpl: FileUploadRepositoryImpl,
) : MessageHandlerServiceInterface {

    @Suppress("LongMethod")
    override suspend fun process(s3File: S3File) {
        logger.info("Started Processing file from S3: bucket='${s3File.bucket}', key='${s3File.key}'")

        val fileContent = withContext(Dispatchers.IO) {
            s3Importer.fetchObjectContent(s3File.bucket, s3File.key).readAllBytes()
        }

        logger.info("Successfully downloaded file from S3: size=${fileContent.size} bytes")

        val stockUpdates = processFileContent(s3File.key, fileContent)
        val metadata = s3Importer.fetchObjectMetadata(s3File.bucket, s3File.key)
        val authorName = metadata[AUTHOR_NAME_METADATA] ?: ""
        val authorEmail = metadata[AUTHOR_EMAIL_METADATA] ?: ""

        val fileUploadBase = createFileUploadModel(
            fileName = s3File.fileName,
            market = "",
            dcs = emptySet(),
            authorName = authorName,
            authorEmail = authorEmail,
        )

        if (stockUpdates.isNotEmpty()) {
            // validate stockUpdates here
            val dcs = stockUpdates.map { it.dcCode }.toSet()
            val market = getMarket(dcs.first())
            val fileUpload = fileUploadBase.copy(
                market = market,
                dcs = dcs,
            )

            val dcCodeSet = stockUpdates.map { it.dcCode }.toSet()

            try {
                stockUpdateService.validateStockUpdateDateRange(
                    dcCode = dcCodeSet.first(),
                    dates = stockUpdates.map { it.date }.toSet(),
                )
            } catch (e: IllegalArgumentException) {
                val errorFile = fileUpload.copy(status = FileUploadStatus.ERROR, message = e.message,)
                fileInsertOperation(metricsDSLContext, errorFile)
                throw e
            }

            // get skus from skuSpecificationService
            val stockUpdateSkus = stockUpdateService.getSkuSpecificationList(
                dcList = stockUpdateService.getDcConfigList(dcCodeSet),
                stockUpdateCSVRows = stockUpdates,
            )

            // get currentStockUpdates from inventoryStockUpdateService.getStockUpdates
            val currentStockUpdates = stockUpdateService.getCurrentStockUpdates(
                stockUpdateSkus = stockUpdateSkus,
                dcCodeSet = dcCodeSet,
                dateRange = createDateRange(stockUpdates),
            )

            metricsDSLContext.withTagName("stock-update-insert-batch")
                .transactionAsync { tx ->
                    val txDsl = tx.dsl()

                    stockUpdateService.update(
                        dslContext = txDsl,
                        stockUpdateCSVRows = stockUpdates,
                        stockUpdateSkus = stockUpdateSkus,
                        currentStockUpdates = currentStockUpdates,
                        authorName = authorName,
                        authorEmail = authorEmail,
                    )
                    logger.info("Successfully inserted ${stockUpdates.size} stock updates from file: ${s3File.key}.")

                    fileInsertOperation(txDsl, fileUpload)
                }.await()
        } else {
            logger.warn("No valid stock updates found in file: ${s3File.fileName}")

            val errorFile = fileUploadBase.copy(
                status = FileUploadStatus.ERROR,
                message = "No valid stock updates found in file",
            )
            fileInsertOperation(metricsDSLContext, errorFile)
        }
    }

    private fun createDateRange(stockUpdatesList: List<StockUpdateCSVRow>) = DateRange(
        fromDate = stockUpdatesList.minOf { it.date },
        toDate = stockUpdatesList.maxOf { it.date },
    )

    private fun fileInsertOperation(fileDslContext: DSLContext, file: FileUploadModel) {
        fileUploadRepositoryImpl.insert(dslContext = fileDslContext, file = file)
        logger.info("Successfully inserted file: ${file.filename} in file upload table with ${file.status} status.")
    }

    private fun processFileContent(fileName: String, content: ByteArray): List<StockUpdateCSVRow> {
        logger.info("Converting file $fileName to List<StockUpdateCSVRow> object.")
        val file = ProcessFile.processFileContent(
            fileName,
            content,
            listOf(',', ';'),
            FileTypeDescriptor(StockUpdateParsedFile.columns, ::StockUpdateParsedFile),
            violationHandlersConfig = ViolationHandlersConfig(
                preViolationHandlers = ViolationHandlersPerFile.createInstances(),
                excludeViolationHandlers = listOf(NegativeNumbersViolationHandler::class),
            ),
        )
        if (file.hasSevereViolations()) {
            val message = file.violations.joinToString { it.message }
            logger.error(
                "Error while processing file $fileName, because: $message.",
            )
        } else if (file.violations.isNotEmpty()) {
            val message = file.violations.joinToString { it.message }
            logger.warn(
                "Warning while processing file $fileName, because: $message.",
            )
        }
        return if (file.run { !hasSevereViolations() && violations.isEmpty() }) {
            file.toStockUpdates()
        } else {
            emptyList()
        }
    }

    private fun getMarket(dcCode: String): String =
        dcConfigService.dcConfigurations[dcCode]?.market ?: run {
            val message = "Error: market value not found for the dcCode $dcCode"
            logger.warn(message)
            throw NotFoundException(message)
        }

    private fun createFileUploadModel(
        fileName: String,
        market: String,
        dcs: Set<String>,
        authorName: String,
        authorEmail: String,
    ): FileUploadModel =
        FileUploadModel(
            id = UUID.randomUUID(),
            filename = fileName,
            market = market,
            dcs = dcs,
            status = FileUploadStatus.IMPORTED,
            message = null,
            authorName = authorName,
            authorEmail = authorEmail,
            fileType = FileType.STOCK_UPDATE
        )

    private fun ProcessFileResult.toStockUpdates(): List<StockUpdateCSVRow> =
        this.parsedFile?.data?.mapNotNull { row ->
            row[StockUpdateParsedFile.QTY_HEADER]?.let { stockUpdateQty ->
                with(StockUpdateParsedFile) {
                    StockUpdateCSVRow(
                        skuCode = row[SKU_CODE_HEADER],
                        dcCode = row[DC_CODE_HEADER],
                        date = LocalDate.parse(row[DATE_HEADER]),
                        qty = stockUpdateQty.toInt(),
                        reason = row[REASON_HEADER],
                        reasonDetails = row[REASON_DETAILS_HEADER],
                    )
                }
            } ?: run {
                logger.warn("Skipping invalid stock update record: $row")
                null
            }
        } ?: emptyList()

    override suspend fun name(): String = "File Consumer Service"

    companion object : Logging
}
