package com.hellofresh.cif.fileconsumer.service.service

import com.hellofresh.cif.business.stockupdate.model.Reason
import com.hellofresh.cif.business.stockupdate.model.StockUpdateDto
import com.hellofresh.cif.business.stockupdate.repository.StockUpdateRepositoryInterface
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.fileconsumer.service.model.StockUpdateCSVRow
import com.hellofresh.cif.inventory.StockUpdateService as InventoryStockUpdateService
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.validateDates
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.sku.models.SkuIdAndSpecification
import java.time.LocalDate
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.DSLContext

const val DEFAULT_STOCK_UPDATE_VERSION = 1

class StockUpdateService(
    private val inventoryStockUpdateService: InventoryStockUpdateService,
    private val skuSpecificationService: SkuSpecificationService,
    private val dcConfigService: DcConfigService,
    private val stockUpdateRepositoryImpl: StockUpdateRepositoryInterface,
) {
    @Suppress("LongParameterList")
    fun update(
        dslContext: DSLContext,
        stockUpdateCSVRows: List<StockUpdateCSVRow>,
        stockUpdateSkus: List<SkuIdAndSpecification>,
        currentStockUpdates: List<com.hellofresh.cif.inventory.StockUpdate>,
        authorName: String,
        authorEmail: String,
    ) {
        val dcCodeSet = stockUpdateCSVRows.map { it.dcCode }.toSet()
        val dcs = getDcConfigList(dcCodeSet = dcCodeSet)

        // prepare stockUpdate List for insert
        val upsertStockUpdateList = stockUpdateCSVRows.map {
            val sku = stockUpdateSkus.first { sku -> sku.second.skuCode == it.skuCode }

            // get the stockUpdate version by the key from current stockUpdates
            val skuVersion = currentStockUpdates
                .firstOrNull { it.skuId == sku.first }
                ?.version

            val productionWeek = ProductionWeek(
                date = it.date,
                productionStartDay = dcs.first().productionStart,
            )

            // prepare the new stockUpdate with week, version, skuId, uom, user details
            StockUpdateDto(
                skuId = sku.first,
                dcCode = it.dcCode,
                date = it.date,
                version = skuVersion?.plus(1) ?: DEFAULT_STOCK_UPDATE_VERSION,
                quantity = SkuQuantity.fromLong(it.qty.toLong(), sku.second.uom),
                reason = Reason.fromValue(it.reason),
                reasonDetail = it.reasonDetails,
                authorName = authorName,
                authorEmail = authorEmail,
                deleted = false,
                week = productionWeek.weekString,
            )
        }

        stockUpdateRepositoryImpl
            .upsertStockUpdate(dslContext, stockUpdates = upsertStockUpdateList)
            .execute()
    }

    fun getSkuSpecificationList(
        dcList: List<DistributionCenterConfiguration>,
        stockUpdateCSVRows: List<StockUpdateCSVRow>
    ): List<SkuIdAndSpecification> =
        skuSpecificationService
            .skuCodeLookUp(dcList)
            .filter { (key, _) ->
                stockUpdateCSVRows.any { it.skuCode == key.skuCode }
            }
            .map { it.value.first to it.value.second }

    suspend fun validateStockUpdateDateRange(dcCode: String, dates: Set<LocalDate>) {
        requireNotNull(
            inventoryStockUpdateService.getCurrentStockUpdateRange(dcCode),
        ) { "Couldn't get current stock update range for dc code $dcCode" }
            .also { stockUpdateRange ->
                stockUpdateRange.validateDates(dcCode, dates)
            }
    }

    suspend fun getCurrentStockUpdates(
        stockUpdateSkus: List<SkuIdAndSpecification>,
        dcCodeSet: Set<String>,
        dateRange: DateRange,
    ): List<com.hellofresh.cif.inventory.StockUpdate> =
        inventoryStockUpdateService.getStockUpdates(
            skuIds = stockUpdateSkus.map { it.first }.toSet(), // set of skuIds
            ranges = mapOf(dcCodeSet.first() to dateRange),
        )

    fun getDcConfigList(dcCodeSet: Set<String>) =
        dcConfigService
            .dcConfigurations
            .filterKeys { it in dcCodeSet }
            .values
            .toList()

    companion object : Logging
}
