package com.hellofresh.cif.skuspecification

import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.lib.kafka.ConsumerProcessorConfig
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategy
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.kafka.serde.ProtoDeserializer
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.skuspecification.youfoodz.YfSkuSpecificationRepositoryImpl
import com.hellofresh.proto.stream.ye.planning.yfCulinarySku.v1.YfCulinarySku
import io.micrometer.core.instrument.MeterRegistry
import org.apache.kafka.common.serialization.StringDeserializer

class YfSkuSpecificationApp(
    private val meterRegistry: MeterRegistry,
    private val dslContext: MetricsDSLContext,
    private val consumerConfig: Map<String, String>,
    private val pollConfig: PollConfig,
) {
    suspend fun runApp() {
        val yfSkuSpecificationService = YfSkuSpecificationService(
            YfSkuSpecificationRepositoryImpl(dslContext),
        )
        shutdownNeeded {
            CoroutinesProcessor(
                pollConfig,
                rawYfCulinaryConsumerConfig(),
                meterRegistry,
                yfSkuSpecificationService::save,
                handleDeserializationException = DeserializationExceptionStrategy.create(
                    ConfigurationLoader.getStringOrFail("consumer.poison-pill.strategy"),
                    meterRegistry,
                ),
                recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                    meterRegistry,
                    "yf_sku_specification_processor_write_failure",
                ),
            ).also {
                HealthChecks.add(it)
                StartUpChecks.add(it)
            }
        }.run()
    }

    private fun rawYfCulinaryConsumerConfig() =
        ConsumerProcessorConfig(
            consumerConfig,
            StringDeserializer(),
            ProtoDeserializer<YfCulinarySku>(),
            yfCulinarySkuTopics,
        )
}
