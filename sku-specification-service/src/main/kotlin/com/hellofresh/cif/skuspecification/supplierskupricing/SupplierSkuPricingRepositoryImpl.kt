package com.hellofresh.cif.skuspecification.supplierskupricing

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.skuSpecification.schema.tables.SupplierSkuPricing.SUPPLIER_SKU_PRICING
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.DSLContext

class SupplierSkuPricingRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext
) : SupplierSkuPricingRepository {

    private val saveSupplierSkuPricing = "save-supplier-sku-pricing"

    override suspend fun saveSupplierSkuPricing(supplierSkuPricing: Set<SupplierSkuPricing>) {
        val enabledSupplierSkuPricing = supplierSkuPricing.filter { it.enabled }
        metricsDSLContext.withTagName(saveSupplierSkuPricing)
            .transactionAsync { txConf ->
                newBatchQueryBuilder(txConf.dsl()).let { queryBuilder ->
                    enabledSupplierSkuPricing
                        .map { v ->
                            queryBuilder.bind(
                                v.id, v.supplierSkuId, v.leadTime, v.enabled, v.market, v.startDate, v.endDate,
                                // on update
                                v.leadTime, v.enabled, v.market, v.startDate, v.endDate
                            )
                        }
                        .lastOrNull()
                        ?.execute()
                }
            }.await()
    }

    private fun newBatchQueryBuilder(dsl: DSLContext) = dsl.batch(
        SUPPLIER_SKU_PRICING.run {
            dsl.insertInto(this)
                .columns(
                    ID,
                    SUPPLIER_SKU_ID,
                    LEAD_TIME,
                    ENABLED,
                    MARKET,
                    START_DATE,
                    END_DATE,
                )
                .values(UUID(0, 0), UUID(0, 0), 0, true, "", LocalDate.now(), LocalDate.now())
                .onDuplicateKeyUpdate()
                .set(LEAD_TIME, 0)
                .set(ENABLED, true)
                .set(MARKET, "")
                .set(START_DATE, LocalDate.now())
                .set(END_DATE, LocalDate.now())
        },
    )
}
