package com.hellofresh.cif.skuspecification

import com.hellofresh.cif.lib.kafka.serde.getValue
import com.hellofresh.cif.lib.kafka.serde.objectMapper
import com.hellofresh.cif.lib.kafka.serde.or
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.skuspecification.model.SkuSpecification
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient
import io.confluent.kafka.serializers.KafkaAvroDeserializer
import java.util.UUID
import org.apache.avro.generic.GenericRecord
import org.apache.kafka.common.serialization.Deserializer

class SkuDeserializer(schemaRegistryClient: SchemaRegistryClient) : Deserializer<SkuSpecification> {
    private val kafkaAvroDeserializer = KafkaAvroDeserializer(schemaRegistryClient)

    @Suppress("LocalVariableName", "VariableNaming")
    override fun deserialize(topic: String?, data: ByteArray?): SkuSpecification {
        requireNotNull(data).let {
            val record = kafkaAvroDeserializer.deserialize(topic, data) as GenericRecord
            val market: String by record
            val code: String by record
            val id: String by record

            val category: String by record

            val cooling_type: GenericRecord by record
            val coolingType = cooling_type.get("name").toString()

            val packaging: GenericRecord by record
            val packagingType = packaging.get("type").toString()

            val name: String by record
            require(name.isNotEmpty()) { "name must not be empty" }
            val third_pw_parent_id: String? by record.or(null)
            val parent_id = if (third_pw_parent_id == null || third_pw_parent_id!!.isBlank()) {
                null
            } else {
                UUID.fromString(third_pw_parent_id)
            }
            val acceptable_code_life: Int by record

            return SkuSpecification(
                id = UUID.fromString(id),
                market = market,
                category = category,
                code = code,
                name = name,
                coolingType = coolingType,
                packaging = packagingType,
                parentId = parent_id,
                acceptableCodeLife = acceptable_code_life,
                fumigationAllowed = record.fumigated,
                uom = record.uom,
                brands = extractBrands(record)
            )
        }
    }
}

private fun extractBrands(record: GenericRecord): List<String> {
    val brandsField = record.get("brands")
    return (brandsField as? Collection<*>)?.mapNotNull { it?.toString() } ?: emptyList()
}

/**
 * Currently [com.hellofresh.cif.skuspecification.culinarySkuTopics] contains UOM in a json string called `extras`.
 * The key is `wms_uom` and the values in this string are `ea, lbs`.
 *
 * **Note:** This is only suitable for US market. This should be refactored later to adapt for
 * the other markets. One possibility is to use `packing.unit`.
 * **/
private fun extractUom(extras: String): Any? {
    val innerExtras = objectMapper.readTree(extras)?.get("extras")?.textValue() ?: return null
    return objectMapper.readTree(innerExtras)?.get("wms_uom")?.textValue()
}

private fun extractFumigated(extras: String): Any? {
    val innerExtras = objectMapper.readTree(extras)?.get("extras")?.textValue() ?: return null
    return objectMapper.readTree(innerExtras)?.get("fumigated")
}

private val GenericRecord.uom: SkuUOM
    get() {
        val extras: String? by this
        return extras
            .takeIf { !it.isNullOrBlank() }
            ?.let { toUOMString(extractUom(it)) }
            ?: SkuUOM.UOM_UNIT
    }

private val GenericRecord.fumigated: Boolean?
    get() {
        val extras: String? by this
        return extras
            .takeIf { !it.isNullOrBlank() }
            ?.let {
                toFumigatedBoolean(extractFumigated(it))
            }
    }

private fun toUOMString(uom: Any?): SkuUOM {
    val sanitized = uom?.toString()?.trim()?.lowercase() ?: return SkuUOM.UOM_UNIT

    return when (sanitized) {
        "ea" -> SkuUOM.UOM_UNIT
        "lbs" -> SkuUOM.UOM_LBS
        else -> SkuUOM.UOM_UNIT
    }
}

private fun toFumigatedBoolean(fumigated: Any?): Boolean? =
    fumigated?.toString()?.trim()?.toBoolean()
