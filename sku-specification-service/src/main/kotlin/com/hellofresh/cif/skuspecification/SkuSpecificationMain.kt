package com.hellofresh.cif.skuspecification

import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.kafka.loadSchemaRegistryClientConfig
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.skuspecification.suppliersku.SupplierSkuApp
import com.hellofresh.cif.skuspecification.supplierskupricing.SupplierSkuPricingApp
import io.confluent.kafka.schemaregistry.client.CachedSchemaRegistryClient
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient
import io.confluent.kafka.schemaregistry.client.rest.RestService
import io.confluent.kafka.serializers.AbstractKafkaSchemaSerDeConfig
import kotlin.time.Duration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.kafka.clients.consumer.ConsumerConfig

private const val HEALTH_CHECK_PORT = 8081
private const val DB_POOL_SIZE = 5

@Suppress("LongMethod")
suspend fun main() {
    val meterRegistry = createMeterRegistry()
    val parallelism = ConfigurationLoader.getIntegerOrDefault("parallelism", 1)
    val dslContext =
        DBConfiguration.jooqMasterDslContext(DB_POOL_SIZE * parallelism, meterRegistry)
    StatusServer.run(
        meterRegistry,
        HEALTH_CHECK_PORT
    )
    val schemaRegistryClient: SchemaRegistryClient = CachedSchemaRegistryClient(
        RestService(ConfigurationLoader.getStringOrFail("schema.registry.url")),
        AbstractKafkaSchemaSerDeConfig.MAX_SCHEMAS_PER_SUBJECT_DEFAULT,
        loadSchemaRegistryClientConfig(),
    )
    val consumerConfig: Map<String, String> = ConfigurationLoader.loadKafkaConsumerConfigurations() +
        mapOf(
            ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG to "false",
        )
    val yfConsumerConfig: Map<String, String> = ConfigurationLoader.loadKafkaConsumerConfigurations() +
        mapOf(
            ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG to "false",
            ConsumerConfig.GROUP_ID_CONFIG to ConfigurationLoader.getStringOrFail("group.id.yf"),
        )
    val pollConfig = PollConfig(
        Duration.parse(ConfigurationLoader.getStringOrFail("poll.timeout")),
        ConfigurationLoader.getStringOrFail("poll.interval_ms").toLong(),
        Duration.parse(ConfigurationLoader.getStringOrFail("process.timeout")),
    )

    val skuSpecApp = SkuSpecificationApp(
        meterRegistry,
        consumerConfig = consumerConfig,
        schemaRegistryClient = schemaRegistryClient,
        pollConfig = pollConfig,
    )

    val yfSkuSpecApp = YfSkuSpecificationApp(
        meterRegistry,
        dslContext = dslContext,
        consumerConfig = yfConsumerConfig,
        pollConfig = pollConfig,
    )
    val supplierSkuApp = SupplierSkuApp(
        meterRegistry,
        dslContext = dslContext,
        consumerConfig = consumerConfig,
        schemaRegistryClient = schemaRegistryClient,
        pollConfig = pollConfig,
    )

    val supplierSkuPricingApp = SupplierSkuPricingApp(
        meterRegistry,
        dslContext = dslContext,
        consumerConfig = consumerConfig,
        schemaRegistryClient = schemaRegistryClient,
        pollConfig = pollConfig,
    )

    withContext(Dispatchers.IO) {
        repeat(parallelism) {
            launch { skuSpecApp.runApp() }
        }
        repeat(parallelism) {
            launch { supplierSkuApp.runApp() }
        }
        repeat(parallelism) {
            launch { supplierSkuPricingApp.runApp() }
        }
        repeat(parallelism) {
            launch { yfSkuSpecApp.runApp() }
        }
    }
}
