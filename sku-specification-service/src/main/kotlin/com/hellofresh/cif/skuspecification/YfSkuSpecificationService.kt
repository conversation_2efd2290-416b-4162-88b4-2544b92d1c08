package com.hellofresh.cif.skuspecification

import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.skuspecification.model.SkuSpecification
import com.hellofresh.cif.skuspecification.youfoodz.YfSkuSpecificationRepositoryImpl
import com.hellofresh.proto.stream.ye.planning.yfCulinarySku.v1.YfCulinarySku
import com.hellofresh.proto.stream.ye.planning.yfCulinarySku.v1.YfCulinarySku.UOM
import java.util.UUID
import org.apache.kafka.clients.consumer.ConsumerRecords

const val YOUFOODZ_COOLING_TYPE = "Ambient"
const val YOUFOODZ_PACKAGING = "Meal Kit"

class YfSkuSpecificationService(
    private val repository: YfSkuSpecificationRepositoryImpl
) {
    suspend fun save(specifications: ConsumerRecords<String, YfCulinarySku>) {
        val skus = specifications.map { mapToYfSkuSpecification(it.value()) }
        repository.saveSkuSpecification(skus)
    }

    private fun mapToYfSkuSpecification(csku: YfCulinarySku): SkuSpecification =
        SkuSpecification(
            id = UUID.fromString(csku.id),
            coolingType = YOUFOODZ_COOLING_TYPE,
            name = csku.name,
            packaging = YOUFOODZ_PACKAGING,
            code = csku.code,
            category = csku.category,
            parentId = null,
            acceptableCodeLife = csku.acceptableCodeLife,
            market = csku.market.lowercase(),
            uom = mapToSkuUOM(csku.baseUom),
            fumigationAllowed = null,
        )

    companion object {
        private fun mapToSkuUOM(uom: UOM): SkuUOM =
            when (uom) {
                UOM.UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
                UOM.UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
                UOM.UOM_UNIT -> SkuUOM.UOM_UNIT
                UOM.UOM_KG -> SkuUOM.UOM_KG
                UOM.UOM_LBS -> SkuUOM.UOM_LBS
                UOM.UOM_GAL -> SkuUOM.UOM_GAL
                UOM.UOM_LITRE -> SkuUOM.UOM_LITRE
                UOM.UOM_OZ -> SkuUOM.UOM_OZ
            }
    }
}
