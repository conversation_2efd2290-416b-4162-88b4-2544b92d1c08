package com.hellofresh.cif.skuspecification.supplierskupricing

import org.apache.kafka.clients.consumer.ConsumerRecords

class SupplierSkuPricingService(private val supplierSkuRepository: SupplierSkuPricingRepository) {

    suspend fun saveSupplierSkuPricing(supplierSkuPricing: ConsumerRecords<String, SupplierSkuPricing>) {
        val supplierSkuPricingList = supplierSkuPricing
            .mapNotNull { it.value() }
            .toSet()
        if (supplierSkuPricingList.isNotEmpty()) {
            supplierSkuRepository.saveSupplierSkuPricing(supplierSkuPricingList)
        }
    }
}
