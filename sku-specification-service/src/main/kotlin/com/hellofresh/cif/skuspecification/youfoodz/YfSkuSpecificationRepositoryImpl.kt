package com.hellofresh.cif.skuspecification.youfoodz

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.skuSpecification.schema.enums.Uom
import com.hellofresh.cif.skuSpecification.schema.tables.SkuSpecificationYf
import com.hellofresh.cif.skuspecification.model.SkuSpecification
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.Batch
import org.jooq.impl.DSL
import org.jooq.impl.DSL.insertInto

class YfSkuSpecificationRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext
) : YfSkuSpecificationRepository {
    override suspend fun saveSkuSpecification(skuSpecification: List<SkuSpecification>) {
        val batchQueryBuilder = BatchQueryBuilder(metricsDSLContext)

        skuSpecification.forEach { sku ->
            batchQueryBuilder.bind(sku)
        }

        batchQueryBuilder.runnableQuery.executeAsync().await()
    }

    companion object {

        fun mapToDbUom(skuUOM: SkuUOM) =
            when (skuUOM) {
                SkuUOM.UOM_UNSPECIFIED -> Uom.UOM_UNSPECIFIED
                SkuUOM.UOM_UNRECOGNIZED -> Uom.UOM_UNRECOGNIZED
                SkuUOM.UOM_UNIT -> Uom.UOM_UNIT
                SkuUOM.UOM_KG -> Uom.UOM_KG
                SkuUOM.UOM_LBS -> Uom.UOM_LBS
                SkuUOM.UOM_GAL -> Uom.UOM_GAL
                SkuUOM.UOM_LITRE -> Uom.UOM_LITRE
                SkuUOM.UOM_OZ -> Uom.UOM_OZ
            }
    }

    private class BatchQueryBuilder(dsl: MetricsDSLContext) {
        private val query = dsl.withTagName("insert-yf-sku-specification")
            .batch(
                SkuSpecificationYf.SKU_SPECIFICATION_YF.run {
                    insertInto(this)
                        .columns(
                            ID,
                            COOLING_TYPE,
                            NAME,
                            PACKAGING,
                            CODE,
                            CATEGORY,
                            PARENT_ID,
                            ACCEPTABLE_CODE_LIFE,
                            MARKET,
                            UOM,
                        )
                        .values(UUID.randomUUID(), null, null, null, null, null, null, null, null, Uom.UOM_UNIT)
                        .onDuplicateKeyUpdate()
                        .set(COOLING_TYPE, DSL.excluded(COOLING_TYPE))
                        .set(NAME, DSL.excluded(NAME))
                        .set(PACKAGING, DSL.excluded(PACKAGING))
                        .set(CODE, DSL.excluded(CODE))
                        .set(CATEGORY, DSL.excluded(CATEGORY))
                        .set(PARENT_ID, DSL.excluded(PARENT_ID))
                        .set(ACCEPTABLE_CODE_LIFE, DSL.excluded(ACCEPTABLE_CODE_LIFE))
                        .set(MARKET, DSL.excluded(MARKET))
                        .set(UOM, DSL.excluded(UOM))
                },
            )

        lateinit var runnableQuery: RunnableQuery

        fun bind(sku: SkuSpecification): RunnableQuery {
            if (!this::runnableQuery.isInitialized) {
                runnableQuery = RunnableQuery(query)
            }

            query.bind(
                sku.id,
                sku.coolingType,
                sku.name,
                sku.packaging,
                sku.code,
                sku.category,
                sku.parentId,
                sku.acceptableCodeLife,
                sku.market,
                mapToDbUom(sku.uom),
            )

            return runnableQuery
        }

        class RunnableQuery(val query: Batch) {
            fun executeAsync() = query.executeAsync()
        }
    }
}
