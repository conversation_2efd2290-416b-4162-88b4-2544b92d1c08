package com.hellofresh.cif.skuspecification.supplierskupricing

import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient
import io.confluent.kafka.serializers.KafkaAvroDeserializer
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.UUID
import org.apache.avro.generic.GenericRecord
import org.apache.avro.util.Utf8
import org.apache.kafka.common.serialization.Deserializer

class SupplierSkuPricingDeserializer(schemaRegistryClient: SchemaRegistryClient) : Deserializer<SupplierSkuPricing> {
    private val kafkaAvroDeserializer = KafkaAvroDeserializer(schemaRegistryClient)
    private val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssX")

    @Suppress("VariableNaming")
    override fun deserialize(topic: String, data: ByteArray): SupplierSkuPricing? {
        val record = kafkaAvroDeserializer.deserialize(topic, data) as GenericRecord
        val id = extractStringField(record, "id")
        val supplierSkuId: String? = extractStringField(record, "supplier_sku_id")
        val marketValue: String = extractStringField(record, "market") ?: ""
        val leadTimeValue: Int = extractIntField(record, "lead_time")
        val enabledValue: Boolean = extractBooleanField(record, "enabled")
        val startDateValue: String? = extractStringField(record, "start_date")
        val endDateValue: String? = extractStringField(record, "end_date")
        if (startDateValue == null || endDateValue == null) {
            return null
        }
        return SupplierSkuPricing(
            id = UUID.fromString(id),
            supplierSkuId = UUID.fromString(supplierSkuId),
            enabled = enabledValue,
            leadTime = leadTimeValue,
            market = marketValue,
            startDate = LocalDate.parse(startDateValue, formatter),
            endDate = LocalDate.parse(endDateValue, formatter),
        )
    }

    private fun extractStringField(record: GenericRecord, fieldName: String) =
        if (record.hasField(fieldName)) {
            (record.get(fieldName) as Utf8?).let { it?.toString() }
        } else {
            null
        }
    private fun extractIntField(record: GenericRecord, fieldName: String) =
        if (record.hasField(fieldName)) {
            (record.get(fieldName)).let { it?.toString()?.toInt() } ?: 0
        } else {
            0
        }
    private fun extractBooleanField(record: GenericRecord, fieldName: String) =
        if (record.hasField(fieldName)) {
            (record.get(fieldName)).let { it?.toString().toBoolean() }
        } else {
            false
        }
}
