package com.hellofresh.cif.skuspecification.suppliersku

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient
import io.confluent.kafka.serializers.KafkaAvroDeserializer
import java.util.UUID
import org.apache.avro.generic.GenericRecord
import org.apache.avro.util.Utf8
import org.apache.kafka.common.serialization.Deserializer
import org.apache.logging.log4j.kotlin.Logging

class SupplierSkuDeserializer(schemaRegistryClient: SchemaRegistryClient) : Deserializer<SupplierSku> {
    private val kafkaAvroDeserializer = KafkaAvroDeserializer(schemaRegistryClient)

    @Suppress("VariableNaming")
    override fun deserialize(topic: String, data: ByteArray): SupplierSku? {
        val record = kafkaAvroDeserializer.deserialize(topic, data) as GenericRecord
        val supplierSkuStatus = extractStringField(record, "supplier_sku_status")
        val supplierSkuId: String? = extractStringField(record, "supplier_sku_id")
        val culinarySkuId: String? = extractStringField(record, "culinary_sku_id")
        val extras: String? = extractStringField(record, "extras")

        @Suppress("ComplexCondition")
        if (supplierSkuId == null ||
            culinarySkuId == null ||
            extras == null ||
            supplierSkuStatus == null
        ) {
            logger.info(
                "Invalid record, skipping. Either of supplierId, " +
                    "culinarySkuId, extras are null: $supplierSkuId, $culinarySkuId, $extras, $supplierSkuStatus"
            )
            return null
        }
        return objectMapper.readValue(extras, Extras::class.java)?.mlor?.let {
            SupplierSku(
                UUID.fromString(culinarySkuId),
                UUID.fromString(supplierSkuId),
                supplierSkuStatus,
                it
            )
        }
    }

    private fun extractStringField(record: GenericRecord, fieldName: String) =
        if (record.hasField(fieldName)) {
            (record.get(fieldName) as Utf8?).let { it?.toString() }
        } else {
            null
        }

    companion object : Logging {
        val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class Extras(
    @JsonProperty("min_shelf_life_on_deliver")
    val mlor: Int?
)
