package com.hellofresh.cif.skuspecification

import com.hellofresh.sku.models.SKU_SPECIFICATION_TOPIC
import com.hellofresh.sku.models.SkuSpecification
import java.util.UUID
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord

class SkuSpecificationService(
    private val producer: KafkaProducer<String, SkuSpecification>,
) {
    fun publish(specifications: ConsumerRecords<String, com.hellofresh.cif.skuspecification.model.SkuSpecification>) {
        specifications.map { mapToSkuSpecification(it.value()) }
            .forEach {
                producer.send(ProducerRecord(SKU_SPECIFICATION_TOPIC, it.first.toString(), it.second))
            }
    }

    private fun mapToSkuSpecification(csku: com.hellofresh.cif.skuspecification.model.SkuSpecification): Pair<UUID, SkuSpecification> =
        csku.id to SkuSpecification(
            coolingType = csku.coolingType,
            name = csku.name,
            packaging = csku.packaging,
            skuCode = csku.code,
            category = csku.category,
            parentId = csku.parentId,
            acceptableCodeLife = csku.acceptableCodeLife,
            market = csku.market,
            fumigationAllowed = csku.fumigationAllowed,
            uom = csku.uom,
            brands = csku.brands,
        )
}
