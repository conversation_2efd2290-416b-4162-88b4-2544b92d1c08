package com.hellofresh.cif.skuspecification.model

import com.hellofresh.cif.models.SkuUOM
import java.util.UUID

data class SkuSpecification(
    val id: UUID,
    val market: String,
    val category: String,
    val code: String,
    val name: String,
    val coolingType: String,
    val packaging: String,
    val parentId: UUID?,
    val acceptableCodeLife: Int,
    val fumigationAllowed: Boolean?,
    val uom: SkuUOM,
    val brands: List<String> = emptyList(),
)
