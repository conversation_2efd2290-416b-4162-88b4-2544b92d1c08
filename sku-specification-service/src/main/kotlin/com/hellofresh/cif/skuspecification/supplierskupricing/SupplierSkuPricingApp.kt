package com.hellofresh.cif.skuspecification.supplierskupricing

import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.lib.kafka.ConsumerProcessorConfig
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategy
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.shutdown.shutdownNeeded
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient
import io.micrometer.core.instrument.MeterRegistry
import org.apache.kafka.common.serialization.StringDeserializer

class SupplierSkuPricingApp(
    private val meterRegistry: MeterRegistry,
    private val dslContext: MetricsDSLContext,
    private val consumerConfig: Map<String, String>,
    private val schemaRegistryClient: SchemaRegistryClient,
    private val pollConfig: PollConfig,
) {
    suspend fun runApp() {
        shutdownNeeded {
            CoroutinesProcessor(
                pollConfig,
                supplierSkuPricingTopicsConsumerConfig(),
                meterRegistry,
                getSupplierSkuPricingService()::saveSupplierSkuPricing,
                handleDeserializationException = DeserializationExceptionStrategy.create(
                    ConfigurationLoader.getStringOrFail("consumer.poison-pill.strategy"),
                    meterRegistry,
                ),
                recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                    meterRegistry,
                    "supplier_sku_processor_write_failure",
                ),
            ).also {
                HealthChecks.add(it)
                StartUpChecks.add(it)
            }
        }.run()
    }

    private fun getSupplierSkuPricingService(): SupplierSkuPricingService {
        val supplierSkuPricingRepo = SupplierSkuPricingRepositoryImpl(dslContext)
        return SupplierSkuPricingService(supplierSkuPricingRepo)
    }

    private fun supplierSkuPricingTopicsConsumerConfig() =
        ConsumerProcessorConfig<String, SupplierSkuPricing>(
            consumerConfig,
            StringDeserializer(),
            SupplierSkuPricingDeserializer(schemaRegistryClient),
            supplierSKUPricingTopics,
        )
}
