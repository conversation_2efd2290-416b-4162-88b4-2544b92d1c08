package com.hellofresh.cif.skuspecification.suppliersku

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.skuSpecification.schema.tables.SupplierSku.SUPPLIER_SKU
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.DSLContext
import org.jooq.impl.DSL

class SupplierSkuRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext
) : SupplierSkuRepository {

    private val saveSupplierSkus = "save-supplier-skus"
    private val getMlorForSku = "get-supplier-sku-mlor"

    override suspend fun saveSupplierSkus(supplierSkus: Set<SupplierSku>) {
        val (toDelete, toUpsert) = supplierSkus.partition { it.mlor == 0 }
        metricsDSLContext.withTagName(saveSupplierSkus)
            .transactionAsync { txConf ->
                val supplierSkuIdsToDelete = toDelete.map { it.skuId }
                txConf.dsl().deleteFrom(SUPPLIER_SKU).where(SUPPLIER_SKU.SKU_ID.`in`(supplierSkuIdsToDelete)).execute()

                val nowUtc = OffsetDateTime.now(ZoneOffset.UTC)
                newBatchQueryBuilder(txConf.dsl()).let { queryBuilder ->
                    toUpsert
                        .map { v ->
                            queryBuilder.bind(
                                v.skuId,
                                v.supplierSkuId,
                                v.mlor,
                                v.status,
                                nowUtc,
                                nowUtc,
                                // on update
                                v.mlor,
                                v.status,
                            )
                        }
                        .lastOrNull()
                        ?.execute()
                }
            }.await()
    }

    override suspend fun getMlorDays(skuId: UUID): Int? =
        metricsDSLContext.withTagName(getMlorForSku)
            .select(DSL.min(SUPPLIER_SKU.MLOR_DAYS))
            .from(SUPPLIER_SKU)
            .where(SUPPLIER_SKU.SKU_ID.eq(skuId))
            .and(SUPPLIER_SKU.STATUS.equalIgnoreCase("active"))
            .groupBy(SUPPLIER_SKU.MLOR_DAYS)
            .fetchAsync().thenApply {
                if (it.size == 0) {
                    null
                } else {
                    it.first().value1()
                }
            }.await()

    private fun newBatchQueryBuilder(dsl: DSLContext) = dsl.batch(
        SUPPLIER_SKU.run {
            dsl.insertInto(this)
                .columns(
                    SKU_ID,
                    SUPPLIER_SKU_ID,
                    MLOR_DAYS,
                    STATUS,
                    CREATED_AT,
                    UPDATED_AT,
                )
                .values(UUID(0, 0), UUID(0, 0), -1, "", OffsetDateTime.MIN, OffsetDateTime.MIN)
                .onDuplicateKeyUpdate()
                .set(MLOR_DAYS, -1)
                .set(STATUS, "")
        },
    )
}
