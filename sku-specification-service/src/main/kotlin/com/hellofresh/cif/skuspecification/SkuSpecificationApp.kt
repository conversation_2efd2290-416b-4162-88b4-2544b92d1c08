package com.hellofresh.cif.skuspecification

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.lib.kafka.ConsumerProcessorConfig
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategy
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.kafka.serde.JacksonSerializer
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.sku.models.SkuSpecification
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient
import io.micrometer.core.instrument.MeterRegistry
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.common.serialization.StringDeserializer
import org.apache.kafka.common.serialization.StringSerializer

internal typealias SkuSpecificationConsumerConfig = Map<String, String>
internal typealias SkuSpecificationProducerConfig = Map<String, String>

class SkuSpecificationApp(
    private val meterRegistry: MeterRegistry,
    private val consumerConfig: Map<String, String>,
    private val producerConfig: SkuSpecificationProducerConfig = ConfigurationLoader.loadKafkaProducerConfigurations(),
    private val schemaRegistryClient: SchemaRegistryClient,
    private val pollConfig: PollConfig
) {
    suspend fun runApp() {
        val skuSpecificationService = SkuSpecificationService(skuSpecificationTopicProducer())
        shutdownNeeded {
            CoroutinesProcessor(
                pollConfig,
                rawCulinaryConsumerConfig(),
                meterRegistry,
                skuSpecificationService::publish,
                handleDeserializationException = DeserializationExceptionStrategy.create(
                    ConfigurationLoader.getStringOrFail("consumer.poison-pill.strategy"),
                    meterRegistry,
                ),
                recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                    meterRegistry,
                    "sku_specification_processor_write_failure",
                ),
            ).also {
                HealthChecks.add(it)
                StartUpChecks.add(it)
            }
        }.run()
    }

    private fun skuSpecificationTopicProducer(): KafkaProducer<String, SkuSpecification> =
        KafkaProducer(
            producerConfig,
            StringSerializer(),
            JacksonSerializer<SkuSpecification>(objectMapper),
        )

    private fun rawCulinaryConsumerConfig() =
        ConsumerProcessorConfig(
            consumerConfig,
            StringDeserializer(),
            SkuDeserializer(schemaRegistryClient),
            culinarySkuTopics,
        )

    companion object {
        private val objectMapper = ObjectMapper().findAndRegisterModules()
    }
}
