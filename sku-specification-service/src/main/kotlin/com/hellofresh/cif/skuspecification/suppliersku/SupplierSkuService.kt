package com.hellofresh.cif.skuspecification.suppliersku

import org.apache.kafka.clients.consumer.ConsumerRecords

class SupplierSkuService(private val supplierSkuRepository: SupplierSkuRepository) {

    suspend fun saveSupplierSkus(supplierSkus: ConsumerRecords<String, SupplierSku>) {
        val supplierSkusWithMlor = supplierSkus
            .mapNotNull { it.value() }
            .toSet()
        if (supplierSkusWithMlor.isNotEmpty()) {
            supplierSkuRepository.saveSupplierSkus(supplierSkusWithMlor)
        }
    }
}
