application.name=sku-specification-service
parallelism=1

group.id=csku-inventory-forecast.sku-specification-service.v14
group.id.yf=csku-inventory-forecast.sku-specification-service-yf.v2
auto.offset.reset=earliest

# 5 min
max.poll.interval.ms=600000

# poll will either wait 5 seconds or for the 20KB or 500 records
fetch.min.bytes=20000
fetch.max.wait.ms=5000
max.poll.records=500

poll.interval_ms=10000
poll.timeout=PT5S
process.timeout=PT15S

consumer.poison-pill.strategy=LOG_ERROR_IGNORE
