import io.gitlab.arturbosch.detekt.Detekt
import java.net.ServerSocket
import java.time.Duration
import nu.studer.gradle.jooq.JooqGenerate
import org.jetbrains.kotlin.konan.properties.loadProperties


plugins {
    id("com.hellofresh.cif.common-conventions")
    hellofresh.helm

    alias(libs.plugins.sonar)
    alias(libs.plugins.jacoco)
    alias(libs.plugins.gradle.docker.compose)
    alias(libs.plugins.jooq)
    alias(libs.plugins.detekt)
}

extra["rollout"] = false
val freeServerSocket = ServerSocket(0)
val jooqDbPort = freeServerSocket.localPort
freeServerSocket.close()
System.setProperty("INVENTORY_DB_JOOQ_PORT", "$jooqDbPort")

// region ---------------------------------------------------------------------- topic versioning experiment
@Suppress("unchecked_cast")
val topicVersions = provider {
    allprojects.map { "${it.projectDir}/src/main/resources/topic-versions.properties" }
        .filter { File(it).exists() }
        .map { loadProperties(it).toList() }
        .flatten().toMap() as Map<String, String>
}

@Suppress("MagicNumber")
tasks.register("runInventoryDbWithMigration").configure {
    dockerCompose {
        configurations {
            useComposeFiles.set(listOf("docker-compose-jooq.yaml"))
            setProjectName(null)
            waitForHealthyStateTimeout.set(Duration.ofSeconds(60))
        }
        environment.put("INVENTORY_DB_JOOQ_PORT", "$jooqDbPort")
        project.rootProject.tasks.composeUp.get().up()
    }
}

allprojects {
    pluginManager.apply("jacoco")
    tasks.withType<Jar>().configureEach {
        duplicatesStrategy = DuplicatesStrategy.INCLUDE
    }
    tasks.withType<JooqGenerate>().configureEach {
        this.dependsOn(":runInventoryDbWithMigration")
    }

    tasks.withType<com.hellofresh.gradle.helm.GenerateHelmChart>().configureEach {
        properties.putAll(topicVersions)

        // region -------------------------------------------------------------- deployment experiment
        // @see https://hellofresh.atlassian.net/browse/CIF-661
        // @see ./ci/helm-chart-gen.sh
        // https://github.com/GoogleContainerTools/jib/tree/master/jib-gradle-plugin#outputpaths-closure
        doLast {
            // TODO this can be configured, and we should use the real path it
            //      was configured to and not the default path.
            project.layout.buildDirectory.file("jib-image.digest").orNull?.let {
                copy {
                    duplicatesStrategy = DuplicatesStrategy.INCLUDE
                    from(it)
                    into(dst)
                }
            }
        }
        // endregion ----------------------------------------------------------- deployment experiment
    }
}

// endregion ------------------------------------------------------------------- topic versioning experiment

sonar {
    properties {
        property(
            "sonar.exclusions",
            "**/src/main/generated/**, **/models/**",
        )
        property("sonar.host.url", "https://sonarqube.tools-k8s.hellofresh.io")
        property("sonar.links.homepage", "https://github.com/hellofresh/csku-inventory-forecast")
        property(
            "sonar.links.issue",
            "https://hellofresh.atlassian.net/secure/RapidBoard.jspa?rapidView=72&projectKey=CIF",
        )
        property("sonar.links.ci", "https://ci.hellofresh.io/teams/scm.ordering/pipelines/csku-inventory-forecast")
        property("sonar.links.scm", "https://github.com/hellofresh/csku-inventory-forecast")
        property("sonar.links.scm_dev", "**************:hellofresh/csku-inventory-forecast")
        property(
            "sonar.coverage.jacoco.xmlReportPaths",
            "${project.layout.buildDirectory.get()}/reports/jacoco/jacocoAggregatedReport/jacocoAggregatedReport.xml",
        )
        // use absolute path to .detect due to a limitation of sonar-kotlin that checks only
        // sub-projects of depth one.
        property(
            "detekt.sonar.kotlin.config.path",
            "/runner/_work/csku-inventory-forecast/csku-inventory-forecast/.detekt.yaml"
        )
    }
}

tasks {
    withType<Detekt>().configureEach {
        val javaVersion = "20"

        config.from("$rootDir/.detekt.yaml")

        ignoreFailures = false
        jvmTarget = javaVersion
        buildUponDefaultConfig = true
        parallel = true
        setSource(files(projectDir))
        setIncludes(setOf("**/*.kt", "**/*.kts"))
        setExcludes(setOf("**/resources/**", "**/build/**", "**/generated/**"))

        val ci = System.getenv().containsKey("CI")
        reports {
            txt.required.set(false)
            html.required.set(!ci)
            xml.required.set(ci)
        }
    }

    register<Detekt>("detektFormat") {
        description = "Reformat all Kotlin files."
        autoCorrect = true
        ignoreFailures = true
    }

    check {
        dependsOn("detekt")
    }

    val cleanDetekt by registering(Delete::class) {
        setDelete(detekt.get().reportsDir.get())
    }

    clean {
        dependsOn(cleanDetekt)
    }
}

dependencies {
    detektPlugins(libs.detekt.formatting)
    detekt(libs.detekt.cli)
    detekt(libs.kotlin.compiler.embeddable)
}
