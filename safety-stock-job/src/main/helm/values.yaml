---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
# Deployment experiment, see build.gradle.kts
tag: 'latest'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

vaultNamespace: services/@projectName@

serviceAccountAnnotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/csku-inventory-forecast-@tier@-role"

deployments:
  app:
    replicaCount: 1
    minAvailable: 0
    deploymentStrategy:
      type: Recreate
    containerPorts:
      http: 8080
      http-actuator: 8081
    repository: '@dockerRepository@'
    pullPolicy: IfNotPresent
    resources:
      requests:
        memory: '3Gi'
        cpu: '2'
      limits:
        memory: '3Gi'
        cpu: '2'
    nodeSelector: { }
    tolerations: [ ]
    affinity: { }
    podAnnotations: { }
    env:
       HF_INVENTORY_DB_HOST: 'vault:@tier@/key-value/data/inventory#DB_REPLICA_HOST'
       HF_INVENTORY_READONLY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#READONLY_DB_USERNAME'
       HF_INVENTORY_READONLY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#READONLY_DB_PASSWORD'
       HF_INVENTORY_DB_MASTER_HOST: 'vault:@tier@/key-value/data/inventory#DB_MASTER_HOST'
       HF_INVENTORY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#DB_USERNAME'
       HF_INVENTORY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#DB_PASSWORD'
       HF_STATSIG_SDK_KEY: 'vault:@tier@/key-value/data/misc#STATSIG_SDK_KEY'
    hpa:
      enabled: false
    spotInstance:
      preferred: true
    livenessProbe:
      httpGet:
        path: /health
        port: 8081
      periodSeconds: 15
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    startupProbe:
      httpGet:
        path: /startup
        port: 8081
      failureThreshold: 2
      periodSeconds: 15

services:
  app:
    enablePrometheus: true
    metricPortName: 'http-actuator'
    metricPath: '/prometheus'
    enabled: true
    type: ClusterIP
    ports:
      http: 80
      http-actuator: 8081

configMap:
  HF_TIER: '@tier@'
  SLACK_ALERT_CHANNEL: '@slackAlertChannel@-@tier@'
  SLACK_WEBHOOK: 'vault:common/key-value/data/misc#SLACK_URL'
