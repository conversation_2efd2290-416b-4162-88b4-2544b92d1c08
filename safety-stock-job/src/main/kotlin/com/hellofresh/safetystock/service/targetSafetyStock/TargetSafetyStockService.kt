package com.hellofresh.safetystock.service.targetSafetyStock

import com.hellofresh.cif.safetystock.Configuration
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystock.model.SkuRiskRating.LOW
import com.hellofresh.safetystock.job.TargetSafetyStockJob.Companion.logger
import com.hellofresh.safetystock.repo.SafetyStockRepository
import java.math.BigDecimal

class TargetSafetyStockService(
    private val targetSafetyStockImportService: TargetSafetyStockImportService,
    private val safetyStockRepository: SafetyStockRepository,
) {

    suspend fun process() {
        logger.info("Starting to import the target safety stocks.")
        val targetSafetyStocks = targetSafetyStockImportService.process()
        if (targetSafetyStocks.isNotEmpty()) {
            logger.info("Preparing the import safety stocks completed = $targetSafetyStocks")
            val safetyStocks = targetSafetyStocks.map { toSafetyStock(it) }
            safetyStockRepository.upsertSafetyStocks(
                safetyStocks = safetyStocks,
                isAlgorithmForecastVariance = false,
            )
        } else {
            logger.info("Preparing the import safety stocks completed, 0 target Safety Stocks produced.")
        }
    }

    private fun toSafetyStock(targetSafetyStockImport: TargetSafetyStockImport) =
        SafetyStock(
            dcCode = targetSafetyStockImport.dcCode,
            skuId = targetSafetyStockImport.skuId,
            week = targetSafetyStockImport.week,
            value = targetSafetyStockImport.safetyStock,
            configuration = Configuration(
                riskMultiplier = BigDecimal.ZERO,
                skuRiskRating = LOW,
                bufferPercentage = BigDecimal.ZERO,
            ),
            strategy = targetSafetyStockImport.strategy,
        )
}
