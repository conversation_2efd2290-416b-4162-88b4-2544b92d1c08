package com.hellofresh.safetystock.service

import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.safetystock.Configuration
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystock.SafetyStockConfigurations
import com.hellofresh.safetystock.repo.SafetyStockDemandRepository
import com.hellofresh.safetystock.repo.SafetyStockSkuDemand
import com.hellofresh.safetystock.repo.buffer.SafetyStockBufferRepository
import com.hellofresh.safetystock.repo.buffer.SafetyStockBuffers
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.DayOfWeek
import java.time.LocalDate

class SafetyStockBuffersFormulaService(
    private val safetyStockDemandRepo: SafetyStockDemandRepository,
    private val safetyStockBufferRepository: SafetyStockBufferRepository
) : SafetyStockBase() {

    suspend fun calculateSafetyStocks(
        dcConfigs: Set<DistributionCenterConfiguration>,
        safetyStockConfigurations: SafetyStockConfigurations
    ): List<SafetyStock> =
        dcConfigs
            .groupBy { SafetyStockDcKey(it.productionStart, it.getLatestProductionStart()) }
            .flatMap { (safetyStockDcKey, dcConfigs) ->
                val (productionStartDay, latestProductionStart) = safetyStockDcKey

                val safetyStockDemands = safetyStockDemandRepo.fetchSafetyStockSkuDemand(
                    latestProductionStart,
                    dcConfigs.map { it.dcCode }.toSet(),
                ).groupBy { it.dcCode }

                val safetyStockBuffers =
                    safetyStockBufferRepository.fetchSafetyStockBuffersFromWeek(
                        DcWeek(latestProductionStart, productionStartDay).value,
                        dcConfigs.toSet(),
                    )

                dcConfigs
                    .flatMap {
                        calculateSafetyStocksForDcByWeeks(
                            it,
                            safetyStockDemands,
                            safetyStockConfigurations,
                            safetyStockBuffers,
                        )
                    }
            }

    private fun calculateSafetyStocksForDcByWeeks(
        distributionCenterConfiguration: DistributionCenterConfiguration,
        safetyStockSkuDemandByDc: Map<String, List<SafetyStockSkuDemand>>,
        safetyStockConfigurations: SafetyStockConfigurations,
        safetyStockBuffers: SafetyStockBuffers,
    ): List<SafetyStock> = run {
        val currentWeek = distributionCenterConfiguration.getCurrentWeek()
        val dcCode = distributionCenterConfiguration.dcCode
        val demandsBySku = safetyStockSkuDemandByDc[dcCode]?.groupBy { it.skuId } ?: emptyMap()

        demandsBySku.flatMap { (skuId, skuDemands) ->
            val skuDemandsByWeek = skuDemands.groupBy {
                ProductionWeek(it.date, distributionCenterConfiguration.productionStart)
            }

            skuDemandsByWeek.keys.maxOrNull()
                ?.let { maxDemandWeek ->
                    val dcMaxWeekTo = maxOf(maxDemandWeek, currentWeek)
                    (currentWeek..dcMaxWeekTo)
                        .map { safetyStockWeek ->
                            val configuration = safetyStockConfigurations.getConfiguration(dcCode, skuId, safetyStockWeek)
                            val buffer = safetyStockBuffers.getBuffer(dcCode, skuId, safetyStockWeek)
                            val skuSafetyStock = calculateSkuSafetyStock(
                                safetyStockWeek,
                                skuDemandsByWeek,
                                buffer.bufferPercentage,
                            )

                            SafetyStock(
                                dcCode,
                                skuId,
                                safetyStockWeek.dcWeek.toString(),
                                skuSafetyStock,
                                Configuration(
                                    riskMultiplier = configuration.riskMultiplier,
                                    skuRiskRating = configuration.skuRiskRating,
                                    bufferPercentage = buffer.bufferPercentage,
                                ),
                                strategy = SAFETY_STOCK_DEFAULT_STRATEGY,
                            )
                        }
                } ?: emptyList()
        }
    }

    private fun calculateSkuSafetyStock(
        dcWeek: ProductionWeek,
        skuDemands: Map<ProductionWeek, List<SafetyStockSkuDemand>>,
        bufferPercentage: BigDecimal,
    ): Long {
        val demandQuantityByWeek = skuDemands[dcWeek]?.sumOf { demand ->
            demand.quantity
        } ?: BigDecimal.ZERO

        return calculateSafetyStockFormula(
            demandQuantityByWeek,
            bufferPercentage,
        )
    }

    private fun calculateSafetyStockFormula(
        demandQty: BigDecimal,
        bufferPercentage: BigDecimal,
    ): Long {
        val safetyStockQty = demandQty
            .multiply(bufferPercentage)
            .setScale(0, RoundingMode.HALF_UP)
            .toLong()

        return capSafetyStock(safetyStockQty, demandQty.toLong())
    }

    companion object {

        private data class SafetyStockDcKey(
            val productionStart: DayOfWeek,
            val latestProductionStart: LocalDate,
        )
    }
}
