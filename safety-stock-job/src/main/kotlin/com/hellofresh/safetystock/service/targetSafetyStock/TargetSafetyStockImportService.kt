package com.hellofresh.safetystock.service.targetSafetyStock

import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.safetystock.ProcessFile
import com.hellofresh.cif.safetystock.model.FileType
import com.hellofresh.cif.safetystock.model.ParsedFile
import com.hellofresh.cif.safetystock.model.ProcessFileResult
import com.hellofresh.cif.skuSpecificationLib.SkuCodeDcKey
import com.hellofresh.cif.skuSpecificationLib.SkuCodeLookUp
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import java.time.Instant
import java.util.UUID
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging

private const val BUCKET_NAME = "hf-bi-dwh-uploader"
private const val TARGET_SAFETY_STOCK_PREFIX = "ip_safety_stock/target_safety_stock/"
private const val FILE_PROCESSED_TIME = 3600L

class TargetSafetyStockImportService(
    private val importer: S3Importer,
    private val dcConfigService: DcConfigService,
    private val skuSpecificationService: SkuSpecificationService,
    val s3BucketSuffix: String,
) {
    @get:Synchronized
    @set:Synchronized
    lateinit var lastFileTime: Instant

    suspend fun process(): List<TargetSafetyStockImport> {
        val result: List<TargetSafetyStockImport>
        val fileKeys = importer.listObjects("$BUCKET_NAME$s3BucketSuffix", TARGET_SAFETY_STOCK_PREFIX)
        if (fileKeys.isEmpty()) {
            logger.info("There are no target safety stocks files to be processed.")
            result = emptyList()
        } else {
            logger.info("Total target safety stock files to be processed = ${fileKeys.size}, fileKeys = $fileKeys")
            if (!this::lastFileTime.isInitialized) {
                lastFileTime = Instant.now().minusSeconds(FILE_PROCESSED_TIME)
            }
            val fileContents = fileKeys.filter { it.lastModified > lastFileTime }
                .mapNotNull { s3File ->
                    runCatching {
                        val content = withContext(Dispatchers.IO) {
                            importer.fetchObjectContent(s3File.bucket, s3File.key).readAllBytes()
                        }
                        content to s3File
                    }.onFailure {
                        logger.warn("Failed to fetch or read file ${s3File.key}: ${it.message}", it)
                    }.getOrNull()
                }.also { files ->
                    lastFileTime = files.maxOf { it.second.lastModified }
                }

            result = if (fileContents.isEmpty()) {
                logger.info("There are no target safety stocks file content to be processed.")
                emptyList()
            } else {
                fileContents.flatMap { (bytes, file) ->
                    val imports = processContent(bytes, file)
                    if (imports.isNotEmpty()) {
                        logger.info("Prepared target safety stocks = $imports")
                    } else {
                        logger.info("Processed an empty target safety stock file.")
                    }
                    imports
                }
            }
        }
        return result
    }

    private fun processContent(content: ByteArray, s3File: S3File): List<TargetSafetyStockImport> {
        val processFileResult = ProcessFile.processFileContent(
            s3File.fileName,
            FileType.TARGET_SAFETY_STOCK_IMPORT,
            content,
        )

        if (processFileResult.hasSevereViolations()) {
            val message = processFileResult.violations.joinToString { it.message }
            logger.error(
                "Error while processing the target safety stock import file $s3File, because: $message.",
            )
        } else if (processFileResult.violations.isNotEmpty()) {
            val message = processFileResult.violations.joinToString { it.message }
            logger.warn(
                "Warning while processing the target safety stock import file $s3File, because: $message.",
            )
        }
        return if (processFileResult.run { !hasSevereViolations() && violations.isEmpty() }) {
            toTargetSafetyStockImport(processFileResult)
        } else {
            emptyList()
        }
    }

    private fun toTargetSafetyStockImport(result: ProcessFileResult): List<TargetSafetyStockImport> {
        val rows = result.parsedFile?.data ?: emptyList()
        return if (rows.isNotEmpty()) {
            val distributionCenters = rows.map { it[ParsedFile.TargetSafetyStockImport.DC_HEADER] }
                .distinct().mapNotNull { dcConfigService.dcConfigurations[it] } ?: emptyList()

            val skuCodeLookUp: SkuCodeLookUp = skuSpecificationService.skuCodeLookUp(distributionCenters)

            rows.mapNotNull { row ->
                val dcCode = row[ParsedFile.TargetSafetyStockImport.DC_HEADER]
                getSkuId(skuCodeLookUp, dcCode, row[ParsedFile.TargetSafetyStockImport.SKU_HEADER])
                    ?.let { skuId ->
                        TargetSafetyStockImport(
                            dcCode,
                            skuId,
                            row[ParsedFile.TargetSafetyStockImport.WEEK_HEADER],
                            row[ParsedFile.TargetSafetyStockImport.STRATEGY],
                            row[ParsedFile.TargetSafetyStockImport.SAFETY_STOCK_HEADER].toDouble().toLong(),
                        )
                    }
            }
        } else {
            emptyList()
        }
    }

    private fun getSkuId(skuCodeLookUp: SkuCodeLookUp, dcCode: String, skuCode: String): UUID? =
        skuCodeLookUp[SkuCodeDcKey(skuCode, dcCode)]?.first
            ?: run {
                logger.error("SkuId not found for dc: $dcCode and skuCode: $skuCode while processing target safety stock import")
                null
            }

    companion object : Logging
}

data class TargetSafetyStockImport(val dcCode: String, val skuId: UUID, val week: String, val strategy: String, val safetyStock: Long) {
    companion object
}
