package com.hellofresh.safetystock.service

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DcConfigWeekRange
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.cif.safetystock.model.SafetyStockValue
import com.hellofresh.cif.skuinput.repo.SupplierRepositoryImpl
import com.hellofresh.safetystock.repo.SafetyStockDemandRepository
import com.hellofresh.safetystock.repo.SafetyStockSkuDemand
import com.hellofresh.sku.models.SupplierSkuDetail
import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode.HALF_UP
import java.time.DayOfWeek
import java.time.LocalDate
import java.util.UUID
import org.apache.commons.math3.stat.descriptive.moment.Mean
import org.apache.commons.math3.stat.descriptive.moment.StandardDeviation

private const val SAFETYSTOCK_TIMEINCREMENT = 7
private const val SAFETYSTOCK_SERVICELEVEL = 1.88
private const val LEAD_TIME_WEEK_DAYS = 7

private const val SAFETY_STOCK_DEMAND_WEEKS = 7

class SafetyStockEuRepository(
    private val safetyStockParams: SafetyStockFormulaParams,
    metricsDSLContext: MetricsDSLContext,
) : SafetyStockBase() {

    private val supplierRepository = SupplierRepositoryImpl(metricsDSLContext)
    private val safetyStockDemandRepository = SafetyStockDemandRepository(metricsDSLContext)

    private val safetyStockTimeIncrement = BigDecimal(SAFETYSTOCK_TIMEINCREMENT)
    private val safetyStockServiceLevel = BigDecimal(SAFETYSTOCK_SERVICELEVEL)
    private val stdDevLeadTimeFactor = BigDecimal(safetyStockParams.standardDeviationLeadTimeFactor)
    private val leadTimeWeekDays = BigDecimal(LEAD_TIME_WEEK_DAYS)

    suspend fun fetchSkuSafetyStock(
        dcConfigs: Set<DistributionCenterConfiguration>
    ): Map<SafetyStockKey, SafetyStockValue> {
        val supplierSkuDetails = supplierRepository.fetchSupplierSkuDetails(
            dcConfigs.map { it.market }.toSet(),
            null,
        )

        return dcConfigs
            .groupBy { SafetyStockDcKey(it.productionStart, it.getLatestProductionStart()) }
            .flatMap { (safetyStockDcKey, dcConfigs) ->
                val safetyStockKeyLeadTimes = calculateSupplierSkuLeadTimes(dcConfigs, supplierSkuDetails)
                if (safetyStockKeyLeadTimes.isNotEmpty()) {
                    val safetyStockDemands = fetchSafetyStockDemand(
                        safetyStockDcKey.latestProductionStart,
                        safetyStockKeyLeadTimes.keys.toSet(),
                        safetyStockKeyLeadTimes.flatMap { it.value.map { it.skuId } }.toSet(),
                    )

                    safetyStockKeyLeadTimes
                        .flatMap { (dcCode, skuLeadTimes) ->
                            calculateSafetyStocksForDc(
                                dcCode,
                                safetyStockDcKey.productionStart,
                                safetyStockDcKey.latestProductionStart,
                                skuLeadTimes,
                                safetyStockDemands,
                            )
                        }
                } else {
                    emptyList()
                }
            }.toMap()
    }

    private fun calculateSupplierSkuLeadTimes(
        dcConfigs: List<DistributionCenterConfiguration>,
        supplierSkuDetails: Map<UUID, List<SupplierSkuDetail>>
    ): Map<String, List<SkuLeadTime>> =
        dcConfigs.groupBy { it.zoneId }
            .flatMap { (zoneId, dcConfigs) ->
                val now = LocalDate.now(zoneId)
                supplierSkuDetails
                    .mapNotNull { (skuId, supplierSkuDetails) ->
                        supplierSkuDetails
                            .filter { skuDetail -> safetyStockParams.minimumAllowedMlor?.let { skuDetail.mlor >= it } ?: true }
                            .mapNotNull { it.getMaxLeadTimeByDate(now) }.maxOfOrNull { it }
                            ?.let { skuId to it }
                    }.map { (skuId, leadTime) -> SkuLeadTime(skuId, leadTime) }
                    .let {
                        dcConfigs.map { dcConfig -> dcConfig.dcCode to it }
                    }
            }.toMap()

    private fun calculateSafetyStocksForDc(
        dcCode: String,
        productionStartDay: DayOfWeek,
        latestProductionStart: LocalDate,
        skuLeadTimes: List<SkuLeadTime>,
        allDemand: List<SafetyStockSkuDemand>
    ): List<Pair<SafetyStockKey, SafetyStockValue>> = run {
        val allDcDemands = allDemand.filter { it.dcCode == dcCode }
        val allDemandsByWeek = allDcDemands.groupBy { ProductionWeek(it.date, productionStartDay) }

        allDemandsByWeek.keys.maxOrNull()
            ?.let { maxDemandWeek ->

                val currentWeek = ProductionWeek(latestProductionStart, productionStartDay)
                val dcWeekTo = maxOf(maxDemandWeek, currentWeek)
                DcConfigWeekRange(currentWeek, dcWeekTo)
                    .flatMap { safetyStockWeek ->

                        val demandWeekTo =
                            minOf(maxOf(maxDemandWeek, safetyStockWeek), safetyStockWeek.plusWeeks(SAFETY_STOCK_DEMAND_WEEKS - 1))
                        val demandWeekRange =
                            DcConfigWeekRange(demandWeekTo.minusWeeks(SAFETY_STOCK_DEMAND_WEEKS - 1), demandWeekTo)

                        skuLeadTimes.map { (skuId, leadTime) ->
                            val safetyStockValue = calculateSkuSafetyStock(
                                safetyStockDcWeekRange = demandWeekRange, leadTime,
                                skuDemands = allDemandsByWeek
                                    .mapValues { (_, demands) ->
                                        demands.filter { it.skuId == skuId }
                                    },
                            )
                            SafetyStockKey(dcCode, safetyStockWeek.dcWeek, skuId) to
                                SafetyStockValue(
                                    safetyStock = safetyStockValue,
                                    strategy = SAFETY_STOCK_DEFAULT_STRATEGY
                                )
                        }
                    }
            } ?: emptyList()
    }

    private fun calculateSkuSafetyStock(
        safetyStockDcWeekRange: DcConfigWeekRange,
        skuLeadTime: Int,
        skuDemands: Map<ProductionWeek, List<SafetyStockSkuDemand>>,
    ): Long {
        val demandByWeekInRange = skuDemands.filter { (key, value) -> safetyStockDcWeekRange.contains(key) && value.isNotEmpty() }

        val demandByWeek = safetyStockDcWeekRange.map { week ->
            demandByWeekInRange[week]?.sumOf { it.quantity }?.toDouble() ?: 0.0
        }.toDoubleArray()

        val avgDemandByWeek = Mean().evaluate(demandByWeek)

        val stdDevDemand = StandardDeviation().evaluate(demandByWeek, avgDemandByWeek)

        val safetyStockQty = calculateSafetyStockFormula(
            avgDemandByWeek.toBigDecimal(),
            stdDevDemand.toBigDecimal(),
            skuLeadTime
        )
            .times(calculateOccurrenceFactor(demandByWeekInRange.size))
            .setScale(0, HALF_UP)
            .toLong()

        return capSafetyStock(safetyStockQty, demandByWeek.sum().toLong())
    }

    private fun calculateSafetyStockFormula(avgDemand: BigDecimal, stdDevDemand: BigDecimal, leadTime: Int) =
        safetyStockServiceLevel.multiply(
            (
                (
                    (leadTime.toBigDecimal().divide(safetyStockTimeIncrement, MathContext.DECIMAL64))
                        .multiply(
                            stdDevDemand.pow(2),
                        )
                    ).plus(
                    (calculateStdDevLeadTime(leadTime).multiply(avgDemand))
                        .pow(2),
                )
                ).sqrt(MathContext.DECIMAL64),
        )

    private fun calculateStdDevLeadTime(leadTime: Int) =
        stdDevLeadTimeFactor.multiply(leadTime.toBigDecimal().div(leadTimeWeekDays))

    @Suppress("MagicNumber")
    private fun calculateOccurrenceFactor(numberOfDemandWeeks: Int) =
        if (safetyStockParams.recurrenceFactorEnabled) {
            when {
                numberOfDemandWeeks > 4 -> BigDecimal.ONE
                numberOfDemandWeeks in 3..4 -> BigDecimal(0.5)
                else -> BigDecimal.ZERO
            }
        } else {
            BigDecimal.ONE
        }

    private suspend fun fetchSafetyStockDemand(
        latestProductionStart: LocalDate,
        dcCodes: Set<String>,
        skuIds: Set<UUID>
    ) =
        safetyStockDemandRepository.fetchSafetyStockSkuDemand(
            dateFrom = latestProductionStart.minusWeeks(SAFETY_STOCK_DEMAND_WEEKS.toLong()),
            dcCodes = dcCodes,
            skuIds = skuIds,
        )
}

private data class SkuLeadTime(val skuId: UUID, val leadTime: Int)
private data class SafetyStockDcKey(
    val productionStart: DayOfWeek,
    val latestProductionStart: LocalDate,
)

data class SafetyStockFormulaParams(
    val standardDeviationLeadTimeFactor: Double,
    val minimumAllowedMlor: Int?,
    val recurrenceFactorEnabled: Boolean
)
