package com.hellofresh.safetystock.service

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystock.SafetyStockConfigurations
import com.hellofresh.cif.safetystock.toConfiguration
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationConfigRepository
import com.hellofresh.safetystock.repo.SafetyStockImportRepository
import com.hellofresh.safetystock.safetymultiplier.service.SafetyStockImport
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging

class SafetyStockImportRecommendationFormulaService(
    private val safetyStockImportRepository: SafetyStockImportRepository,
    private val supplyQuantityRecommendationConfigRepository: SupplyQuantityRecommendationConfigRepository,
    private val safetyStockRiskFormulaService: SafetyStockRiskFormulaService
) {

    suspend fun calculateSafetyStocks(
        dcConfigs: Set<DistributionCenterConfiguration>,
        safetyStockConfigurations: SafetyStockConfigurations
    ): List<SafetyStock> =
        if (dcConfigs.isEmpty()) {
            emptyList()
        } else {
            val dcConfigMap = dcConfigs.associateBy { it.dcCode }
            withContext(Dispatchers.IO) {
                val recommendationEnabledSafetyStockImportsDeferred =
                    async { fetchRecommendationEnabledSafetyStockImports(dcConfigs) }

                val safetyStockFromMultiplier = safetyStockRiskFormulaService.calculateSafetyStocks(
                    dcConfigs,
                    safetyStockConfigurations,
                ).groupBy { it.dcCode to it.skuId }

                val enabledSafetyStockImportsByDcAndSku = recommendationEnabledSafetyStockImportsDeferred.await()
                    .groupBy { it.dcCode to it.skuId }

                (enabledSafetyStockImportsByDcAndSku.keys + safetyStockFromMultiplier.keys)
                    .flatMap { dcSkuKey ->
                        // Take safety stock imports from enabled recommendation skus, default to risk multiplier values
                        enabledSafetyStockImportsByDcAndSku[dcSkuKey]
                            ?.let { imports ->
                                dcConfigMap[dcSkuKey.first]
                                    ?.let { dcConfig -> imports.map { toSafetyStock(it, safetyStockConfigurations, dcConfig) } }
                            }
                            ?: (safetyStockFromMultiplier[dcSkuKey] ?: emptyList())
                    }
            }
        }

    private fun toSafetyStock(
        safetyStockImport: SafetyStockImport,
        safetyStockConfigurations: SafetyStockConfigurations,
        distributionCenterConfiguration: DistributionCenterConfiguration
    ) =
        with(safetyStockImport) {
            SafetyStock(
                dcCode,
                skuId,
                week,
                safetyStock,
                safetyStockConfigurations.getConfiguration(
                    dcCode,
                    skuId,
                    ProductionWeek(
                        week,
                        distributionCenterConfiguration.productionStart,
                        distributionCenterConfiguration.zoneId,
                    ),
                ).toConfiguration(),
                strategy = DEFAULT_STRATEGY,
            )
        }

    private suspend fun fetchRecommendationEnabledSafetyStockImports(distributionCenters: Set<DistributionCenterConfiguration>): List<SafetyStockImport> =
        if (distributionCenters.isEmpty()) {
            emptyList()
        } else {
            distributionCenters.groupBy { it.getCurrentWeek().weekString }
                .flatMap { (week, dcs) ->
                    val sqrConfigurations = supplyQuantityRecommendationConfigRepository.fetchSupplyQuantityRecommendationConfigurations(
                        week,
                        dcs,
                    )
                    dcs.map { it.dcCode to sqrConfigurations.getRecommendationEnabledSkus(it.dcCode) }
                        .filter { (_, skus) -> skus.isNotEmpty() }
                        .flatMap { (dc, skus) ->
                            safetyStockImportRepository.fetchSafetyStockImports(
                                week,
                                setOf(dc),
                                skus.toSet(),
                            )
                        }
                }
        }

    companion object : Logging
}
