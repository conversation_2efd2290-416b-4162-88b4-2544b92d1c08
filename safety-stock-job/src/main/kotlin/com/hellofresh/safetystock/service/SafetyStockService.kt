package com.hellofresh.safetystock.service

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.featureflags.Context.MARKET
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.SafetyStockMultiplierFormula
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystock.SafetyStockConfigurations
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationConfigRepository
import com.hellofresh.safetystock.repo.SafetyStockDemandRepository
import com.hellofresh.safetystock.repo.SafetyStockImportRepository
import com.hellofresh.safetystock.repo.buffer.SafetyStockBufferRepository
import com.hellofresh.safetystock.safetymultiplier.repo.SafetyStockMultiplierRepositoryImpl

const val DACH_MARKET = "DACH"
private const val EU_MARKET = "EU"
private const val US_MARKET = "US"

private const val EU_STD_DEV_LEAD_TIME_FACTOR = 0.10

class SafetyStockService(
    readDslContext: MetricsDSLContext,
    readWriteDslContext: MetricsDSLContext,
    private val statsigFeatureFlagClient: StatsigFeatureFlagClient,
) {

    private val safetyEuStockRepository = SafetyStockEuRepository(
        SafetyStockFormulaParams(
            standardDeviationLeadTimeFactor = EU_STD_DEV_LEAD_TIME_FACTOR,
            minimumAllowedMlor = null,
            recurrenceFactorEnabled = false,
        ),
        readDslContext,
    )

    private val safetyStockRiskFormulaService = SafetyStockRiskFormulaService(
        SafetyStockDemandRepository(readDslContext),
        SafetyStockMultiplierRepositoryImpl(readDslContext, readWriteDslContext),
    )

    private val safetyStockRecommendationFormulaService = SafetyStockImportRecommendationFormulaService(
        SafetyStockImportRepository(readDslContext, readWriteDslContext),
        SupplyQuantityRecommendationConfigRepository(readDslContext),
        safetyStockRiskFormulaService,
    )

    private val safetyStockBuffersFormulaService = SafetyStockBuffersFormulaService(
        SafetyStockDemandRepository(readDslContext),
        SafetyStockBufferRepository(readDslContext),
    )

    suspend fun fetchSkuSafetyStock(
        dcConfigs: Set<DistributionCenterConfiguration>,
        safetyStockConfigurations: SafetyStockConfigurations,
    ): List<SafetyStock> =
        dcConfigs.groupBy { it.market }
            .flatMap { (market, dcs) ->

                if (isRiskMultiplierFormulaMarket(market)) {
                    if (market == DACH_MARKET) {
                        safetyStockRecommendationFormulaService.calculateSafetyStocks(
                            dcConfigs,
                            safetyStockConfigurations,
                        )
                    } else {
                        safetyStockRiskFormulaService.calculateSafetyStocks(dcs.toSet(), safetyStockConfigurations)
                    }
                } else {
                    when (market) {
                        US_MARKET -> safetyStockBuffersFormulaService.calculateSafetyStocks(
                            dcs.toSet(),
                            safetyStockConfigurations,
                        )

                        EU_MARKET -> fetchEuSafetyStocks(dcs, safetyStockConfigurations)

                        else -> emptyList()
                    }
                }
            }

    private suspend fun fetchEuSafetyStocks(
        dcs: List<DistributionCenterConfiguration>,
        safetyStockConfigurations: SafetyStockConfigurations
    ): List<SafetyStock> {
        val dcMap = dcs.associateBy { it.dcCode }
        return safetyEuStockRepository.fetchSkuSafetyStock(dcs.toSet())
            .mapNotNull { (key, safetyStockValue) ->
                dcMap[key.dcCode]?.let { dc ->
                    val configuration = safetyStockConfigurations.getConfiguration(
                        key.dcCode,
                        key.skuId,
                        ProductionWeek(key.dcWeek.toString(), dc.productionStart, dc.zoneId),
                    )
                    SafetyStock(
                        key.dcCode,
                        key.skuId,
                        key.dcWeek.toString(),
                        safetyStockValue.safetyStock,
                        configuration.riskMultiplier,
                        configuration.skuRiskRating,
                        strategy = safetyStockValue.strategy,
                    )
                }
            }
    }

    private fun isRiskMultiplierFormulaMarket(market: String) =
        statsigFeatureFlagClient.isEnabledFor(SafetyStockMultiplierFormula(setOf(ContextData(MARKET, market))))
}
