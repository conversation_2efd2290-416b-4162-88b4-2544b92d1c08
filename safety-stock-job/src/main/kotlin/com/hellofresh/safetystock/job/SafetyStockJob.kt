package com.hellofresh.safetystock.job

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.safetystock.DEFAULT_TARGET_SAFETY_STOCK_STRATEGY
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystock.SafetyStockConfigurations
import com.hellofresh.cif.safetystock.repository.SafetyStockConfigurationRepository
import com.hellofresh.safetystock.repo.SafetyStockRepository
import com.hellofresh.safetystock.repo.SafetyStockRepositoryImpl.DcAndWeek
import com.hellofresh.safetystock.service.SafetyStockService
import org.apache.logging.log4j.kotlin.Logging

class SafetyStockJob(
    private val safetyStockService: SafetyStockService,
    private val dcConfigService: DcConfigService,
    private val safetyStockRepository: SafetyStockRepository,
    private val safetyStockConfigurationRepository: SafetyStockConfigurationRepository

) {
    suspend fun run() {
        val distributionCenters = dcConfigService.dcConfigurations.values.filter {
            it.enabled
        }.toSet()

        val safetyStockConfigurations = safetyStockConfigurationRepository.fetchSafetyStockConfigurations(
            distributionCenters,
        )

        val newSafetyStocks =
            safetyStockService.fetchSkuSafetyStock(
                distributionCenters,
                safetyStockConfigurations,
            )

        val oldSafetyStocks = createMissingSafetyStocks(newSafetyStocks, safetyStockConfigurations, distributionCenters)

        val safetyStocks = newSafetyStocks + oldSafetyStocks

        if (safetyStocks.isNotEmpty()) {
            safetyStockRepository.upsertSafetyStocks(
                safetyStocks = safetyStocks,
                isAlgorithmForecastVariance = true,
            )
        }
    }

    private suspend fun createMissingSafetyStocks(
        safetyStocks: List<SafetyStock>,
        safetyStockConfigurations: SafetyStockConfigurations,
        distributionCenters: Set<DistributionCenterConfiguration>
    ): List<SafetyStock> {
        val safetyStockKeys = safetyStocks.map { it.toKey() }
        val dcAndWeeks = safetyStockKeys
            .groupBy { it.dcCode }
            .mapValues { (_, values) -> values.minOf { it.dcWeek.toString() } }
            .map { (dc, week) -> DcAndWeek(dc, week) }.toSet()
        val dcMap = distributionCenters.associateBy { it.dcCode }

        val existingKeys = safetyStockRepository.fetchSafetyStocksFromDcAndWeek(dcAndWeeks).map { it.toKey() }

        return existingKeys.filter { !safetyStockKeys.contains(it) }
            .mapNotNull { key ->
                dcMap[key.dcCode]?.let { dc ->
                    val configuration = safetyStockConfigurations.getConfiguration(
                        key.dcCode,
                        key.skuId,
                        ProductionWeek(key.dcWeek.toString(), dc.productionStart, dc.zoneId),
                    )
                    SafetyStock(
                        key.dcCode,
                        key.skuId,
                        key.dcWeek.toString(),
                        0L,
                        configuration.riskMultiplier,
                        configuration.skuRiskRating,
                        strategy = DEFAULT_TARGET_SAFETY_STOCK_STRATEGY, // As we are creating missing safety stocks, we use the default strategy
                    )
                }
            }
    }

    companion object : Logging
}
