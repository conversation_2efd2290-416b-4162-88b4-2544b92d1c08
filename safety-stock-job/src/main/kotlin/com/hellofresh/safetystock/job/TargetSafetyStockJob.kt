package com.hellofresh.safetystock.job

import com.hellofresh.safetystock.service.targetSafetyStock.TargetSafetyStockService
import org.apache.logging.log4j.kotlin.Logging

class TargetSafetyStockJob(
    private val targetSafetyStockService: TargetSafetyStockService,
) {
    suspend fun run() {
        runCatching {
            logger.info("Starting to process the target safety stocks.")
            targetSafetyStockService.process()
            logger.info("Processing the target safety stocks completed.")
        }.onFailure {
            logger.info("Error occurred while processing the target safety stocks $it.")
        }
    }

    companion object : Logging
}
