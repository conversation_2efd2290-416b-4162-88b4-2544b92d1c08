@file:Suppress("TooManyFunctions")

package com.hellofresh.safetystock

import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.featureflags.StatsigFactory
import com.hellofresh.cif.lib.KrontabScheduler
import com.hellofresh.cif.lib.MeteredJob
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.safetystock.repository.SafetyStockConfigurationRepository
import com.hellofresh.cif.shutdown.shutdownHook
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.skuSpecificationLib.repo.SkuSpecificationRepositoryImpl
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.cif.sqs.SQSClientBuilder
import com.hellofresh.safetystock.job.SafetyStockJob
import com.hellofresh.safetystock.job.TargetSafetyStockJob
import com.hellofresh.safetystock.repo.SafetyStockRepositoryImpl
import com.hellofresh.safetystock.service.SafetyStockService
import com.hellofresh.safetystock.service.targetSafetyStock.TargetSafetyStockImportService
import com.hellofresh.safetystock.service.targetSafetyStock.TargetSafetyStockService
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import software.amazon.awssdk.services.sqs.SqsClient

private const val HTTP_PORT = 8081
private const val WRITE_NUMBER_OF_THREADS = 5
private const val READ_NUMBER_OF_THREADS = 10
private const val ASSUME_ROLE_ARN = "assume.role.arn"

private fun jobTimeMinutes(): Int = ConfigurationLoader.getStringOrFail("job.time_minutes").toInt()
private fun jobTimeSeconds(): Int? = ConfigurationLoader.getStringIfPresent("job.time_seconds")?.toInt()
private fun getS3BucketSuffix() = if (ConfigurationLoader.isLive()) "" else "-staging"
private fun jobPeriod() =
    jobTimeSeconds()?.let { it to TimeUnit.SECONDS }
        ?: (jobTimeMinutes() to TimeUnit.MINUTES)

private fun targetSafetyStockJobPeriod() =
    (jobTimeMinutes() to TimeUnit.MINUTES)

private val sqsClientMainIt: SqsClient = SQSClientBuilder.getSqsClient()
private val sqsClientMainBi: SqsClient =
    SQSClientBuilder.getSqsClient(ConfigurationLoader.getStringOrFail(ASSUME_ROLE_ARN), "safety-stock-job-session")

fun main() {
    val meterRegistry = createMeterRegistry()
    val readMetricsDSLContext = DBConfiguration.jooqReadOnlyDslContext(
        READ_NUMBER_OF_THREADS,
        meterRegistry,
    )
    scheduleJob(meterRegistry, readMetricsDSLContext)
    StatusServer.run(
        meterRegistry,
        HTTP_PORT,
    )
}

@Suppress("LongMethod")
fun scheduleJob(meterRegistry: HelloFreshMeterRegistry, readMetricsDSLContext: MetricsDSLContext) {
    val readWriteMetricsDSLContext = DBConfiguration.jooqMasterDslContext(
        WRITE_NUMBER_OF_THREADS,
        meterRegistry,
    )
    val statsigFeatureFlagClient = statsigFeatureFlagClient()
    val safetyStockService =
        SafetyStockService(readMetricsDSLContext, readWriteMetricsDSLContext, statsigFeatureFlagClient)
    val skuSpecificationService =
        SkuSpecificationService(meterRegistry, SkuSpecificationRepositoryImpl(readMetricsDSLContext))
    val dcRepository = DcRepositoryImpl(readMetricsDSLContext)
    val dcConfigService = DcConfigService(meterRegistry, dcRepository)
    val safetyStockRepository = SafetyStockRepositoryImpl(readMetricsDSLContext, readWriteMetricsDSLContext)
    val safetyStockJob = SafetyStockJob(
        safetyStockService,
        dcConfigService,
        safetyStockRepository,
        SafetyStockConfigurationRepository(readMetricsDSLContext),
    )
    val (jobPeriod, timeUnit) = jobPeriod()
    val meteredSafetyStockJob = MeteredJob(meterRegistry, "safety-stock-job", safetyStockJob::run)
    shutdownNeeded {
        KrontabScheduler(
            period = jobPeriod,
            timeUnit = timeUnit,
            executor = Executors.newSingleThreadExecutor(),
        )
    }.schedule {
        meteredSafetyStockJob.execute()
    }

    val s3ImporterTargetSafetyStock =
        S3Importer(ConfigurationLoader.getStringOrFail(ASSUME_ROLE_ARN), "target-safety-stock-session")

    val targetSafetyStockImportService = TargetSafetyStockImportService(
        importer = s3ImporterTargetSafetyStock,
        dcConfigService = dcConfigService,
        skuSpecificationService = skuSpecificationService,
        s3BucketSuffix = getS3BucketSuffix(),
    )
    val targetSafetyStockService = TargetSafetyStockService(targetSafetyStockImportService, safetyStockRepository)
    val targetSafetyStockJob = TargetSafetyStockJob(
        targetSafetyStockService = targetSafetyStockService,
    )

    val (targetSafetyStockJobPeriod, targetSafetyStockJobTimeUnit) = targetSafetyStockJobPeriod()
    val meteredTargetSafetyStockJob = MeteredJob(meterRegistry, "target-safety-stock-job", targetSafetyStockJob::run)
    shutdownNeeded {
        KrontabScheduler(
            period = targetSafetyStockJobPeriod,
            timeUnit = targetSafetyStockJobTimeUnit,
            executor = Executors.newSingleThreadExecutor(),
        )
    }.schedule {
        meteredTargetSafetyStockJob.execute()
    }

    val s3Importer =
        S3Importer(ConfigurationLoader.getStringOrFail("assume.role.arn"), "safety-stock-multiplier-session")

    launchSafetyMultiplierSqsListener(
        readMetricsDSLContext,
        readWriteMetricsDSLContext,
        dcConfigService,
        s3Importer,
        sqsClientMainIt,
    )

    launchUSSafetyStockBufferSQSListener(
        readWriteMetricsDSLContext,
        dcConfigService,
        skuSpecificationService,
        s3Importer,
        sqsClientMainBi,
    )

    launchSafetyStockImportSQSListener(
        readMetricsDSLContext,
        readWriteMetricsDSLContext,
        dcConfigService,
        skuSpecificationService,
        s3Importer,
        sqsClientMainBi,
    )
}

private fun statsigFeatureFlagClient() = StatsigFactory.build(
    ::shutdownHook,
    sdkKey = ConfigurationLoader.getStringOrFail("HF_STATSIG_SDK_KEY"),
    userId = ConfigurationLoader.getStringOrFail("application.name"),
    isOffline = ConfigurationLoader.getStringIfPresent("statsig.offline")?.toBoolean() ?: false,
    hfTier = ConfigurationLoader.getStringOrFail("HF_TIER"),
)

@Suppress("LongParameterList")
private fun launchSafetyMultiplierSqsListener(
    readMetricsDSLContext: MetricsDSLContext,
    readWriteMetricsDSLContext: MetricsDSLContext,
    dcConfigService: DcConfigService,
    s3Importer: S3Importer,
    sqsClient: SqsClient,
) {
    val skuInputDataRepositoryImpl = SkuInputDataRepositoryImpl(readMetricsDSLContext, dcConfigService)

    val safetyMultiplierSqsListener = shutdownNeeded {
        SQSListenerFactory.createSafetyMultiplierSqsListener(
            readMetricsDSLContext,
            readWriteMetricsDSLContext,
            skuInputDataRepositoryImpl,
            s3Importer,
            sqsClient,
        )
    }

    CoroutineScope(Dispatchers.IO).launch {
        safetyMultiplierSqsListener.run()
    }
}

@Suppress("LongParameterList")
private fun launchUSSafetyStockBufferSQSListener(
    readWriteMetricsDSLContext: MetricsDSLContext,
    dcConfigService: DcConfigService,
    skuSpecificationService: SkuSpecificationService,
    s3Importer: S3Importer,
    sqsClient: SqsClient,
) {
    val safetyBufferSqsListener = shutdownNeeded {
        SQSListenerFactory.createUSSafetyStockBufferSQSListener(
            readWriteMetricsDSLContext,
            dcConfigService,
            skuSpecificationService,
            s3Importer,
            sqsClient,
        )
    }

    CoroutineScope(Dispatchers.IO).launch {
        safetyBufferSqsListener.run()
    }
}

@Suppress("LongParameterList")
private fun launchSafetyStockImportSQSListener(
    readMetricsDSLContext: MetricsDSLContext,
    readWriteMetricsDSLContext: MetricsDSLContext,
    dcConfigService: DcConfigService,
    skuSpecificationService: SkuSpecificationService,
    s3Importer: S3Importer,
    sqsClient: SqsClient,
) {
    val safetyStockImportSqsListener = shutdownNeeded {
        SQSListenerFactory.createSafetyStockImportSQSListener(
            readMetricsDSLContext,
            readWriteMetricsDSLContext,
            dcConfigService,
            skuSpecificationService,
            s3Importer,
            sqsClient,
        )
    }

    CoroutineScope(Dispatchers.IO).launch {
        safetyStockImportSqsListener.run()
    }
}
