package com.hellofresh.safetystock

import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.s3.S3EventMessageParser
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.safetystock.reader.ParquetFileReader
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.cif.sqs.SQSListener
import com.hellofresh.cif.sqs.SQSMessageProxy
import com.hellofresh.safetystock.repo.SafetyStockBufferRepositoryImpl
import com.hellofresh.safetystock.repo.SafetyStockImportRepository
import com.hellofresh.safetystock.safetymultiplier.repo.SafetyStockMultiplierRepositoryImpl
import com.hellofresh.safetystock.safetymultiplier.service.S3SafetyMultiplierService
import com.hellofresh.safetystock.safetymultiplier.service.S3USSafetyStockBufferService
import com.hellofresh.safetystock.safetymultiplier.service.SafetyStockImportService
import software.amazon.awssdk.services.sqs.SqsClient

object SQSListenerFactory {
    private val s3EventMessageParser = S3EventMessageParser()

    fun createSafetyMultiplierSqsListener(
        readMetricsDSLContext: MetricsDSLContext,
        readWriteMetricsDSLContext: MetricsDSLContext,
        skuInputDataRepositoryImpl: SkuInputDataRepositoryImpl,
        s3Importer: S3Importer,
        sqsClient: SqsClient,
    ): SQSListener {
        val safetyStockMultiplierRepository =
            SafetyStockMultiplierRepositoryImpl(readMetricsDSLContext, readWriteMetricsDSLContext)

        val s3SafetyMultiplierService = S3SafetyMultiplierService(
            s3Importer,
            safetyStockMultiplierRepository,
            skuInputDataRepositoryImpl
        )
        val sqsService = SQSMessageProxy(
            s3SafetyMultiplierService,
            s3EventMessageParser,
        )
        val safetyMultiplierSqsUrl = ConfigurationLoader.getStringOrFail("aws.sqs.url")
        return SQSListener(sqsService, sqsClient, safetyMultiplierSqsUrl)
    }

    fun createUSSafetyStockBufferSQSListener(
        readWriteMetricsDSLContext: MetricsDSLContext,
        dcConfigService: DcConfigService,
        skuSpecificationService: SkuSpecificationService,
        s3Importer: S3Importer,
        sqsClient: SqsClient,
    ): SQSListener {
        val safetyStockBufferRepo = SafetyStockBufferRepositoryImpl(readWriteMetricsDSLContext)
        val s3UsSafetyStockBufferProcessor = S3USSafetyStockBufferService(
            s3Importer,
            ParquetFileReader(),
            dcConfigService,
            skuSpecificationService,
            safetyStockBufferRepo
        )
        val usSafeStockSqsService = SQSMessageProxy(
            s3UsSafetyStockBufferProcessor,
            s3EventMessageParser,
        )

        val getUsSafetyStockSqsUrl = ConfigurationLoader.getStringOrFail("aws.sqs.url.us_safety_stock")
        return SQSListener(
            usSafeStockSqsService,
            sqsClient,
            getUsSafetyStockSqsUrl,
        )
    }

    @Suppress("LongParameterList")
    fun createSafetyStockImportSQSListener(
        readMetricsDSLContext: MetricsDSLContext,
        readWriteMetricsDSLContext: MetricsDSLContext,
        dcConfigService: DcConfigService,
        skuSpecificationService: SkuSpecificationService,
        s3Importer: S3Importer,
        sqsClient: SqsClient,
    ): SQSListener {
        val s3UsSafetyStockBufferProcessor = SafetyStockImportService(
            s3Importer,
            dcConfigService,
            skuSpecificationService,
            SafetyStockImportRepository(readMetricsDSLContext, readWriteMetricsDSLContext),
        )
        val usSafeStockSqsService = SQSMessageProxy(
            s3UsSafetyStockBufferProcessor,
            s3EventMessageParser,
        )

        val safetyStockImportSqsUrl = ConfigurationLoader.getStringOrFail("aws.sqs.url.safety_stock_import")
        return SQSListener(
            usSafeStockSqsService,
            sqsClient,
            safetyStockImportSqsUrl,
        )
    }
}
