package com.hellofresh.safetystock.repo

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.safety.stock.job.schema.Tables.SAFETY_STOCKS
import com.hellofresh.cif.safety.stock.job.schema.tables.records.SafetyStocksRecord
import com.hellofresh.cif.safetystock.Configuration
import com.hellofresh.cif.safetystock.DEFAULT_TARGET_SAFETY_STOCK_STRATEGY
import com.hellofresh.cif.safetystock.SafetyStock
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Condition
import org.jooq.JSONB
import org.jooq.TableField
import org.jooq.impl.DSL

const val BATCH_SIZE = 1000

class SafetyStockRepositoryImpl(
    private val readDslContext: MetricsDSLContext,
    private val readWriteDslContext: MetricsDSLContext,
) : SafetyStockRepository {

    private val upsertSafetyStocksAsync = "async-upsert-safety-stocks"
    private val upsertSafetyStocks = "upsert-safety-stocks"

    private val emptyJsonB = JSONB.valueOf("{}")
    private val configurationCoalesce = DSL.coalesce(SAFETY_STOCKS.CONFIGURATION, emptyJsonB)
    private val excludedConfigurationCoalesce = DSL.coalesce(
        DSL.excluded(SAFETY_STOCKS.CONFIGURATION),
        emptyJsonB,
    )

    private val configurationDistinctCondition = DSL.not(
        configurationCoalesce.contains(excludedConfigurationCoalesce)
            .and(
                excludedConfigurationCoalesce.contains(configurationCoalesce),
            ),
    )

    private val strategyCoalesce = DSL.coalesce(SAFETY_STOCKS.STRATEGY, DEFAULT_TARGET_SAFETY_STOCK_STRATEGY)
    private val excludedStrategyCoalesce = DSL.coalesce(
        DSL.excluded(SAFETY_STOCKS.STRATEGY),
        DEFAULT_TARGET_SAFETY_STOCK_STRATEGY,
    )

    private val strategyDistinctCondition = DSL.not(
        strategyCoalesce.contains(excludedStrategyCoalesce)
            .and(
                excludedStrategyCoalesce.contains(strategyCoalesce),
            ),
    )

    override suspend fun upsertSafetyStocks(safetyStocks: List<SafetyStock>, isAlgorithmForecastVariance: Boolean) {
        logger.info("Saving ${safetyStocks.size} safetyStocks")
        if (safetyStocks.isEmpty()) return

        val updatedSafetyStocks = readWriteDslContext.withTagName(upsertSafetyStocksAsync)
            .transactionResultAsync { conf ->
                val txDsl = readWriteDslContext.withMeteredConfiguration(conf)
                safetyStocks
                    .chunked(BATCH_SIZE)
                    .flatMap { batch -> txDsl.upsert(batch, isAlgorithmForecastVariance) }
            }.await()

        logger.info("Updated ${updatedSafetyStocks.size} safetyStocks")
    }

    private fun MetricsDSLContext.upsert(safetyStocks: List<SafetyStock>, isAlgorithmForecastVariance: Boolean): List<SafetyStock> {
        val records: List<SafetyStocksRecord> = toRecords(safetyStocks)
        val updateCondition = isDistinctFromExcluded(SAFETY_STOCKS.SAFETY_STOCK)
            .or(configurationDistinctCondition)
            .or(strategyDistinctCondition)
            .let { baseCondition ->
                if (isAlgorithmForecastVariance) {
                    baseCondition.and(SAFETY_STOCKS.STRATEGY.eq(DEFAULT_TARGET_SAFETY_STOCK_STRATEGY))
                } else {
                    baseCondition
                }
            }

        val updatedSafetyStocks = this.withTagName(upsertSafetyStocks)
            .insertInto(SAFETY_STOCKS)
            .set(records)
            .onDuplicateKeyUpdate()
            .set(SAFETY_STOCKS.SAFETY_STOCK, DSL.excluded(SAFETY_STOCKS.SAFETY_STOCK))
            .set(SAFETY_STOCKS.CONFIGURATION, DSL.excluded(SAFETY_STOCKS.CONFIGURATION))
            .set(SAFETY_STOCKS.STRATEGY, DSL.excluded(SAFETY_STOCKS.STRATEGY))
            .where(updateCondition)
            .returning()
            .fetch()
            .map { record -> toSafetyStock(record) }
        return updatedSafetyStocks
    }

    override suspend fun fetchSafetyStocksFromDcAndWeek(dcAndWeek: Set<DcAndWeek>): List<SafetyStock> {
        if (dcAndWeek.isEmpty()) return emptyList()

        return readDslContext.withTagName("fetch-safety-stocks-from-week")
            .selectFrom(SAFETY_STOCKS)
            .where(
                dcAndWeek
                    .map { (dc, week) -> SAFETY_STOCKS.DC_CODE.eq(dc).and(SAFETY_STOCKS.WEEK.ge(week)) }
                    .reduce { acc, condition -> acc.or(condition) },
            ).fetchAsync()
            .thenApply {
                toSafetyStocks(it)
            }.await()
    }

    private fun toRecords(safetyStocks: List<SafetyStock>): List<SafetyStocksRecord> =
        safetyStocks.map { safetyStockParam ->
            SafetyStocksRecord().apply {
                dcCode = safetyStockParam.dcCode
                skuId = safetyStockParam.skuId
                week = safetyStockParam.week
                safetyStock = safetyStockParam.value
                configuration = JSONB.valueOf(objectMapper.writeValueAsString(safetyStockParam.configuration))
                strategy = safetyStockParam.strategy
            }
        }

    private fun toSafetyStocks(safetyStockRecords: List<SafetyStocksRecord>) =
        safetyStockRecords.map { record -> toSafetyStock(record) }

    private fun toSafetyStock(record: SafetyStocksRecord) =
        SafetyStock(
            dcCode = record[SAFETY_STOCKS.DC_CODE],
            skuId = record[SAFETY_STOCKS.SKU_ID],
            week = record[SAFETY_STOCKS.WEEK],
            value = record[SAFETY_STOCKS.SAFETY_STOCK],
            configuration = objectMapper.readValue<Configuration>(record[SAFETY_STOCKS.CONFIGURATION].data()),
            strategy = record[SAFETY_STOCKS.STRATEGY],
        )

    companion object : Logging {

        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()

        private fun <T> isDistinctFromExcluded(field: TableField<SafetyStocksRecord, T>): Condition =
            field.isDistinctFrom(DSL.excluded(field))
    }

    data class DcAndWeek(val dcCode: String, val week: String)
}
