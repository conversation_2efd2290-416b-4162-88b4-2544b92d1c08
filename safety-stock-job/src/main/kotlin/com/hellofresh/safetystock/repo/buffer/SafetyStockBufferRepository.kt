package com.hellofresh.safetystock.repo.buffer

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.safety.stock.job.schema.Tables.SAFETY_STOCK_BUFFER
import kotlinx.coroutines.future.await
import org.jooq.impl.DSL

class SafetyStockBufferRepository(
    private val metricsDSLContext: MetricsDSLContext,
) {

    suspend fun fetchSafetyStockBuffersFromWeek(
        week: String,
        distributionCenters: Set<DistributionCenterConfiguration>
    ): SafetyStockBuffers {
        val dcMap = distributionCenters.associateBy { it.dcCode }

        val safetyStockMinWeek = safetyStockMinFromWeekTable(dcMap.keys, week)

        val buffers = metricsDSLContext.withTagName("fetch-safety-stock-buffers-from-week")
            .select(
                SAFETY_STOCK_BUFFER.DC_CODE,
                SAFETY_STOCK_BUFFER.WEEK,
                SAFETY_STOCK_BUFFER.SKU_ID,
                SAFETY_STOCK_BUFFER.BUFFER,
            )
            .from(SAFETY_STOCK_BUFFER)
            .leftJoin(safetyStockMinWeek).on(
                SAFETY_STOCK_BUFFER.DC_CODE.eq(
                    safetyStockMinWeek.field(SAFETY_STOCK_BUFFER.DC_CODE),
                ),
                SAFETY_STOCK_BUFFER.SKU_ID.eq(
                    safetyStockMinWeek.field(SAFETY_STOCK_BUFFER.SKU_ID),
                ),
            )
            .where(
                SAFETY_STOCK_BUFFER.DC_CODE.`in`(dcMap.keys)
                    .and(
                        SAFETY_STOCK_BUFFER.WEEK.ge(
                            DSL.coalesce(
                                safetyStockMinWeek.field(SAFETY_STOCK_BUFFER.WEEK),
                                week,
                            ),
                        ),
                    ),
            )
            .fetchAsync()
            .thenApply { results ->
                results.mapNotNull { record ->
                    val dcCode = record[SAFETY_STOCK_BUFFER.DC_CODE]
                    dcMap[dcCode]?.let { dc ->
                        SafetyStockBuffer(
                            dcCode = dcCode,
                            skuId = record[SAFETY_STOCK_BUFFER.SKU_ID],
                            productionWeek = ProductionWeek(
                                record[SAFETY_STOCK_BUFFER.WEEK],
                                dc.productionStart,
                                dc.zoneId,
                            ),
                            bufferPercentage = record[SAFETY_STOCK_BUFFER.BUFFER],
                        )
                    }
                }
            }.await()
        return SafetyStockBuffers(buffers)
    }

    private fun safetyStockMinFromWeekTable(dcCodes: Set<String>, requestedFromWeek: String) =
        metricsDSLContext.select(
            SAFETY_STOCK_BUFFER.DC_CODE,
            SAFETY_STOCK_BUFFER.SKU_ID,
            SAFETY_STOCK_BUFFER.WEEK,
        ).distinctOn(
            SAFETY_STOCK_BUFFER.DC_CODE,
            SAFETY_STOCK_BUFFER.SKU_ID,
        ).from(SAFETY_STOCK_BUFFER)
            .where(
                SAFETY_STOCK_BUFFER.DC_CODE.`in`(dcCodes)
                    .and(
                        SAFETY_STOCK_BUFFER.WEEK.le(requestedFromWeek),
                    ),
            ).asTable("safety_stock_buffers_min_week")
}
