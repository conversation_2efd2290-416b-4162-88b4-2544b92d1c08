package com.hellofresh.safetystock.repo.buffer

import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import java.math.BigDecimal
import java.util.UUID

class SafetyStockBuffers(
    safetyStockBuffers: List<SafetyStockBuffer>,
) {

    private val dcBuffers = safetyStockBuffers
        .groupBy { it.dcCode }
        .mapValues { (_, skus) ->
            skus.groupBy { it.skuId }
                .mapValues { (_, weeks) -> weeks.associateBy { it.productionWeek } }
        }

    fun getBuffer(dcCode: String, skuId: UUID, productionWeek: ProductionWeek): SafetyStockBuffer =
        dcBuffers[dcCode]
            ?.get(skuId)
            ?.let { weekBuffer ->
                weekBuffer[productionWeek]
                    ?: (
                        weekBuffer
                            .filter { (week, _) -> week < productionWeek }
                            .maxByOrNull { (week, _) -> week }
                            ?.value
                        )
            }
            ?: SafetyStockBuffer(
                dcCode, skuId, productionWeek, SafetyStockBuffer.Companion.DEFAULT_BUFFER_PERCENTAGE,
            )
}

data class SafetyStockBuffer(
    val dcCode: String,
    val skuId: UUID,
    val productionWeek: ProductionWeek,
    val bufferPercentage: BigDecimal
) {
    companion object {
        val DEFAULT_BUFFER_PERCENTAGE: BigDecimal = BigDecimal.ZERO
    }
}
