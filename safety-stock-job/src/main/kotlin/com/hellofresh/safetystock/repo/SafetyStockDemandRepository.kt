package com.hellofresh.safetystock.repo

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.sku_inputs_lib.schema.Tables
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.impl.DSL

class SafetyStockDemandRepository(
    private val metricsDSLContext: MetricsDSLContext
) {

    suspend fun fetchSafetyStockSkuDemand(
        dateFrom: LocalDate,
        dcCodes: Set<String>,
        includeZero: Boolean = false,
        skuIds: Set<UUID> = emptySet(),
        skuCategories: Set<String> = emptySet()
    ) =
        metricsDSLContext.withTagName("fetch-demand-safetystock")
            .select(Tables.DEMAND.DC_CODE, Tables.DEMAND.DATE, Tables.DEMAND.SKU_ID, Tables.DEMAND.QUANTITY)
            .from(Tables.DEMAND)
            .join(Tables.SKU_SPECIFICATION_VIEW).on(Tables.SKU_SPECIFICATION_VIEW.ID.eq(Tables.DEMAND.SKU_ID))
            .where(
                Tables.DEMAND.DATE.ge(dateFrom),
                Tables.DEMAND.DC_CODE.`in`(dcCodes),
                if (includeZero) {
                    Tables.DEMAND.QUANTITY.ge(BigDecimal(0))
                } else {
                    Tables.DEMAND.QUANTITY.gt(BigDecimal(0))
                },
                if (skuIds.isEmpty()) {
                    DSL.trueCondition()
                } else {
                    Tables.SKU_SPECIFICATION_VIEW.ID.`in`(skuIds)
                },
                if (skuCategories.isEmpty()) {
                    DSL.trueCondition()
                } else {
                    Tables.SKU_SPECIFICATION_VIEW.CATEGORY.`in`(skuCategories)
                },
            )
            .fetchAsync()
            .thenApply { records ->
                records.map {
                    SafetyStockSkuDemand(
                        it[Tables.DEMAND.DC_CODE],
                        it[Tables.DEMAND.SKU_ID],
                        it[Tables.DEMAND.DATE],
                        it[Tables.DEMAND.QUANTITY],
                    )
                }
            }.await()
}

data class SafetyStockSkuDemand(val dcCode: String, val skuId: UUID, val date: LocalDate, val quantity: BigDecimal)
