package com.hellofresh.safetystock.repo

import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.safetystock.repo.SafetyStockRepositoryImpl.DcAndWeek

interface SafetyStockRepository {
    suspend fun upsertSafetyStocks(safetyStocks: List<SafetyStock>, isAlgorithmForecastVariance: <PERSON>olean)
    suspend fun fetchSafetyStocksFromDcAndWeek(dcAndWeek: Set<DcAndWeek>): List<SafetyStock>
}
