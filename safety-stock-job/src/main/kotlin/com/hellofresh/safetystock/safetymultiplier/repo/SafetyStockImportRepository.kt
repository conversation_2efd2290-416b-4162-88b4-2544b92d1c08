package com.hellofresh.safetystock.repo

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.safety.stock.job.schema.Tables.SAFETY_STOCK_IMPORT
import com.hellofresh.safetystock.safetymultiplier.service.SafetyStockImport
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.impl.DSL

class SafetyStockImportRepository(
    private val readDslContext: MetricsDSLContext,
    private val readWriteDslContext: MetricsDSLContext
) {

    suspend fun upsertSafetyStocks(safetyStockImport: List<SafetyStockImport>) {
        with(SAFETY_STOCK_IMPORT) {
            val queries = safetyStockImport.map {
                readWriteDslContext
                    .insertInto(this)
                    .columns(SKU_ID, DC_CODE, WEEK, SAFETY_STOCK)
                    .values(
                        it.skuId,
                        it.dcCode,
                        it.week,
                        it.safetyStock,
                    )
                    .onConflictOnConstraint(primaryKey)
                    .doUpdate()
                    .set(SAFETY_STOCK, DSL.excluded(SAFETY_STOCK))
            }
            readWriteDslContext
                .withTagName("upsert-safety-stock-import")
                .batch(queries)
                .executeAsync()
                .await()
        }
    }

    suspend fun fetchSafetyStockImports(weekFrom: String, dcCodes: Set<String>, skus: Set<UUID> = emptySet()) =
        readDslContext.withTagName("fetch-safety-stock-imports")
            .selectFrom(SAFETY_STOCK_IMPORT)
            .where(SAFETY_STOCK_IMPORT.DC_CODE.`in`(dcCodes))
            .and(SAFETY_STOCK_IMPORT.WEEK.ge(weekFrom))
            .and(
                if (skus.isNotEmpty()) {
                    SAFETY_STOCK_IMPORT.SKU_ID.`in`(skus)
                } else {
                    DSL.trueCondition()
                },
            )
            .fetchAsync()
            .thenApply { result ->
                result.map {
                    SafetyStockImport(it.dcCode, it.skuId, it.week, it.safetyStock)
                }
            }
            .await()
}
