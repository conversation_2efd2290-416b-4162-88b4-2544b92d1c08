package com.hellofresh.safetystock.safetymultiplier.service

import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.safetystock.ProcessFile
import com.hellofresh.cif.safetystock.model.FileType
import com.hellofresh.cif.safetystock.model.ParsedFile
import com.hellofresh.cif.safetystock.model.ProcessFileResult
import com.hellofresh.cif.skuSpecificationLib.SkuCodeDcKey
import com.hellofresh.cif.skuSpecificationLib.SkuCodeLookUp
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.sqs.MessageHandlerServiceInterface
import com.hellofresh.safetystock.repo.SafetyStockImportRepository
import java.util.UUID
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging

class SafetyStockImportService(
    private val importer: S3Importer,
    private val dcConfigService: DcConfigService,
    private val skuSpecificationService: SkuSpecificationService,
    private val safetyStockImportRepository: SafetyStockImportRepository,
) : MessageHandlerServiceInterface {
    override suspend fun process(s3File: S3File) {
        val allBytes = withContext(Dispatchers.IO) {
            importer.fetchObjectContent(s3File.bucket, s3File.key).readAllBytes()
        }

        val safetyStockImports = processContent(allBytes, s3File)

        if (safetyStockImports.isNotEmpty()) {
            safetyStockImportRepository.upsertSafetyStocks(safetyStockImports)
            logger.info("${safetyStockImports.size} safety stock imports upserted successfully")
        }
    }

    override suspend fun name(): String = "Safety Stock Imports"

    private fun processContent(content: ByteArray, s3File: S3File): List<SafetyStockImport> {
        val processFileResult = ProcessFile.processFileContent(
            s3File.fileName,
            FileType.SAFETY_STOCK_IMPORT,
            content,
        )

        if (processFileResult.hasSevereViolations()) {
            val message = processFileResult.violations.joinToString { it.message }
            logger.error(
                "Error while processing the safety stock import file $s3File, because: $message.",
            )
        } else if (processFileResult.violations.isNotEmpty()) {
            val message = processFileResult.violations.joinToString { it.message }
            logger.warn(
                "Warning while processing the safety stock import file $s3File, because: $message.",
            )
        }
        return if (processFileResult.run { !hasSevereViolations() && violations.isEmpty() }) {
            toSafetyStockImport(processFileResult)
        } else {
            emptyList()
        }
    }

    private fun toSafetyStockImport(result: ProcessFileResult): List<SafetyStockImport> {
        val rows = result.parsedFile?.data ?: emptyList()
        return if (rows.isNotEmpty()) {
            val distributionCenters = rows.map { it[ParsedFile.SafetyStockImport.DC_HEADER] }
                .distinct().mapNotNull { dcConfigService.dcConfigurations[it] } ?: emptyList()

            val skuCodeLookUp = skuSpecificationService.skuCodeLookUp(distributionCenters)

            rows.mapNotNull { row ->
                val dcCode = row[ParsedFile.SafetyStockImport.DC_HEADER]
                getSkuId(skuCodeLookUp, dcCode, row[ParsedFile.SafetyStockImport.SKU_HEADER])
                    ?.let { skuId ->
                        SafetyStockImport(
                            dcCode,
                            skuId,
                            row[ParsedFile.SafetyStockImport.WEEK_HEADER],
                            row[ParsedFile.SafetyStockImport.SAFETY_STOCK_HEADER].toDouble().toLong(),
                        )
                    }
            }
        } else {
            emptyList()
        }
    }

    private fun getSkuId(skuCodeLookUp: SkuCodeLookUp, dcCode: String, skuCode: String): UUID? =
        skuCodeLookUp[SkuCodeDcKey(skuCode, dcCode)]?.first
            ?: run {
                logger.error("SkuId not found for dc: $dcCode and skuCode: $skuCode while processing safety stock import")
                null
            }

    companion object : Logging
}

data class SafetyStockImport(val dcCode: String, val skuId: UUID, val week: String, val safetyStock: Long) {
    companion object
}
