package com.hellofresh.safetystock.safetymultiplier.service

import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.safetystock.model.SafetyStockBuffer
import com.hellofresh.cif.safetystock.reader.ParquetFileReader
import com.hellofresh.cif.skuSpecificationLib.SkuCodeDcKey
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.sqs.MessageHandlerServiceInterface
import com.hellofresh.safetystock.repo.SafetyStockBufferRepository
import java.math.BigDecimal
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.apache.avro.generic.GenericRecord
import org.apache.logging.log4j.kotlin.Logging

class S3USSafetyStockBufferService(
    private val importer: S3<PERSON>mporter,
    private val parquetReader: <PERSON>rquetFileReader,
    private val dcConfigService: DcConfigService,
    private val skuSpecificationService: SkuSpecificationService,
    private val safetyStockBufferRepo: SafetyStockBufferRepository
) : MessageHandlerServiceInterface {
    override suspend fun process(s3File: S3File) {
        val allBytes = withContext(Dispatchers.IO) {
            importer.fetchObjectContent(s3File.bucket, s3File.key).readAllBytes()
        }

        val safetyStockBuffers = convertToSafetyStockBuffer(
            parquetReader.readParquetFromByteArray(allBytes),
            dcConfigService,
        )

        if (safetyStockBuffers.isNotEmpty()) {
            safetyStockBufferRepo.upsertSafetyStockBuffer(safetyStockBuffers)
        }
    }

    override suspend fun name(): String = "US Safety Stock Buffer"

    private suspend fun convertToSafetyStockBuffer(
        records: List<GenericRecord>,
        dcConfigService: DcConfigService,
    ): List<SafetyStockBuffer> {
        val dcMap = records.map { it.get("dc").toString() }.mapNotNull {
            dcConfigService.dcConfigurations[it]
        }
        val skuIDMap = skuSpecificationService.skuCodeLookUp(dcMap)

        val dataList: List<SafetyStockBuffer> = records.mapNotNull { record ->
            val dc = record.get("dc").toString()
            val week = record.get("start_week").toString()
            val buffer = record.get("total_buffer").toString().toDouble()
            val fullSku = record.get("full_sku").toString()

            val skuId = skuIDMap[SkuCodeDcKey(fullSku, dc)]?.first
            if (skuId != null) {
                SafetyStockBuffer(skuId, dc, week, BigDecimal.valueOf(buffer))
            } else {
                logger.error("SKU ID for code: $fullSku not found for week: $week, dc: $dc. Skipping this record.")
                null
            }
        }

        return dataList
    }

    companion object : Logging
}
