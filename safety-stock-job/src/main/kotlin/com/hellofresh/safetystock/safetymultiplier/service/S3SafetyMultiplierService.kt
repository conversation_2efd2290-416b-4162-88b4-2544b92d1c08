package com.hellofresh.safetystock.safetymultiplier.service

import com.github.benmanes.caffeine.cache.Cache
import com.github.benmanes.caffeine.cache.Caffeine
import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.safetystock.ProcessFile
import com.hellofresh.cif.safetystock.model.FileType
import com.hellofresh.cif.safetystock.model.ParsedFile
import com.hellofresh.cif.safetystock.model.ProcessFileResult
import com.hellofresh.cif.safetystock.model.SafetyStockMultiplier
import com.hellofresh.cif.safetystock.model.SkuRiskRating
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.cif.sqs.MessageHandlerServiceInterface
import com.hellofresh.safetystock.safetymultiplier.repo.SafetyStockMultiplierRepository
import java.util.Locale
import java.util.UUID
import java.util.concurrent.TimeUnit
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging

const val MEDIUM_LOW_HYPEN = "MEDIUM-LOW"

@Suppress("MagicNumber")
class S3SafetyMultiplierService(
    private val importer: S3Importer,
    private val safetyStockMultiplierRepository: SafetyStockMultiplierRepository,
    private val skuInputDataRepositoryImpl: SkuInputDataRepositoryImpl
) : MessageHandlerServiceInterface {
    private val skuCodeMarketCache: Cache<Pair<String, String>, UUID> = Caffeine.newBuilder()
        .expireAfterWrite(1, TimeUnit.HOURS)
        .maximumSize(100_000)
        .build()

    override suspend fun process(s3File: S3File) {
        val allBytes = withContext(Dispatchers.IO) {
            importer.fetchObjectContent(s3File.bucket, s3File.key).readAllBytes()
        }
        val safetyStockMultipliers = processContent(allBytes, s3File.key)
        if (safetyStockMultipliers.isNotEmpty()) {
            safetyStockMultiplierRepository.upsertSafetyStockMultiplier(safetyStockMultipliers)
        }
    }

    override suspend fun name(): String = "Safety Stock Multiplier"

    private suspend fun processContent(content: ByteArray, fileName: String): List<SafetyStockMultiplier> {
        val file = SafetyStockMultiplierFile(
            ProcessFile.processFileContent(
                fileName,
                FileType.SAFETY_STOCK_MULTIPLIER,
                content,
            ),
        )

        if (file.processFileResult.hasSevereViolations()) {
            val message = file.processFileResult.violations.joinToString { it.message }
            logger.error(
                "Error while processing the safety stock multiplier file $fileName, because: $message.",
            )
        } else if (file.processFileResult.violations.isNotEmpty()) {
            val message = file.processFileResult.violations.joinToString { it.message }
            logger.warn(
                "Warning while processing the safety stock multiplier file $fileName, because: $message.",
            )
        }
        return if (file.processFileResult.run { !hasSevereViolations() && violations.isEmpty() }) {
            file.toSafetyStockMultiplierData()
        } else {
            emptyList()
        }
    }

    private suspend fun getSkuId(dcCode: String, skuCode: String): UUID? {
        val market = skuInputDataRepositoryImpl.fetchDcConfig()[dcCode]?.market?.lowercase(Locale.getDefault()) ?: run {
            logger.error("Market not found in the dc config for dcCode = $dcCode.")
            return null
        }
        val key = market to skuCode
        return (skuCodeMarketCache.getIfPresent(key) ?: loadAndCacheSkuId(market, skuCode, key))
            ?: run {
                logger.error("SkuId not found for the market: $market and skuCode: $skuCode")
                null
            }
    }

    private suspend fun loadAndCacheSkuId(
        market: String,
        skuCode: String,
        key: Pair<String, String>
    ): UUID? {
        val skuSpecifications = skuInputDataRepositoryImpl.fetchSkus(market).also {
            if (it.isEmpty()) logger.error("SKU's not found for the market: $market and skuCode: $skuCode")
        }

        skuSpecifications.forEach { (id, spec) ->
            skuCodeMarketCache.put(market to spec.skuCode, id)
        }
        return skuCodeMarketCache.getIfPresent(key) ?: run {
            logger.error(
                "SkuId not found for the market: $market and skuCode: $skuCode",
            )
            return null
        }
    }

    private suspend fun SafetyStockMultiplierFile.toSafetyStockMultiplierData(): List<SafetyStockMultiplier> {
        val file = this.processFileResult.parsedFile
        return file?.data?.mapNotNull {
            val dcCode = it[ParsedFile.SafetyStockMultiplier.DC_HEADER]
            val skuId = getSkuId(dcCode, it[ParsedFile.SafetyStockMultiplier.SKU_HEADER])
            if (skuId != null) {
                SafetyStockMultiplier(
                    skuId = skuId,
                    dcCode = dcCode,
                    skuRiskRating = SkuRiskRating.valueOf(
                        convertSkuRating(
                            it[ParsedFile.SafetyStockMultiplier.SKU_RISK_RATING_HEADER]
                                .uppercase(Locale.getDefault()),
                        ),
                    ),
                    targetWeek = it[ParsedFile.SafetyStockMultiplier.TARGET_WEEK_HEADER].toLong(),
                    weekCover = it[ParsedFile.SafetyStockMultiplier.WEEK_COVER_HEADER].toLong(),
                    targetWeekMultiplier = it[
                        ParsedFile.SafetyStockMultiplier.TARGET_WEEK_MULTIPLIER_HEADER,
                    ].toBigDecimal(),
                    weekCoverMultiplier = it[
                        ParsedFile.SafetyStockMultiplier.WEEK_COVER_MULTIPLIER_HEADER,
                    ].toBigDecimal(),
                )
            } else {
                null
            }
        } ?: emptyList()
    }

    private fun convertSkuRating(skuRiskRating: String): String =
        skuRiskRating.let {
            if (MEDIUM_LOW_HYPEN == it) {
                SkuRiskRating.MEDIUM_LOW.name
            } else {
                skuRiskRating
            }
        }

    companion object : Logging

    data class SafetyStockMultiplierFile(
        val processFileResult: ProcessFileResult
    )
}
