package com.hellofresh.safetystock.repo

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.safety.stock.job.schema.Tables
import com.hellofresh.cif.safetystock.model.SafetyStockBuffer

class SafetyStockBufferRepositoryImpl(
    private val readWriteDslContext: MetricsDSLContext
) : SafetyStockBufferRepository {
    override suspend fun upsertSafetyStockBuffer(safetyStockBuffer: List<SafetyStockBuffer>) {
        with(Tables.SAFETY_STOCK_BUFFER) {
            val queries = safetyStockBuffer.map { ssb ->
                readWriteDslContext
                    .insertInto(this)
                    .columns(SKU_ID, DC_CODE, WEEK, BUFFER)
                    .values(
                        ssb.skuId,
                        ssb.dcCode,
                        ssb.week,
                        ssb.buffer,
                    )
                    .onConflict(SKU_ID, DC_CODE, WEEK)
                    .doUpdate()
                    .set(BUFFER, ssb.buffer)
            }
            readWriteDslContext
                .withTagName("upsert-safety-stock-buffer")
                .batch(queries)
                .execute()
        }
    }
}
