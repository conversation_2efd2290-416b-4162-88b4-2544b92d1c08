package com.hellofresh.safetystock.safetymultiplier.repo

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.safety.stock.job.schema.Tables.SAFETY_STOCK_MULTIPLIER
import com.hellofresh.cif.safety.stock.job.schema.enums.SkuRiskRating as SkuRiskRatingDb
import com.hellofresh.cif.safety.stock.job.schema.tables.records.SafetyStockMultiplierRecord
import com.hellofresh.cif.safetystock.model.SafetyStockMultiplier
import com.hellofresh.cif.safetystock.model.SafetyStockMultipliers
import com.hellofresh.cif.safetystock.model.SkuRiskRating
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Condition
import org.jooq.TableField
import org.jooq.impl.DSL

const val BATCH_SIZE = 1000

class SafetyStockMultiplierRepositoryImpl(
    private val readDslContext: MetricsDSLContext,
    private val readWriteDslContext: MetricsDSLContext,
) : SafetyStockMultiplierRepository {

    private val upsertSafetyStockMultiplierAsync = "async-upsert-safety-stock-multiplier"
    private val upsertSafetyStockMultiplier = "upsert-safety-stock-multiplier"

    override suspend fun upsertSafetyStockMultiplier(safetyStockMultipliers: List<SafetyStockMultiplier>) {
        logger.info("Saving ${safetyStockMultipliers.size} safetyStockMultipliers")
        val updatedSafetyStocks = readWriteDslContext.withTagName(upsertSafetyStockMultiplierAsync)
            .transactionResultAsync { conf ->
                val txDsl = readWriteDslContext.withMeteredConfiguration(conf)
                safetyStockMultipliers
                    .chunked(BATCH_SIZE)
                    .flatMap { batch -> txDsl.upsert(batch) }
            }.await()

        logger.info("Updated ${updatedSafetyStocks.size} safetyStockMultipliers")
    }

    private fun MetricsDSLContext.upsert(safetyStockMultipliers: List<SafetyStockMultiplier>): List<SafetyStockMultiplier> {
        val records: List<SafetyStockMultiplierRecord> = safetyStockMultipliers.map { toRecord(it) }
        val updatedSafetyStocks = this.withTagName(upsertSafetyStockMultiplier)
            .insertInto(SAFETY_STOCK_MULTIPLIER)
            .set(records)
            .onDuplicateKeyUpdate()
            .set(
                SAFETY_STOCK_MULTIPLIER.TARGET_WEEK_MULTIPLIER,
                DSL.excluded(SAFETY_STOCK_MULTIPLIER.TARGET_WEEK_MULTIPLIER),
            )
            .set(
                SAFETY_STOCK_MULTIPLIER.WEEK_COVER_MULTIPLIER,
                DSL.excluded(SAFETY_STOCK_MULTIPLIER.WEEK_COVER_MULTIPLIER),
            )
            .where(
                isDistinctFromExcluded(SAFETY_STOCK_MULTIPLIER.TARGET_WEEK_MULTIPLIER)
                    .or(isDistinctFromExcluded(SAFETY_STOCK_MULTIPLIER.WEEK_COVER_MULTIPLIER)),
            )
            .returning()
            .fetch()
            .map { record -> toSafetyStockMultiplier(record) }
        return updatedSafetyStocks
    }

    override suspend fun fetchSafetyStockMultiplier(dcCodes: Set<String>, weekCover: Long?): SafetyStockMultipliers =
        readDslContext.withTagName("fetch-safety-stock-multipliers-by-dc")
            .selectFrom(SAFETY_STOCK_MULTIPLIER)
            .where(
                SAFETY_STOCK_MULTIPLIER.DC_CODE.`in`(dcCodes),
                weekCover?.let { SAFETY_STOCK_MULTIPLIER.WEEK_COVER.eq(weekCover) } ?: DSL.trueCondition(),
            )
            .fetchAsync()
            .thenApply { results ->
                SafetyStockMultipliers(
                    results.map { record -> toSafetyStockMultiplier(record) },
                )
            }.await()

    private fun toRecord(safetyStockMultiplier: SafetyStockMultiplier): SafetyStockMultiplierRecord =
        SafetyStockMultiplierRecord().apply {
            skuId = safetyStockMultiplier.skuId
            dcCode = safetyStockMultiplier.dcCode
            skuRiskRating = toDbSkuRiskRating(safetyStockMultiplier.skuRiskRating)
            targetWeek = safetyStockMultiplier.targetWeek
            weekCover = safetyStockMultiplier.weekCover
            targetWeekMultiplier = safetyStockMultiplier.targetWeekMultiplier
            weekCoverMultiplier = safetyStockMultiplier.weekCoverMultiplier
        }

    private fun toSafetyStockMultiplier(record: SafetyStockMultiplierRecord): SafetyStockMultiplier =
        SafetyStockMultiplier(
            skuId = record[SAFETY_STOCK_MULTIPLIER.SKU_ID],
            dcCode = record[SAFETY_STOCK_MULTIPLIER.DC_CODE],
            skuRiskRating = toSkuRiskRating(record[SAFETY_STOCK_MULTIPLIER.SKU_RISK_RATING]),
            targetWeek = record[SAFETY_STOCK_MULTIPLIER.TARGET_WEEK],
            weekCover = record[SAFETY_STOCK_MULTIPLIER.WEEK_COVER],
            targetWeekMultiplier = record[SAFETY_STOCK_MULTIPLIER.TARGET_WEEK_MULTIPLIER],
            weekCoverMultiplier = record[SAFETY_STOCK_MULTIPLIER.WEEK_COVER_MULTIPLIER],
        )

    private fun toDbSkuRiskRating(skuRiskRatingType: SkuRiskRating): SkuRiskRatingDb =
        when (skuRiskRatingType) {
            SkuRiskRating.CRITICAL -> SkuRiskRatingDb.CRITICAL
            SkuRiskRating.HIGH -> SkuRiskRatingDb.HIGH
            SkuRiskRating.MEDIUM -> SkuRiskRatingDb.MEDIUM
            SkuRiskRating.MEDIUM_LOW -> SkuRiskRatingDb.MEDIUM_LOW
            SkuRiskRating.LOW -> SkuRiskRatingDb.LOW
        }

    companion object : Logging {
        private fun <T> isDistinctFromExcluded(field: TableField<SafetyStockMultiplierRecord, T>): Condition =
            field.isDistinctFrom(DSL.excluded(field))

        fun toSkuRiskRating(skuRiskRating: SkuRiskRatingDb): SkuRiskRating =
            when (skuRiskRating) {
                SkuRiskRatingDb.CRITICAL -> SkuRiskRating.CRITICAL
                SkuRiskRatingDb.HIGH -> SkuRiskRating.HIGH
                SkuRiskRatingDb.MEDIUM -> SkuRiskRating.MEDIUM
                SkuRiskRatingDb.MEDIUM_LOW -> SkuRiskRating.MEDIUM_LOW
                SkuRiskRatingDb.LOW -> SkuRiskRating.LOW
            }
    }
}
