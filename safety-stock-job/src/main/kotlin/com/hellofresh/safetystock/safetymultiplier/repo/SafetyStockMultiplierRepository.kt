package com.hellofresh.safetystock.safetymultiplier.repo

import com.hellofresh.cif.safetystock.model.SafetyStockMultiplier
import com.hellofresh.cif.safetystock.model.SafetyStockMultipliers

interface SafetyStockMultiplierRepository {
    suspend fun upsertSafetyStockMultiplier(safetyStockMultipliers: List<SafetyStockMultiplier>)

    suspend fun fetchSafetyStockMultiplier(dcCodes: Set<String>, weekCover: Long? = null): SafetyStockMultipliers
}
