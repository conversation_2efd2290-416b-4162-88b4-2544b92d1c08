package com.hellofresh.inventory.snapshot.repository

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID

fun DcInventorySnapshot.Companion.default() =
    DcInventorySnapshot(
        dcCode = "FX",
        snapshotId = UUID.randomUUID(),
        snapshotTime = OffsetDateTime.now(UTC),
        aggregatedDcSnapshotDate = OffsetDateTime.now(UTC).toLocalDate(),
        isLatest = true,
        skusSnapshot = listOf(SkuInventorySnapshot.default()),
    )

fun SkuInventorySnapshot.Companion.default() = SkuInventorySnapshot(
    skuId = UUID.randomUUID(),
    inventory = listOf(
        Inventory(
            qty = SkuQuantity.fromBigDecimal(BigDecimal(100L)),
            expiryDate = LocalDate.now(UTC).plusDays(10),
            location = Location("", LOCATION_TYPE_STORAGE, null),
        ),
    ),
)
