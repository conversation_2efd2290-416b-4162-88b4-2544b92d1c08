package com.hellofresh.inventory.snapshot.reader

import com.hellofresh.cif.safetystock.violations.SevereViolation
import com.hellofresh.cif.safetystock.violations.Violation
import com.hellofresh.cif.safetystock.violations.WarnViolation
import com.hellofresh.inventory.snapshot.model.ParsedFile

sealed interface ProcessFileResult {
    val parsedFile: ParsedFile?
    val violations: List<Violation>
    fun hasSevereViolations(): Boolean

    class Valid(
        override val parsedFile: ParsedFile,
        override val violations: List<WarnViolation>
    ) : ProcessFileResult {
        override fun hasSevereViolations() = false
    }

    class Invalid(
        override val parsedFile: ParsedFile?,
        override val violations: List<Violation>,
    ) : ProcessFileResult {

        override fun hasSevereViolations() = violations.any { it is SevereViolation }
    }
}
