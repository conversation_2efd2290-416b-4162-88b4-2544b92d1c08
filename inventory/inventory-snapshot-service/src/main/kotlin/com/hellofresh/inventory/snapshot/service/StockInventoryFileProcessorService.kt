package com.hellofresh.inventory.snapshot.service

import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.inventory.snapshot.schema.enums.FileUploadStatus
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.skuSpecificationLib.SkuCodeDcKey
import com.hellofresh.cif.skuSpecificationLib.SkuCodeLookUp
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.sqs.MessageHandlerServiceInterface
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_UNSPECIFIED
import com.hellofresh.inventory.snapshot.model.FileUpload
import com.hellofresh.inventory.snapshot.model.ParsedFile
import com.hellofresh.inventory.snapshot.model.StockUpload
import com.hellofresh.inventory.snapshot.reader.ProcessFile
import com.hellofresh.inventory.snapshot.reader.ProcessFileResult
import com.hellofresh.inventory.snapshot.reader.violations.ViolationHandlersPerFile
import com.hellofresh.inventory.snapshot.repository.DcInventorySnapshot
import com.hellofresh.inventory.snapshot.repository.FileUploadRepository
import com.hellofresh.inventory.snapshot.repository.InventorySnapshotRepository
import com.hellofresh.inventory.snapshot.repository.SkuInventorySnapshot
import java.time.LocalDate
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging

const val MARKET_METADATA = "market"
const val AUTHOR_NAME_METADATA = "author_name"
const val AUTHOR_EMAIL_METADATA = "author_email"

class StockInventoryFileProcessorService(
    private val importer: S3Importer,
    private val skuService: SkuSpecificationService,
    private val fileUploadsRepository: FileUploadRepository,
    private val inventorySnapshotRepository: InventorySnapshotRepository,
    private val dcConfigService: DcConfigService,
    private val configuration: Configuration
) : MessageHandlerServiceInterface {
    override suspend fun process(s3File: S3File) {
        logger.info("Processing key: ${s3File.bucket}/${s3File.key}")
        val stockUploads = parseStockUploads(s3File.bucket, s3File.key)
            .filter { it.dc in configuration.allowedDcs }

        val keyTokens = s3File.key.split("/").takeLast(2)
        val metadata = importer.fetchObjectMetadata(s3File.bucket, s3File.key)

        val dcs = stockUploads.mapNotNull { dcConfigService.dcConfigurations[it.dc] }.distinct()

        val fileUploads = FileUpload(
            id = keyTokens.firstOrNull()?.let {
                runCatching { UUID.fromString(it) }
                    .onFailure { logger.warn("Unable to parse UUID for stock uploaded file: $it") }
                    .getOrDefault(UUID.randomUUID())
            } ?: UUID.randomUUID(),
            filename = keyTokens.last(),
            market = metadata[MARKET_METADATA] ?: "",
            dcs = dcs.map { it.dcCode },
            status = FileUploadStatus.IMPORTED,
            authorName = metadata[AUTHOR_NAME_METADATA] ?: "",
            authorEmail = metadata[AUTHOR_EMAIL_METADATA] ?: "",
            message = null,
        )

        kotlin.runCatching {
            val markets = dcs.map { it.market }.toSet()
            require(markets.count() == 1) {
                "Multiple or 0 markets found for Dc names $dcs. Total ${markets.count()}"
            }

            val skuMap = skuService.skuCodeLookUp(dcs)

            stockUploads
                .groupBy { it.dc to it.inventoryDate }
                .map { dcSnapshotGroup ->
                    val (dcCode, inventoryDate) = dcSnapshotGroup.key

                    val dcInventorySnapshot = buildDcSnapshot(dcCode, inventoryDate, dcSnapshotGroup.value, skuMap)

                    inventorySnapshotRepository.save(dcInventorySnapshot)
                    logger.info("Saved inventory snapshot for DC: $dcCode for $inventoryDate")
                }
        }.onSuccess {
            logger.info("File ${s3File.bucket}/${s3File.key} import completed.")
            fileUploadsRepository.insert(fileUploads)
        }.onFailure { error ->
            logger.error("Error in importing file ${s3File.bucket}/${s3File.key}.", error)
            val fileUploadsError = fileUploads.copy(status = FileUploadStatus.ERROR, message = error.message)
            fileUploadsRepository.insert(fileUploadsError)
        }
    }

    private fun parseStockUploads(s3Bucket: String, key: String): List<StockUpload> {
        logger.info("Processing key: $s3Bucket/$key}")
        val allBytes = importer.fetchObjectContent(s3Bucket, key).readAllBytes()
        return processContent(allBytes, key)
    }

    private fun buildDcSnapshot(
        dcCode: DcCode,
        inventoryDate: LocalDate,
        skuStockUploads: List<StockUpload>,
        skuMap: SkuCodeLookUp
    ): DcInventorySnapshot {
        val skuInventories = skuStockUploads
            .groupBy { it.skuCode }
            .map { (skuCode, skuStockUploads) ->

                val (skuID, skuSpec) = skuMap[SkuCodeDcKey(skuCode = skuCode, dcCode = dcCode)]
                    ?: throw IllegalArgumentException("Sku code $skuCode not found in dc $dcCode.")

                val inventory = skuStockUploads.groupBy { it.expiryDate }
                    .map { (expiryDate, stockUploads) ->
                        Inventory(
                            qty = SkuQuantity.fromBigDecimal(stockUploads.sumOf { it.qty }, skuSpec.uom),
                            expiryDate = expiryDate,
                            location = DEFAULT_LOCATION,
                        )
                    }
                SkuInventorySnapshot(
                    skuId = skuID,
                    inventory = inventory,
                )
            }

        val snapshotTime = inventoryDate
            .atStartOfDay()
            .atZone(dcConfigService.dcConfigurations[dcCode]?.zoneId)
            .toOffsetDateTime()

        return DcInventorySnapshot(
            dcCode = dcCode,
            snapshotId = UUID.randomUUID(),
            snapshotTime = snapshotTime,
            isLatest = true,
            aggregatedDcSnapshotDate = snapshotTime.toLocalDate(),
            skusSnapshot = skuInventories,
        )
    }

    private fun processContent(content: ByteArray, fileName: String): List<StockUpload> {
        val file = StockUploadsFile(
            ProcessFile.processFileContent(
                fileName,
                content,
                violationHandlers = ViolationHandlersPerFile.createInstances(),
            ),
        )

        if (file.processFileResult.hasSevereViolations()) {
            val message = file.processFileResult.violations.joinToString { it.message }
            logger.error(
                "Error while processing the stock uploads file $fileName, because: $message.",
            )
            throw IllegalArgumentException(message)
        } else if (file.processFileResult.violations.isNotEmpty()) {
            val message = file.processFileResult.violations.joinToString { it.message }
            logger.warn(
                "Warning while processing the  the stock uploads file $fileName, because: $message.",
            )
            throw IllegalArgumentException(message)
        }
        return if (file.processFileResult.run { !hasSevereViolations() && violations.isEmpty() }) {
            file.toStockUploadsData()
        } else {
            emptyList()
        }
    }

    private fun StockUploadsFile.toStockUploadsData(): List<StockUpload> =
        this.processFileResult.parsedFile?.data?.map {
            StockUpload(
                dc = it.getMandatory(ParsedFile.StockUpload.DC_HEADER),
                inventoryDate = it.getMandatoryDateColumn(ParsedFile.StockUpload.DATE_OF_INVENTORY_HEADER),
                skuCode = it.getMandatory(ParsedFile.StockUpload.SKU_CODE_HEADER),
                qty = it.getMandatory(ParsedFile.StockUpload.QTY_HEADER).toBigDecimal(),
                expiryDate = it.getOptionalDateColumn(ParsedFile.StockUpload.EXPIRY_DATE_HEADER),
            )
        } ?: emptyList()

    data class StockUploadsFile(
        val processFileResult: ProcessFileResult
    )

    override suspend fun name(): String =
        "StockInventoryFileProcessorService"

    companion object : Logging {
        internal val DEFAULT_LOCATION = Location("", LOCATION_TYPE_UNSPECIFIED, "")

        data class Configuration(val allowedDcs: Set<String>, val environment: String)
    }
}
