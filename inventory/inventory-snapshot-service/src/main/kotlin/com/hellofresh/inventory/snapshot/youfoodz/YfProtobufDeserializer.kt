package com.hellofresh.inventory.snapshot.youfoodz

import com.hellofresh.dateUtil.models.toNullableOffSetDateTime
import com.hellofresh.dateUtil.models.toOffsetDateTime
import com.hellofresh.inventory.models.LocationType
import com.hellofresh.inventory.snapshot.deserializer.InventorySnapshotKey
import com.hellofresh.inventory.snapshot.deserializer.InventorySnapshotValue
import java.util.UUID
import org.apache.kafka.common.header.Headers
import org.apache.kafka.common.serialization.Deserializer

private const val YOU_FOODZ_PREFIX = "yf"

object YfInventorySnapshotKeyDeserializer : Deserializer<InventorySnapshotKey> {
    override fun deserialize(topic: String?, data: ByteArray?): InventorySnapshotKey {
        check(data != null) { "Key can not be null" }
        return with(
            com.hellofresh.proto.stream.ye.distributionCenter.inventory.snapshot.v2.InventorySnapshotKey.parseFrom(
                data
            ),
        ) {
            InventorySnapshotKey(distributionCenterCode, skuCode)
        }
    }
}

object YfInventorySnapshotValueDeserializer : Deserializer<InventorySnapshotValue> {

    override fun deserialize(topic: String?, data: ByteArray?): InventorySnapshotValue =
        deserialize(topic, null, data)

    override fun deserialize(topic: String?, headers: Headers?, data: ByteArray?): InventorySnapshotValue {
        check(data != null) { "Value can not be null" }
        return with(
            com.hellofresh.proto.stream.ye.distributionCenter.inventory.snapshot.v2.InventorySnapshotValue.parseFrom(
                data
            ),
        ) {
            InventorySnapshotValue(
                snapshotId = UUID.fromString(inventorySnapshot.snapshotId),
                snapshotTime = snapshotTime.toOffsetDateTime(),
                stockState = stockState.name,
                quantity = inventorySnapshot.quantity.value.toBigDecimal(),
                expirationTime = expirationTime.toNullableOffSetDateTime(),
                locationId = inventorySnapshot.locationId,
                locationType = LocationType.parse(inventorySnapshot.locationType.name),
                transportModuleId = if (inventorySnapshot.hasTransportModuleId()) inventorySnapshot.transportModuleId else null,
                messageCount = inventorySnapshot.snapshotCount.toInt(),
                poReference = if (!purchaseOrderRevision.formatted.isNullOrEmpty()) purchaseOrderRevision.formatted else null,
                hashPrefix = YOU_FOODZ_PREFIX,
            )
        }
    }
}
