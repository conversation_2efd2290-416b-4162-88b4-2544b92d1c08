package com.hellofresh.inventory.snapshot.reader

import com.hellofresh.cif.safetystock.violations.ReaderViolation
import com.hellofresh.cif.safetystock.violations.ReaderViolation.HeadersNotMatching
import com.hellofresh.cif.safetystock.violations.ReaderViolation.NotACsv
import com.hellofresh.inventory.snapshot.model.ParsedFile
import com.hellofresh.inventory.snapshot.model.ParsedFile.StockUpload
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.apache.commons.csv.CSVRecord
import org.apache.logging.log4j.kotlin.Logging

object CSVFileUploadReader : Logging {
    private val csvFormat: CSVFormat = CSVFormat.DEFAULT.builder()
        .setAllowMissingColumnNames(true)
        .setIgnoreHeaderCase(true)
        .setTrim(true)
        .build()

    @Suppress("SpreadOperator")
    fun toCsvRecords(byteArray: ByteArray): CsvFileUploadResult {
        val violations = mutableListOf<ReaderViolation>()
        var fileType: ParsedFile? = null

        return byteArray.inputStream().bufferedReader().use { reader ->
            runCatching {
                val delimiter = ','
                val header = reader.readLine().replace("[^\\w.$delimiter]".toRegex(), "")
                val columns = header.split(delimiter)
                val records = CSVParser.parse(
                    reader,
                    csvFormat.builder()
                        .setDelimiter(delimiter)
                        .setHeader(*header.split(delimiter).toTypedArray())
                        .build(),
                ).records
                fileType = detectFileTypeFromHeader(records, columns)
                    .also {
                        if (it == null) violations.add(HeadersNotMatching())
                    }
            }.onFailure { violations.add(NotACsv()) }
            CsvFileUploadResult(fileType, violations)
        }
    }

    private fun detectFileTypeFromHeader(records: List<CSVRecord>, columns: List<String>): ParsedFile? =
        columns.takeIf {
            it.isNotEmpty() && it.containsAll(StockUpload.allColumns)
        }
            ?.let { StockUpload(records) }
}

data class CsvFileUploadResult(
    val parsedFile: ParsedFile?,
    val errors: List<ReaderViolation>
)
