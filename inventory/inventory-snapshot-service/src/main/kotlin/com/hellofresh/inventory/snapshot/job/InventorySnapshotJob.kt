package com.hellofresh.inventory.snapshot.job

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.lib.memoize
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.inventory.models.StockState
import com.hellofresh.inventory.models.StockState.STOCK_STATE_ACTIVE
import com.hellofresh.inventory.models.StockState.STOCK_STATE_OUTSIDE
import com.hellofresh.inventory.models.StockState.STOCK_STATE_UNSPECIFIED
import com.hellofresh.inventory.snapshot.job.InventorySnapshotResolver.getCompletedInventorySnapshots
import com.hellofresh.inventory.snapshot.repository.DcInventorySnapshot
import com.hellofresh.inventory.snapshot.repository.DcSnapshotInfo
import com.hellofresh.inventory.snapshot.repository.InventoryRawSnapshot
import com.hellofresh.inventory.snapshot.repository.InventorySnapshotRepository
import com.hellofresh.inventory.snapshot.repository.RawInventorySnapshotRepository
import com.hellofresh.inventory.snapshot.repository.SkuInventoryRawSnapshot
import io.micrometer.core.instrument.MeterRegistry
import java.time.OffsetDateTime
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.apache.logging.log4j.kotlin.Logging

internal const val LAST_SNAPSHOT_IDS_COUNT = 2
typealias SkuCodeLookUp = Map<InventorySnapshotJob.MarketSkuCodeKey, SkuDetails>

@Suppress("LongParameterList")
class InventorySnapshotJob(
    private val dcConfigService: DcConfigService,
    private val skuInputDataRepository: SkuInputDataRepository,
    private val rawInventorySnapshotRepository: RawInventorySnapshotRepository,
    private val inventorySnapshotRepository: InventorySnapshotRepository,
    private val statsigFeatureFlagClient: StatsigFeatureFlagClient,
    meterRegistry: MeterRegistry
) {

    private val inventorySnapshotMapper = InventorySnapshotMapper(meterRegistry)

    suspend fun run() {
        val dcSnapshotsInfo = rawInventorySnapshotRepository.fetchLatestNonProcessedInventorySnapshots()
        logger.info("Starting Job with Dc Snapshots : ${dcSnapshotsInfo.map { it.dcCode }}")

        val skuLookUpMap = fetchSkuLookUp(dcSnapshotsInfo)

        val snapshotsLookUpMap = fetchSnapshotsLookUp()

        dcSnapshotsInfo.filter { dcConfigService.dcConfigurations[it.dcCode] != null }
            .forEach { dcSnapshots ->
                val dcConfig = dcConfigService.dcConfigurations[dcSnapshots.dcCode]
                dcSnapshots.pendingInventorySnapshotsInfo.sortedBy { it.timestamp }.map { pendingInventorySnapshotInfo ->
                    val dcSnapshotDataToProcess = getCompletedInventorySnapshots(
                        dcSnapshots.dcCode,
                        pendingInventorySnapshotInfo,
                        snapshotsLookUpMap
                    )
                    if (dcConfig != null && dcSnapshotDataToProcess != null) {
                        logger.info("Starting inventory process for dc ${dcSnapshots.dcCode}")
                        processSnapshot(dcConfig, dcSnapshotDataToProcess, skuLookUpMap)
                    } else {
                        logger.warn("No valid snapshots found for $dcSnapshots")
                    }
                }
            }
        logger.info("Job Finished")
    }

    private fun fetchSnapshotsLookUp(): (UUID) -> InventoryRawSnapshot? = { snapshotId: UUID ->
        runBlocking { rawInventorySnapshotRepository.fetchInventoryRawSnapshot(snapshotId) }
    }.memoize()

    private suspend fun processSnapshot(
        dcConfiguration: DistributionCenterConfiguration,
        snapshotsData: DcInventoryRawSnapshot,
        skuLookUpMap: SkuCodeLookUp
    ) {
        val dcInventorySnapshot = calculateDcInventorySnapshot(dcConfiguration, snapshotsData, skuLookUpMap)
        inventorySnapshotRepository.save(dcInventorySnapshot)
        logger.info(
            "Snapshot ${snapshotsData.snapshotId} - ${snapshotsData.snapshotTimestamp} " +
                "for dc ${dcConfiguration.dcCode} with ${dcInventorySnapshot.skusSnapshot.size} skus ",
        )
    }

    private fun calculateDcInventorySnapshot(
        dcConfiguration: DistributionCenterConfiguration,
        dcSnapshotsData: DcInventoryRawSnapshot,
        skuLookUpMap: SkuCodeLookUp
    ) = run {
        val dcSnapshotData = filterRawSnapshots(dcSnapshotsData)
        val dcSnapshotTime = calculateDcSnapshotTime(dcConfiguration, dcSnapshotData.snapshotTimestamp)
        val currentSkuSnapshots =
            inventorySnapshotMapper.toInventorySnapshot(
                dcConfiguration,
                dcSnapshotData,
                skuLookUpMap,
                statsigFeatureFlagClient,
            )

        DcInventorySnapshot(
            dcConfiguration.dcCode,
            dcSnapshotData.snapshotId,
            dcSnapshotTime,
            dcSnapshotData.isLatest,
            dcSnapshotTime.toLocalDate(),
            currentSkuSnapshots,
        )
    }

    private fun filterRawSnapshots(dcSnapshotsData: DcInventoryRawSnapshot) =
        dcSnapshotsData.copy(
            skuInventorySnapshots = filterSkuRawSnapshots(dcSnapshotsData.skuInventorySnapshots),
        )

    private fun filterSkuRawSnapshots(skuInventoryRawSnapshots: List<SkuInventoryRawSnapshot>) =
        skuInventoryRawSnapshots.filter { isValidState(it.state) }

    private fun isValidState(stockState: StockState) =
        stockState in setOf(
            STOCK_STATE_ACTIVE,
            STOCK_STATE_OUTSIDE,
            STOCK_STATE_UNSPECIFIED,
        )

    private suspend fun fetchSkuLookUp(dcSnapshotInfos: List<DcSnapshotInfo>): SkuCodeLookUp =
        dcSnapshotInfos.mapNotNull { dcConfigService.dcConfigurations[it.dcCode]?.market }
            .distinct()
            .flatMap { market ->
                skuInputDataRepository.fetchSkus(market).map { (skuId, skuSpec) ->
                    MarketSkuCodeKey(market, skuSpec.skuCode) to SkuDetails(skuId, skuSpec.category, skuSpec.uom)
                }
            }.toMap()

    private fun calculateDcSnapshotTime(
        dcConfiguration: DistributionCenterConfiguration,
        snapshotTimestamp: OffsetDateTime
    ) = snapshotTimestamp.atZoneSameInstant(dcConfiguration.zoneId).toOffsetDateTime()

    companion object : Logging
    data class MarketSkuCodeKey(val market: String, val skuCode: String)
}
