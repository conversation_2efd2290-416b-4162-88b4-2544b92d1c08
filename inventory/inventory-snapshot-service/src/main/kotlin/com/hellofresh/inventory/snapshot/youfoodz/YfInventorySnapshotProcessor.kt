package com.hellofresh.inventory.snapshot.youfoodz

import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.lib.kafka.ConsumerProcessorConfig
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategy
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategyType.LOG_ERROR_IGNORE
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.inventory.snapshot.PerformUpsert

private const val YOU_FOODZ_TOPIC_NAME = "public.ye.distribution-center.inventory.snapshot.v2"

class YfInventorySnapshotProcessor(
    private val meterRegistry: HelloFreshMeterRegistry,
    private val update: PerformUpsert,
    private val pollConfig: PollConfig,
    private val consumerConfig: Map<String, String>,
) {
    suspend fun process() {
        shutdownNeeded {
            CoroutinesProcessor(
                pollConfig,
                ConsumerProcessorConfig(
                    consumerConfig,
                    YfInventorySnapshotKeyDeserializer,
                    YfInventorySnapshotValueDeserializer,
                    listOf(YOU_FOODZ_TOPIC_NAME),
                ),
                meterRegistry = meterRegistry,
                process = update,
                handleDeserializationException = DeserializationExceptionStrategy.create(
                    LOG_ERROR_IGNORE,
                    meterRegistry,
                ),
                recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                    meterRegistry,
                    "yf_inventory_snapshot_service_write_failure",
                ),
            )
                .also {
                    HealthChecks.add(it)
                    StartUpChecks.add(it)
                }
        }
            .run()
    }
}
