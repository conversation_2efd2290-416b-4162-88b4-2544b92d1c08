package com.hellofresh.inventory.snapshot.repository

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.inventory.snapshot.schema.Tables
import com.hellofresh.cif.inventory.snapshot.schema.enums.FileType
import com.hellofresh.inventory.snapshot.model.FileUpload
import kotlinx.coroutines.future.await

class FileUploadRepository(
    private val dslContext: MetricsDSLContext,
) {
    suspend fun insert(file: FileUpload) {
        dslContext
            .withTagName("insert-file-upload")
            .insertInto(Tables.FILE_UPLOADS)
            .set(Tables.FILE_UPLOADS.ID, file.id)
            .set(Tables.FILE_UPLOADS.FILE_NAME, file.filename)
            .set(Tables.FILE_UPLOADS.MARKET, file.market)
            .set(Tables.FILE_UPLOADS.DCS, file.dcs.toTypedArray())
            .set(Tables.FILE_UPLOADS.STATUS, file.status)
            .set(Tables.FILE_UPLOADS.MESSAGE, file.message)
            .set(Tables.FILE_UPLOADS.AUTHOR_NAME, file.authorName)
            .set(Tables.FILE_UPLOADS.AUTHOR_EMAIL, file.authorEmail)
            .set(Tables.FILE_UPLOADS.FILE_TYPE, FileType.STOCK_INVENTORY)
            .executeAsync()
            .await()
    }
}
