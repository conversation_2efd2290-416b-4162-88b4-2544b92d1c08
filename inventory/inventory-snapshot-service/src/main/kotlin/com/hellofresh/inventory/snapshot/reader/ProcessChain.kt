package com.hellofresh.inventory.snapshot.reader

import com.hellofresh.cif.safetystock.violations.NoViolation
import com.hellofresh.cif.safetystock.violations.Violation
import com.hellofresh.cif.safetystock.violations.WarnViolation
import com.hellofresh.inventory.snapshot.model.ParsedFile
import com.hellofresh.inventory.snapshot.model.Row
import com.hellofresh.inventory.snapshot.reader.ProcessFileResult.Invalid
import com.hellofresh.inventory.snapshot.reader.ProcessFileResult.Valid
import com.hellofresh.inventory.snapshot.reader.violations.ViolationHandler

class ProcessChain(private val violationHandlers: List<ViolationHandler>) {
    fun process(file: ParsedFile, fileName: String): ProcessFileResult {
        val violation = mutableListOf<Violation>()
        file.data.map { record ->
            violationHandlers.forEach { v ->
                val res = v.handle(ProcessingUnit(fileName, record))
                if (res !is NoViolation) {
                    violation.add(res)
                    return@map
                } else if (res is WarnViolation) {
                    violation.add(res)
                }
            }
        }
        return createProcessResult(
            parsedFile = file,
            violations = violation
                .groupBy { it.javaClass.simpleName }
                .values
                .map { it.first().add(it) },
        )
    }

    companion object {
        fun createProcessResult(
            parsedFile: ParsedFile,
            violations: List<Violation>,
        ) = if (violations.any { it !is NoViolation }) {
            Invalid(
                parsedFile = parsedFile,
                violations = violations,
            )
        } else {
            Valid(
                parsedFile = parsedFile,
                violations = violations.filterIsInstance<WarnViolation>().toList(),
            )
        }
    }
}

data class ProcessingUnit(val fileName: String, val record: Row)
