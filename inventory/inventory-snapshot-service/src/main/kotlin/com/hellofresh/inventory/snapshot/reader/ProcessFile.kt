package com.hellofresh.inventory.snapshot.reader

import com.hellofresh.cif.safetystock.violations.ReaderViolation
import com.hellofresh.cif.safetystock.violations.Violation
import com.hellofresh.inventory.snapshot.reader.ProcessFileResult.Invalid
import com.hellofresh.inventory.snapshot.reader.violations.ViolationHandler
import org.apache.logging.log4j.kotlin.Logging

object ProcessFile : Logging {
    fun processFileContent(
        fileName: String,
        content: ByteArray,
        violationHandlers: List<ViolationHandler> = emptyList()
    ): ProcessFileResult {
        val (parsedFile, readerViolations) = CSVFileUploadReader.toCsvRecords(content)

        if (parsedFile == null || readerViolations.isNotEmpty()) {
            return Invalid(
                parsedFile,
                readerViolations.map {
                    when (it) {
                        is ReaderViolation.DelimiterExtractionFailed -> Violation.DelimiterExtractionFailed()
                        is ReaderViolation.HeadersNotMatching -> Violation.HeadersNotMatching()
                        is ReaderViolation.NotACsv -> Violation.NotACsv()
                    }
                },
            )
        }

        return ProcessChain(violationHandlers).process(parsedFile, fileName)
    }
}
