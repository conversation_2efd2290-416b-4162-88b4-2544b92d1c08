package com.hellofresh.inventory.snapshot.model

import java.time.LocalDate
import org.apache.commons.csv.CSVRecord

sealed interface ParsedFile {
    val mandatoryColumns: List<String>
    val optionalColumns: Map<String, String?>
    val data: List<Row>
    val pKey: List<String>
    fun data(records: List<CSVRecord>): List<Row> = records
        .map { csvRecord ->
            Row(
                recordNumber = csvRecord.recordNumber + 1,
                mandatory = mandatoryColumns.associateWith { csvRecord[it] },
                optional = optionalColumns.mapValues { (name, default) ->
                    if (csvRecord.isMapped(name) && csvRecord.isSet(name)) {
                        csvRecord[name]?.trim() ?: default
                    } else {
                        default
                    }
                },
            )
        }

    class StockUpload(records: List<CSVRecord>) : ParsedFile {
        override val mandatoryColumns: List<String> = Companion.mandatoryColumns
        override val optionalColumns: Map<String, String?> = Companion.optionalColumns
        override val data = data(records)
        override val pKey: List<String> = listOf(
            DC_HEADER,
            SKU_CODE_HEADER,
            QTY_HEADER,
            EXPIRY_DATE_HEADER,
            DATE_OF_INVENTORY_HEADER,
        )

        companion object {
            const val DC_HEADER: String = "DC"
            const val SKU_CODE_HEADER: String = "SKUcode"
            const val QTY_HEADER: String = "Qty"
            const val EXPIRY_DATE_HEADER: String = "Expirydate"
            const val DATE_OF_INVENTORY_HEADER: String = "Dateofinventory"

            val allColumns: List<String> = listOf(
                DC_HEADER,
                SKU_CODE_HEADER,
                QTY_HEADER,
                EXPIRY_DATE_HEADER,
                DATE_OF_INVENTORY_HEADER,
            )

            val optionalColumns = mapOf(
                EXPIRY_DATE_HEADER to null,
            )

            val mandatoryColumns: List<String> = allColumns - optionalColumns.keys

            val intColumnDataTypes = setOf(
                QTY_HEADER,
            )
        }
    }
}

data class Row(val recordNumber: Long, val mandatory: Map<String, String>, val optional: Map<String, String?>) {
    val all = mandatory + optional
    fun getMandatory(name: String) = requireNotNull(this.mandatory[name]) { "Column $name not found" }
    fun getOptional(name: String) = optional[name]

    fun getMandatoryDateColumn(name: String) = LocalDate.parse(getMandatory(name))
    fun getOptionalDateColumn(name: String) = getOptional(name)?.takeIf { it.isNotBlank() }?.let { LocalDate.parse(it) }
}
