package com.hellofresh.inventory.snapshot

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.inventory.snapshot.schema.Tables
import com.hellofresh.inventory.snapshot.deserializer.InventorySnapshotKey
import com.hellofresh.inventory.snapshot.deserializer.InventorySnapshotValue
import java.math.BigDecimal
import java.time.Duration
import java.time.OffsetDateTime
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords

class PerformUpsert(
    private val metricsDSLContext: MetricsDSLContext
) : suspend (ConsumerRecords<InventorySnapshotKey, InventorySnapshotValue>) -> Unit {

    override suspend fun invoke(records: ConsumerRecords<InventorySnapshotKey, InventorySnapshotValue>) {
        metricsDSLContext.transactionAsync {
            val tx = metricsDSLContext.withMeteredConfiguration(it)
            val snapshotDc = records.groupBy { r -> SnapshotDcKey(r.value().snapshotId, r.key().dcCode) }
            tx.saveSnapshot(snapshotDc)
            tx.saveSkuSnapshot(snapshotDc)
        }.await()
    }

    private fun MetricsDSLContext.saveSnapshot(
        snapshotDc: Map<SnapshotDcKey, List<ConsumerRecord<InventorySnapshotKey, InventorySnapshotValue>>>
    ) {
        with(
            withTagName("insert-inventory-snapshot-raw")
                .batch(
                    insertInto(Tables.INVENTORY_SNAPSHOT_RAW)
                        .columns(
                            Tables.INVENTORY_SNAPSHOT_RAW.SNAPSHOT_ID,
                            Tables.INVENTORY_SNAPSHOT_RAW.SNAPSHOT_TIME,
                            Tables.INVENTORY_SNAPSHOT_RAW.DC_CODE,
                            Tables.INVENTORY_SNAPSHOT_RAW.MESSAGE_COUNT,
                        )
                        .values(UUID.randomUUID(), OffsetDateTime.now(), "", 0)
                        .onDuplicateKeyIgnore(),
                ),
        ) {
            snapshotDc.forEach { (key, value) ->
                value.first().let { bind(key.snapshotId, it.value().snapshotTime, key.dcCode, it.value().messageCount) }
            }
            execute()
        }
    }

    private fun MetricsDSLContext.saveSkuSnapshot(
        snapshotDc: Map<SnapshotDcKey, List<ConsumerRecord<InventorySnapshotKey, InventorySnapshotValue>>>
    ) {
        with(
            withTagName("insert-inventory-snapshot-raw-sku")
                .batch(
                    insertInto(Tables.INVENTORY_SNAPSHOT_RAW_SKU)
                        .columns(
                            Tables.INVENTORY_SNAPSHOT_RAW_SKU.HASH_MESSAGE,
                            Tables.INVENTORY_SNAPSHOT_RAW_SKU.SNAPSHOT_ID,
                            Tables.INVENTORY_SNAPSHOT_RAW_SKU.DC_CODE,
                            Tables.INVENTORY_SNAPSHOT_RAW_SKU.SKU_CODE,
                            Tables.INVENTORY_SNAPSHOT_RAW_SKU.STATE,
                            Tables.INVENTORY_SNAPSHOT_RAW_SKU.EXPIRATION_TIMESTAMP,
                            Tables.INVENTORY_SNAPSHOT_RAW_SKU.QUANTITY,
                            Tables.INVENTORY_SNAPSHOT_RAW_SKU.LOCATION_TYPE,
                            Tables.INVENTORY_SNAPSHOT_RAW_SKU.LOCATION_ID,
                            Tables.INVENTORY_SNAPSHOT_RAW_SKU.TRANSPORT_MODULE_ID,
                            Tables.INVENTORY_SNAPSHOT_RAW_SKU.PO_REFERENCE,
                        )
                        .values(
                            "", UUID.randomUUID(), "", "", "", OffsetDateTime.now(), BigDecimal.ZERO,
                            "", "", null, null,
                        )
                        .onDuplicateKeyIgnore(),
                ),
        ) {
            snapshotDc.forEach { (snapshotDcKey, records) ->
                records.forEach { record ->
                    val key = record.key()
                    val value = record.value()
                    val hashPrefix = value.hashPrefix ?: ""
                    bind(
                        "$hashPrefix${record.partition()}${record.offset()}",
                        snapshotDcKey.snapshotId,
                        snapshotDcKey.dcCode,
                        key.skuCode,
                        value.stockState,
                        value.expirationTime,
                        value.quantity,
                        value.locationType.name,
                        value.locationId,
                        value.transportModuleId,
                        value.poReference,
                    )
                }
            }
            execute()
        }
    }

    companion object {

        // only consume snapshots younger than [pastDays]
        val pastDays = Duration.ofDays(2)
    }
}

private data class SnapshotDcKey(val snapshotId: UUID, val dcCode: String)
