package com.hellofresh.inventory.snapshot.job

import com.hellofresh.inventory.snapshot.repository.InventoryRawSnapshot
import com.hellofresh.inventory.snapshot.repository.InventorySnapshotInfo
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging

private const val YOU_FOODZ_DC_CODE = "YC"

object InventorySnapshotResolver : Logging {
    fun getCompletedInventorySnapshots(
        dcCode: String,
        inventorySnapshotInfo: InventorySnapshotInfo,
        snapshotsLookUpMap: (UUID) -> InventoryRawSnapshot?
    ): DcInventoryRawSnapshot? {
        val currentSnapshot = snapshotsLookUpMap(inventorySnapshotInfo.id) ?: return null
        var finalSnapshot: InventoryRawSnapshot? = null

        if (currentSnapshot.isSnapshotCountComplete()) {
            finalSnapshot = currentSnapshot
        } else if (yfCollisionExist(currentSnapshot)) {
            logger.error(
                "Inventory snapshot raw - youfoodz collision happened, snapshot id = ${inventorySnapshotInfo.id}"
            )
            val updatedSnapshot = currentSnapshot.copy(
                dcSnapshots = currentSnapshot.dcSnapshots.filterKeys { it != YOU_FOODZ_DC_CODE },
            )
            if (updatedSnapshot.isSnapshotCountComplete()) {
                finalSnapshot = updatedSnapshot
            }
        }

        return finalSnapshot?.let {
            logger.info("Using completed snapshot ${it.id} with count ${it.availableSkuSnapshotCount()}")
            fetchDcSnapshots(dcCode, inventorySnapshotInfo.id, snapshotsLookUpMap, inventorySnapshotInfo.isLatest)
        }
    }

    private fun yfCollisionExist(currentSnapshot: InventoryRawSnapshot?) = currentSnapshot?.dcSnapshots?.keys?.let { keys ->
        keys.size > 1 && keys.contains(YOU_FOODZ_DC_CODE)
    } ?: false

    private fun InventoryRawSnapshot?.isSnapshotCountComplete() = this?.let {
        availableSkuSnapshotCount() == expectedCount
    } == true

    private fun fetchDcSnapshots(
        dcCode: String,
        currentSnapshotId: UUID,
        snapshotsLookUpMap: (UUID) -> InventoryRawSnapshot?,
        isLatest: Boolean
    ): DcInventoryRawSnapshot? =
        runCatching {
            val currentSnapshot = lookUpSnapshot(dcCode, currentSnapshotId, snapshotsLookUpMap)
            toDcInventoryRawSnapshot(dcCode, currentSnapshot, isLatest)
        }.onFailure {
            logger.error("Could not fetch dc snapshot data", it)
        }.getOrNull()
}

private fun lookUpSnapshot(dcCode: String, snapshotId: UUID, snapshotsLookUpMap: (UUID) -> InventoryRawSnapshot?): InventoryRawSnapshot {
    val snapshot = snapshotsLookUpMap(snapshotId)
    require(snapshot != null) {
        "Could not fetch snapshot id $snapshot"
    }
    require(snapshot.dcSnapshots.containsKey(dcCode)) {
        "Fetched snapshot id $snapshot doesnt contain $dcCode inventory"
    }
    return snapshot
}

private fun toDcInventoryRawSnapshot(dcCode: String, inventoryRawSnapshot: InventoryRawSnapshot, isLatest: Boolean) =
    DcInventoryRawSnapshot(
        inventoryRawSnapshot.id,
        inventoryRawSnapshot.timestamp,
        isLatest,
        dcCode,
        inventoryRawSnapshot.dcSnapshots[dcCode] ?: emptyList(),
    )
