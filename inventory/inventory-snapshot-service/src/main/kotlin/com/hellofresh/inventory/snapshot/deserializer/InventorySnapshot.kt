package com.hellofresh.inventory.snapshot.deserializer

import com.hellofresh.inventory.models.LocationType
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.UUID

data class InventorySnapshotKey(val dcCode: String, val skuCode: String)

data class InventorySnapshotValue(
    val snapshotId: UUID,
    val snapshotTime: OffsetDateTime,
    val stockState: String,
    val quantity: BigDecimal,
    val expirationTime: OffsetDateTime?,
    val locationId: String,
    val locationType: LocationType,
    val transportModuleId: String?,
    val messageCount: Int?,
    val poReference: String?,
    val hashPrefix: String? = null,
)
