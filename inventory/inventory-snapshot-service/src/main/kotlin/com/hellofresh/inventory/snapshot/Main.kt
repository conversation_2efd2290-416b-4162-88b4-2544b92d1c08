package com.hellofresh.inventory.snapshot

import com.google.common.util.concurrent.ThreadFactoryBuilder
import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigFactory
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.lib.KrontabScheduler
import com.hellofresh.cif.lib.MeteredJob
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.kafka.ConsumerProcessorConfig
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategy
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategyType.LOG_RECORD_FAIL
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.s3.S3EventMessageParser
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.shutdown.shutdownHook
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.skuSpecificationLib.repo.SkuSpecificationRepositoryImpl
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.cif.sqs.SQSClientBuilder
import com.hellofresh.cif.sqs.SQSListener
import com.hellofresh.cif.sqs.SQSMessageProxy
import com.hellofresh.inventory.snapshot.deserializer.InventorySnapshotKeyDeserializer
import com.hellofresh.inventory.snapshot.deserializer.InventorySnapshotValueDeserializer
import com.hellofresh.inventory.snapshot.job.InventorySnapshotJob
import com.hellofresh.inventory.snapshot.repository.FileUploadRepository
import com.hellofresh.inventory.snapshot.repository.InventorySnapshotRepository
import com.hellofresh.inventory.snapshot.repository.RawInventorySnapshotRepository
import com.hellofresh.inventory.snapshot.service.StockInventoryFileProcessorService
import com.hellofresh.inventory.snapshot.youfoodz.YfInventorySnapshotProcessor
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit.MINUTES
import kotlin.time.Duration
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.kafka.clients.consumer.ConsumerConfig
import software.amazon.awssdk.services.sqs.SqsClient

private const val HTTP_PORT = 8081
const val TOPIC_NAME = "public.distribution-center.inventory.snapshot.v1"

private fun getParallelismConfig() = ConfigurationLoader.getIntegerOrDefault("parallelism", 1)

private const val DEFAULT_POLL_INTERVAL_MS = 20

private fun jobTimeMinutes(): Int = ConfigurationLoader.getStringOrFail("job.time_minutes").toInt()
private fun getStockInventoryUploadSqsUrl() = ConfigurationLoader.getStringOrFail("aws.sqs.url.stock_inventory_upload")

private val sqsClient: SqsClient = SQSClientBuilder.getSqsClient()

@Suppress("LongMethod")
suspend fun main() {
    val meterRegistry = createMeterRegistry()
    val parallelism = getParallelismConfig()

    val masterDslContext = DBConfiguration.jooqMasterDslContext(parallelism, meterRegistry)
    val readOnlyDslContext = DBConfiguration.jooqReadOnlyDslContext(parallelism, meterRegistry)

    val statsigFeatureFlagClient = StatsigFactory.build(
        ::shutdownHook,
        sdkKey = ConfigurationLoader.getStringOrFail("HF_STATSIG_SDK_KEY"),
        userId = ConfigurationLoader.getStringOrFail("application.name"),
        isOffline = ConfigurationLoader.getStringIfPresent("statsig.offline")?.toBoolean() ?: false,
        hfTier = ConfigurationLoader.getStringOrFail("HF_TIER"),
    )

    scheduleJob(
        meterRegistry = meterRegistry,
        readOnlyDSLContext = readOnlyDslContext,
        masterDSLContext = masterDslContext,
        statsigFeatureFlagClient = statsigFeatureFlagClient,
    )

    val update = PerformUpsert(masterDslContext)

    StatusServer.run(
        meterRegistry,
        HTTP_PORT,
    )

    val coroutineDispatcher = Executors.newFixedThreadPool(
        parallelism,
        ThreadFactoryBuilder().setNameFormat("producer-thread-%d").build(),
    ).asCoroutineDispatcher()
    val pollConfig = PollConfig(
        Duration.parse(ConfigurationLoader.getStringOrFail("poll.timeout")),
        ConfigurationLoader.getIntegerOrDefault("poll.interval_ms", DEFAULT_POLL_INTERVAL_MS).toLong(),
        Duration.parse(ConfigurationLoader.getStringOrFail("process.timeout")),
    )
    val consumerConfig =
        ConfigurationLoader.loadKafkaConsumerConfigurations() + mapOf(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG to "false")

    val yfInventorySnapshotProcessor = YfInventorySnapshotProcessor(
        meterRegistry,
        update,
        pollConfig,
        consumerConfig,
    )

    withContext(coroutineDispatcher) {
        repeat(parallelism) {
            launch { launchProcessor(meterRegistry, update, pollConfig, consumerConfig) }
        }
        repeat(parallelism) {
            launch { yfInventorySnapshotProcessor.process() }
        }
        repeat(parallelism) {
            launch {
                sqsListenerStockInventory(
                    meterRegistry,
                    masterDslContext,
                    sqsClient,
                    getStockInventoryUploadSqsUrl(),
                )
            }
        }
    }
}

suspend fun launchProcessor(
    meterRegistry: HelloFreshMeterRegistry,
    update: PerformUpsert,
    pollConfig: PollConfig,
    consumerConfig: Map<String, String>,
) {
    shutdownNeeded {
        CoroutinesProcessor(
            pollConfig,
            ConsumerProcessorConfig(
                consumerConfig,
                InventorySnapshotKeyDeserializer,
                InventorySnapshotValueDeserializer,
                listOf(TOPIC_NAME),
            ),
            meterRegistry = meterRegistry,
            process = update,
            handleDeserializationException = DeserializationExceptionStrategy.create(
                LOG_RECORD_FAIL,
                meterRegistry,
            ),
            recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                meterRegistry,
                "inventory_snapshot_service_write_failure",
            ),
        )
            .also {
                HealthChecks.add(it)
                StartUpChecks.add(it)
            }
    }
        .run()
}

fun scheduleJob(
    meterRegistry: HelloFreshMeterRegistry,
    readOnlyDSLContext: MetricsDSLContext,
    masterDSLContext: MetricsDSLContext,
    statsigFeatureFlagClient: StatsigFeatureFlagClient,
) {
    val inventorySnapshotJob = InventorySnapshotJob(
        DcConfigService(meterRegistry),
        SkuInputDataRepositoryImpl(readOnlyDSLContext, DcConfigService(meterRegistry)),
        RawInventorySnapshotRepository(readOnlyDSLContext),
        InventorySnapshotRepository(masterDSLContext),
        statsigFeatureFlagClient,
        meterRegistry,
    )

    shutdownNeeded {
        KrontabScheduler(
            period = jobTimeMinutes(),
            timeUnit = MINUTES,
            executor = Executors.newSingleThreadExecutor(),
        )
    }.schedule {
        MeteredJob(meterRegistry, "inventory-snapshot-job") {
            inventorySnapshotJob.run()
        }.execute()
    }
}

@Suppress("LongParameterList")
fun sqsListenerStockInventory(
    meterRegistry: HelloFreshMeterRegistry,
    masterDslContext: MetricsDSLContext,
    sqsClient: SqsClient,
    sqsUrl: String,
) {
    val s3Importer = S3Importer()
    val skuSpecificationRepository = SkuSpecificationRepositoryImpl(masterDslContext)
    val inventorySnapshotRepository = InventorySnapshotRepository(masterDslContext)
    val fileUploadRepository = FileUploadRepository(masterDslContext)
    val proxy = SQSMessageProxy(
        StockInventoryFileProcessorService(
            s3Importer,
            SkuSpecificationService(meterRegistry, skuSpecificationRepository),
            fileUploadRepository,
            inventorySnapshotRepository = inventorySnapshotRepository,
            dcConfigService = DcConfigService(meterRegistry),
            configuration = StockInventoryFileProcessorService.Companion.Configuration(
                allowedDcs = ConfigurationLoader.getSet("stock_inventory_uploads.allowed_dcs"),
                environment = ConfigurationLoader.getEnvironment(),
            ),
        ),
        S3EventMessageParser(),
    )

    val sqsListener = shutdownNeeded { SQSListener(proxy, sqsClient, sqsUrl) }

    CoroutineScope(Dispatchers.IO).launch {
        sqsListener.run()
    }
}
