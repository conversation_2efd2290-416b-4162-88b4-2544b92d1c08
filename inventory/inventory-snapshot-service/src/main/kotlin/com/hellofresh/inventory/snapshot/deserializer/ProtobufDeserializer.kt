package com.hellofresh.inventory.snapshot.deserializer

import com.hellofresh.dateUtil.models.toNullableOffSetDateTime
import com.hellofresh.dateUtil.models.toOffsetDateTime
import com.hellofresh.inventory.models.LocationType
import java.util.UUID
import org.apache.kafka.common.header.Headers
import org.apache.kafka.common.serialization.Deserializer

internal const val MESSAGE_COUNT_HEADER = "messageCount"

object InventorySnapshotKeyDeserializer : Deserializer<InventorySnapshotKey> {
    override fun deserialize(topic: String?, data: ByteArray?): InventorySnapshotKey {
        check(data != null) { "Key can not be null" }
        return with(
            com.hellofresh.proto.stream.distributionCenter.inventory.snapshot.v1.InventorySnapshotKey.parseFrom(data),
        ) {
            InventorySnapshotKey(distributionCenterCode, skuCode)
        }
    }
}

object InventorySnapshotValueDeserializer : Deserializer<InventorySnapshotValue> {

    override fun deserialize(topic: String?, data: ByteArray?): InventorySnapshotValue =
        deserialize(topic, null, data)

    override fun deserialize(topic: String?, headers: Headers?, data: ByteArray?): InventorySnapshotValue {
        check(data != null) { "Value can not be null" }
        return with(
            com.hellofresh.proto.stream.distributionCenter.inventory.snapshot.v1.InventorySnapshotValue.parseFrom(data),
        ) {
            InventorySnapshotValue(
                snapshotId = UUID.fromString(inventorySnapshot.snapshotId),
                snapshotTime = snapshotTime.toOffsetDateTime(),
                stockState = stockState.name,
                quantity = inventorySnapshot.quantity.value.toBigDecimal(),
                expirationTime = expirationTime.toNullableOffSetDateTime(),
                locationId = inventorySnapshot.locationId,
                locationType = LocationType.parse(inventorySnapshot.locationType.name),
                transportModuleId = if (inventorySnapshot.hasTransportModuleId()) inventorySnapshot.transportModuleId else null,
                messageCount = getMessageCount(headers),
                poReference = if (!purchaseOrderRevision.formatted.isNullOrEmpty()) purchaseOrderRevision.formatted else null,
            )
        }
    }

    private fun getMessageCount(headers: Headers?) =
        headers?.lastHeader(MESSAGE_COUNT_HEADER)?.value()?.let { String(it, Charsets.UTF_8) }?.toInt()
}
