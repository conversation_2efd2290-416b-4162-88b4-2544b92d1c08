package com.hellofresh.inventory.snapshot.job

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.featureflags.Context.CATEGORY
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.DisableExpiryUnusable
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.lib.memoize
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.purchaseorder.PoUtils
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.snapshot.job.InventorySnapshotJob.MarketSkuCodeKey
import com.hellofresh.inventory.snapshot.repository.SkuInventoryRawSnapshot
import com.hellofresh.inventory.snapshot.repository.SkuInventorySnapshot
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import org.apache.logging.log4j.kotlin.Logging

class InventorySnapshotMapper(private val meterRegistry: MeterRegistry) {

    private val missingSkuIdCounter = Counter.builder("missing_csku_code_to_id_mapping_counter")
        .description("Count and record the number of missing SKU code to SKU id mappings")

    fun toInventorySnapshot(
        dcConfiguration: DistributionCenterConfiguration,
        inventoryRawSnapshots: DcInventoryRawSnapshot,
        skuLookUpMap: SkuCodeLookUp,
        statsigFeatureFlagClient: StatsigFeatureFlagClient,
    ) = run {
        val unusableRulesEnabledFlag = isUnusableRulesEnabled(
            statsigFeatureFlagClient,
        )
        inventoryRawSnapshots.skuInventorySnapshots.groupBy { it.skuCode }
            .mapNotNull { (skuCode, rawSnapshots) ->
                lookupSkuId(skuCode, dcConfiguration, skuLookUpMap)
                    ?.let { (skuId, category, skuUOM) ->
                        SkuInventorySnapshot(
                            skuId = skuId,
                            inventory = toInventory(
                                dcConfiguration,
                                rawSnapshots,
                                category,
                                unusableRulesEnabledFlag,
                                skuUOM,
                            ),
                        )
                    }
            }
    }

    private fun toInventory(
        dcConfiguration: DistributionCenterConfiguration,
        skuInventoryRawSnapshots: List<SkuInventoryRawSnapshot>,
        category: String,
        unusableRulesEnabledFlag: (DCCategoryKey) -> Boolean,
        skuUOM: SkuUOM,
    ) =
        skuInventoryRawSnapshots.mapNotNull { skuInventoryRawSnapshot ->
            val isUnusableRulesEnabled = unusableRulesEnabledFlag(DCCategoryKey(dcConfiguration.dcCode, category))

            if (skuInventoryRawSnapshot.poReference == null || (PoUtils.isPoRefValidForDc(dcConfiguration.dcCode, skuInventoryRawSnapshot.poReference))) {
                with(skuInventoryRawSnapshot) {
                    Inventory(
                        qty = SkuQuantity.fromBigDecimal(quantity, skuUOM),
                        expiryDate = getExpiryDate(
                            dcConfiguration,
                            skuInventoryRawSnapshot,
                            isUnusableRulesEnabled,
                        ),
                        location = Location(
                            skuInventoryRawSnapshot.locationId,
                            skuInventoryRawSnapshot.locationType,
                            skuInventoryRawSnapshot.transportModuleId,
                        ),
                        poReference = skuInventoryRawSnapshot.poReference,
                    )
                }
            } else {
                null
            }
        }

    private fun getExpiryDate(
        dcConfiguration: DistributionCenterConfiguration,
        skuInventoryRawSnapshot: SkuInventoryRawSnapshot,
        isUnusableRulesEnabled: Boolean,
    ) = if (isUnusableRulesEnabled) {
        null
    } else {
        skuInventoryRawSnapshot.expirationTimestamp
            ?.atZoneSameInstant(dcConfiguration.zoneId)?.toLocalDate()
    }

    fun lookupSkuId(
        skuCode: String,
        dcConfiguration: DistributionCenterConfiguration,
        skuCodeLookupMap: SkuCodeLookUp
    ): SkuDetails? =
        skuCodeLookupMap[MarketSkuCodeKey(dcConfiguration.market, skuCode)]
            ?: run {
                logger.warn {
                    "SkuId not found for skuCode=$skuCode and dcCode=${dcConfiguration.dcCode}, and market=${dcConfiguration.market}"
                }
                missingSkuIdCounter.tag("csku", skuCode).register(meterRegistry).increment()
                null
            }

    private fun isUnusableRulesEnabled(
        statsigFeatureFlagClient: StatsigFeatureFlagClient
    ): (DCCategoryKey) -> Boolean = { key: DCCategoryKey ->
        statsigFeatureFlagClient.isEnabledFor(
            DisableExpiryUnusable(setOf(ContextData(DC, key.dcCode), ContextData(CATEGORY, key.skuCategory))),
        )
    }.memoize()

    companion object : Logging
}

private data class DCCategoryKey(
    val dcCode: String,
    val skuCategory: String,
)
