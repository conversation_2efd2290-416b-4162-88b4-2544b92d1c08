package com.hellofresh.inventory.snapshot.repository

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.inventory.snapshot.schema.Tables.INVENTORY_PROCESSED_SNAPSHOTS
import com.hellofresh.cif.inventory.snapshot.schema.Tables.INVENTORY_SNAPSHOT_RAW
import com.hellofresh.cif.inventory.snapshot.schema.Tables.INVENTORY_SNAPSHOT_RAW_SKU
import com.hellofresh.inventory.models.LocationType
import com.hellofresh.inventory.models.StockState
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.impl.DSL

private const val FETCH_INVENTORY_SNAPSHOTS = "fetch-inventory-snapshot-raw"
private const val LATEST_SNAPSHOT_TIME = "latest_snapshot_time"

class RawInventorySnapshotRepository(private val dslContext: MetricsDSLContext) {
    suspend fun fetchLatestNonProcessedInventorySnapshots(): List<DcSnapshotInfo> {
        val latestProcessedSnapshot = dslContext
            .select(
                INVENTORY_PROCESSED_SNAPSHOTS.DC_CODE,
                DSL.max(INVENTORY_PROCESSED_SNAPSHOTS.SNAPSHOT_TIME).`as`(LATEST_SNAPSHOT_TIME),
            )
            .from(INVENTORY_PROCESSED_SNAPSHOTS)
            .groupBy(INVENTORY_PROCESSED_SNAPSHOTS.DC_CODE)
            .asTable("latest_processed_snapshot")

        return dslContext
            .select(
                INVENTORY_SNAPSHOT_RAW.DC_CODE,
                INVENTORY_SNAPSHOT_RAW.SNAPSHOT_ID,
                INVENTORY_SNAPSHOT_RAW.SNAPSHOT_TIME,
                latestProcessedSnapshot.field(LATEST_SNAPSHOT_TIME),
            )
            .from(INVENTORY_SNAPSHOT_RAW)
            .leftJoin(latestProcessedSnapshot)
            .on(INVENTORY_SNAPSHOT_RAW.DC_CODE.eq(latestProcessedSnapshot.field(INVENTORY_PROCESSED_SNAPSHOTS.DC_CODE)))
            .leftJoin(INVENTORY_PROCESSED_SNAPSHOTS)
            .on(
                INVENTORY_SNAPSHOT_RAW.SNAPSHOT_ID.eq(INVENTORY_PROCESSED_SNAPSHOTS.SNAPSHOT_ID)
                    .and(INVENTORY_SNAPSHOT_RAW.DC_CODE.eq(INVENTORY_PROCESSED_SNAPSHOTS.DC_CODE)),
            )
            .where(INVENTORY_PROCESSED_SNAPSHOTS.SNAPSHOT_ID.isNull())
            .orderBy(INVENTORY_SNAPSHOT_RAW.SNAPSHOT_TIME.desc())
            .fetchAsync()
            .thenApply { result ->
                result.groupBy { it.get(INVENTORY_SNAPSHOT_RAW.DC_CODE) }
                    .mapNotNull { (dcCode, records) ->
                        DcSnapshotInfo(
                            dcCode = dcCode,
                            pendingInventorySnapshotsInfo = records.map {
                                val snapshotTime = it.get(INVENTORY_SNAPSHOT_RAW.SNAPSHOT_TIME)
                                val latestSnapshotTime = it.get("latest_snapshot_time", OffsetDateTime::class.java)
                                InventorySnapshotInfo(
                                    id = it.get(INVENTORY_SNAPSHOT_RAW.SNAPSHOT_ID),
                                    timestamp = snapshotTime,
                                    isLatest = latestSnapshotTime?.let { latest -> snapshotTime > latest } ?: true,
                                )
                            },
                        )
                    }
            }.await()
    }

    suspend fun fetchInventoryRawSnapshot(snapshotId: UUID): InventoryRawSnapshot? =
        dslContext.withTagName(FETCH_INVENTORY_SNAPSHOTS)
            .select()
            .from(INVENTORY_SNAPSHOT_RAW)
            .join(INVENTORY_SNAPSHOT_RAW_SKU)
            .on(
                INVENTORY_SNAPSHOT_RAW.SNAPSHOT_ID.eq(INVENTORY_SNAPSHOT_RAW_SKU.SNAPSHOT_ID)
                    .and(INVENTORY_SNAPSHOT_RAW.DC_CODE.eq(INVENTORY_SNAPSHOT_RAW_SKU.DC_CODE)),
            )
            .where(INVENTORY_SNAPSHOT_RAW.SNAPSHOT_ID.eq(snapshotId))
            .fetchAsync()
            .thenApply { result ->
                if (result.isNotEmpty) {
                    InventoryRawSnapshot(
                        id = result.first()[INVENTORY_SNAPSHOT_RAW.SNAPSHOT_ID],
                        timestamp = result.maxOf { it[INVENTORY_SNAPSHOT_RAW.SNAPSHOT_TIME] },
                        expectedCount = result.mapNotNull { it[INVENTORY_SNAPSHOT_RAW.MESSAGE_COUNT] }.maxOfOrNull { it },
                        dcSnapshots = result.groupBy { it[INVENTORY_SNAPSHOT_RAW.DC_CODE] }
                            .mapValues { (_, dcRecords) ->
                                dcRecords.map {
                                    SkuInventoryRawSnapshot(
                                        skuCode = it[INVENTORY_SNAPSHOT_RAW_SKU.SKU_CODE],
                                        quantity = it[INVENTORY_SNAPSHOT_RAW_SKU.QUANTITY],
                                        expirationTimestamp = it[INVENTORY_SNAPSHOT_RAW_SKU.EXPIRATION_TIMESTAMP],
                                        locationId = it[INVENTORY_SNAPSHOT_RAW_SKU.LOCATION_ID],
                                        locationType = LocationType.parse(
                                            it[INVENTORY_SNAPSHOT_RAW_SKU.LOCATION_TYPE],
                                        ),
                                        transportModuleId = it[INVENTORY_SNAPSHOT_RAW_SKU.TRANSPORT_MODULE_ID],
                                        state = StockState.parse(
                                            it[INVENTORY_SNAPSHOT_RAW_SKU.STATE],
                                        ),
                                        poReference = it[INVENTORY_SNAPSHOT_RAW_SKU.PO_REFERENCE],
                                    )
                                }
                            },
                    )
                } else {
                    null
                }
            }.await()

    companion object {
        val stockStateActive = StockState.STOCK_STATE_ACTIVE.toString()
    }
}

data class DcSnapshotInfo(val dcCode: String, val pendingInventorySnapshotsInfo: List<InventorySnapshotInfo>)
data class InventorySnapshotInfo(val id: UUID, val timestamp: OffsetDateTime, val isLatest: Boolean)

data class InventoryRawSnapshot(
    val id: UUID,
    val timestamp: OffsetDateTime,
    val expectedCount: Int?,
    val dcSnapshots: Map<String, List<SkuInventoryRawSnapshot>>
) {
    fun availableSkuSnapshotCount() = dcSnapshots.values.sumOf { it.size }
}

data class SkuInventoryRawSnapshot(
    val skuCode: String,
    val quantity: BigDecimal,
    val expirationTimestamp: OffsetDateTime?,
    val locationId: String,
    val locationType: LocationType,
    val transportModuleId: String?,
    val state: StockState,
    val poReference: String?,
)
