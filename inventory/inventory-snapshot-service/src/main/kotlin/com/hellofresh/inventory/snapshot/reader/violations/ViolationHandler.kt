package com.hellofresh.inventory.snapshot.reader.violations

import com.hellofresh.cif.safetystock.violations.Violation
import com.hellofresh.cif.safetystock.violations.Violation.BlankCell
import com.hellofresh.cif.safetystock.violations.Violation.InvalidDataTypeFormat
import com.hellofresh.cif.safetystock.violations.Violation.InvalidNumberOfColumns
import com.hellofresh.cif.safetystock.violations.Violation.NegativeNumbers
import com.hellofresh.cif.safetystock.violations.noViolation
import com.hellofresh.inventory.snapshot.model.ParsedFile.StockUpload
import com.hellofresh.inventory.snapshot.reader.ProcessingUnit
import kotlin.reflect.full.createInstance
import kotlin.reflect.full.primaryConstructor
import org.apache.logging.log4j.kotlin.Logging

interface ViolationHandler {
    fun handle(data: ProcessingUnit): Violation

    companion object : Logging
}

sealed interface ViolationHandlersPerFile : ViolationHandler {
    companion object {
        fun createInstances(
            violationsHandlersPerFile: List<ViolationHandlersPerFile> = emptyList()
        ): List<ViolationHandlersPerFile> =
            ViolationHandlersPerFile::class.sealedSubclasses
                .filter { it.primaryConstructor?.parameters?.size ?: 0 == 0 }
                .map { it.objectInstance ?: it.createInstance() }
                .plus(violationsHandlersPerFile)
    }
}

object DataTypeVerificationHandler : ViolationHandlersPerFile {
    override fun handle(data: ProcessingUnit): Violation {
        val isValid = checkDataType(data, StockUpload.intColumnDataTypes) { it.toInt() }
        return if (isValid) noViolation else InvalidDataTypeFormat(data.record.recordNumber.toInt())
    }

    private fun checkDataType(data: ProcessingUnit, headers: Set<String>, conversion: (String) -> Number?): Boolean =
        headers.all { header -> conversion(data.record.getMandatory(header)) != null }
}

object NegativeNumbersViolationHandler : ViolationHandlersPerFile {
    @Suppress("UnnecessaryParentheses")
    override fun handle(data: ProcessingUnit): Violation =
        if (data.record.all[StockUpload.QTY_HEADER]?.let { it.toInt() < 0 } == true) {
            NegativeNumbers(data.record.recordNumber.toInt())
        } else {
            noViolation
        }
}

class InvalidNumberOfColumnsViolationHandler : ViolationHandlersPerFile {
    var size: Int? = null
    override fun handle(data: ProcessingUnit): Violation =
        if (size != null && size != data.record.all.size) {
            InvalidNumberOfColumns(data.record.recordNumber.toInt())
        } else {
            size = data.record.all.size
            noViolation
        }
}

object BlankCellViolationHandler : ViolationHandlersPerFile {
    override fun handle(data: ProcessingUnit): Violation =
        if (data.record.mandatory.values.any { it.isBlank() }) {
            BlankCell(data.record.recordNumber.toInt())
        } else {
            noViolation
        }
}
