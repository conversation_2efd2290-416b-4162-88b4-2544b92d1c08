package com.hellofresh.inventory.snapshot.repository

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.inventory.snapshot.schema.Tables.INVENTORY_ALL_SNAPSHOTS
import com.hellofresh.cif.inventory.snapshot.schema.Tables.INVENTORY_PROCESSED_SNAPSHOTS
import com.hellofresh.cif.inventory.snapshot.schema.Tables.INVENTORY_SNAPSHOT
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventoryValue
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.JSONB
import org.jooq.impl.DSL.insertInto

private const val SAVE_SNAPSHOTS = "save-inventory-snapshots"
private const val UPSERT_SNAPSHOT = "upsert-aggregated-inventory-snapshot"
private const val INSERT_SNAPSHOT = "insert-inventory-all-snapshots"
private const val INSERT_INVENTORY_PROCESSED_SNAPSHOT = "insert-inventory-processed-snapshots"

class InventorySnapshotRepository(private val dslContext: MetricsDSLContext) {
    private val insertSnapshotQuery = insertInto(INVENTORY_ALL_SNAPSHOTS)
        .columns(
            INVENTORY_ALL_SNAPSHOTS.DC_CODE,
            INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_ID,
            INVENTORY_ALL_SNAPSHOTS.SKU_ID,
            INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_TIME,
            INVENTORY_ALL_SNAPSHOTS.VALUE,
        )
        .values(null, null, null, null, JSONB.valueOf(null))
        .onConflictDoNothing()

    suspend fun save(dcInventorySnapshot: DcInventorySnapshot) {
        if (dcInventorySnapshot.isEmpty()) return

        dslContext.withTagName(SAVE_SNAPSHOTS).transactionAsync { conf ->
            val tx = dslContext.withMeteredConfiguration(conf)
            if (dcInventorySnapshot.isLatest) {
                upsertAggregatedSnapshot(tx, dcInventorySnapshot)
            }
            if (dcInventorySnapshot.skusSnapshot.isNotEmpty()) {
                insertInventoryProcessedSnapshot(tx, dcInventorySnapshot)
                insertSnapshot(tx, dcInventorySnapshot)
            }
        }.await()
    }

    private fun upsertAggregatedSnapshot(dslContext: MetricsDSLContext, dcInventorySnapshot: DcInventorySnapshot) {
        dslContext.withTagName(UPSERT_SNAPSHOT).deleteFrom(INVENTORY_SNAPSHOT)
            .where(INVENTORY_SNAPSHOT.DC_CODE.eq(dcInventorySnapshot.dcCode))
            .and(INVENTORY_SNAPSHOT.DATE.eq(dcInventorySnapshot.aggregatedDcSnapshotDate))
            .execute()

        dslContext.batch(
            insertInto(INVENTORY_SNAPSHOT)
                .columns(
                    INVENTORY_SNAPSHOT.DC_CODE,
                    INVENTORY_SNAPSHOT.DATE,
                    INVENTORY_SNAPSHOT.SKU_ID,
                    INVENTORY_SNAPSHOT.SNAPSHOT_TIME,
                    INVENTORY_SNAPSHOT.SNAPSHOT_ID,
                    INVENTORY_SNAPSHOT.VALUE,
                )
                .values(null, null, null, null, null, JSONB.valueOf(null)),
        ).apply {
            dcInventorySnapshot.skusSnapshot.forEach {
                val jsonbValue = JSONB.valueOf(objectMapper.writeValueAsString(InventoryValue(it.inventory)))
                bind(
                    dcInventorySnapshot.dcCode,
                    dcInventorySnapshot.aggregatedDcSnapshotDate,
                    it.skuId,
                    dcInventorySnapshot.snapshotTime,
                    dcInventorySnapshot.snapshotId,
                    jsonbValue,
                )
            }
            execute()
        }
    }

    private fun insertSnapshot(dslContext: MetricsDSLContext, dcInventorySnapshot: DcInventorySnapshot) {
        dslContext.withTagName(INSERT_SNAPSHOT)
            .batch(insertSnapshotQuery)
            .apply {
                dcInventorySnapshot.skusSnapshot
                    .forEach {
                        val jsonbValue = JSONB.valueOf(objectMapper.writeValueAsString(InventoryValue(it.inventory)))
                        bind(
                            dcInventorySnapshot.dcCode,
                            dcInventorySnapshot.snapshotId,
                            it.skuId,
                            dcInventorySnapshot.snapshotTime,
                            jsonbValue,
                        )
                    }
            }.execute()
    }

    private fun insertInventoryProcessedSnapshot(dslContext: MetricsDSLContext, dcInventorySnapshot: DcInventorySnapshot) {
        dslContext.withTagName(INSERT_INVENTORY_PROCESSED_SNAPSHOT)
            .insertInto(INVENTORY_PROCESSED_SNAPSHOTS)
            .columns(
                INVENTORY_PROCESSED_SNAPSHOTS.DC_CODE,
                INVENTORY_PROCESSED_SNAPSHOTS.SNAPSHOT_ID,
                INVENTORY_PROCESSED_SNAPSHOTS.SNAPSHOT_TIME,
            )
            .values(
                dcInventorySnapshot.dcCode,
                dcInventorySnapshot.snapshotId,
                dcInventorySnapshot.snapshotTime,
            ).onConflictDoNothing()
            .execute()
    }

    companion object {
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}

data class DcInventorySnapshot(
    val dcCode: String,
    val snapshotId: UUID,
    val snapshotTime: OffsetDateTime,
    val isLatest: Boolean,
    val aggregatedDcSnapshotDate: LocalDate,
    val skusSnapshot: List<SkuInventorySnapshot>,
) {
    fun isEmpty() = skusSnapshot.isEmpty()

    companion object
}

data class SkuInventorySnapshot(
    val skuId: UUID,
    val inventory: List<Inventory>
) {
    companion object
}
