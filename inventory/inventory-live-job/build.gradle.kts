plugins {
    id("com.hellofresh.cif.application-conventions")
    `test-functional`
    alias(libs.plugins.jooq)
}

group = "$group.inventory-live-job"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "inventory_live_snapshot|inventory_snapshot|dc_config|inventory_activity|" +
                            "inventory_activity_type|demand|sku_specification|uom"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(libs.jooq.core)
    implementation(projects.inventoryModels)
    implementation(projects.lib)
    implementation(projects.lib.configuration)
    implementation(projects.lib.logging)
    implementation(projects.lib.db)
    implementation(projects.inventory.inventoryLib)
    implementation(projects.demandLib)
    implementation(projects.distributionCenterLib)
    implementation(projects.skuInputsLib)

    testImplementation(libs.mockk)
    testImplementation(testFixtures(projects.inventoryModels))
    testImplementation(testFixtures(projects.skuModels))
    testImplementation(testFixtures(projects.distributionCenterModels))
    testImplementation(testFixtures(projects.lib.featureflags))

    testFunctionalImplementation(projects.libTests)
    testFunctionalImplementation(libs.testcontainers.core)
    testFunctionalImplementation(libs.testcontainers.postgresql)
    testFunctionalImplementation(libs.testcontainers.junit)
    testFunctionalImplementation(projects.dateUtilModels)
    testFunctionalImplementation(testFixtures(projects.lib.featureflags))
    testFunctionalImplementation(testFixtures(projects.inventoryModels))
    testFunctionalRuntimeOnly(projects.inventoryDb)
}
