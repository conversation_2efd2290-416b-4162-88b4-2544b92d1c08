package com.hellofresh.cif.inventorylivejob.service

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.inventory.InventoryActivityRepository
import com.hellofresh.cif.inventorylivejob.service.InventoryActivityCommandsTest.Companion.createAdjustment
import com.hellofresh.cif.inventorylivejob.service.InventoryActivityCommandsTest.Companion.createMovementFromStaging
import com.hellofresh.cif.inventorylivejob.service.InventoryActivityCommandsTest.Companion.defaultExpiryDate
import com.hellofresh.cif.inventorylivejob.service.InventoryActivityCommandsTest.Companion.defaultLocationId
import com.hellofresh.cif.inventorylivejob.service.InventoryActivityCommandsTest.Companion.defaultSkuId
import com.hellofresh.cif.inventorylivejob.service.InventoryActivityCommandsTest.Companion.defaultTmID
import com.hellofresh.cif.models.DateTimeRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuQuantity.Companion
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventoryAdjustmentTypeId
import com.hellofresh.inventory.models.InventoryMovementsTypeId.MPR
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import com.hellofresh.inventory.models.inventory.live.SkuLiveInventory
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.math.RoundingMode.HALF_UP
import java.time.OffsetDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test

class ActivityInventoryProcessorTest {

    private val inventoryActivityRepository = mockk<InventoryActivityRepository>()

    private val defaultDc = DistributionCenterConfiguration.Companion.default()

    private val defaultInventoryAdjustment = createAdjustment(BigDecimal(150))
    private val activityInventoryProcessor = ActivityInventoryProcessor(inventoryActivityRepository)

    @AfterEach
    fun afterEach() {
        clearAllMocks()
    }

    @Test
    fun `activity is processed for matching sku inventory`() {
        val from = OffsetDateTime.now(defaultDc.zoneId)
        val to = from.plusHours(1)
        val fromLiveSnapshot = createLiveInventorySnapshot(from, BigDecimal(100), BigDecimal(200))
        val toSnapshot = createInventorySnapshot(to, BigDecimal(100000), BigDecimal(500))

        coEvery {
            inventoryActivityRepository.fetchInventoryActivity(setOf(defaultDc.dcCode), DateTimeRange(from, to))
        } returns listOf(defaultInventoryAdjustment)

        val newLiveSnapshot =
            runBlocking { activityInventoryProcessor.processInventoryActivity(defaultDc, fromLiveSnapshot, toSnapshot) }

        assertEquals(toSnapshot.snapshotId, newLiveSnapshot.snapshotId)
        assertEquals(toSnapshot.snapshotTime, newLiveSnapshot.snapshotTime)
        assertEquals(defaultDc.dcCode, newLiveSnapshot.dcCode)
        with(newLiveSnapshot.skus.first()) {
            val toSkuInventory = toSnapshot.skus.first()
            val fromLiveSkuInventory = fromLiveSnapshot.skus.first()
            assertEquals(toSkuInventory.inventory.first { it.isStorage() }.qty, inventory.first { it.isStorage() }.qty)
            assertEquals(
                fromLiveSkuInventory.inventory.first { it.isStaging() }.qty.getValue() + defaultInventoryAdjustment.quantity.getValue(),
                inventory.first { it.isStaging() }.qty.getValue(),
            )
        }
    }

    @Test
    fun `activity is processed just for matching inventory location key`() {
        val from = OffsetDateTime.now(defaultDc.zoneId)
        val to = from.plusHours(1)
        val fromLiveSnapshot = createLiveInventorySnapshot(from, BigDecimal(100), BigDecimal(200))
            .let {
                it.copy(
                    skus = listOf(
                        it.skus.first().copy(
                            inventory = it.skus.first().inventory + listOf(
                                Inventory(
                                    SkuQuantity.fromBigDecimal(
                                        BigDecimal(989384),
                                    ),
                                    null,
                                    Location(
                                        UUID.randomUUID().toString(),
                                        LOCATION_TYPE_STAGING, null,
                                    ),
                                    null,
                                ),
                            ),
                        ),
                    ),
                )
            }

        val toSnapshot = createInventorySnapshot(to, BigDecimal(100000), BigDecimal(500))

        coEvery {
            inventoryActivityRepository.fetchInventoryActivity(setOf(defaultDc.dcCode), DateTimeRange(from, to))
        } returns listOf(defaultInventoryAdjustment)

        val newLiveSnapshot =
            runBlocking { activityInventoryProcessor.processInventoryActivity(defaultDc, fromLiveSnapshot, toSnapshot) }

        with(newLiveSnapshot.skus.first()) {
            val toSkuInventory = toSnapshot.skus.first()
            val fromLiveSkuInventory = fromLiveSnapshot.skus.first()
            assertEquals(3, inventory.size)
            assertEquals(toSkuInventory.inventory.first { it.isStorage() }.qty, inventory.first { it.isStorage() }.qty)
            assertEquals(
                fromLiveSkuInventory.inventory.first {
                    it.location.id == defaultLocationId && it.isStaging()
                }.qty + defaultInventoryAdjustment.quantity,
                newLiveSnapshot.skus.first().inventory.first { it.location.id == defaultLocationId && it.isStaging() }.qty,
            )
            assertEquals(
                fromLiveSkuInventory.inventory.first { it.location.id != defaultLocationId && it.isStaging() }.qty,
                newLiveSnapshot.skus.first().inventory.first { it.location.id != defaultLocationId && it.isStaging() }.qty,
            )
        }
    }

    @Test
    fun `activities are processed in order for a existing sku inventory`() {
        val from = OffsetDateTime.now(defaultDc.zoneId)
        val to = from.plusHours(1)
        val fromLiveSnapshot = createLiveInventorySnapshot(from, BigDecimal(100), BigDecimal(200))
        val toSnapshot = createInventorySnapshot(to, BigDecimal(100000), BigDecimal(500))
        val inventoryAdjustment1 = createAdjustment(BigDecimal(-150)) // make Inventory to be deleted
        val inventoryAdjustment2 = createAdjustment(BigDecimal(150).setScale(5, HALF_UP))

        coEvery {
            inventoryActivityRepository.fetchInventoryActivity(setOf(defaultDc.dcCode), DateTimeRange(from, to))
        } returns listOf(
            inventoryAdjustment1,
            inventoryAdjustment2,
        )

        val newLiveSnapshot =
            runBlocking { activityInventoryProcessor.processInventoryActivity(defaultDc, fromLiveSnapshot, toSnapshot) }

        with(newLiveSnapshot.skus.first()) {
            assertEquals(
                inventoryAdjustment2.quantity.getValue().stripTrailingZeros().toPlainString().toBigDecimal(),
                inventory.first { it.isStaging() }.qty.getValue(),
            )
        }
    }

    @Test
    fun `live snapshot staging is not updated when there are no activities to process`() {
        val from = OffsetDateTime.now(defaultDc.zoneId)
        val to = from.plusHours(1)
        val fromLiveSnapshot = createLiveInventorySnapshot(from, BigDecimal(100), BigDecimal(200))
        val toSnapshot = createInventorySnapshot(to, BigDecimal(100000), BigDecimal(500))

        coEvery {
            inventoryActivityRepository.fetchInventoryActivity(setOf(defaultDc.dcCode), DateTimeRange(from, to))
        } returns emptyList()

        val newLiveSnapshot =
            runBlocking { activityInventoryProcessor.processInventoryActivity(defaultDc, fromLiveSnapshot, toSnapshot) }

        with(newLiveSnapshot.skus.first()) {
            val toSkuInventory = toSnapshot.skus.first()
            val fromLiveSkuInventory = fromLiveSnapshot.skus.first()
            assertEquals(toSkuInventory.inventory.first { it.isStorage() }.qty, inventory.first { it.isStorage() }.qty)
            assertEquals(
                fromLiveSkuInventory.inventory.first { it.isStaging() }.qty,
                inventory.first { it.isStaging() }.qty,
            )
        }
    }

    @Test
    fun `inventory is not updated when there are no valid activities to process`() {
        val from = OffsetDateTime.now(defaultDc.zoneId)
        val to = from.plusHours(1)
        val fromLiveSnapshot = createLiveInventorySnapshot(from, BigDecimal(100), BigDecimal(200))
        val toSnapshot = createInventorySnapshot(to, BigDecimal(100000), BigDecimal(500))

        val inventoryActivity = createMovementFromStaging(BigDecimal(100), defaultSkuId)
            .copy(typeId = MPR.name)

        coEvery {
            inventoryActivityRepository.fetchInventoryActivity(setOf(defaultDc.dcCode), DateTimeRange(from, to))
        } returns listOf(inventoryActivity)

        val newLiveSnapshot =
            runBlocking { activityInventoryProcessor.processInventoryActivity(defaultDc, fromLiveSnapshot, toSnapshot) }

        with(newLiveSnapshot.skus.first()) {
            val toSkuInventory = toSnapshot.skus.first()
            val fromLiveSkuInventory = fromLiveSnapshot.skus.first()
            assertEquals(toSkuInventory.inventory.first { it.isStorage() }.qty, inventory.first { it.isStorage() }.qty)
            assertEquals(
                fromLiveSkuInventory.inventory.first { it.isStaging() }.qty,
                inventory.first { it.isStaging() }.qty,
            )
        }
    }

    @Test
    fun `inventory is not updated when inventory activity doesnt match location`() {
        val from = OffsetDateTime.now(defaultDc.zoneId)
        val to = from.plusHours(1)
        val fromLiveSnapshot = createLiveInventorySnapshot(from, BigDecimal(100), BigDecimal(200))
        val toSnapshot = createInventorySnapshot(to, BigDecimal(100000), BigDecimal(500))

        val inventoryActivity = createAdjustment(BigDecimal(100), defaultSkuId).copy(
            locationId = UUID.randomUUID().toString(),
        )

        coEvery {
            inventoryActivityRepository.fetchInventoryActivity(setOf(defaultDc.dcCode), DateTimeRange(from, to))
        } returns listOf(inventoryActivity)

        val newLiveSnapshot =
            runBlocking { activityInventoryProcessor.processInventoryActivity(defaultDc, fromLiveSnapshot, toSnapshot) }

        with(newLiveSnapshot.skus.first()) {
            val toSkuInventory = toSnapshot.skus.first()
            val fromLiveSkuInventory = fromLiveSnapshot.skus.first()
            assertEquals(toSkuInventory.inventory.first { it.isStorage() }.qty, inventory.first { it.isStorage() }.qty)
            assertEquals(
                fromLiveSkuInventory.inventory.first { it.isStaging() }.qty,
                inventory.first { it.isStaging() }.qty,
            )
        }
    }

    @Test
    fun `returns skus with any cleardown adjustment over staging`() {
        val from = OffsetDateTime.now(defaultDc.zoneId)
        val to = from.plusHours(1)

        val inventoryActivity = createAdjustment(BigDecimal(100), defaultSkuId)
        val inventoryActivityCleardown = createAdjustment(
            BigDecimal(100),
            UUID.randomUUID(),
        ).copy(typeId = InventoryAdjustmentTypeId.CLR.name, locationType = LOCATION_TYPE_STAGING)
        val inventoryActivityCleardownNonStaging = inventoryActivityCleardown.copy(
            skuId = UUID.randomUUID(),
            locationType = LocationType.entries.first {
                !it.isStaging()
            }
        )

        coEvery {
            inventoryActivityRepository.fetchInventoryActivity(setOf(defaultDc.dcCode), DateTimeRange(from, to), any())
        } returns listOf(inventoryActivity, inventoryActivityCleardown, inventoryActivityCleardownNonStaging)

        val skusWithCleardown =
            runBlocking { activityInventoryProcessor.getCleardownInventoryActivities(from, to, defaultDc) }

        assertEquals(1, skusWithCleardown.size)
        assertEquals(inventoryActivityCleardown, skusWithCleardown.first())
    }

    fun createLiveInventorySnapshot(snapshotTime: OffsetDateTime, stagingQty: BigDecimal, storageQty: BigDecimal, skuId: UUID = defaultSkuId) =
        LiveInventorySnapshot(
            dcCode = defaultDc.dcCode,
            snapshotId = UUID.randomUUID(),
            snapshotTime = snapshotTime.toLocalDateTime(),
            skus = listOf(
                SkuLiveInventory(
                    skuId = skuId,
                    inventory = listOf(
                        Inventory(
                            qty = Companion.fromBigDecimal(stagingQty),
                            defaultExpiryDate,
                            Location(defaultLocationId, LOCATION_TYPE_STAGING, defaultTmID),
                        ),
                        Inventory(
                            qty = Companion.fromBigDecimal(storageQty),
                            null,
                            Location(defaultLocationId, LOCATION_TYPE_STORAGE, defaultTmID),
                        ),
                    ),
                ),
            ),
        )

    private fun createInventorySnapshot(snapshotTime: OffsetDateTime, stagingQty: BigDecimal, storageQty: BigDecimal, skuId: UUID = defaultSkuId) =
        com.hellofresh.inventory.models.InventorySnapshot(
            dcCode = defaultDc.dcCode,
            snapshotId = UUID.randomUUID(),
            snapshotTime = snapshotTime.toLocalDateTime(),
            skus = listOf(
                com.hellofresh.inventory.models.SkuInventory(
                    skuId = skuId,
                    inventory = listOf(
                        Inventory(
                            qty = Companion.fromBigDecimal(stagingQty),
                            expiryDate = null,
                            location = Location("", LOCATION_TYPE_STAGING, null),
                        ),
                        Inventory(
                            qty = Companion.fromBigDecimal(storageQty),
                            expiryDate = null,
                            location = Location("", LOCATION_TYPE_STORAGE, null),
                        ),
                    ),
                ),
            ),
        )
}
