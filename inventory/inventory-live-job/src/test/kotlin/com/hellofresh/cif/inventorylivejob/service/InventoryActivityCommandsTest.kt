package com.hellofresh.cif.inventorylivejob.service

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventoryAdjustment
import com.hellofresh.inventory.models.InventoryAdjustmentTypeId
import com.hellofresh.inventory.models.InventoryMovement
import com.hellofresh.inventory.models.InventoryMovementsTypeId
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_PRODUCTION
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import com.hellofresh.inventory.models.default
import com.hellofresh.inventory.models.random
import java.math.BigDecimal
import java.math.RoundingMode.HALF_UP
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class InventoryActivityCommandsTest {

    @ParameterizedTest
    @CsvSource("MIN", "MQU")
    fun `activity movements from staging should remove inventory `(
        inventoryMovementsTypeIds: InventoryMovementsTypeId
    ) {
        val inventoryActivity = createMovementFromStaging(quantity = BigDecimal(50))
            .copy(
                typeId = inventoryMovementsTypeIds.name,
            )

        val inventory = Inventory(
            SkuQuantity.fromBigDecimal(BigDecimal(100)),
            inventoryActivity.expirationDate,
            Location(
                inventoryActivity.originLocationId,
                inventoryActivity.originLocationType,
                inventoryActivity.transportModuleId,
            ),
        )

        val command = inventoryActivity.createCommand()
        assertNull(command!!.execute(inventory).inventory)
    }

    @Test
    fun `activity movements without matching inventory should return same `() {
        val inventoryActivity = createMovementFromStaging(quantity = BigDecimal(50))

        val inventory = Inventory.random()

        val command = inventoryActivity.createCommand()
        assertEquals(inventory, command?.execute(inventory)?.inventory)
    }

    @ParameterizedTest
    @CsvSource("MPR")
    fun `activity movements to staging should create inventory`(inventoryMovementsTypeIds: InventoryMovementsTypeId) {
        val inventoryActivity = createMovementToStaging(quantity = BigDecimal(50).setScale(5, HALF_UP))
            .copy(
                typeId = inventoryMovementsTypeIds.name,
            )

        val command = inventoryActivity.createCommand()
        val newInventory = command?.execute(null)?.inventory

        assertEquals(
            inventoryActivity.quantity.getValue().toPlainString().toBigDecimal(),
            newInventory?.qty?.getValue()
        )
        assertEquals(inventoryActivity.destinationLocationId, newInventory?.location?.id)
        assertEquals(inventoryActivity.destinationLocationType, newInventory?.location?.type)
        assertEquals(inventoryActivity.transportModuleId, newInventory?.location?.transportModuleId)
        assertEquals(inventoryActivity.expirationDate, newInventory?.expiryDate)
    }

    @ParameterizedTest
    @CsvSource(
        "50,100,150.00000",
        "-25,100,75.00000",
    )
    fun `activity adjustment in staging should add activity quantity`(
        adjustmentQty: BigDecimal,
        inventoryQty: BigDecimal,
        expectedQty: BigDecimal
    ) {
        val location = Location(UUID.randomUUID().toString(), LOCATION_TYPE_STAGING, defaultTmID)
        val inventoryActivity = InventoryAdjustment.Companion.default()
            .copy(
                quantity = SkuQuantity.fromBigDecimal(adjustmentQty),
                locationId = location.id,
                locationType = location.type,
                transportModuleId = location.transportModuleId,
                expirationDate = null,
            )

        val inventory = Inventory(SkuQuantity.fromBigDecimal(inventoryQty), null, location)

        val command = inventoryActivity.createCommand()
        val newInventory = command?.execute(inventory)?.inventory

        assertEquals(inventoryActivity.locationId, command?.inventoryLocation?.location?.id)
        assertEquals(inventoryActivity.locationType, command?.inventoryLocation?.location?.type)
        assertEquals(inventoryActivity.transportModuleId, command?.inventoryLocation?.location?.transportModuleId)

        assertEquals(expectedQty.stripTrailingZeros().toPlainString().toBigDecimal(), newInventory?.qty?.getValue())
        assertEquals(inventory.location, newInventory?.location)
        assertEquals(inventory.expiryDate, newInventory?.expiryDate)
    }

    @Test
    fun `activity adjustment in staging should create activity quantity when no matching inventory location`() {
        val location = Location(UUID.randomUUID().toString(), LOCATION_TYPE_STAGING, defaultTmID)
        val inventoryActivity = InventoryAdjustment.Companion.default()
            .copy(
                quantity = SkuQuantity.fromBigDecimal(BigDecimal(150).setScale(5, HALF_UP)),
                locationId = location.id,
                locationType = location.type,
                transportModuleId = location.transportModuleId,
                expirationDate = LocalDate.now().plusDays(22),
            )

        val command = inventoryActivity.createCommand()
        val newInventory = command?.execute(null)?.inventory

        assertEquals(inventoryActivity.locationId, command?.inventoryLocation?.location?.id)
        assertEquals(inventoryActivity.locationType, command?.inventoryLocation?.location?.type)
        assertEquals(inventoryActivity.transportModuleId, command?.inventoryLocation?.location?.transportModuleId)

        assertEquals(
            inventoryActivity.quantity.getValue().toPlainString().toBigDecimal(),
            newInventory?.qty?.getValue()
        )
        assertEquals(command?.inventoryLocation?.location, newInventory?.location)
        assertEquals(command?.inventoryLocation?.expiryDate, newInventory?.expiryDate)
    }

    @ParameterizedTest
    @CsvSource("UPD", "CLR")
    fun `cleardown adjustments quantity overwrites inventory`(cleardownType: InventoryAdjustmentTypeId) {
        val inventory = Inventory.Companion.random()
            .let { it.copy(location = it.location.copy(type = LOCATION_TYPE_STAGING)) }

        val cleardownAdjustment = InventoryAdjustment.Companion.default()
            .copy(
                typeId = cleardownType.name,
                locationId = inventory.location.id,
                locationType = inventory.location.type,
                transportModuleId = inventory.location.transportModuleId,
                expirationDate = inventory.expiryDate,
            )

        val command = cleardownAdjustment.createCommand()!!
        val commandResult = command.execute(inventory)

        assertEquals(cleardownAdjustment.activityTime.toLocalDateTime(), commandResult.cleardownTime)

        assertEquals(cleardownAdjustment.locationId, command.inventoryLocation.location.id)
        assertEquals(cleardownAdjustment.locationType, command.inventoryLocation.location.type)
        assertEquals(cleardownAdjustment.transportModuleId, command.inventoryLocation.location.transportModuleId)

        val newInventory = commandResult.inventory
        assertEquals(
            cleardownAdjustment.remainingQuantity.getValue().stripTrailingZeros().toPlainString().toBigDecimal(),
            newInventory?.qty?.getValue()
        )
        assertEquals(command.inventoryLocation.location, newInventory?.location)
        assertEquals(command.inventoryLocation.expiryDate, newInventory?.expiryDate)
    }

    @Test
    fun `movements with same origin and destination should not create any command`() {
        val inventoryActivity = InventoryMovement.Companion.default()
            .copy(
                typeId = UUID.randomUUID().toString(),
                originLocationType = LOCATION_TYPE_PRODUCTION,
                destinationLocationType = LOCATION_TYPE_PRODUCTION,
            )

        assertNull(inventoryActivity.createCommand())
    }

    @Test
    fun `inventory is created from activity movement`() {
        val inventoryMovement = createMovementToStaging(quantity = BigDecimal(123L).setScale(5, HALF_UP))

        val createCommand = inventoryMovement.createCommand()

        assertNotNull(createCommand)
        with(createCommand.execute(null).inventory!!) {
            assertEquals(inventoryMovement.destinationLocationId, this.location.id)
            assertEquals(inventoryMovement.destinationLocationType, this.location.type)
            assertEquals(inventoryMovement.transportModuleId, this.location.transportModuleId)
            assertEquals(inventoryMovement.expirationDate, this.expiryDate)
            assertEquals(
                inventoryMovement.quantity.getValue().stripTrailingZeros().toPlainString().toBigDecimal(),
                this.qty.getValue()
            )
        }
    }

    @Test
    fun `inventory is created from activity movement with unknown type id`() {
        val inventoryMovement = createMovementToStaging(
            quantity = BigDecimal(123L).setScale(5, HALF_UP),
        ).copy(typeId = "")

        val createCommand = inventoryMovement.createCommand()

        assertNotNull(createCommand)
        with(createCommand.execute(null).inventory!!) {
            assertEquals(inventoryMovement.destinationLocationId, this.location.id)
            assertEquals(inventoryMovement.destinationLocationType, this.location.type)
            assertEquals(inventoryMovement.transportModuleId, this.location.transportModuleId)
            assertEquals(inventoryMovement.expirationDate, this.expiryDate)
            assertEquals(
                inventoryMovement.quantity.getValue().stripTrailingZeros().toPlainString().toBigDecimal(),
                this.qty.getValue()
            )
        }
    }

    @Test
    fun `inventory is deleted from activity movement`() {
        val inventoryMovement = createMovementFromStaging(quantity = BigDecimal(123L))

        val createCommand = inventoryMovement.createCommand()

        assertNotNull(createCommand)

        assertNull(createCommand.execute(null).inventory)
    }

    @Test
    fun `inventory is deleted from activity movement with unknown type id`() {
        val inventoryMovement = createMovementFromStaging(quantity = BigDecimal(123L)).copy(typeId = "")

        val createCommand = inventoryMovement.createCommand()

        assertNotNull(createCommand)
        assertNull(createCommand.execute(null).inventory)
    }

    companion object {

        val defaultSkuId = UUID.randomUUID()
        val defaultLocationId = UUID.randomUUID().toString()
        val defaultTmID = UUID.randomUUID().toString()
        val defaultExpiryDate = LocalDate.now().plusWeeks(1)
        fun createAdjustment(quantity: BigDecimal, skuId: UUID = defaultSkuId) =
            InventoryAdjustment.Companion.default().copy(
                skuId = skuId,
                locationId = defaultLocationId,
                locationType = LOCATION_TYPE_STAGING,
                transportModuleId = defaultTmID,
                quantity = SkuQuantity.fromBigDecimal(quantity),
                expirationDate = defaultExpiryDate,
            )

        fun createMovementToStaging(quantity: BigDecimal, skuId: UUID = defaultSkuId) =
            InventoryMovement.Companion.default().copy(
                skuId = skuId,
                typeId = InventoryMovementsTypeId.MPR.name,
                originLocationType = LOCATION_TYPE_STORAGE,
                destinationLocationId = defaultLocationId,
                destinationLocationType = LOCATION_TYPE_STAGING,
                transportModuleId = defaultTmID,
                quantity = SkuQuantity.fromBigDecimal(quantity),
                expirationDate = defaultExpiryDate,
            )

        fun createMovementFromStaging(quantity: BigDecimal, skuId: UUID = defaultSkuId) =
            InventoryMovement.Companion.default().copy(
                skuId = skuId,
                typeId = InventoryMovementsTypeId.MIN.name,
                originLocationType = LOCATION_TYPE_STAGING,
                originLocationId = defaultLocationId,
                destinationLocationType = LOCATION_TYPE_STORAGE,
                transportModuleId = defaultTmID,
                quantity = SkuQuantity.fromBigDecimal(quantity),
                expirationDate = defaultExpiryDate,
            )
    }
}
