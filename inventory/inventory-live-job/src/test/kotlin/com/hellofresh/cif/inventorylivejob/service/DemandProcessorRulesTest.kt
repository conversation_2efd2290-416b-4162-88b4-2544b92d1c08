package com.hellofresh.cif.inventorylivejob.service

import com.hellofresh.cif.demand.DemandRepository
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.skuinput.repo.DcSkuCodeKey
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.demand.models.ActualConsumption
import com.hellofresh.demand.models.Demand
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import com.hellofresh.inventory.models.inventory.live.SkuLiveInventory
import com.hellofresh.inventory.models.inventory.live.random
import com.hellofresh.inventory.models.random
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.default
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.DayOfWeek.FRIDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DemandProcessorRulesTest {

    private val demandRepository = mockk<DemandRepository>()
    private val skuInputDataRepository = mockk<SkuInputDataRepository>()

    private val distributionCenterConfiguration = DistributionCenterConfiguration.Companion.default()

    private val today = LocalDateTime.now(distributionCenterConfiguration.zoneId)

    private val skuId = UUID.randomUUID()
    private val skuSpecification = SkuSpecification.Companion.default
    private val defaultDemandQty = SkuQuantity.fromLong(100L)
    private val defaultInventorySnapshot = LiveInventorySnapshot.Companion.random().copy(
        dcCode = distributionCenterConfiguration.dcCode,
        snapshotTime = today,
    )
    private val date = LocalDate.parse(
        "2024-04-09",
        DateTimeFormatter.ofPattern("yyyy-MM-dd")
    )

    @BeforeEach
    fun beforeEach() {
        coEvery { skuInputDataRepository.fetchSkuLookUp(setOf(distributionCenterConfiguration.dcCode)) } returns
            mapOf(DcSkuCodeKey(distributionCenterConfiguration.dcCode, skuSpecification.skuCode) to (skuId to skuSpecification))

        coEvery {
            demandRepository.findDemands(
                setOf(distributionCenterConfiguration.dcCode), DateRange.oneDay(date),
            )
        } returns listOf(
            Demand(
                skuId, distributionCenterConfiguration.dcCode, date, defaultDemandQty,
                ActualConsumption(
                    SkuQuantity.fromLong(0), false
                )
            ),
        )
    }

    @AfterEach
    fun afterEach() {
        clearAllMocks()
    }

    @Test
    fun `demand is not served if cleardown happened yesterday and yesterday is one last 3 production week`() {
        val expectedInventory = Inventory(
            SkuQuantity.fromBigDecimal(defaultDemandQty.getValue() * BigDecimal(3)),
            null,
            Location("", LOCATION_TYPE_STAGING, null)
        )

        val sku = SkuLiveInventory(
            skuId = skuId,
            inventory = listOf(
                expectedInventory,
                Inventory.Companion.random().copy(location = Location("", LOCATION_TYPE_QUARANTINE, null)),
            ),
            cleardownTime = date.atStartOfDay()
        )
        val inventorySnapshot = defaultInventorySnapshot.copy(
            snapshotTime = date.atStartOfDay(),
            skus = listOf(sku)
        )

        val inventorySnapshotResult =
            runBlocking {
                DemandProcessor(
                    demandRepository,
                    skuInputDataRepository,
                    StatsigTestFeatureFlagClient(emptySet())
                )
                    .serveDemand(
                        inventorySnapshot,
                        distributionCenterConfiguration.copy(
                            productionStart = FRIDAY,
                            cleardown = date.dayOfWeek
                        )
                    )
            }

        assertEquals(inventorySnapshot.dcCode, inventorySnapshotResult.dcCode)
        assertEquals(inventorySnapshot.snapshotId, inventorySnapshotResult.snapshotId)
        assertEquals(inventorySnapshot.snapshotTime, inventorySnapshotResult.snapshotTime)

        assertEquals(1, inventorySnapshotResult.skus.size)
        assertEquals(2, inventorySnapshotResult.skus.first().inventory.size)
        assertEquals(
            expectedInventory,
            inventorySnapshotResult.skus.first().inventory.first(),
        )
    }
}
