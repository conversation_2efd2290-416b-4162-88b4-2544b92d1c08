package com.hellofresh.cif.inventorylivejob.service

import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.inventory.models.default
import com.hellofresh.inventory.models.random
import java.util.UUID
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test

class InventoryMapperTest {

    @Test
    fun `inventory snapshot is mapped to live inventory`() {
        val staging = Inventory.random().copy(
            location = Location(UUID.randomUUID().toString(), LOCATION_TYPE_STAGING, UUID.randomUUID().toString()),
        )
        val storage = Inventory.random().copy(
            location = Location(UUID.randomUUID().toString(), LOCATION_TYPE_STORAGE, UUID.randomUUID().toString()),
        )
        val quarantine = Inventory.random().copy(
            location = Location(UUID.randomUUID().toString(), LOCATION_TYPE_QUARANTINE, UUID.randomUUID().toString()),
        )
        val inventorySnapshot = InventorySnapshot.default().copy(
            skus = listOf(
                SkuInventory(UUID.randomUUID(), inventory = listOf(staging, storage, quarantine)),
            ),
        )

        val liveInventorySnapshot = InventoryMapper.mapToLiveInventorySnapshot(inventorySnapshot)

        assertEquals(inventorySnapshot.snapshotId, liveInventorySnapshot.snapshotId)
        assertEquals(inventorySnapshot.snapshotTime, liveInventorySnapshot.snapshotTime)
        assertEquals(inventorySnapshot.dcCode, liveInventorySnapshot.dcCode)
        assertEquals(liveInventorySnapshot.skus.first().skuId, inventorySnapshot.skus.first().skuId)
        val inventories = liveInventorySnapshot.skus.first().inventory

        assertEquals(
            staging.qty,
            inventories.first { i -> i.location == staging.location }.qty,
        )
        assertEquals(
            staging.expiryDate,
            inventories.first { i -> i.location == staging.location }.expiryDate,
        )
        assertEquals(
            storage.qty,
            inventories.first { i -> i.location == storage.location }.qty,
        )
        assertEquals(
            storage.expiryDate,
            inventories.first { i -> i.location == storage.location }.expiryDate,
        )

        assertEquals(
            quarantine.qty,
            inventories.first { i -> i.location == quarantine.location }.qty,
        )
        assertEquals(
            quarantine.expiryDate,
            inventories.first { i -> i.location == quarantine.location }.expiryDate,
        )
    }
}
