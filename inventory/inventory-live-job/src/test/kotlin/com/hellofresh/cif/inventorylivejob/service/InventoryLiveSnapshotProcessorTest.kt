package com.hellofresh.cif.inventorylivejob.service

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.inventory.InventoryRepository
import com.hellofresh.cif.inventory.InventoryService
import com.hellofresh.cif.inventorylivejob.service.InventoryActivityCommandsTest.Companion.defaultExpiryDate
import com.hellofresh.cif.inventorylivejob.service.InventoryActivityCommandsTest.Companion.defaultLocationId
import com.hellofresh.cif.inventorylivejob.service.InventoryActivityCommandsTest.Companion.defaultSkuId
import com.hellofresh.cif.inventorylivejob.service.InventoryActivityCommandsTest.Companion.defaultTmID
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventoryAdjustment
import com.hellofresh.inventory.models.InventoryAdjustmentTypeId
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.default
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import com.hellofresh.inventory.models.inventory.live.SkuLiveInventory
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach

class InventoryLiveSnapshotProcessorTest {

    private lateinit var inventoryLiveSnapshotProcessor: InventoryLiveSnapshotProcessor
    private val defaultDc = DistributionCenterConfiguration.Companion.default()
    private val dcRepository = mockk<DcRepository>()
    private var dcConfigService: DcConfigService = DcConfigService(SimpleMeterRegistry(), dcRepository)
    private val activityInventoryProcessor = mockk<ActivityInventoryProcessor>()
    private val inventoryService = mockk<InventoryService>()
    private val demandProcessor = mockk<DemandProcessor>()
    private val inventoryRepository = mockk<InventoryRepository>()

    @BeforeEach
    fun beforeEach() {
        coEvery { dcRepository.fetchDcConfigurations() } returns listOf(defaultDc)
        dcConfigService = DcConfigService(SimpleMeterRegistry(), dcRepository)
        inventoryLiveSnapshotProcessor = InventoryLiveSnapshotProcessor(
            activityInventoryProcessor,
            demandProcessor,
            inventoryService,
            inventoryRepository,
        )
    }

    @AfterEach
    fun afterEach() {
        clearAllMocks()
    }

    @Test
    fun `should be able to merge live inventory snapshots for non-cleardown skus with new location`() {
        val skuId1 = UUID.randomUUID()
        val sameLocationId = "same-location-id"
        val newLocationId = "new-location-id"
        val atCleardownRCVAdjustment = createAdjustment(100, skuId1, sameLocationId, InventoryAdjustmentTypeId.RCV.name)
        val skusWithInventoryActivity = listOf(atCleardownRCVAdjustment)

        val inventory0 = createInventory(5000, sameLocationId)

        val inventory1 = createInventory(10000, sameLocationId)
        val inventory2 = createInventory(2000, newLocationId)
        val liveInventorySnapshotWithCleardownSkus = LiveInventorySnapshot(
            dcCode = "dc-code-fixture",
            snapshotTime = LocalDateTime.now(UTC),
            snapshotId = UUID.randomUUID(),
            skus = listOf(
                SkuLiveInventory(
                    skuId = skuId1,
                    inventory = listOf(
                        inventory0,
                    ),
                ),
            ),
        )
        val liveInventorySnapshotAtCleardown = LiveInventorySnapshot(
            dcCode = "dc-code-fixture",
            snapshotTime = LocalDateTime.now(UTC),
            snapshotId = UUID.randomUUID(),
            skus = listOf(
                SkuLiveInventory(
                    skuId = skuId1,
                    inventory = listOf(
                        inventory1,
                        inventory2,
                    ),
                ),
            ),
        )

        val mergedSnapshot = inventoryLiveSnapshotProcessor.refreshCleardownSkusAtLocationLevel(
            liveInventorySnapshotWithCleardownSkus,
            liveInventorySnapshotAtCleardown,
            skusWithInventoryActivity,
        )

        assertEquals(1, mergedSnapshot.size)

        val mergedSku = mergedSnapshot.find { it.skuId == skuId1 }!!
        assertEquals(
            10000L,
            mergedSku.inventory.find {
                it.location.id == "same-location-id"
            }!!.qty.getValue().toLong(),
        ) // Quantity should be updated
        assertEquals(
            10000L,
            mergedSku.inventory.find {
                it.location.id == "same-location-id"
            }!!.qty.getValue().toLong(),
        ) // Quantity should be updated

        assertEquals(
            2000L,
            mergedSku.inventory.find {
                it.location.id == "new-location-id"
            }!!.qty.getValue().toLong(),
        ) // New location should be added
    }

    @Test
    fun `should update the live inventory snapshots for ALL non-cleardown skus`() {
        val skuId1 = UUID.randomUUID()
        val locationId1 = "location-id-1"
        val locationId2 = "location-id-2"
        val locationId3 = "location-id-3"
        val atCleardownCLRAdjustment1 = createAdjustment(
            100,
            skuId1,
            locationId1,
            InventoryAdjustmentTypeId.RCV.name
        )
        val atCleardownCLRAdjustment2 = createAdjustment(
            100,
            skuId1,
            locationId2,
            InventoryAdjustmentTypeId.RCV.name
        )
        val atCleardownCLRAdjustment3 = createAdjustment(
            100,
            skuId1,
            locationId3,
            InventoryAdjustmentTypeId.RCV.name
        )
        val skusWithClearDownActivity = listOf(
            atCleardownCLRAdjustment1,
            atCleardownCLRAdjustment2,
            atCleardownCLRAdjustment3
        )

        val inventory0 = createInventory(1000, locationId1)
        val inventory1 = createInventory(1000, locationId2)
        val inventory2 = createInventory(1000, locationId3)

        val inventory3 = createInventory(2000, locationId1)
        val inventory4 = createInventory(2000, locationId2)
        val inventory5 = createInventory(2000, locationId3)
        val liveInventorySnapshotWithCleardownSkus = LiveInventorySnapshot(
            dcCode = "dc-code-fixture",
            snapshotTime = LocalDateTime.now(UTC),
            snapshotId = UUID.randomUUID(),
            skus = listOf(
                SkuLiveInventory(
                    skuId = skuId1,
                    inventory = listOf(
                        inventory0,
                        inventory1,
                        inventory2,
                    ),
                ),
            ),
        )
        val liveInventorySnapshotAtCleardown = LiveInventorySnapshot(
            dcCode = "dc-code-fixture",
            snapshotTime = LocalDateTime.now(UTC),
            snapshotId = UUID.randomUUID(),
            skus = listOf(
                SkuLiveInventory(
                    skuId = skuId1,
                    inventory = listOf(
                        inventory3,
                        inventory4,
                        inventory5,
                    ),
                ),
            ),
        )

        val mergedSnapshot = inventoryLiveSnapshotProcessor.refreshCleardownSkusAtLocationLevel(
            liveInventorySnapshotWithCleardownSkus,
            liveInventorySnapshotAtCleardown,
            skusWithClearDownActivity,
        )

        assertEquals(1, mergedSnapshot.size)

        val mergedSku = mergedSnapshot.find { it.skuId == skuId1 }!!
        assertEquals(
            2000L,
            mergedSku.inventory.find {
                it.location.id == locationId1
            }!!.qty.getValue().toLong(),
        ) // Quantity should be updated - because its RCV
        assertEquals(
            2000L,
            mergedSku.inventory.find {
                it.location.id == locationId1
            }!!.qty.getValue().toLong(),
        ) // Quantity should be updated - because its RCV

        assertEquals(
            2000L,
            mergedSku.inventory.find {
                it.location.id == locationId1
            }!!.qty.getValue().toLong(),
        ) // Quantity should be updated - because its RCV
    }

    private fun createAdjustment(
        quantity: Long,
        skuId: UUID = defaultSkuId,
        locationId: String = defaultLocationId,
        typeId: String = InventoryAdjustmentTypeId.CLR.name
    ) =
        InventoryAdjustment.Companion.default().copy(
            skuId = skuId,
            locationId = locationId,
            locationType = LOCATION_TYPE_STAGING,
            transportModuleId = defaultTmID,
            quantity = SkuQuantity.fromLong(quantity),
            expirationDate = defaultExpiryDate,
            typeId = typeId,
        )

    private fun createInventory(quantity: Int, locationId: String): Inventory =
        Inventory(
            qty = SkuQuantity.fromBigDecimal(BigDecimal(quantity)),
            expiryDate = LocalDate.now(UTC).plusDays(10),
            location = Location(
                id = locationId,
                type = LOCATION_TYPE_STAGING,
                transportModuleId = "transportModuleId",
            ),
            poReference = "2024701483_E01",
        )
}
