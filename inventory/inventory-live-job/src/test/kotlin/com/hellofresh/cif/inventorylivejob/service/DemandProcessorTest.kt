package com.hellofresh.cif.inventorylivejob.service

import com.hellofresh.cif.demand.DemandRepository
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuQuantity.Companion
import com.hellofresh.cif.skuinput.repo.DcSkuCodeKey
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.demand.models.ActualConsumption
import com.hellofresh.demand.models.Demand
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import com.hellofresh.inventory.models.inventory.live.SkuLiveInventory
import com.hellofresh.inventory.models.inventory.live.random
import com.hellofresh.inventory.models.random
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.default
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class DemandProcessorTest {

    private val demandRepository = mockk<DemandRepository>()
    private val skuInputDataRepository = mockk<SkuInputDataRepository>()
    private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())

    private val distributionCenterConfiguration = DistributionCenterConfiguration.Companion.default()

    private val today = LocalDateTime.now(distributionCenterConfiguration.zoneId)

    private val skuId = UUID.randomUUID()
    private val skuSpecification = SkuSpecification.Companion.default
    private val defaultDemandQty = SkuQuantity.fromLong(100L)
    private val defaultInventorySnapshot = LiveInventorySnapshot.Companion.random().copy(
        dcCode = distributionCenterConfiguration.dcCode,
        snapshotTime = today,
    )

    @BeforeEach
    fun beforeEach() {
        coEvery { skuInputDataRepository.fetchSkuLookUp(setOf(distributionCenterConfiguration.dcCode)) } returns
            mapOf(DcSkuCodeKey(distributionCenterConfiguration.dcCode, skuSpecification.skuCode) to (skuId to skuSpecification))

        coEvery {
            demandRepository.findDemands(
                setOf(distributionCenterConfiguration.dcCode), DateRange.oneDay(today.toLocalDate()),
            )
        } returns listOf(
            Demand(
                skuId, distributionCenterConfiguration.dcCode, today.toLocalDate(), defaultDemandQty,
                ActualConsumption(Companion.fromLong(0), false)
            ),
        )
    }

    @AfterEach
    fun afterEach() {
        clearAllMocks()
    }

    @Test
    fun `demand is serve partially from usable staging location`() {
        val expectedInventory = Inventory(
            SkuQuantity.fromBigDecimal(defaultDemandQty.getValue() * BigDecimal(3)),
            null,
            Location("", LOCATION_TYPE_STAGING, null),
            null,
        )
        val sku = SkuLiveInventory(
            skuId,
            listOf(
                expectedInventory,
                Inventory.Companion.random().copy(location = Location("", LOCATION_TYPE_QUARANTINE, null)),
            ),
        )
        val inventorySnapshot = defaultInventorySnapshot.copy(skus = listOf(sku))

        val inventorySnapshotResult =
            runBlocking {
                DemandProcessor(demandRepository, skuInputDataRepository, statsigFeatureFlagClient)
                    .serveDemand(inventorySnapshot, distributionCenterConfiguration)
            }

        assertEquals(inventorySnapshot.dcCode, inventorySnapshotResult.dcCode)
        assertEquals(inventorySnapshot.snapshotId, inventorySnapshotResult.snapshotId)
        assertEquals(inventorySnapshot.snapshotTime, inventorySnapshotResult.snapshotTime)

        assertEquals(1, inventorySnapshotResult.skus.size)
        assertEquals(2, inventorySnapshotResult.skus.first().inventory.size)
        assertEquals(
            expectedInventory.copy(
                qty = Companion.fromBigDecimal(
                    expectedInventory.qty.getValue() - BigDecimal(
                        defaultDemandQty.getValue().toLong(),
                    ),
                ),
            ),
            inventorySnapshotResult.skus.first().inventory.first(),
        )
    }

    @Test
    fun `demand is completely serve from usable staging location`() {
        val expectedInventory = Inventory(
            defaultDemandQty,
            null,
            Location("", LOCATION_TYPE_STAGING, null),
            null,
        )
        val sku = SkuLiveInventory(
            skuId,
            listOf(
                expectedInventory,
                Inventory.Companion.random().copy(location = Location("", LOCATION_TYPE_QUARANTINE, null)),
            ),
        )
        val inventorySnapshot = defaultInventorySnapshot.copy(skus = listOf(sku))

        val inventorySnapshotResult =
            runBlocking {
                DemandProcessor(demandRepository, skuInputDataRepository, statsigFeatureFlagClient)
                    .serveDemand(inventorySnapshot, distributionCenterConfiguration)
            }

        assertEquals(1, inventorySnapshotResult.skus.size)
        assertEquals(2, inventorySnapshotResult.skus.first().inventory.size)
        assertEquals(
            LOCATION_TYPE_STAGING,
            inventorySnapshotResult.skus.first().inventory.first { it.location.type.isStaging() }.location.type,
        )
        assertEquals(
            LOCATION_TYPE_QUARANTINE,
            inventorySnapshotResult.skus.first().inventory.first { it.location.type.isStorage() }.location.type,
        )
    }

    @Test
    fun `sku without qty before serving are deleted after serving demand`() {
        val expectedInventory = Inventory(
            defaultDemandQty,
            null,
            Location("", LOCATION_TYPE_STAGING, null),
            null,
        )
        val zeroInventory = Inventory(
            Companion.fromBigDecimal(BigDecimal.ZERO),
            LocalDate.now(),
            Location("", LOCATION_TYPE_STAGING, null),
            null,
        )
        val sku = SkuLiveInventory(
            skuId,
            listOf(expectedInventory, zeroInventory),
        )
        val inventorySnapshot = defaultInventorySnapshot.copy(skus = listOf(sku))

        val inventorySnapshotResult =
            runBlocking {
                DemandProcessor(demandRepository, skuInputDataRepository, statsigFeatureFlagClient)
                    .serveDemand(inventorySnapshot, distributionCenterConfiguration)
            }

        assertEquals(1, inventorySnapshotResult.skus.size)
        assertEquals(
            expectedInventory.copy(qty = Companion.fromBigDecimal(BigDecimal.ZERO)),
            inventorySnapshotResult.skus.first().inventory.first(),
        )
    }

    @Test
    fun `sku is deleted when there is no inventory after serving demand`() {
        val expectedInventory = Inventory(
            Companion.fromBigDecimal(BigDecimal.ZERO),
            null,
            Location("", LOCATION_TYPE_STAGING, null),
            null,
        )
        val sku = SkuLiveInventory(
            skuId,
            listOf(expectedInventory),
        )
        val inventorySnapshot = defaultInventorySnapshot.copy(skus = listOf(sku))

        val inventorySnapshotResult =
            runBlocking {
                DemandProcessor(demandRepository, skuInputDataRepository, statsigFeatureFlagClient)
                    .serveDemand(inventorySnapshot, distributionCenterConfiguration)
            }

        assertEquals(0, inventorySnapshotResult.skus.size)
    }

    @Test
    fun `demand is serve with expires first policy`() {
        val inventoryQty = defaultDemandQty.div(SkuQuantity.fromLong(4))
        val expectedInventory1 = Inventory(
            Companion.fromBigDecimal(defaultDemandQty.getValue()),
            null,
            Location("1", LOCATION_TYPE_STAGING, null),
            null,
        )
        val expectedInventory2 = Inventory(
            Companion.fromBigDecimal(defaultDemandQty.getValue()),
            today.plusMonths(2).toLocalDate(),
            Location("2", LOCATION_TYPE_STAGING, null),
            null,
        )
        val expectedInventory3 = Inventory(
            Companion.fromBigDecimal(inventoryQty.getValue()),
            today.plusMonths(1).toLocalDate(),
            Location("3", LOCATION_TYPE_STAGING, null),
            null,
        )
        val sku = SkuLiveInventory(skuId, listOf(expectedInventory1, expectedInventory2, expectedInventory3).shuffled())
        val inventorySnapshot = defaultInventorySnapshot.copy(skus = listOf(sku))

        val inventorySnapshotResult =
            runBlocking {
                DemandProcessor(demandRepository, skuInputDataRepository, statsigFeatureFlagClient)
                    .serveDemand(inventorySnapshot, distributionCenterConfiguration)
            }

        assertEquals(3, inventorySnapshotResult.skus.first().inventory.size)
        assertEquals(
            expectedInventory3.copy(
                qty = Companion.fromBigDecimal(
                    BigDecimal.ZERO,
                ),
            ),
            inventorySnapshotResult.skus.first().inventory.first { it.location.id == expectedInventory3.location.id },
        )
        assertEquals(
            expectedInventory2.copy(
                qty = SkuQuantity.fromBigDecimal(
                    expectedInventory2.qty.getValue() - (defaultDemandQty - inventoryQty).getValue(),
                ),
            ),
            inventorySnapshotResult.skus.first().inventory.first { it.location.id == expectedInventory2.location.id },
        )
        assertEquals(
            expectedInventory1.copy(qty = SkuQuantity.fromBigDecimal(expectedInventory1.qty.getValue())),
            inventorySnapshotResult.skus.first().inventory.first {
                it.location.id == expectedInventory1.location.id
            },
        )
    }

    @Test
    fun `demand is serve from usable acl inventory`() {
        val acl = 3
        coEvery { skuInputDataRepository.fetchSkuLookUp(setOf(distributionCenterConfiguration.dcCode)) } returns
            mapOf(DcSkuCodeKey(distributionCenterConfiguration.dcCode, skuSpecification.skuCode) to (skuId to skuSpecification.copy(acceptableCodeLife = acl)))

        val inventoryQty = defaultDemandQty.times(SkuQuantity.fromLong(3))
        val expectedInventory1 = Inventory(
            inventoryQty,
            today.plusDays(acl - 1L).toLocalDate(),
            Location("1", LOCATION_TYPE_STAGING, null),
            null,
        )
        val expectedInventory2 = Inventory(
            inventoryQty,
            today.plusDays(acl.toLong()).toLocalDate(),
            Location("2", LOCATION_TYPE_STAGING, null),
            null,
        )

        val sku = SkuLiveInventory(skuId, listOf(expectedInventory1, expectedInventory2).shuffled())
        val inventorySnapshot = defaultInventorySnapshot.copy(skus = listOf(sku))

        val inventorySnapshotResult =
            runBlocking {
                DemandProcessor(demandRepository, skuInputDataRepository, statsigFeatureFlagClient)
                    .serveDemand(inventorySnapshot, distributionCenterConfiguration)
            }

        assertEquals(1, inventorySnapshotResult.skus.first().inventory.size)
        assertEquals(
            expectedInventory2.copy(
                qty = SkuQuantity.fromBigDecimal(expectedInventory2.qty.getValue() - defaultDemandQty.getValue()),
            ),
            inventorySnapshotResult.skus.first().inventory.first { it.location.id == expectedInventory2.location.id },
        )
    }
}
