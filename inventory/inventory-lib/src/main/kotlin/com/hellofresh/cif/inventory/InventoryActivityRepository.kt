package com.hellofresh.cif.inventory

import com.hellofresh.cif.inventory.lib.schema.enums.InventoryActivityType
import com.hellofresh.cif.models.DateTimeRange
import com.hellofresh.inventory.models.InventoryActivity

interface InventoryActivityRepository {
    suspend fun fetchInventoryActivity(
        dcCodes: Set<String>,
        dateTimeRange: DateTimeRange,
        inventoryActivityEventType: InventoryActivityEventType? = null
    ): List<InventoryActivity>

    data class InventoryActivityEventType(val type: InventoryActivityType?, val typeIds: Set<String>? = null)
}
