package com.hellofresh.cif.inventory

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.inventory.lib.schema.Tables
import com.hellofresh.cif.inventory.lib.schema.Tables.INVENTORY_LIVE_SNAPSHOT
import com.hellofresh.cif.models.DateRange
import com.hellofresh.inventory.models.InventoryValue
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import com.hellofresh.inventory.models.inventory.live.SkuLiveInventory
import java.time.ZoneId
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging

private const val FETCH_BY_DCS_NAME = "fetch-live-inventory-by-dc"
private const val FETCH_BY_SKU_NAME = "fetch-live-inventory-by-sku"

class LiveInventoryRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
) : LiveInventoryRepository {
    override suspend fun fetchBy(dcCodes: Set<String>, dateRange: DateRange): List<LiveInventorySnapshot> =
        internalFetchBy(dcCodes, dateRange, null)

    override suspend fun fetchBy(dcCodes: Set<String>, dateRange: DateRange, skuId: UUID): List<LiveInventorySnapshot> =
        internalFetchBy(dcCodes, dateRange, skuId)

    private suspend fun internalFetchBy(dcCodes: Set<String>, dateRange: DateRange, skuId: UUID?) =
        metricsDSLContext.withTagName(skuId?.let { FETCH_BY_SKU_NAME } ?: FETCH_BY_DCS_NAME)
            .select(
                INVENTORY_LIVE_SNAPSHOT.DC_CODE,
                INVENTORY_LIVE_SNAPSHOT.SNAPSHOT_TIME,
                INVENTORY_LIVE_SNAPSHOT.SNAPSHOT_ID,
                INVENTORY_LIVE_SNAPSHOT.DATE,
                INVENTORY_LIVE_SNAPSHOT.SKU_ID,
                INVENTORY_LIVE_SNAPSHOT.CLEARDOWN_TIME,
                INVENTORY_LIVE_SNAPSHOT.VALUE,
                Tables.DC_CONFIG.ZONE_ID,
            )
            .from(INVENTORY_LIVE_SNAPSHOT)
            .join(Tables.DC_CONFIG).on(Tables.DC_CONFIG.DC_CODE.eq(Tables.INVENTORY_LIVE_SNAPSHOT.DC_CODE))
            .where(
                INVENTORY_LIVE_SNAPSHOT.DC_CODE!!.`in`(dcCodes)
                    .and(INVENTORY_LIVE_SNAPSHOT.DATE!!.between(dateRange.fromDate, dateRange.toDate))
                    .let {
                        skuId?.let { id -> it.and(INVENTORY_LIVE_SNAPSHOT.SKU_ID!!.eq(id)) } ?: it
                    },
            ).fetchAsync()
            .thenApply {
                it.groupBy { record ->
                    record.get(INVENTORY_LIVE_SNAPSHOT.DC_CODE) to record.get(INVENTORY_LIVE_SNAPSHOT.DATE)
                }.map { (key, records) ->
                    val dc = key.first
                    val firstRecord = records.first()
                    val zoneId = ZoneId.of(firstRecord.get(Tables.DC_CONFIG.ZONE_ID))
                    LiveInventorySnapshot(
                        dcCode = dc,
                        snapshotId = firstRecord.get(INVENTORY_LIVE_SNAPSHOT.SNAPSHOT_ID),
                        snapshotTime = firstRecord.get(INVENTORY_LIVE_SNAPSHOT.SNAPSHOT_TIME)
                            .atZoneSameInstant(zoneId).toLocalDateTime(),
                        skus = records.map { record ->
                            SkuLiveInventory(
                                skuId = record.get(INVENTORY_LIVE_SNAPSHOT.SKU_ID),
                                cleardownTime = record.get(
                                    INVENTORY_LIVE_SNAPSHOT.CLEARDOWN_TIME,
                                )?.atZoneSameInstant(zoneId)?.toLocalDateTime(),
                                inventory = objectMapper.readValue<InventoryValue>(
                                    record.get(
                                        INVENTORY_LIVE_SNAPSHOT.VALUE,
                                    ).data(),
                                ).inventory,
                            )
                        },
                    )
                }
            }.await()

    companion object : Logging {
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
