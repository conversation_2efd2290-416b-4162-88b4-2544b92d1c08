package com.hellofresh.cif.inventory

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.inventory.lib.schema.Tables.DC_CONFIG
import com.hellofresh.cif.inventory.lib.schema.Tables.INVENTORY_ALL_SNAPSHOTS
import com.hellofresh.cif.inventory.lib.schema.Tables.INVENTORY_CLEARDOWN_TRIGGER
import com.hellofresh.cif.inventory.lib.schema.Tables.INVENTORY_PROCESSED_SNAPSHOTS
import com.hellofresh.cif.inventory.lib.schema.Tables.INVENTORY_SNAPSHOT
import com.hellofresh.cif.models.DateRange
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.InventoryValue
import com.hellofresh.inventory.models.SkuInventory
import java.time.DayOfWeek
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.impl.DSL.row
import org.jooq.impl.DSL.select

private const val FETCH_BY_DCS_NAME = "fetch-inventory-by-dc"
private const val FETCH_BY_DCS_NAME_CLEARDOWN = "fetch-inventory-by-dc-cleardown"
private const val FETCH_BY_SKU_NAME = "fetch-inventory-by-sku"
private const val FETCH_CLEARDOWN_TRIGGER = "fetch-cleardown-trigger"
private const val FETCH_BY_SNAPSHOT_ID_CLEARDOWN = "fetch-inventory-by-snapshot-id-cleardown"

private const val DAYS_IN_PAST = 2L

class InventoryRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
) : InventoryRepository {
    override suspend fun fetchBy(dcCodes: Set<String>, dateRange: DateRange): List<InventorySnapshot> =
        internalFetchBy(dcCodes, dateRange, null)

    override suspend fun fetchBy(dcCodes: Set<String>, dateRange: DateRange, skuId: UUID): List<InventorySnapshot> =
        internalFetchBy(dcCodes, dateRange, skuId)

    private suspend fun internalFetchBy(dcCodes: Set<String>, dateRange: DateRange, skuId: UUID?) =
        metricsDSLContext.withTagName(skuId?.let { FETCH_BY_SKU_NAME } ?: FETCH_BY_DCS_NAME)
            .select(
                INVENTORY_SNAPSHOT.DC_CODE,
                INVENTORY_SNAPSHOT.SNAPSHOT_TIME,
                INVENTORY_SNAPSHOT.SNAPSHOT_ID,
                INVENTORY_SNAPSHOT.DATE,
                INVENTORY_SNAPSHOT.SKU_ID,
                INVENTORY_SNAPSHOT.VALUE,
                DC_CONFIG.ZONE_ID,
            )
            .from(INVENTORY_SNAPSHOT)
            .join(DC_CONFIG).on(DC_CONFIG.DC_CODE.eq(INVENTORY_SNAPSHOT.DC_CODE))
            .where(
                INVENTORY_SNAPSHOT.DC_CODE!!.`in`(dcCodes)
                    .and(INVENTORY_SNAPSHOT.DATE!!.between(dateRange.fromDate, dateRange.toDate))
                    .let {
                        skuId?.let { id -> it.and(INVENTORY_SNAPSHOT.SKU_ID!!.eq(id)) } ?: it
                    },
            ).fetchAsync()
            .thenApply {
                it.groupBy { record ->
                    record.get(INVENTORY_SNAPSHOT.DC_CODE) to record.get(INVENTORY_SNAPSHOT.DATE)
                }.map { (key, records) ->
                    val dc = key.first
                    val firstRecord = records.first()
                    InventorySnapshot(
                        dcCode = dc,
                        snapshotId = firstRecord.get(INVENTORY_SNAPSHOT.SNAPSHOT_ID),
                        snapshotTime = firstRecord.get(INVENTORY_SNAPSHOT.SNAPSHOT_TIME)
                            .atZoneSameInstant(ZoneId.of(firstRecord.get(DC_CONFIG.ZONE_ID))).toLocalDateTime(),
                        skus = records.map { record ->
                            SkuInventory(
                                skuId = record.get(INVENTORY_SNAPSHOT.SKU_ID),
                                inventory = objectMapper.readValue<InventoryValue>(
                                    record.get(
                                        INVENTORY_SNAPSHOT.VALUE,
                                    ).data(),
                                ).inventory,
                            )
                        },
                    )
                }
            }.await()

    override suspend fun getCleardownDetails(dcCodes: Set<String>): List<CleardownDetail> =
        metricsDSLContext.withTagName(FETCH_CLEARDOWN_TRIGGER)
            .select(
                DC_CONFIG.DC_CODE,
                DC_CONFIG.CLEARDOWN,
                DC_CONFIG.ZONE_ID,
                DC_CONFIG.SCHEDULED_CLEARDOWN_TIME,
                INVENTORY_CLEARDOWN_TRIGGER.TIMESTAMP,
                INVENTORY_PROCESSED_SNAPSHOTS.SNAPSHOT_TIME,
                INVENTORY_CLEARDOWN_TRIGGER.INVENTORY_SNAPSHOT_ID,
            )
            .distinctOn(DC_CONFIG.DC_CODE)
            .from(DC_CONFIG)
            .leftJoin(INVENTORY_CLEARDOWN_TRIGGER).on(DC_CONFIG.DC_CODE.eq(INVENTORY_CLEARDOWN_TRIGGER.DC_CODE))
            .leftJoin(
                INVENTORY_PROCESSED_SNAPSHOTS,
            ).on(INVENTORY_CLEARDOWN_TRIGGER.INVENTORY_SNAPSHOT_ID.eq(INVENTORY_PROCESSED_SNAPSHOTS.SNAPSHOT_ID))
            .where(DC_CONFIG.DC_CODE.`in`(dcCodes).and(DC_CONFIG.HAS_CLEARDOWN.isTrue))
            .orderBy(DC_CONFIG.DC_CODE, INVENTORY_CLEARDOWN_TRIGGER.TIMESTAMP.desc())
            .fetchAsync()
            .thenApply {
                it.map { record ->
                    val zoneId = ZoneId.of(record.get(DC_CONFIG.ZONE_ID))
                    CleardownDetail(
                        dcCode = record.get(DC_CONFIG.DC_CODE),
                        scheduledCleardownDay = DayOfWeek.valueOf(record.get(DC_CONFIG.CLEARDOWN)),
                        zoneId = zoneId,
                        triggeredCleardownTime = (record.get(INVENTORY_PROCESSED_SNAPSHOTS.SNAPSHOT_TIME) ?: record.get(INVENTORY_CLEARDOWN_TRIGGER.TIMESTAMP))
                            ?.atZoneSameInstant(zoneId),
                        scheduledCleardownTime = record.get(DC_CONFIG.SCHEDULED_CLEARDOWN_TIME),
                        snapshotId = record.get(INVENTORY_CLEARDOWN_TRIGGER.INVENTORY_SNAPSHOT_ID),
                    )
                }
            }.await()

    override suspend fun fetchInventory(dcSnapshot: Set<DcSnapshot>): List<InventorySnapshot> =
        metricsDSLContext.withTagName(FETCH_BY_SNAPSHOT_ID_CLEARDOWN).select(
            INVENTORY_ALL_SNAPSHOTS.DC_CODE,
            INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_ID,
            INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_TIME,
            INVENTORY_ALL_SNAPSHOTS.SKU_ID,
            INVENTORY_ALL_SNAPSHOTS.VALUE,
            DC_CONFIG.ZONE_ID,
        )
            .from(INVENTORY_ALL_SNAPSHOTS)
            .join(DC_CONFIG)
            .on(DC_CONFIG.DC_CODE.eq(INVENTORY_ALL_SNAPSHOTS.DC_CODE))
            .where(
                row(INVENTORY_ALL_SNAPSHOTS.DC_CODE, INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_ID).`in`(
                    dcSnapshot.map { row(it.dcCode, it.snapshotId) },
                ),
            )
            .fetchAsync()
            .thenApply {
                it.groupBy { rec ->
                    DcSnapshot(
                        rec.get(INVENTORY_ALL_SNAPSHOTS.DC_CODE),
                        rec.get(INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_ID),
                    )
                }
                    .mapValues { (dcSnapshot, records) ->
                        val first = records.first()
                        InventorySnapshot(
                            dcCode = dcSnapshot.dcCode,
                            snapshotId = dcSnapshot.snapshotId,
                            snapshotTime = first.get(INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_TIME)
                                .atZoneSameInstant(ZoneId.of(first.get(DC_CONFIG.ZONE_ID)))
                                .toLocalDateTime(),
                            skus = records.map { record ->
                                SkuInventory(
                                    skuId = record.get(INVENTORY_ALL_SNAPSHOTS.SKU_ID),
                                    inventory = objectMapper.readValue<InventoryValue>(
                                        record.get(INVENTORY_ALL_SNAPSHOTS.VALUE).data(),
                                    ).inventory,
                                )
                            },
                        )
                    }.values.toList()
            }.await()

    override suspend fun fetchInventoryAtSpecificTime(
        dcCodes: Set<String>,
        timestamp: ZonedDateTime
    ): List<InventorySnapshot> =
        metricsDSLContext.withTagName(FETCH_BY_DCS_NAME_CLEARDOWN)
            .select(
                INVENTORY_ALL_SNAPSHOTS.DC_CODE,
                INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_ID,
                INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_TIME,
                INVENTORY_ALL_SNAPSHOTS.SKU_ID,
                INVENTORY_ALL_SNAPSHOTS.VALUE,
            )
            .from(INVENTORY_ALL_SNAPSHOTS)
            .where(
                row(
                    INVENTORY_ALL_SNAPSHOTS.DC_CODE,
                    INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_ID,
                )
                    .`in`(
                        select(
                            INVENTORY_ALL_SNAPSHOTS.DC_CODE,
                            INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_ID,
                        ).distinctOn(INVENTORY_ALL_SNAPSHOTS.DC_CODE)
                            .from(INVENTORY_ALL_SNAPSHOTS)
                            .where(
                                INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_TIME.between(
                                    timestamp.minusDays(DAYS_IN_PAST).toOffsetDateTime(),
                                    timestamp.toOffsetDateTime(),
                                )
                                    .and(INVENTORY_ALL_SNAPSHOTS.DC_CODE.`in`(dcCodes)),

                            ).orderBy(
                                INVENTORY_ALL_SNAPSHOTS.DC_CODE,
                                INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_TIME.desc(),
                                INVENTORY_ALL_SNAPSHOTS.CREATED_AT.desc(),
                            ),
                    ),
            ).fetchAsync()
            .thenApply {
                it.groupBy { it.get(INVENTORY_ALL_SNAPSHOTS.DC_CODE) }
                    .map { (dcCode, records) ->
                        val snapshotId = records.first().get(INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_ID)
                        val snapshotTime =
                            records.first().get(
                                INVENTORY_ALL_SNAPSHOTS.SNAPSHOT_TIME,
                            ).atZoneSameInstant(timestamp.zone).toLocalDateTime()
                        InventorySnapshot(
                            dcCode = dcCode,
                            snapshotId = snapshotId,
                            snapshotTime = snapshotTime,
                            skus = records.map { record ->
                                SkuInventory(
                                    skuId = record.get(INVENTORY_ALL_SNAPSHOTS.SKU_ID),
                                    inventory = objectMapper.readValue<InventoryValue>(
                                        record.get(INVENTORY_ALL_SNAPSHOTS.VALUE).data(),
                                    ).inventory,
                                )
                            },
                        )
                    }
            }.await()

    data class CleardownDetail(
        val dcCode: String,
        val scheduledCleardownDay: DayOfWeek,
        val zoneId: ZoneId,

        /** Snapshot's timestamp if exists otherwise trigger's timestamp **/
        val triggeredCleardownTime: ZonedDateTime?,
        val scheduledCleardownTime: LocalTime,

        /** Only exists if cleardown is triggered**/
        val snapshotId: UUID? = null

    ) {
        val scheduledDateTime: ZonedDateTime
            get() {
                val now = ZonedDateTime.now(zoneId)

                return if (now.toLocalTime() >= scheduledCleardownTime) {
                    now.with(TemporalAdjusters.previousOrSame(scheduledCleardownDay))
                } else {
                    now.with(TemporalAdjusters.previous(scheduledCleardownDay))
                }.with(scheduledCleardownTime)
            }
    }

    companion object : Logging {
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
