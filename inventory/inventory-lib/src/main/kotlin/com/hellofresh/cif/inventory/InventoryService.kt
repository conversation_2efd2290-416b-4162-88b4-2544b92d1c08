package com.hellofresh.cif.inventory

import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.ApplyUnusableMovementsStock
import com.hellofresh.cif.featureflags.FeatureFlag.TriggeredCleardownTimeForInventorySnapshot
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.inventory.InventoryActivityRepository.InventoryActivityEventType
import com.hellofresh.cif.inventory.InventoryRepositoryImpl.CleardownDetail
import com.hellofresh.cif.inventory.lib.schema.enums.InventoryActivityType.MOV
import com.hellofresh.cif.lib.groupByFirstToSet
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.DateTimeRange
import com.hellofresh.inventory.models.CleardownData
import com.hellofresh.inventory.models.CleardownMode
import com.hellofresh.inventory.models.CleardownMode.SCHEDULED
import com.hellofresh.inventory.models.CleardownMode.TRIGGERED
import com.hellofresh.inventory.models.InventoryActivity
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.inventory.models.inbounds.InventorySnapshotInbounds.Companion.calculateInboundTimeRange
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.UUID
import org.apache.logging.log4j.kotlin.Logging

private const val DAYS_IN_PAST = 7L
internal const val NUMBER_OF_DAYS_FOR_CALCULATION = 112L
internal const val SCHEDULED_CLEARDOWN_DAYS_AFTER_MANUAL = 3L

class InventoryService(
    private val inventoryRepository: InventoryRepository,
    private val liveInventoryRepository: LiveInventoryRepository,
    private val inventoryActivityRepository: InventoryActivityRepository,
    private val statsig: StatsigFeatureFlagClient,
) {

    suspend fun fetchInventorySnapshots(
        dcConfigs: Set<DistributionCenterConfiguration>,
        skuId: UUID? = null
    ): InventorySnapshots {
        val dcCodes = dcConfigs.map { it.dcCode }.toSet()
        val resolvedCleardownDetails = getResolvedLatestCleardownTime(dcCodes)

        val cleardownsDetails = getCleardownDetails(resolvedCleardownDetails, skuId)
            .onEach {
                logger.info(
                    "Cleardown Inventory for ${it.dcCode}: " +
                        "Cleardown: ${it.cleardownMode} / ${it.cleardownTime}, " +
                        "Snapshot:${it.snapshot?.snapshotId} / ${it.snapshot?.snapshotTime}",
                )
            }
        val dcDateRanges = calculationInputDcDateRanges(dcConfigs, cleardownsDetails)

        val inventoryByDateSnapshots = getInventoryByDateSnapshots(dcDateRanges, skuId)
        val liveInventoryByDateSnapshots = getLiveInventoryByDateSnapshots(dcDateRanges, skuId)

        val inventoryActivities = fetchInventoryActivities(dcConfigs, cleardownsDetails, dcDateRanges)

        return InventorySnapshots(
            inventoryByDateSnapshots,
            liveInventoryByDateSnapshots,
            cleardownsDetails,
            inventoryActivities,
        )
    }

    private suspend fun getCleardownDetails(
        cleardownDetails: Map<String, ResolvedCleardown>,
        skuId: UUID?
    ): List<CleardownData> {
        val dcSnapshots = inventoryRepository.fetchInventory(cleardownDetails.values.toList()).associateBy { it.dcCode }

        return cleardownDetails.values.map { resolvedCleardown ->
            val snapshot = dcSnapshots[resolvedCleardown.dcCode]
            CleardownData(
                dcCode = resolvedCleardown.dcCode,
                cleardownTime = resolvedCleardown.cleardownTime.toLocalDateTime(),
                cleardownMode = resolvedCleardown.cleardownMode,
                snapshot = skuId?.let {
                    snapshot?.copy(skus = filterSkuInventory(snapshot.skus, skuId))
                } ?: snapshot,
            )
        }
    }

    private fun filterSkuInventory(inventory: List<SkuInventory>, skuId: UUID?) =
        skuId?.let { inventory.filter { inv -> inv.skuId == it } } ?: inventory

    private suspend fun getInventoryByDateSnapshots(
        dataRanges: Map<DateRange, Set<DistributionCenterConfiguration>>,
        skuId: UUID?
    ) = dataRanges.flatMap { (dateRange, dcConfigs) ->
        val dcs = dcConfigs.map { it.dcCode }.toSet()
        skuId?.let { inventoryRepository.fetchBy(dcs, dateRange, skuId) }
            ?: inventoryRepository.fetchBy(dcs, dateRange)
    }

    private suspend fun getLiveInventoryByDateSnapshots(
        dataRanges: Map<DateRange, Set<DistributionCenterConfiguration>>,
        skuId: UUID?
    ) = dataRanges.flatMap { (dateRange, dcConfigs) ->
        val dcs = dcConfigs.map { it.dcCode }.toSet()
        skuId?.let { liveInventoryRepository.fetchBy(dcs, dateRange, skuId) }
            ?: liveInventoryRepository.fetchBy(dcs, dateRange)
    }

    suspend fun getResolvedLatestCleardownTime(dcCodes: Set<String>): Map<String, ResolvedCleardown> {
        val cleardownDetails = inventoryRepository.getCleardownDetails(dcCodes)

        return cleardownDetails.associateBy({ it.dcCode }) { cleardownDetail ->
            cleardownDetail.resolve(cleardownMode(cleardownDetail))
        }
    }

    // Return Triggered if triggered cleardown time exists and is triggered in last 3 days of the scheduled
    private fun cleardownMode(cleardownDetail: CleardownDetail): CleardownMode {
        resolveCleardownModeFromConfig(cleardownDetail)?.let { return it }

        val scheduledTime = cleardownDetail.scheduledDateTime
        val triggeredTime = cleardownDetail.triggeredCleardownTime

        return when {
            triggeredTime == null -> SCHEDULED

            /** If triggered in the last [SCHEDULED_CLEARDOWN_DAYS_AFTER_MANUAL] days of scheduled, we keep it triggered */
            triggeredTime.toLocalDate() >= scheduledTime.toLocalDate()
                .minusDays(SCHEDULED_CLEARDOWN_DAYS_AFTER_MANUAL) -> TRIGGERED

            else -> SCHEDULED
        }
    }

    private fun resolveCleardownModeFromConfig(cleardownDetail: CleardownDetail): CleardownMode? {
        val dcCode = cleardownDetail.dcCode
        val manualDcs = ConfigurationLoader.getSet("only.manual.cleardown.dcs")
        return when {
            dcCode in manualDcs -> TRIGGERED
            !statsig.isEnabledFor(
                TriggeredCleardownTimeForInventorySnapshot(setOf(DC.data(dcCode)))
            ) -> SCHEDULED
            else -> null
        }
    }

    private suspend fun fetchInventoryActivities(
        dcConfigs: Set<DistributionCenterConfiguration>,
        cleardownInventorySnapshots: List<CleardownData>,
        dcDateRanges: Map<DateRange, Set<DistributionCenterConfiguration>>
    ): List<InventoryActivity> {
        val dcByCode = dcConfigs.associateBy { it.dcCode }
        val inboundInventoryActivities = cleardownInventorySnapshots
            .mapNotNull { cleardownData ->
                dcByCode[cleardownData.dcCode]?.let { dcConfig ->
                    (dcConfig.zoneId to calculateInboundTimeRange(cleardownData)) to cleardownData.dcCode
                }
            }.groupByFirstToSet()
            .flatMap { (key, dcs) ->
                val (zoneID, inboundLocalDateTimeRange) = key
                inventoryActivityRepository.fetchInventoryActivity(
                    dcs,
                    DateTimeRange(
                        inboundLocalDateTimeRange.start.atZone(zoneID).toOffsetDateTime(),
                        inboundLocalDateTimeRange.endInclusive.atZone(zoneID).toOffsetDateTime(),
                    ),
                )
            }

        val unUsableMovements = fetchMovementsForUnusableEvaluationDcs(dcDateRanges)

        val existingActivityIds = inboundInventoryActivities.map { it.activityId }.toSet()
        return inboundInventoryActivities + unUsableMovements.filter { !existingActivityIds.contains(it.activityId) }
    }

    private suspend fun fetchMovementsForUnusableEvaluationDcs(
        dcDateRanges: Map<DateRange, Set<DistributionCenterConfiguration>>
    ): List<InventoryActivity> =
        calculationInputDcDateTimeRanges(dcDateRanges)
            .mapValues { (_, dcs) ->
                dcs.filter {
                    statsig.isEnabledFor(ApplyUnusableMovementsStock(setOf(ContextData(DC, it.dcCode))))
                }.map { it.dcCode }
            }
            .flatMap { (dateTimeRange, unusableMovementsDcs) ->
                if (unusableMovementsDcs.isNotEmpty()) {
                    inventoryActivityRepository.fetchInventoryActivity(
                        unusableMovementsDcs.toSet(),
                        dateTimeRange,
                        InventoryActivityEventType(MOV),
                    )
                } else {
                    emptyList()
                }
            }

    companion object : Logging {

        private fun calculationInputDcDateTimeRanges(
            dcDateRanges: Map<DateRange, Set<DistributionCenterConfiguration>>
        ): Map<DateTimeRange, Set<DistributionCenterConfiguration>> =
            dcDateRanges.flatMap { (dateRange, dcs) ->
                dcs.map { dc ->
                    calculationInputDcDateTimeRange(dateRange, dc) to dc
                }
            }.groupByFirstToSet()

        private fun calculationInputDcDateTimeRange(
            dateRange: DateRange,
            dc: DistributionCenterConfiguration,
        ) = DateTimeRange(
            dateRange.fromDate.atTime(LocalTime.MIDNIGHT).atZone(dc.zoneId).toOffsetDateTime(),
            dateRange.toDate.atTime(LocalTime.MIDNIGHT.minusNanos(1)).atZone(dc.zoneId).toOffsetDateTime(),
        )

        fun calculationInputDcDateRanges(
            dcConfigs: Set<DistributionCenterConfiguration>,
            inventorySnapshots: InventorySnapshots
        ) =
            dcConfigs.map { dcConfig ->
                calculationInputDcDateRange(dcConfig, inventorySnapshots) to dcConfig
            }.groupByFirstToSet()

        fun calculationInputDcDateRange(
            dcConfig: DistributionCenterConfiguration,
            inventorySnapshots: InventorySnapshots
        ) =
            calculationInputDcDateRange(dcConfig, inventorySnapshots.getLatestCleardown(dcConfig.dcCode))

        fun calculationInputDcDateRanges(
            dcConfigs: Set<DistributionCenterConfiguration>,
            cleardownInventorySnapshots: List<CleardownData>
        ): Map<DateRange, Set<DistributionCenterConfiguration>> {
            val cleardownsByDc = cleardownInventorySnapshots.associateBy { it.dcCode }
            return dcConfigs.map { dcConfig ->
                calculationInputDcDateRange(dcConfig, cleardownsByDc[dcConfig.dcCode]) to dcConfig
            }.groupByFirstToSet()
        }

        /**
         * Returns input source data range that have to be fetched.
         * DataRange starts from the minimum data of (cleardown data - 1 day), (today - 7 days), (inbounds data before cleardown)
         **/
        fun calculationInputDcDateRange(
            dcConfig: DistributionCenterConfiguration,
            cleardownData: CleardownData?
        ): DateRange {
            val minInventoryDateRangeFrom = LocalDate.now(dcConfig.zoneId).minusDays(DAYS_IN_PAST)

            val beforeCleardownInventoryDateRangeFrom = cleardownData?.cleardownTime?.toLocalDate()?.minusDays(1)
            val inboundsInventoryMovementDataRangeFrom = cleardownData?.let {
                calculateInboundTimeRange(cleardownData).start.toLocalDate()
            }

            return DateRange(
                fromDate = listOfNotNull(
                    minInventoryDateRangeFrom,
                    beforeCleardownInventoryDateRangeFrom,
                    inboundsInventoryMovementDataRangeFrom,
                ).min(),
                toDate = LocalDate.now(dcConfig.zoneId).plusDays(NUMBER_OF_DAYS_FOR_CALCULATION),
            )
        }
    }
}

sealed class ResolvedCleardown(
    val dcCode: String,
    val cleardownMode: CleardownMode,
    val cleardownTime: ZonedDateTime,
    val snapshotId: UUID? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ResolvedCleardown

        if (dcCode != other.dcCode) return false
        if (cleardownMode != other.cleardownMode) return false
        if (cleardownTime != other.cleardownTime) return false
        if (snapshotId != other.snapshotId) return false

        return true
    }

    override fun hashCode(): Int {
        var result = dcCode.hashCode()
        result = 31 * result + cleardownMode.hashCode()
        result = 31 * result + cleardownTime.hashCode()
        result = 31 * result + (snapshotId?.hashCode() ?: 0)
        return result
    }
}

class TriggeredCleardown(dcCode: String, cleardownTime: ZonedDateTime, snapshotId: UUID?) : ResolvedCleardown(
    dcCode = dcCode,
    cleardownMode = TRIGGERED,
    cleardownTime = cleardownTime.truncatedTo(ChronoUnit.MINUTES),
    snapshotId = snapshotId,
)

class ScheduledCleardown(dcCode: String, cleardownTime: ZonedDateTime) : ResolvedCleardown(
    dcCode = dcCode,
    cleardownMode = SCHEDULED,
    cleardownTime = cleardownTime.truncatedTo(ChronoUnit.MINUTES),
    snapshotId = null,
)

fun CleardownDetail.resolve(mode: CleardownMode): ResolvedCleardown =
    if (mode == TRIGGERED) {
        TriggeredCleardown(dcCode, triggeredCleardownTime!!, snapshotId)
    } else {
        ScheduledCleardown(dcCode, scheduledDateTime)
    }
