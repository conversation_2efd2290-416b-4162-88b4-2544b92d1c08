package com.hellofresh.cif.inventory

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.inventory.lib.schema.Tables.STOCK_UPDATE
import com.hellofresh.cif.inventory.lib.schema.enums.Uom
import com.hellofresh.cif.inventory.lib.schema.enums.Uom.UOM_GAL
import com.hellofresh.cif.inventory.lib.schema.enums.Uom.UOM_KG
import com.hellofresh.cif.inventory.lib.schema.enums.Uom.UOM_LBS
import com.hellofresh.cif.inventory.lib.schema.enums.Uom.UOM_LITRE
import com.hellofresh.cif.inventory.lib.schema.enums.Uom.UOM_OZ
import com.hellofresh.cif.inventory.lib.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.inventory.lib.schema.enums.Uom.UOM_UNRECOGNIZED
import com.hellofresh.cif.inventory.lib.schema.enums.Uom.UOM_UNSPECIFIED
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Record
import org.jooq.impl.DSL
import org.jooq.impl.DSL.field
import org.jooq.impl.DSL.partitionBy
import org.jooq.impl.DSL.rowNumber

const val ROW_NUM = "row_num"

class StockUpdateRepositoryImpl(
    private val dslContext: MetricsDSLContext,
) : StockUpdateRepository {
    private val getStockUpdateTag = "get-stock-update"
    private val getStockUpdateByDcsAndWeeksAndSkuIds = "get-stock-update-by-dcs-and-weeks-sku-ids"

    override suspend fun getStockUpdate(skuIds: Set<UUID>, ranges: Map<String, DateRange>): List<StockUpdate> {
        if (ranges.isEmpty()) return listOf()

        val dcDateRangeConditions = DSL.or(
            ranges.map { (dc, range) ->
                DSL.and(
                    STOCK_UPDATE.DC_CODE.eq(dc),
                    STOCK_UPDATE.DATE.between(range.fromDate, range.toDate),
                )
            },
        )
        val rankedStockUpdates = createRankedStockUpdatesQuery()
            .where(
                if (skuIds.isNotEmpty()) STOCK_UPDATE.SKU_ID.`in`(skuIds) else DSL.trueCondition(),
                dcDateRangeConditions,
            ).asTable("lastVersionStockUpdatesWithDeleted")

        return dslContext.withTagName(getStockUpdateTag)
            .selectFrom(rankedStockUpdates)
            .where(field(ROW_NUM, Int::class.java).eq(1))
            .fetchAsync()
            .thenApply {
                it.map { record ->
                    toStockUpdate(record)
                }
            }.await()
    }

    override suspend fun getStockUpdate(ranges: Map<String, DateRange>): List<StockUpdate> =
        getStockUpdate(setOf(), ranges)

    override suspend fun getStockUpdatesBy(dcs: Set<String>, weeks: Set<String>, skuIds: Set<UUID>): List<StockUpdate> {
        val rankedStockUpdates = createRankedStockUpdatesQuery()
            .where(
                STOCK_UPDATE.DC_CODE.`in`(dcs)
                    .and(STOCK_UPDATE.WEEK.`in`(weeks))
                    .and(STOCK_UPDATE.SKU_ID.`in`(skuIds)),
            ).orderBy(STOCK_UPDATE.VERSION.desc())
            .asTable("lastVersionStockUpdatesWithDeleted")

        return dslContext.withTagName(getStockUpdateByDcsAndWeeksAndSkuIds)
            .selectFrom(rankedStockUpdates)
            .where(field(ROW_NUM, Int::class.java).eq(1))
            .fetchAsync()
            .thenApply {
                it.map { record ->
                    toStockUpdate(record)
                }
            }.await()
    }

    private fun createRankedStockUpdatesQuery() =
        dslContext.select(
            STOCK_UPDATE.SKU_ID,
            STOCK_UPDATE.DC_CODE,
            STOCK_UPDATE.DATE,
            STOCK_UPDATE.QUANTITY,
            STOCK_UPDATE.UOM,
            STOCK_UPDATE.REASON,
            STOCK_UPDATE.REASON_DETAIL,
            STOCK_UPDATE.AUTHOR_NAME,
            STOCK_UPDATE.AUTHOR_EMAIL,
            STOCK_UPDATE.VERSION,
            STOCK_UPDATE.DELETED,
            STOCK_UPDATE.CREATED_AT,
            STOCK_UPDATE.WEEK,
            rowNumber().over(
                partitionBy(STOCK_UPDATE.DC_CODE, STOCK_UPDATE.SKU_ID, STOCK_UPDATE.DATE)
                    .orderBy(STOCK_UPDATE.VERSION.desc()),
            ).`as`(ROW_NUM),
        )
            .from(STOCK_UPDATE)

    companion object : Logging {

        fun toStockUpdate(record: Record) =
            StockUpdate(
                skuId = record[STOCK_UPDATE.SKU_ID],
                dcCode = record[STOCK_UPDATE.DC_CODE],
                date = record[STOCK_UPDATE.DATE],
                week = record[STOCK_UPDATE.WEEK],
                quantity = SkuQuantity.fromBigDecimal(
                    record[STOCK_UPDATE.QUANTITY],
                    mapToSkuUom(record[STOCK_UPDATE.UOM]),
                ),
                reason = record[STOCK_UPDATE.REASON],
                reasonDetail = record[STOCK_UPDATE.REASON_DETAIL],
                authorName = record[STOCK_UPDATE.AUTHOR_NAME],
                authorEmail = record[STOCK_UPDATE.AUTHOR_EMAIL],
                version = record[STOCK_UPDATE.VERSION],
                createdAt = record[STOCK_UPDATE.CREATED_AT],
                deleted = record[STOCK_UPDATE.DELETED],
            )
    }
}

internal fun mapToSkuUom(uom: Uom) =
    when (uom) {
        UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
        UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
        UOM_UNIT -> SkuUOM.UOM_UNIT
        UOM_KG -> SkuUOM.UOM_KG
        UOM_LBS -> SkuUOM.UOM_LBS
        UOM_GAL -> SkuUOM.UOM_GAL
        UOM_LITRE -> SkuUOM.UOM_LITRE
        UOM_OZ -> SkuUOM.UOM_OZ
    }
