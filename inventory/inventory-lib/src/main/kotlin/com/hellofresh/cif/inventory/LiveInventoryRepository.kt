package com.hellofresh.cif.inventory

import com.hellofresh.cif.models.DateRange
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import java.util.UUID

interface LiveInventoryRepository {

    suspend fun fetchBy(dcCodes: Set<String>, dateRange: DateRange): List<LiveInventorySnapshot>
    suspend fun fetchBy(dcCodes: Set<String>, dateRange: DateRange, skuId: UUID): List<LiveInventorySnapshot>
}
