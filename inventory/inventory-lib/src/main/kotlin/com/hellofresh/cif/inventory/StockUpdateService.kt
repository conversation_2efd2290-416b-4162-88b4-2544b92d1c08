package com.hellofresh.cif.inventory

import com.hellofresh.cif.models.DateRange
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID

class StockUpdateService(
    private val inventoryService: InventoryService,
    private val stockUpdateRepository: StockUpdateRepository,
) {

    suspend fun getCurrentStockUpdateRange(dcCode: String): DateRange? =
        inventoryService.getResolvedLatestCleardownTime(setOf(dcCode))[dcCode]
            ?.cleardownTime
            ?.let { lastCleardownTime ->
                val lastCleardownDate = lastCleardownTime.toLocalDate()
                val today = OffsetDateTime.now(lastCleardownTime.offset).toLocalDate()
                return if (lastCleardownDate <= today) {
                    DateRange(lastCleardownDate, today)
                } else {
                    null
                }
            }

    suspend fun getCurrentStockUpdates(dcCodes: Set<String>): CurrentStockUpdates =
        dcCodes.mapNotNull { dcCode -> getCurrentStockUpdateRange(dcCode)?.let { dcCode to it } }
            .toMap()
            .let { dcDateRanges ->
                stockUpdateRepository.getStockUpdate(emptySet(), dcDateRanges)
                    .groupBy { DcSku(it.dcCode, it.skuId) }
                    .mapNotNull { (dcSku, stockUpdates) ->
                        val stockUpdatesByDate = stockUpdates.associateBy { it.date }
                        dcDateRanges[dcSku.dcCode]
                            ?.let { dateRange -> dcSku to dateRange.associateWith { date -> stockUpdatesByDate[date] } }
                    }.toMap()
            }

    suspend fun getStockUpdates(dcCode: String, weeks: Set<String>, skuIds: Set<UUID>): List<StockUpdate> =
        stockUpdateRepository.getStockUpdatesBy(setOf(dcCode), weeks, skuIds)

    suspend fun getStockUpdates(skuIds: Set<UUID>, ranges: Map<String, DateRange>): List<StockUpdate> =
        stockUpdateRepository.getStockUpdate(skuIds, ranges)

    suspend fun getCurrentStockUpdate(dcCode: String, skuId: UUID): Map<LocalDate, StockUpdate?> =
        getCurrentStockUpdateRange(dcCode)
            ?.let { dateRange ->
                val stockUpdates = stockUpdateRepository.getStockUpdate(setOf(skuId), mapOf(dcCode to dateRange)).associateBy {
                    it.date
                }
                dateRange.associateWith { date -> stockUpdates[date] }
            }
            ?: emptyMap()
}

typealias CurrentStockUpdates = Map<DcSku, Map<LocalDate, StockUpdate?>>

data class DcSku(val dcCode: String, val skuId: UUID)
