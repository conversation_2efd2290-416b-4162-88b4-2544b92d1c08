package com.hellofresh.cif.inventory

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.inventory.InventoryActivityRepository.InventoryActivityEventType
import com.hellofresh.cif.inventory.lib.schema.Tables.DC_CONFIG
import com.hellofresh.cif.inventory.lib.schema.Tables.INVENTORY_ACTIVITY
import com.hellofresh.cif.inventory.lib.schema.enums.InventoryActivityType
import com.hellofresh.cif.inventory.lib.schema.enums.InventoryActivityType.ADJ
import com.hellofresh.cif.inventory.lib.schema.enums.InventoryActivityType.MOV
import com.hellofresh.cif.models.DateTimeRange
import com.hellofresh.inventory.models.InventoryActivity
import com.hellofresh.inventory.models.InventoryAdjustment
import com.hellofresh.inventory.models.InventoryAdjustmentValue
import com.hellofresh.inventory.models.InventoryMovement
import com.hellofresh.inventory.models.InventoryMovementValue
import com.hellofresh.inventory.models.LocationType
import java.time.OffsetDateTime
import java.time.ZoneId
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.JSONB
import org.jooq.Record9
import org.jooq.impl.DSL

class InventoryActivityRepositoryImpl(private val metricsDSLContext: MetricsDSLContext) : InventoryActivityRepository {

    override suspend fun fetchInventoryActivity(
        dcCodes: Set<String>,
        dateTimeRange: DateTimeRange,
        inventoryActivityEventType: InventoryActivityEventType?
    ): List<InventoryActivity> {
        logger.info("Fetching Inventory Activity")
        if (dcCodes.isEmpty()) {
            return emptyList()
        }
        return metricsDSLContext.withTagName("inventory-activity-by-dc-date-range")
            .select(
                INVENTORY_ACTIVITY.ACTIVITY_ID,
                INVENTORY_ACTIVITY.ACTIVITY_TIME,
                INVENTORY_ACTIVITY.DC_CODE,
                INVENTORY_ACTIVITY.SKU_ID,
                INVENTORY_ACTIVITY.TYPE,
                INVENTORY_ACTIVITY.TYPE_ID,
                INVENTORY_ACTIVITY.VALUE,
                INVENTORY_ACTIVITY.PUBLISHED_TIME,
                DC_CONFIG.ZONE_ID,
            )
            .from(INVENTORY_ACTIVITY)
            .join(DC_CONFIG).on(DC_CONFIG.DC_CODE.eq(INVENTORY_ACTIVITY.DC_CODE))
            .where(
                INVENTORY_ACTIVITY.ACTIVITY_TIME.between(
                    dateTimeRange.fromDateTime,
                    dateTimeRange.toDateTime,
                ),
            )
            .and(INVENTORY_ACTIVITY.DC_CODE.`in`(dcCodes))
            .and(
                inventoryActivityEventType?.type?.let { type ->
                    INVENTORY_ACTIVITY.TYPE.eq(type)
                } ?: DSL.trueCondition(),
            )
            .and(
                inventoryActivityEventType?.typeIds?.let { typeIds ->
                    INVENTORY_ACTIVITY.TYPE_ID.`in`(typeIds)
                } ?: DSL.trueCondition(),
            )
            .fetchAsync()
            .thenApply {
                it.mapNotNull { record ->
                    mapToInventoryActivity(record)
                }
            }.await()
    }

    private fun mapToInventoryActivity(
        record:
        Record9<UUID, OffsetDateTime, String, UUID, InventoryActivityType, String, JSONB, OffsetDateTime, String>
    ) =
        when (record.get(INVENTORY_ACTIVITY.TYPE)) {
            ADJ -> {
                val inventoryAdjustmentValue = getInventoryAdjustment(record.get(INVENTORY_ACTIVITY.VALUE))
                with(record) {
                    InventoryAdjustment(
                        activityId = get(INVENTORY_ACTIVITY.ACTIVITY_ID),
                        activityTime = get(INVENTORY_ACTIVITY.ACTIVITY_TIME)
                            .atZoneSameInstant(ZoneId.of(get(DC_CONFIG.ZONE_ID))).toOffsetDateTime(),
                        publishedTime = get(INVENTORY_ACTIVITY.PUBLISHED_TIME),
                        dcCode = get(INVENTORY_ACTIVITY.DC_CODE),
                        skuId = get(INVENTORY_ACTIVITY.SKU_ID),
                        typeId = get(INVENTORY_ACTIVITY.TYPE_ID),
                        expirationDate = inventoryAdjustmentValue.expirationDate,
                        quantity = inventoryAdjustmentValue.quantity,
                        locationId = inventoryAdjustmentValue.locationId,
                        locationType = inventoryAdjustmentValue.locationType,
                        remainingQuantity = inventoryAdjustmentValue.remainingQuantity,
                        transportModuleId = inventoryAdjustmentValue.transportModuleId,
                        poNumber = inventoryAdjustmentValue.poNumber,
                    )
                }
            }

            MOV -> {
                val inventoryMovementValue = getInventoryMovement(record.get(INVENTORY_ACTIVITY.VALUE))
                with(record) {
                    InventoryMovement(
                        activityId = get(INVENTORY_ACTIVITY.ACTIVITY_ID),
                        activityTime = get(INVENTORY_ACTIVITY.ACTIVITY_TIME)
                            .atZoneSameInstant(ZoneId.of(get(DC_CONFIG.ZONE_ID))).toOffsetDateTime(),
                        publishedTime = get(INVENTORY_ACTIVITY.PUBLISHED_TIME),
                        dcCode = get(INVENTORY_ACTIVITY.DC_CODE),
                        skuId = get(INVENTORY_ACTIVITY.SKU_ID),
                        typeId = get(INVENTORY_ACTIVITY.TYPE_ID),
                        expirationDate = inventoryMovementValue.expirationDate,
                        quantity = inventoryMovementValue.quantity,
                        originLocationId = inventoryMovementValue.originLocationId,
                        originLocationType = inventoryMovementValue.originLocationType,
                        destinationLocationId = inventoryMovementValue.destinationLocationId,
                        destinationLocationType = inventoryMovementValue.destinationLocationType,
                        remainingQuantity = inventoryMovementValue.remainingQuantity,
                        transportModuleId = inventoryMovementValue.transportModuleId,
                        poNumber = inventoryMovementValue.poNumber,
                    )
                }
            }

            else -> null
        }

    private fun getInventoryAdjustment(inventoryAdjustment: JSONB): InventoryAdjustmentValue =
        objectMapper.readValue(
            inventoryAdjustment.toString(),
            InventoryAdjustmentValue::class.java,
        ).apply {
            InventoryAdjustmentValue(
                quantity = quantity,
                expirationDate = expirationDate,
                locationId = locationId,
                locationType = LocationType.parse(locationType.name),
                remainingQuantity = remainingQuantity,
                transportModuleId = transportModuleId,
                poNumber = poNumber,
            )
        }

    private fun getInventoryMovement(inventoryMovement: JSONB): InventoryMovementValue =
        objectMapper.readValue(
            inventoryMovement.toString(),
            InventoryMovementValue::class.java,
        ).apply {
            InventoryMovementValue(
                quantity = quantity,
                expirationDate = expirationDate,
                originLocationId = originLocationId,
                originLocationType = originLocationType,
                destinationLocationId = destinationLocationId,
                destinationLocationType = destinationLocationType,
                remainingQuantity = remainingQuantity,
                transportModuleId = transportModuleId,
                poNumber = poNumber,
            )
        }

    companion object : Logging {
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
