package com.hellofresh.cif.inventory

import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

interface StockUpdateRepository {

    /**
     * Fetches stock updates for the given skus and dc x date ranges
     * @param skuIds If the list is empty. It fetches every stock update
     * matching the [ranges]
     *
     * @param ranges map of DC code to date range. Fetch stock update only
     * between the date ranges for the corresponding DC. Is the list is empty
     * it returns empty
     *
     * **Note:** Both [DateRange.toDate] and [DateRange.fromDate] are inclusive
     */

    suspend fun getStockUpdate(skuIds: Set<UUID>, ranges: Map<String, DateRange>): List<StockUpdate>
    suspend fun getStockUpdate(ranges: Map<String, DateRange>): List<StockUpdate>
    suspend fun getStockUpdatesBy(dcs: Set<String>, weeks: Set<String>, skuIds: Set<UUID>): List<StockUpdate>
}

data class StockUpdate(
    val skuId: UUID,
    val dcCode: String,
    val date: LocalDate,
    val week: String,
    val quantity: SkuQuantity,
    val reason: String,
    val reasonDetail: String?,
    val authorName: String?,
    val authorEmail: String,
    val version: Int,
    val createdAt: LocalDateTime,
    val deleted: Boolean,
) {
    companion object
}
