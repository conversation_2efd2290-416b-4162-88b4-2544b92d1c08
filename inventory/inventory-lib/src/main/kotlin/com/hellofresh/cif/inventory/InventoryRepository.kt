package com.hellofresh.cif.inventory

import com.hellofresh.cif.inventory.InventoryRepositoryImpl.CleardownDetail
import com.hellofresh.cif.models.DateRange
import com.hellofresh.inventory.models.InventorySnapshot
import java.time.ZonedDateTime
import java.util.UUID

interface InventoryRepository {

    suspend fun fetchBy(dcCodes: Set<String>, dateRange: DateRange): List<InventorySnapshot>
    suspend fun fetchBy(dcCodes: Set<String>, dateRange: DateRange, skuId: UUID): List<InventorySnapshot>
    suspend fun fetchBy(snapshotId: UUID): List<InventorySnapshot> = listOf()

    suspend fun getCleardownDetails(dcCodes: Set<String>): List<CleardownDetail>

    suspend fun fetchInventory(resolvedCleardowns: Collection<ResolvedCleardown>): List<InventorySnapshot> {
        val (withSnapshotId, withCleardownTime) = resolvedCleardowns.partition { it.snapshotId != null }
        return withCleardownTime.groupBy { it.cleardownTime }
            .flatMap { (time, cleardowns) ->
                fetchInventoryAtSpecificTime(
                    dcCodes = cleardowns.map { it.dcCode }.toSet(),
                    timestamp = time,
                )
            } + fetchInventory(
            withSnapshotId
                .mapNotNull { resolvedCleardown ->
                    resolvedCleardown.snapshotId?.let { DcSnapshot(resolvedCleardown.dcCode, resolvedCleardown.snapshotId) }
                }.toSet(),
        )
    }

    suspend fun fetchInventory(dcSnapshot: Set<DcSnapshot>): List<InventorySnapshot>
    suspend fun fetchInventoryAtSpecificTime(dcCodes: Set<String>, timestamp: ZonedDateTime): List<InventorySnapshot>
}

data class DcSnapshot(val dcCode: String, val snapshotId: UUID)
