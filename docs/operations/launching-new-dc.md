# How to Launch a new DC

This document describes all the necessary steps to launch a new Distribution Center on SIV.

## Steps

### Create a new DC

#### Change the [sku-demand-forecast](https://github.com/hellofresh/sku-demand-forecast) project

1. Add a new DC to [csku-inventory-forecast.intermediate.distribution-center](https://github.com/hellofresh/sku-demand-forecast/blob/master/docker/dev/records/csku-inventory-forecast.intermediate.distribution-center) file;
2. Create a new PR project;
3. Once it is merged, run the [update-dc-topic](https://github.com/hellofresh/sku-demand-forecast/actions/workflows/update-dc-topic.yaml) GitHub Actions workflow.
4. Ensure the new DC has been produced in the topic
[akhq@staging](https://inventory-akhq.staging-k8s.hellofresh.io/ui/cluster-sasl/topic/csku-inventory-forecast.intermediate.distribution-center.v4/data?sort=Newest&partition=All)
and [akhq@live](https://inventory-akhq.live-k8s.hellofresh.io/ui/cluster-sasl/topic/csku-inventory-forecast.intermediate.distribution-center.v4/data?sort=Newest&partition=All);
5. You can use [this](https://github.com/hellofresh/sku-demand-forecast/pull/360) PR as reference.
