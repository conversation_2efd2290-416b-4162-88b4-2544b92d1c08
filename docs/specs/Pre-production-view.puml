@startuml
title Pre-Production view 1 day ahead

actor Buyer

participant FE
box Forecast BE
    participant ForecastAPI
    participant CalculationRepository
    database PreProduction
end box

Buyer --> FE: GET /calculation/{dcCode}?consumptionDaysAhead=<numDays>
FE --> ForecastAPI: GET pre-production data
ForecastAPI -> ForecastAPI: now - <numDays> <= 1 day
alt "production date offset > 1 day ahead"
    ForecastAPI --> FE: Calculation HTTP 400
    FE --> Buyer: Error: Not supported yet
else "production date 1 day ahead"
    ForecastAPI -> CalculationRepository: invoke
    CalculationRepository -> PreProduction: fetch data from PreProduction table
    PreProduction --> CalculationRepository: PreProduction
    CalculationRepository --> ForecastAPI: Calculation
    ForecastAPI --> FE: Calculation HTTP 200
    FE --> Buyer: Daily Stock 1 day ahead
end

@enduml
