# 6. Configuration library usage

Date: 2023-01-10

## Status

Accepted

## Context

Usage of configuration library:

Kotlin libs project is an orphan project without ownership from any team, and it's not evolved and maintained.
It doesn't bring so much value and removes agility as we can not control versions and dependencies,would like to
replace kotlin libs configuration with the common configuration library.

Requirement : It should be possible for the new configuration library to load the configurations from environment, properties
from *.properties file based on preferred profile.

## Decision

Each project will use `configuration library` to load the properties. [projects.lib.configuration]

## Consequences

- Each project will be responsible for loading the configurations using `configuration library`.
- We will end having some duplicated code to load the configurations in our projects.
