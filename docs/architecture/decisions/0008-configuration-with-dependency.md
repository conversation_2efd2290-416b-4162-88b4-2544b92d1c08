# 8. Keep Configuration, Dependen<PERSON> and the Library in same module

Date: 2023-03-03

## Status

**Accepted**

## Context

We parse configurations and create dependencies in the `main` function of our services. All these dependencies are created in exactly the same way and use the
same configurations. This causes a lot of code duplication and also blocks us from writing tests for the dependency creation. Also, the same configuration has
to be duplicated, making the whole dependency management more fragile and cumbersome.

_We need to put all the dependency creation and the configuration in a common place to be reusable_

## Proposal

We propose to create separate module for each dependency. Each module will contain following

1. Dependency creation
2. Configuration needed for the dependency creation
3. Library if needed to work with the dependency.

For e.g., We will move all the **Kafka** related configurations, creation of consumer and producers and the libraries
like `CoroutineProcessor`, `intsantTimestamp()`, etc. in separate module - `lib:kafka`.

**Why put all of them together?**

1. We could make use of the library functions and constructs available in the dependency to create the configuration safely.
2. Any service which imports this module will automatically get everything it needs to work with this dependency.
3. Easy to track dependency graph.
4. Standardize the steps needed to create a dependency, for e.g. we need to add healthcheck when we create DB dependency

**More requirements**

1. The lib will provide some meaningful default to the configurations.
2. The service will however should be to overwrite the defaults.
3. The library shall not hide the dependency API. It only helps to create it. For e.g. anyone importing `lib:kafka`, will get access to the `kafka` lib too.
4. The dependency created should have tests.

## Consequences

- Each dependency needed to be moved to a separate module.
- Each project need to use this lib and overwrite the defaults when needed.
