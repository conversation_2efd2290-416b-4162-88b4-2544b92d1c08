# 1. Record architecture decisions

Date: 2022-05-02

## Status

Accepted

## Context

We need to record the architectural decisions made on this project.
To create a common vocabulary, documenting functional and non-functional choices for future reference,
improves new joiner onboarding experience and external oversight.

## Decision

We will use Architecture Decision Records, as [described by <PERSON>](https://cognitect.com/blog/2011/11/15/documenting-architecture-decisions).

## Consequences

See <PERSON>'s article linked above.
See <PERSON>'s [adr-tools](https://github.com/npryce/adr-tools).
