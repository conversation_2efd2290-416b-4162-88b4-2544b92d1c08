# 4. Gradle plugins usage

Date: 2022-12-15

## Status

Accepted

## Context

Following is the list of HF Gradle plugins we use and what they do.

**hellofresh.config**
This plugin

- Parses the `project.properies` file and makes it available for other plugins.
- Reads `java-version` file and sets it.
- Sets `commitId` and `shortCommitId`

**hellofresh.ecr**
This literally registers `com.hellofresh.gradle.aws.EcrLogin` as a task called `ecrLogin`

**hellofresh.detekt**
Customizes `io.gitlab.arturbosch.detekt` plugin

**hellofresh.docker**
Customizes `com.google.cloud.tools.jib` and adds extra options on top of it.

**HelmPlugin**
This plugin provides `generateHelmChart` task for deploying services to Kubernetes using Helm. It also does a bunch of additional things like detecting and
merging configuration files for different environments while generating the charts.
We need to further investigate the usage of this plugin and tasks before it can be removed since this task is crucial to our deployment process.

**VersionManagementPlugin**
This plugin helps to manage versions for plugins and dependencies in centralized way.
See [docs](https://github.com/hellofresh/gradle-plugins/blob/master/src/main/kotlin/com/hellofresh/gradle/VersionManagementPlugin.kt)
for more info.

**hellofresh.kotlin**
Specifies Kotlin aspects for all source sets.

**Pros and Cons of having gradle-plugins**

**Pros**

- Standardization.
- No need to maintain it in a particular project.

**Cons**

- Unnecessarily hiding details of underlying tools.
- No team owns this plugin and whenever there are bugs or features needed it is unclear who is supposed to work on them. Important to note that seems there is
  no active community around this project at the moment of writing it.
- Complicated implementation so far plugins have to be universal and cover many use cases that we might not need.

## Decision

- We will move from gradle-plugins to native usage of each plugin
- We will remove unnecessary gradle dependency from non kotlin modules
- We will build new CI/CD pipelines for each module type (gradle/non-gradle) with basic usage of CD actions, jib, helm chart generation, docker build, etc.

## Consequences

- Our projects will have an explicit and standard configuration easier to understand and evolve
- It will require a significant effort from the team so this should be done in small steps
