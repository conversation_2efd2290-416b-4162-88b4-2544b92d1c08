# 2. Generate DTOs from the OpenApi definition

Date: 2022-07-01

## Status

Accepted

## Context

The team is invested into developing and evolving the API specification for the Forecast API in the form of an [OpenApi file](../../../forecast-api/src/main/resources/openapi.yaml). Both Frontend and Backend team members use this file to implement the specification.<br/>
It is however possible to use this file to actually generate API controller stubs and DTOs from an OpenApi specification. This ensures that the spec and code are kept in sync at all times. The tooling is available [here](<https://github.com/OpenAPITools/openapi-generator/>) and it supports Kotlin.<br/>
Clarification: models are suffixed with `Request` and `Response` and they must be used only to implement API request/response interactions in routes. Using these generated classes in
other layers of the application is disallowed.

## Decision

This proposal is to introduce an OpenApi code generator tool to generate DTOs from the OpenApi file for the backend. These files will _not_ be checked into the source tree and instead the code generator will be called as a prerequisite to the compile step in order to ensure that the DTOs are generated and available for compiling and linking.
This way we ensure the API specification and the code will never diverge.

## Consequences

Changes to [openapi.yaml](../../../forecast-api/src/main/resources/openapi.yaml) spec will immediately be reflected in the code. This is especially important for existing API definitions and should provide an early indication for breaking changes.<br/>
If this proposal is accepted, it opens up the possibility to having Ktor routes generated too (the tooling supports this too). This ensures that not only DTOs but the full API spec is implemented as defined (including paths, content negotiation, response types etc.)<br/>
Lastly, other projects within SIV might also take advantage of this approach given that teams are aligned on using OpenApi for defining APIs.
