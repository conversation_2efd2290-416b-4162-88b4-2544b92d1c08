# 7. Create and export fixtures for the shared models

Date: 2023-10-16

## Status

**Accepted**

## Context

**Current status**

Currently we don't have any standard regarding fixtures. However the most common approach we have is to create factory method and copy paste it everywhere we use
```kotlin
fun createSomeObject(
    param1: Int = 0,
    param2: String = "some default"
) = SomeObject(param1, param2, "Some more defaults")
```
The examples could be seen here

[PickToLightServiceTest.kt](https://github.com/hellofresh/csku-inventory-forecast/blob/master/actual-consumption-service/src/test/kotlin/com/hellofresh/cif/actualconsumption/PickToLightServiceTest.kt)

[SharedCalculationRules.kt](https://github.com/hellofresh/csku-inventory-forecast/blob/master/calculator-rule/src/test/kotlin/com/hellofresh/cif/calculator/SharedCalculationRules.kt)

We sometimes create the object in the test itself.
e.g.
[CalculationInboundRulesTest.kt](https://github.com/hellofresh/csku-inventory-forecast/blob/master/calculator-rule/src/test/kotlin/com/hellofresh/cif/calculator/CalculationInboundRulesTest.kt)

[CalculationExtendedRulesTest.kt](https://github.com/hellofresh/csku-inventory-forecast/blob/master/calculator-rule/src/test/kotlin/com/hellofresh/cif/calculator/CalculationExtendedRulesTest.kt)

There are several problem with these approach
1. Tests are hard to read. It breaks the immersion and the focus from the business logic.
2. Fields which are not necessary are mocked and added, which takes away the focus from the fields needed for the test.
3. Any change in the schema is followed by plenty of changes for the tests all over. e.g. - [PR](https://github.com/hellofresh/csku-inventory-forecast/pull/2588/files)
4. Copy makes the test error-prone.

> _We want to keep the creation of the fixture at onc place for easy change and readability._
>
> _We want to instantiate only the fields needed for the test._

## Proposal
We create the object to be of `data class` and then create fixtures at a common place which is going to be the module which export the `models`. We have a gradle-plugin as `test-fixtures`.
All the libs which uses this `model` could import the fixture as
```kotlin
testFixturesImplementation(libs.demand.models)
```
The fixtures are of two types

**Default**
```kotlin
    SomeObject.companion.default() = SomeObject(
        param1 = 0,
        param2 = "some default value",
    )
```

**Random**
```kotlin

SomeObject.companion.random() = SomeObject(
        param1 = Random.nextInt(1000),
        param2 = UUID.randomUUID().toString(),
    )
```
**Benefits**
1. `random` is helpful when you need to create multiple objects with some unique constraints
2. Any lib using the `models` can also get the fixtures prepared for it.
3. Only set the params needed like `SomeObject.random().copy(paramUnderTest="test")`
4. `companion` is useful to avoid polluting the namespace. Just by typing the class name, you get the functions to return fixtures.
5. The object could be nested and complex, we can handle the complexity to create all the nested objects at 1 place and abstract from the test.

There is an example of the proposal here - [Default.kt](https://github.com/hellofresh/csku-inventory-forecast/blob/master/inventory-models/src/testFixtures/kotlin/Default.kt#L16)

## Consequences
The target object has to be a data class to get full advantage of this design.
