# 10. Ensuring Authentication for Newly Introduced REST APIs

Date: 2023-10-12

## Status

**Accepted**

## Context

Currently, in the forecast-api service we have different APIs exposed, some APIs are authenticated and some APIs are not
authenticated. it is imperative to maintain robust security practices.

One critical aspect is to ensure that any newly introduced REST APIs are appropriately authenticated before they are
exposed to clients or integrated into the system. This helps protect against unauthorized access and potential security
breaches.

## Proposal

To enforce authentication for newly introduced REST APIs, the following step will be taken:

Token-Based Authentication:

The authentication mechanism will be based on token-based authentication, utilizing industry-standard protocols like JWT
(JSON Web Tokens). Each API request must include a valid token in the request headers.

During local testing and development, the authentication mechanisms are bypassed. This allows developers to focus on the
functionality of the application without the added complexity of authentication.

**Why do we require authentication for all APIs ?**

1. Protection from unauthorized access.
2. To maintain data integrity.
3. Protecting Against Denial of Service (DoS) Attacks.
4. Maintaining Trust with Users and Clients.
5. Consistency - Implementing a standardized authentication approach ensures uniformity across different APIs, reducing
   the likelihood of security oversights.

## Consequences

1. Token Size and Payload :
   Larger payloads in JWTs can lead to increased network overhead. Consideration should be given to the size of the token
   and its impact on API performance.

2. Key management :
   Safeguarding the keys used to sign and verify JWT tokens is crucial. Compromised keys can lead to security breaches.
