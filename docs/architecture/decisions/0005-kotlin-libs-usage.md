# 5. Kotlin libs usage

Date: 2022-12-15

## Status

Accepted

## Context

Usages of kotlin-libs:

1. Kafka streams applications -> Already decided to remove
2. Commons module for logging and metrics (dependencies and configuration)
3. Commons configuration properties


Kotlin libs project is an orphan project without ownership from any team, and it's not evolved and maintained.
It doesn't bring so much value and removes agility as we can not control versions and dependencies.

## Decision

Each project will use its own logging and metrics configuration/implementation


## Consequences

- Each project will be responsible for its core basic libraries like logging and metrics
- We will end having some duplicated code and configuration in our projects
