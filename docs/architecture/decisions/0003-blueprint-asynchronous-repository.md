# 3. Blueprint to Implement a Database repository

Date: 2022-10-19

## Status

Accepted

## Context

The JOOQ framework uses [CompletionStage](https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/CompletionStage.html)
to fetch the database data asynchronously. The current repository implementations isolate the upstream user requests
from the downstream database calls using CompletableFuture from the Jooq Thread Pool.

Using [CompletableFuture](https://docs.oracle.com/javase/8/docs/api/java/util/concurrent/CompletableFuture.html) could
lead the code to be very difficult to follow, due to chaining or combining mechanism.

## Decision

Use [kotlin coroutines](https://kotlinlang.org/docs/multiplatform-mobile-concurrency-and-coroutines.html#coroutines) and
[kotlinx-coroutines-jdk8](https://kotlinlang.org/api/kotlinx.coroutines/kotlinx-coroutines-jdk8/) to wrap a Jooq `CompletableFuture`
inside a [coroutine scope](https://kotlinlang.org/api/kotlinx.coroutines/kotlinx-coroutines-core/kotlinx.coroutines/-coroutine-scope/).

Example:
```kotlin
class Repository(
  dataSource: DataSource,
  dbPoolSize: Int = 10
) {
  private val dbThreadPool: ExecutorService = Executors.newFixedThreadPool(dbPoolSize)
  private val jooqConfig = DefaultConfiguration().apply {
    setSQLDialect(POSTGRES)
    setDataSource(dataSource)
    setExecutor(dbThreadPool)
  }

  suspend fun findSku(id: UUID): Sku? {
    return DSL.using(jooqConfig)
      .selectFrom(SKU_SPECIFICATION)
      .where(SKU_SPECIFICATION.ID.eq(id))
      // https://stackoverflow.com/questions/68915965/does-jooq-play-nicely-with-kotlin-coroutines
      .fetchAsync() // Even when the jooq metadata is generated using kotlin generator, it is still a CompletableFuture
      .thenApply(this@Repository::mapToSKU)
      .await() // use kotlinx-coroutines-jdk8 to integrate a coroutine with CompletableFuture
  }

  private fun mapToSKU(records: Result<SkuSpecificationRecord>?): Sku? {
    // map to SKU
  }
}
```

## Consequences

Wrapping a CompletableFuture inside a coroutine scope could lead to more readable code. It looks like a traditional
blocking code, but it is async behind the scenes.

The application can scale better since the coroutine is a lightweight thread. This means that the repository
implementation can handle much more requests than the actual number of carrier threads available in the JOOQ thread pool.

Besides, a coroutine is natively supported in kotlin, and Ktor is built using a coroutine.
So the repository can be seamlessly integrated with Ktor.
