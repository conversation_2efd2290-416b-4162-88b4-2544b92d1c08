# 9. Create the purchase order, demand & inventory domains

Date: 2023-03-17

## Status

**Accepted**

## Context

Currently the live calculations are not stable in calculator-job.
  a. Not able to understand the calculation rules related to staging & storage stocks.
  b. Challenging to add new features related to live calculations.
  c. Potential for issues.

We would like to segregate the building of purchase orders, demand and inventory domains, so that the complexity of the
calculator-job is reduced.

## Proposal

We propose to create separate libraries for building purchase orders, demand and inventory domain.

1. Create the purchase orders service which has the contract to build the purchase orders from the database.
2. Create the purchase orders model library which has the business logic i.e some of the calculation rules would be moved here.
3. Create the demand service which has the contract to build the demand data from the database.
4. Create the demand model library which has the business logic i.e some of the calculation rules would be moved here.
5. Create the inventory service which has the contract to build the inventory data from the database.
6. Create the inventory model library which has business logic i.e some of the calculation rules would be moved here.
7. Any service could reuse the purchase orders, demand and inventory libraries without worrying about applying the logic for past,today,future.
   (i.e separation of concerns)

**Why do we require a domain services ?**

1. Each library encapsulates the business logic to build the data [purchase orders, demand & inventory] which improves maintainability, testability.
2. Improved communication.
3. Separation of concerns.
4. Helps to reuse the domain data in different services.
5. Helps to provide more clarity about the live calculation requirements which enables the team to deliver feature's faster.

## Consequences

- Building of purchase orders, demand and inventory data would be segregated.
- The purchase orders, demand and inventory library would be reused across services.
- Calculator-job would start consuming purchase orders, demand and inventory data using the contract for live calculation.
  [i.e the responsibility of calculator job is only to combine the domain data for calculation]
- For example purchase orders - there would be 2 libraries.
  - purchase order model - contains the purchase order related data classes + po rules.
  - purchase order service - contains the business logic to build the purchase order data [contract, service layer and repository].

