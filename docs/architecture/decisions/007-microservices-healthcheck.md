# 7. Health check for the microservices

Date: 2023-02-24

## Status

**Accepted**

## Context

**Current status**

Currently, our health check always return success as long as the app is up. However, this is faulty as an app could be up and running but still not processing anything.

_We need an healthcheck which indicates if the service is actually healthy._

**What is a healthy app?**

There are various definition of a healthy kafka consumer.
1. <PERSON><PERSON> is able to contact to all the external services.
2. <PERSON><PERSON> is able to connect and then successfully communicate with the external services.
3. <PERSON><PERSON> is able to process records end-to-end successfully. This means fetching the records from input, process and then successfully write to the output services

## Proposal
We shall have following condition as the healthcheck

_App is able to connect and then successfully communicate with the external services._

Just connecting with services doesn't provide guarantees. The consumer for e.g. could fail to fetch the records. Similarly processing the records provide even better health check but that would be much slower and possibly not feasible.

A service in our system connects with various services. Following is the type of services we are connected and the respective healthcheck

### Database
We use <PERSON>kari to connect with the database. _Hikari provide `isRunning` function to expose the health of the connection pool_

### Kafka Consumer
Kafka doesn't provide any function to check the health of the consumer. There is a function to get partition assignment however since authorization check happens at the poll, this is not reliable. _Therefore `poll` is the most reliable check we can have._

### Kafka Producer
_Kafka producer has `partitionsFor(topic)` function_. A Non-exceptional result means that the producer would be able to successfully produce. As it checks for all authorization, connection in this function's execution.

### Http Server
We use Ktor server as for the Http servers. _We should mark it successfully started after we have `intalled` all the modules and the `start()` is successfully_. However, this check is static and not updated if the server has failed.

### Meter Registry
An application should be successfully able to expose metrics. _There is `isClosed()` function in the `MeterRegistry`_.


### Other Microservices
Other microservices in this project should also expose `/health` endpoint. This can be pinged to get the health status.

### Implementation
1. Have an interface which qualified a service to expose a health check
2. Each dependency exposes its healthcheck
3. We may use the same trick as `shutdownNeeded` to add each healthcheck in the in list for a microservice.
4. We combine all these healthcheck to present the final healthcheck

## Consequences

- Rollout will be slower as the health check will actually take a bit of time.
- Every project needs to be changed to adapt to new health check.
