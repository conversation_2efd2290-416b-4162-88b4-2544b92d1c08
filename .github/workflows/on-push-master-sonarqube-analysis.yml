name: Master - Sonarqube analysis
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

on:
  push:
    branches:
      - master

jobs:
  sonarqube-master-analysis:
    runs-on: [ self-hosted, default ]
    timeout-minutes: 15
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout source code
        uses: actions/checkout@v2
        with:
          fetch-depth: 0

      - name: Import Secrets from vault
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults artifactory_username | ARTIFACTORY_USERNAME;
            common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD;
            common/data/defaults SONAR_TOKEN | SONAR_TOKEN;

      - name: Gradle Tests, Jacoco and Sonarqube
        shell: bash
        run: |
          gradle check jacocoAggregatedReport sonar \
            "-Dsonar.login=${{ steps.vault-secrets.outputs.SONAR_TOKEN }}"
        env:
          ARTIFACTORY_USERNAME: ${{ env.ARTIFACTORY_USERNAME }}
          ARTIFACTORY_PASSWORD: ${{ env.ARTIFACTORY_PASSWORD }}
