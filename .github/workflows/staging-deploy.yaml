---
name: staging-deploy
concurrency:
    group: ${{ github.workflow }}-${{ github.head_ref }}
    cancel-in-progress: false

on:
    workflow_dispatch:
        inputs:
            service:
                type: choice
                description: 'Service to deploy in staging'
                required: true
                options:
                    - 'calculator-job'
                    - 'supply-quantity-recommendation-job'
                    - 'demand'
                    - 'inventory-variance-job'
                    - 'forecast-api'
                    - 'output-demand-forecast'
                    - 'sanity-checker'
                    - 'supplier'
                    - 'actual-consumption-service'
                    - 'sku-specification-service'
                    - 'purchase-order-service'
                    - 'goods-received-note-service'
                    - 'distribution-center-api'
                    - 'inventory-snapshot-service'
                    - 'inventory-activity-service'
                    - 'inventory-live-job'
                    - 'file-consumer-service'
                    - 'safety-stock-job'
                    - 'kafka-db-sink'
                    - 'forecast-api-db'
                    - 'benthos-source-to-sink'
                    - 'transfer-order-service'

env:
    BENTHOS_MODULES: kafka-db-sink,forecast-api-db,benthos-source-to-sink

jobs:

    select-module-config:
        name: Select Module Config
        runs-on: [ self-hosted, default ]
        permissions:
            id-token: write
            contents: read
        outputs:
            module: ${{ steps.set-deployment-config.outputs.module }}
            chart: ${{ steps.set-deployment-config.outputs.chart }}
        steps:
            -   name: Checkout source code
                uses: actions/checkout@v3
            -   id: set-deployment-config
                shell: bash
                working-directory: ./.github/config
                run: |
                    set -Exeuo pipefail
                    module=$(jq -r ".config[] | select( .chart == \"${{ github.event.inputs.service }}\").module" modules)
                    chart=$(jq -r ".config[] | select( .chart == \"${{ github.event.inputs.service }}\").chart" modules)
                    echo "module=$module" >> "$GITHUB_OUTPUT"
                    echo "chart=$chart" >> "$GITHUB_OUTPUT"

    deploy:
        needs: [ select-module-config ]
        name: Deploy
        runs-on: [ self-hosted, default ]
        timeout-minutes: 15
        permissions:
            id-token: write
            contents: read
        strategy:
            fail-fast: false

        steps:
            - name: Checkout source
              uses: actions/checkout@v3
            - name: Import vault secrets
              id: vault-secrets
              uses: hellofresh/jetstream-ci-scripts/actions/vault@master
              with:
                  shared-secrets: |
                      common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;
                      common/data/defaults artifactory_username | ARTIFACTORY_USERNAME ;
                      common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD ;
                  secrets: |
                      staging/key-value/data/inventory DB_USERNAME | DB_USERNAME_STAGING ;
                      staging/key-value/data/inventory DB_PASSWORD | DB_PASSWORD_STAGING ;
                      live/key-value/data/inventory DB_USERNAME | DB_USERNAME_LIVE ;
                      live/key-value/data/inventory DB_PASSWORD | DB_PASSWORD_LIVE ;

            - name: Generate release version
              id: chart-version
              uses: hellofresh/jetstream-ci-scripts/actions/cal-ver@master

            - name: Retrieve commit id
              id: version
              shell: bash
              run: echo "new_version=$(git rev-parse --short=12 HEAD)" >> "$GITHUB_OUTPUT"

            - name: containerize
              if: contains(env.BENTHOS_MODULES, needs.select-module-config.outputs.module)
              uses: ./.github/actions/containerize-module
              with:
                  module: ${{ needs.select-module-config.outputs.module }}

            - run: gradle ${{ needs.select-module-config.outputs.module }}:generateHelmChart

            - name: Build container image to registry
              run: gradle ${{ needs.select-module-config.outputs.module }}:jib
              if: ${{ !contains(env.BENTHOS_MODULES, needs.select-module-config.outputs.module) }}
              shell: bash
              env:
                  VERSION: ${{ steps.version.outputs.new_version }}

            - name: Build and push chart
              uses: hellofresh/jetstream-ci-scripts/actions/helm3-build-and-push@master
              with:
                  template-name: "default"
              env:
                  ARTIFACTORY_USERNAME: ${{ env.ARTIFACTORY_USERNAME }}
                  ARTIFACTORY_PASSWORD: ${{ env.ARTIFACTORY_PASSWORD }}
                  CHART_NAME: cif-${{ needs.select-module-config.outputs.chart }}
                  CHART_PATH: 'build/helm/chart/cif-${{ needs.select-module-config.outputs.chart }}'
                  GITHUB_TOKEN: ${{ env.GITHUB_TOKEN }}
                  VERSION: ${{ steps.chart-version.outputs.new_version }}

            - name: Staging - Setup k8s
              uses: hellofresh/jetstream-ci-scripts/actions/build-kubeconfig@master
              with:
                  environment: "staging"

            - name: Deploy Staging
              timeout-minutes: 5
              uses: hellofresh/jetstream-ci-scripts/actions/helm3-deploy@master
              with:
                  release-name: cif-${{ needs.select-module-config.outputs.chart }}
                  chart-name: cif-${{ needs.select-module-config.outputs.chart }}
                  version: ${{ steps.chart-version.outputs.new_version }}
                  values-path: 'build/helm/chart/cif-${{ needs.select-module-config.outputs.chart }}/values-staging.yaml'
                  namespace: 'scm'
                  set-string: tag=${{ needs.select-module-config.outputs.chart }}-${{ steps.version.outputs.new_version }}

