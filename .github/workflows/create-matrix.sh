#!/usr/bin/env bash

# Create matrix json by appending changed benthos modules to all the other
# modules, the script takes 2 arguments -
# 1 - Json array of the benthos modules changed
# 2 - Json file containing the module config

set -o errexit -o posix -o nounset -o pipefail -o errtrace

modulesChanged=$(echo "$1" | tr -d "[" | tr -d "]" | tr "," " ")
otherModules="$(echo "$(<"$2")" | jq | tr -d '\n\r' | tr -d "[:space:]")"

# remove suffix to append more modules
suffix=']}'
modules=${otherModules%"$suffix"}

declare -a array
exists=false
for module in $modulesChanged; do
    exists=true

    # We need rollout and staging deployments for all benthos modules
    array+=("
           {
                  \"module\": \"$module\",
                  \"chart\": \"$module\",
                  \"rollout\": true,
                  \"staging\": true,
                  \"live\": true,
                  \"type\": \"deployment\"
        }"
    )
done

# joins array by a multi-character delimiter
function join_by {
    local d=${1-} f=${2-}
    if shift 2; then
        printf %s "$f" "${@/#/$d}"
    fi
}

if [[ $exists == true ]]; then
    modules="$modules,$(join_by , "${array[@]}")"
fi

# add the suffix back
modules="$modules$suffix"
echo "$modules" | jq | tr -d '\n\r' | tr -d "[:space:]"
