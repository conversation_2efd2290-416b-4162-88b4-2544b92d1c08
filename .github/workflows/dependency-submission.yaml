name: Dependency Submission

on:
  push:
    branches: [ 'master' ]
  pull_request:
    branches: [ 'master' ]

permissions:
  contents: write

jobs:
  dependency-submission:
    runs-on: [ self-hosted, default ]
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-java@v4
      with:
        distribution: temurin
        java-version: 21

    - name: Generate and submit dependency graph
      uses: gradle/actions/dependency-submission@v4
