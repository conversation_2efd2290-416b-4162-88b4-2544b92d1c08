---
name: terraform-apply
concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: false

on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: 'Environment to run terraform apply against'
        required: true
        default: 'staging'
        options:
          - staging
          - live

jobs:
  terraform:
    name: Terraform Apply for ${{ github.event.inputs.environment }}
    runs-on: [ self-hosted, default ]
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Configure aws credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
            aws-region: eu-west-1
            role-to-assume: "arn:aws:iam::489198589229:role/github-actions-supply-automation"
      - name: Checkout source code
        uses: actions/checkout@v3
      - name: Import Vault Secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          export-token: true
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;
      - name: Checkout jetstream-ci-scripts
        uses: actions/checkout@v3
        with:
          repository: hellofresh/jetstream-ci-scripts
          path: jetstream-ci-scripts
          token: ${{ env.GITHUB_TOKEN }}
      - name: Terraform Apply for ${{ github.event.inputs.environment }}
        uses: hellofresh/jetstream-ci-scripts/actions/terraform@master
        with:
          version: "1.5.7"
          action: "apply -auto-approve"
          vault_address: ${{ steps.vault-secrets.outputs.vault-address }}
          vault_token: ${{ env.VAULT_TOKEN }}
          github_token: ${{ env.GITHUB_TOKEN }}
          aws_role: ""
          directory: ${{ github.workspace }}/terraform/${{ github.event.inputs.environment }}
