---
name: cd
concurrency:
    group: ${{ github.workflow }}-${{ github.head_ref }}
    cancel-in-progress: true

on:
    push:
        branches: [ 'master' ]

env:
    BENTHOS_MODULES: kafka-db-sink,forecast-api-db,benthos-source-to-sink

jobs:
    migrate-db:
        name: Migrate the database
        runs-on: [ self-hosted, default ]
        permissions:
            id-token: write
            contents: read
        strategy:
            fail-fast: false
            matrix:
                tier: [ 'staging', 'live']
        steps:
            - name: Checkout actions
              uses: actions/checkout@v3
            - name: Import vault secrets
              id: vault-secrets
              uses: hellofresh/jetstream-ci-scripts/actions/vault@master
              with:
                  shared-secrets: |
                      common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;
                      common/data/defaults artifactory_username | ARTIFACTORY_USERNAME ;
                      common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD ;
                  secrets: |
                      ${{ matrix.tier }}/key-value/data/inventory DB_MASTER_HOST | DB_MASTER_HOST ;
                      ${{ matrix.tier }}/key-value/data/inventory DB_USERNAME | DB_USERNAME ;
                      ${{ matrix.tier }}/key-value/data/inventory DB_PASSWORD | DB_PASSWORD;
                      ${{ matrix.tier }}/key-value/data/inventory READONLY_DB_PASSWORD | READONLY_DB_PASSWORD;
            - name: DB migrate on ${{ matrix.tier }}
              uses: ./.github/actions/flyway/
              with:
                  db_host: ${{ env.DB_MASTER_HOST }}
                  db_username: ${{ env.DB_USERNAME }}
                  db_password: ${{ env.DB_PASSWORD }}
                  db_readonly_password: ${{ env.READONLY_DB_PASSWORD }}

    root-helm-charts:
        name: Generate and Deploy the root helm charts
        runs-on: [ self-hosted, default ]
        timeout-minutes: 5
        permissions:
            id-token: write
            contents: read
        strategy:
            fail-fast: false
        steps:
            - name: Checkout source
              uses: actions/checkout@v3
            - name: Import vault secrets
              id: vault-secrets
              uses: hellofresh/jetstream-ci-scripts/actions/vault@master
              with:
                  shared-secrets: |
                      common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;
                      common/data/defaults artifactory_username | ARTIFACTORY_USERNAME ;
                      common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD ;

            - name: Generate release version
              id: chart-version
              uses: hellofresh/jetstream-ci-scripts/actions/cal-ver@master

            - name: Retrieve commit id
              id: version
              shell: bash
              run: echo "new_version=$(git rev-parse --short=12 HEAD)" >> "$GITHUB_OUTPUT"

            - run: gradle :generateHelmChart

            - name: Build and push chart
              uses: hellofresh/jetstream-ci-scripts/actions/helm3-build-and-push@master
              with:
                  template-name: "default"
              env:
                  ARTIFACTORY_USERNAME: ${{ env.ARTIFACTORY_USERNAME }}
                  ARTIFACTORY_PASSWORD: ${{ env.ARTIFACTORY_PASSWORD }}
                  CHART_NAME: cif-csku-inventory-forecast
                  CHART_PATH: 'build/helm/chart/cif-csku-inventory-forecast'
                  GITHUB_TOKEN: ${{ env.GITHUB_TOKEN }}
                  VERSION: ${{ steps.chart-version.outputs.new_version }}

            - name: Staging - Setup k8s
              uses: hellofresh/jetstream-ci-scripts/actions/build-kubeconfig@master
              with:
                  environment: "staging"

            - name: Deploy Staging
              timeout-minutes: 5
              uses: hellofresh/jetstream-ci-scripts/actions/helm3-deploy@master
              with:
                  release-name: cif-csku-inventory-forecast
                  chart-name: cif-csku-inventory-forecast
                  version: ${{ steps.chart-version.outputs.new_version }}
                  values-path: 'build/helm/chart/cif-csku-inventory-forecast/values-staging.yaml'
                  namespace: 'scm'
                  set-string: tag=cif-csku-inventory-forecast-${{ steps.version.outputs.new_version }}

            - name: Live - Setup k8s
              uses: hellofresh/jetstream-ci-scripts/actions/build-kubeconfig@master
              with:
                  environment: "live"

            - name: Deploy Live
              timeout-minutes: 5
              uses: hellofresh/jetstream-ci-scripts/actions/helm3-deploy@master
              with:
                  release-name: cif-csku-inventory-forecast
                  chart-name: cif-csku-inventory-forecast
                  version: ${{ steps.chart-version.outputs.new_version }}
                  values-path: 'build/helm/chart/cif-csku-inventory-forecast/values-live.yaml'
                  namespace: 'scm'
                  set-string: tag=cif-csku-inventory-forecast-${{ steps.version.outputs.new_version }}

            - if: failure()
              name: slack notification
              uses: ./.github/actions/slack
              with:
                  title: 'Failed to deploy root helm charts :x:'
                  message: 'Job: Deploy root helm charts'
            - if: success()
              name: slack notification
              uses: ./.github/actions/slack
              with:
                  title: 'Deploy root helm charts successful! :heavy_check_mark:'
                  message: 'Job: Deploy root helm charts'

    set-deployment-matrix:
        name: Set deployment matrix
        runs-on: [ self-hosted, default ]
        permissions:
            id-token: write
            contents: read
        outputs:
            matrix: ${{ steps.set-deployment-modules.outputs.modules }}
        steps:
            - name: Checkout source code
              uses: actions/checkout@v3
            - id: set-deployment-modules
              shell: bash
              working-directory: ./.github/config
              run: |
                set -Exeuo pipefail
                content=$(tr '\n\r' ' ' < modules)
                echo "$content" | jq .
                echo "modules=$content" >> "$GITHUB_OUTPUT"

    deploy:
        needs: [ set-deployment-matrix, migrate-db ]
        name: Deploy
        runs-on: [ self-hosted, default ]
        timeout-minutes: 15
        permissions:
            id-token: write
            contents: read
        strategy:
            fail-fast: false
            matrix: ${{fromJson(needs.set-deployment-matrix.outputs.matrix)}}

        steps:
            - name: Checkout source
              uses: actions/checkout@v3
            - name: Import vault secrets
              id: vault-secrets
              uses: hellofresh/jetstream-ci-scripts/actions/vault@master
              with:
                  shared-secrets: |
                      common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;
                      common/data/defaults artifactory_username | ARTIFACTORY_USERNAME ;
                      common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD ;
                  secrets: |
                      staging/key-value/data/inventory DB_USERNAME | DB_USERNAME_STAGING ;
                      staging/key-value/data/inventory DB_PASSWORD | DB_PASSWORD_STAGING ;
                      live/key-value/data/inventory DB_USERNAME | DB_USERNAME_LIVE ;
                      live/key-value/data/inventory DB_PASSWORD | DB_PASSWORD_LIVE ;

            - name: Generate release version
              id: chart-version
              uses: hellofresh/jetstream-ci-scripts/actions/cal-ver@master

            - name: Retrieve commit id
              id: version
              shell: bash
              run: echo "new_version=$(git rev-parse --short=12 HEAD)" >> "$GITHUB_OUTPUT"

            - name: containerize
              if: contains(env.BENTHOS_MODULES, matrix.config.module)
              uses: ./.github/actions/containerize-module
              with:
                  module: ${{ matrix.config.module }}

            - run: gradle ${{ matrix.config.module }}:generateHelmChart

            - name: Build container image to registry
              run: gradle ${{ matrix.config.module }}:jib
              if: ${{ !contains(env.BENTHOS_MODULES, matrix.config.module) }}
              shell: bash
              env:
                  VERSION: ${{ steps.version.outputs.new_version }}

            - name: Build and push chart
              uses: hellofresh/jetstream-ci-scripts/actions/helm3-build-and-push@master
              with:
                  template-name: "default"
              env:
                  ARTIFACTORY_USERNAME: ${{ env.ARTIFACTORY_USERNAME }}
                  ARTIFACTORY_PASSWORD: ${{ env.ARTIFACTORY_PASSWORD }}
                  CHART_NAME: cif-${{ matrix.config.chart }}
                  CHART_PATH: 'build/helm/chart/cif-${{ matrix.config.chart }}'
                  GITHUB_TOKEN: ${{ env.GITHUB_TOKEN }}
                  VERSION: ${{ steps.chart-version.outputs.new_version }}

            - name: Staging - Setup k8s
              uses: hellofresh/jetstream-ci-scripts/actions/build-kubeconfig@master
              with:
                  environment: "staging"

            - name: Deploy Staging
              if: ${{ matrix.config.staging == true }}
              timeout-minutes: 5
              uses: hellofresh/jetstream-ci-scripts/actions/helm3-deploy@master
              with:
                  release-name: cif-${{ matrix.config.chart }}
                  chart-name: cif-${{ matrix.config.chart }}
                  version: ${{ steps.chart-version.outputs.new_version }}
                  values-path: 'build/helm/chart/cif-${{ matrix.config.chart }}/values-staging.yaml'
                  namespace: 'scm'
                  set-string: tag=${{ matrix.config.chart }}-${{ steps.version.outputs.new_version }}

            - name: Ensure Staging Rollout
              if: ${{ (matrix.config.rollout == true) && (matrix.config.staging == true) }}
              uses: hellofresh/jetstream-ci-scripts/actions/ensure-k8s-rollout@master
              with:
                  deployment-selector: 'release=cif-${{ matrix.config.chart }}'
                  deployment-namespace: 'scm'
                  retry-count: 50
              env:
                  WORKLOAD_TYPE: ${{ matrix.config.type }}

            - name: Live - Setup k8s
              uses: hellofresh/jetstream-ci-scripts/actions/build-kubeconfig@master
              with:
                  environment: "live"

            - name: Deploy Live
              if: ${{ matrix.config.live == true }}
              timeout-minutes: 5
              uses: hellofresh/jetstream-ci-scripts/actions/helm3-deploy@master
              with:
                  release-name: cif-${{ matrix.config.chart }}
                  chart-name: cif-${{ matrix.config.chart }}
                  version: ${{ steps.chart-version.outputs.new_version }}
                  values-path: 'build/helm/chart/cif-${{ matrix.config.chart }}/values-live.yaml'
                  namespace: 'scm'
                  set-string: tag=${{ matrix.config.chart }}-${{ steps.version.outputs.new_version }}

            - name: Ensure Live Rollout
              if: ${{ (matrix.config.rollout == true) && (matrix.config.live == true) }}
              uses: hellofresh/jetstream-ci-scripts/actions/ensure-k8s-rollout@master
              with:
                  deployment-selector: 'release=cif-${{ matrix.config.chart }}'
                  deployment-namespace: 'scm'
              env:
                  WORKLOAD_TYPE: ${{ matrix.config.type }}
            - if: failure()
              name: slack notification
              uses: ./.github/actions/slack
              with:
                  title: 'Failed to rollout ${{ matrix.config.chart }} to live! :x:'
                  message: 'Job: Deploy'

            - if: success()
              name: slack notification
              uses: ./.github/actions/slack
              with:
                  title: 'Rollout ${{ matrix.config.chart }} to live successful! :heavy_check_mark:'
                  message: 'Job: Deploy'
