# PR Details

## Ticket
Please replace with TICKET_ID <https://hellofresh.atlassian.net/browse/[TICKET_ID]>

## Description
<!--
A clear description giving context to the reviewer
about the task done and the approach taken to solve
the problem.
-->

### Alerts and metrics changed (If any)
<!-- list of alerts and metrics changed --->

### Tech debt tickets (If any)
<!-- links to tech debt tickets if any tech debt was introduced -->


## Developer checklist
<!-- A self-ckecking checklist -->
* [ ] Did a self code review.
* [ ] All edge cases are covered and tested.
* [ ] Code is optimised for performance under high volume of data.
* [ ] No security vulnerabilities are introduced.
* [ ] Relevant alerts and metrics are added/updated.
* [ ] Code does not introduce any tech debt. (If it does, link the tickets created)
