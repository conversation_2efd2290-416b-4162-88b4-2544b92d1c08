version: 2
updates:
  - package-ecosystem: "gradle"
    directory: "actual-consumption-service/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "benthos-source-to-sink/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "buildSrc/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "calculator-job/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "calculator-models/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "calculator-rule/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "date-util-models/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "demand-lib/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "demand-models/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "demand/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "distribution-center-api/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "distribution-center-models/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]

  - package-ecosystem: "gradle"
    directory: "forecast-api-db/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "forecast-api/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "goods-received-note-service/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "inventory-db/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "inventory-models/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "kafka-db-sink/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "lib-hf-service/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "lib-tests/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "lib/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "lib/configuration/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "lib/db/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "lib/featureflags/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "lib/logging/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "lib/models/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "mvp/end-to-end-test/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "mvp/functional-tests/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "mvp/kafka-mirror-plugin/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "mvp/sanity-checker/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "mvp/scripts/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "output-demand-forecast/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "purchase-order/purchase-order-lib/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "purchase-order/purchase-order-models/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "purchase-order/purchase-order-service/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "sku-inputs-lib/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "sku-models/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "sku-specification-service/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "supplier/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
  - package-ecosystem: "gradle"
    directory: "topic-config/"
    rebase-strategy: "disabled"
    schedule:
      interval: "monthly"
    ignore:
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]

