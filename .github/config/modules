{"config": [{"module": ":calculator-job", "chart": "calculator-job", "rollout": false, "staging": true, "live": true, "type": "statefulset"}, {"module": ":supply-quantity-recommendation-job", "chart": "supply-quantity-recommendation-job", "rollout": false, "staging": true, "live": true, "type": "statefulset"}, {"module": ":demand", "chart": "demand", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": ":inventory-variance-job", "chart": "inventory-variance-job", "rollout": false, "staging": true, "live": true, "type": "deployment"}, {"module": ":forecast-api", "chart": "forecast-api", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": ":output-demand-forecast", "chart": "output-demand-forecast", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": ":mvp:sanity-checker", "chart": "sanity-checker", "rollout": false, "staging": false, "live": true, "type": "deployment"}, {"module": ":supplier", "chart": "supplier", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": ":actual-consumption-service", "chart": "actual-consumption-service", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": ":sku-specification-service", "chart": "sku-specification-service", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": ":purchase-order:purchase-order-service", "chart": "purchase-order-service", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": ":goods-received-note-service", "chart": "goods-received-note-service", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": ":distribution-center-api", "chart": "distribution-center-api", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": ":inventory:inventory-snapshot-service", "chart": "inventory-snapshot-service", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": ":inventory:inventory-activity-service", "chart": "inventory-activity-service", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": ":inventory:inventory-live-job", "chart": "inventory-live-job", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": ":file-consumer-service", "chart": "file-consumer-service", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": ":safety-stock-job", "chart": "safety-stock-job", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": ":transfer-order:transfer-order-service", "chart": "transfer-order-service", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": "kafka-db-sink", "chart": "kafka-db-sink", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": "forecast-api-db", "chart": "forecast-api-db", "rollout": true, "staging": true, "live": true, "type": "deployment"}, {"module": "benthos-source-to-sink", "chart": "benthos-source-to-sink", "rollout": true, "staging": true, "live": true, "type": "deployment"}]}