---
name: Post Slack Message
description: Post a message in the #proj-csku-inventory-forecast-releases
inputs:
  title:
    description: message title
    required: true
  message:
    description: message body
    required: true

runs:
  using: "composite"
  steps:
    - name: Import vault secrets
      id: vault-secrets
      uses: hellofresh/jetstream-ci-scripts/actions/vault@master
      with:
        secrets: |
          common/key-value/data/misc SLACK_URL | SLACK_URL ;

    - name: slack notification
      uses: hellofresh/jetstream-ci-scripts/actions/slack-notification@master
      with:
        slack-url: ${{ env.SLACK_URL }}
        icon-emoji: ':octocat:'
        pretext: ${{ inputs.title }}
        text: ${{ inputs.message }}
        channel: '#proj-csku-inventory-forecast-releases'
        username: 'Github Actions: CD'
