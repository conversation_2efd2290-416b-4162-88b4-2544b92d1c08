---
name: 'Flyway migration'
description: 'Runs flyway DB migration'
inputs:
  db_host:
    description: 'DB Host'
    required: true
  db_username:
    description: 'DB Username'
    required: true
  db_password:
    description: 'DB Password'
    required: true
  db_readonly_password:
    description: 'DB ReadOnly Password'
    required: true

runs:
  using: "composite"
  steps:
    - name: Flyway Info
      shell: bash
      run: >-
        ./.github/actions/flyway/flyway.sh ${{ github.workspace }}
        ${{ inputs.db_host }}
        ${{ inputs.db_username }} ${{ inputs.db_password }} ${{ inputs.db_readonly_password }}
        info
    - name: Flyway Migration
      shell: bash
      run: >-
        ./.github/actions/flyway/flyway.sh ${{ github.workspace }}
        ${{ inputs.db_host }}
        ${{ inputs.db_username }} ${{ inputs.db_password }} ${{ inputs.db_readonly_password }}
        migrate
