#!/usr/bin/env bash
set -Eeuox pipefail

basePath=$1
dbHost=$2
dbUsername=$3
dbPassword=$4
dbReadOnlyPassword=$5
action=$6

docker run --rm \
    --volume "$basePath"/inventory-db/src/main/resources/migrations:/flyway/sql/migration:ro \
    flyway/flyway:9.8 \
    -url="jdbc:postgresql://${dbHost}:5432/inventory" \
    -user="$dbUsername" \
    -password="$dbPassword" \
    -placeholders.cifReadonlyPassword="'${dbReadOnlyPassword}'" \
    "$action"
