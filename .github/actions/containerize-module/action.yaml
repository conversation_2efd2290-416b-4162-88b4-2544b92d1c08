name: 'Containerize a project module'
description: 'Build & Push project module image to ECR'
inputs:
  project:
    description: the gradle module name
    required: false
    default: csku-inventory-forecast
  registryUrl:
    description: the docker registry url
    required: false
    default: 489198589229.dkr.ecr.eu-west-1.amazonaws.com
  module:
    description: the gradle module name
    required: true

runs:
  using: "composite"
  steps:
    - name: Generate Version
      id: version
      shell: bash
      run: echo "new_version=$(git rev-parse --short=12 HEAD)" >> "$GITHUB_OUTPUT"
    - name: Build & Push container image
      uses: docker/build-push-action@v2
      with:
        context: ${{inputs.module}}/
        push: true
        tags: |
          ${{inputs.registryUrl}}/${{inputs.project}}:${{inputs.module}}-${{ steps.version.outputs.new_version }}
