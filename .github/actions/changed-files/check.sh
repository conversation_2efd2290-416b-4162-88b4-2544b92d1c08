#!/usr/bin/env bash
set -o errexit -o posix -o nounset -o pipefail -o errtrace

modules=$(echo "$1" | tr "," " ")
shift
declare -A modulesChanged
for file; do
    for module in $modules; do
        if [[ $file == $module* ]]; then
            modulesChanged["$module"]=1
        fi
    done
done

function join_by {
    local d=${1-} f=${2-}
    if shift 2; then
        printf %s "$f" "${@/#/$d}"
    fi
}

if [[ ! -v modulesChanged[@] ]] || [ ${#modulesChanged[@]} -eq 0 ]; then
    echo "[]"
else
    bar=$(join_by \",\" "${!modulesChanged[@]}")
    echo "[\"$bar\"]"
fi
