---
name: Changed Files
description: Check which modules have their files changed

inputs:
  modules:
    description: comma separated name of the modules
    required: true

outputs:
  modules:
    description: the output module names which have been changed.
    value: ${{ steps.get-modules.outputs.modules }}

runs:
  using: "composite"
  steps:
    - name: Changed Files
      id: changed-files
      uses: tj-actions/changed-files@v35.4.4
    - name: get-modules
      id: get-modules
      shell: bash
      run: echo "modules=$(${{ github.action_path }}/check.sh ${{inputs.modules}} ${{ steps.changed-files.outputs.all_changed_and_modified_files }})" >> $GITHUB_OUTPUT
