# suppress inspection "UnusedProperty" for whole file
#
# This file is sourced by `./ci/set-pipeline.sh` and exported to each project in
# `./settings.gradle.kts`.
#
# !!!! WARNING !!!!
#
# The property names must be lowerCamelCase so that we can source it from Bash
# and use type-safe delegation in Kotlin to retrieve the values, e.g.:
#
#     val dockerRegistry: String by extra
#
# !!!! WARNING !!!!
tld=com
organization=hellofresh
tribe=planning-and-purchasing
squad=supply-automation
projectDisplayName="CSKU Inventory Forecast"
projectName=csku-inventory-forecast
projectKey=cif
tiers=staging,live

awsRegion=eu-west-1
dockerRegistry=489198589229.dkr.ecr.eu-west-1.amazonaws.com
dockerHubRegistry=docker.io
helmChartDir=build/helm/chart
