package com.hellofresh.cif.demand

import InfraPreparation.getMigratedDataSource
import com.google.type.Date
import com.google.type.Decimal
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.demand.schema.Tables.DEMAND
import com.hellofresh.cif.demand.schema.Tables.US_DEMAND
import com.hellofresh.cif.demand.schema.tables.records.UsDemandRecord
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastKey
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastKey.WeekDate
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastVal
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastVal.UOM.UOM_UNIT
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.Instant
import java.time.LocalDate
import java.util.Optional
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.common.TopicPartition
import org.apache.kafka.common.header.internals.RecordHeader
import org.apache.kafka.common.header.internals.RecordHeaders
import org.apache.kafka.common.record.TimestampType.CREATE_TIME
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class PerformUsUpsertTest {
    private lateinit var dsl: MetricsDSLContext
    private lateinit var update: PerformUsDemandUpsert

    @BeforeEach
    fun setUp() {
        dsl = DSL.using(
            DefaultConfiguration().apply {
                setSQLDialect(POSTGRES)
                setDataSource(dataSource)
                setExecutor(Executors.newSingleThreadExecutor())
            },
        ).withMetrics(SimpleMeterRegistry())
        update = PerformUsDemandUpsert(dsl)
    }

    @AfterEach
    fun cleanup() {
        dsl.deleteFrom(US_DEMAND).execute()
        dsl.deleteFrom(DEMAND).execute()
    }

    @Test
    fun `insert new rows`() {
        val crList = (0..2).map { i -> generateUsTestData(i) }

        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to crList))
        runBlocking { update(consumerRecords) }
        val records = dsl.fetch(US_DEMAND).sortedBy { it.dcCode }

        assertEquals(3, records.size)
        records.forEachIndexed { i, r ->
            assertEquals(crList[i].key(), crList[i].value(), r)
        }
    }

    @Test
    fun `updates records`() {
        val input = (0..3).map { i -> generateUsTestData(i) }

        val consumerRecordsBegin = ConsumerRecords(mapOf(TopicPartition("test", 0) to input))
        runBlocking { update(consumerRecordsBegin) }

        val newInput =
            mutableListOf<ConsumerRecord<WeekSkuInventoryDemandForecastKey, WeekSkuInventoryDemandForecastVal?>>()

        newInput.add(
            newConsumerRecord(
                input[0].key(),
                generateUsTestData(0).value(),
            ),
        )

        newInput.add(
            newConsumerRecord(
                input[1].key(),
                generateUsTestData(1).value(),
            ),
        )

        newInput.add(
            newConsumerRecord(
                input[2].key(),
                generateUsTestData(2, demandQty = 300).value(),
            ),
        )

        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to newInput))
        runBlocking { update(consumerRecords) }
        val records = dsl.fetch(US_DEMAND).sortedBy { it.dcCode }

        assertEquals(4, records.size)
        assertEquals(newInput[0].key(), newInput[0].value(), records[0])
        assertEquals(newInput[1].key(), newInput[1].value(), records[1])
        assertEquals(newInput[2].key(), newInput[2].value(), records[2])

        // old timestamped record was not updated
        assertEquals(input[3].key(), input[3].value(), records[3])
    }

    @Test
    fun `null values are ignored`() {
        val rec = generateUsTestData(1)
        // insert null
        val consumerRecNullValue = newConsumerRecord(rec.key(), null)
        val consumerRecordsNullVal = ConsumerRecords(mapOf(TopicPartition("test", 0) to listOf(consumerRecNullValue)))
        runBlocking { update(consumerRecordsNullVal) }
        val records = dsl.fetch(US_DEMAND)
        assertTrue { records.isEmpty() }
    }

    @Test fun `zero values are inserted`() {
        val rec = generateUsTestData(1)
        // insert null
        val consumerRecNullValue = newConsumerRecord(rec.key(), WeekSkuInventoryDemandForecastVal.newBuilder().build())
        val consumerRecordsNullVal = ConsumerRecords(mapOf(TopicPartition("test", 0) to listOf(consumerRecNullValue)))
        runBlocking { update(consumerRecordsNullVal) }
        val records = dsl.fetch(US_DEMAND)
        assertEquals(1, records.size)
        assertEquals(SkuQuantity.fromLong(0).getValue(), records[0].quantity)
    }

    @Test
    fun `'should create demand for each unique key`() {
        val skuId = UUID.randomUUID().toString()
        val date: Date = Date.newBuilder().apply {
            year = 2024
            month = 9
            day = 5
        }.build()
        val date2: Date = Date.newBuilder().apply {
            year = 2024
            month = 9
            day = 6
        }.build()
        val productionWeek: WeekDate = WeekDate.newBuilder().apply {
            year = 2024
            week = 37
        }.build()
        val productionWeek2: WeekDate = WeekDate.newBuilder().apply {
            year = 2024
            week = 38
        }.build()

        val rec1 = generateUsTestDataForUpsert(1, skuId, "BV", date, productionWeek, demandQty = 10)
        val rec2 = generateUsTestDataForUpsert(1, skuId, "BV", date, productionWeek2, demandQty = 20)

        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to listOf(rec1, rec2)))
        runBlocking { update(consumerRecords) }

        val rec3 = generateUsTestDataForUpsert(1, skuId, "BV", date2, productionWeek, demandQty = 5)
        val consumerRecords2 = ConsumerRecords(mapOf(TopicPartition("test", 0) to listOf(rec3)))

        runBlocking { update(consumerRecords2) }

        val us_demand_records = dsl.fetch(US_DEMAND).sortedBy { it.dcCode }

        assertEquals(3, us_demand_records.size)
        assertEquals(10.0, us_demand_records[0].quantity.toDouble()) // Updated quantity for 2024-09-05
        assertEquals(20.0, us_demand_records[1].quantity.toDouble()) // Updated quantity for 2024-09-05
        assertEquals(5.0, us_demand_records[2].quantity.toDouble()) // Updated quantity for 2024-09-06

        val demand_records = dsl.fetch(DEMAND).sortedBy { it.dcCode }

        assertEquals(30.0, demand_records[0].quantity.toDouble()) // Updated quantity for 2024-09-05
        assertEquals(5.0, demand_records[1].quantity.toDouble()) // Updated quantity for 2024-09-05
    }

    private fun assertEquals(k: WeekSkuInventoryDemandForecastKey, v: WeekSkuInventoryDemandForecastVal?, record: UsDemandRecord) {
        assertEquals(k.skuId, record.skuId.toString())
        assertEquals(k.distributionCenterBobCode, record.dcCode)
        assertEquals(k.date.toLocalDate(), record.date)
        assertEquals(k.productionWeek.year, record.week.split("-W")[0].toInt())
        assertEquals(k.productionWeek.week, record.week.split("-W")[1].toInt())
        assertEquals(v?.unit?.toString(), record.uom.toString())

        assertEquals(v?.forecastedDemandedQty?.toDoubleValue() ?: 0, record.quantity.toDouble())
    }

    private fun Date.toLocalDate(): LocalDate = LocalDate.of(this.year, this.month, this.day)

    fun newConsumerRecord(key: WeekSkuInventoryDemandForecastKey, value: WeekSkuInventoryDemandForecastVal?) =
        ConsumerRecord(
            "test", 0, 0,
            Instant.now().toEpochMilli(),
            CREATE_TIME, 1, 1, key, value,
            RecordHeaders(), Optional.empty(),
        )

    companion object {
        private val dataSource = getMigratedDataSource()
    }
}

private fun generateUsTestData(
    i: Int,
    demandQty: Int = 10,
): ConsumerRecord<WeekSkuInventoryDemandForecastKey, WeekSkuInventoryDemandForecastVal?> {
    val key = WeekSkuInventoryDemandForecastKey.newBuilder().apply {
        skuId = UUID.randomUUID().toString()
        distributionCenterBobCode = i.toString()
        date = LocalDate.now().let {
            com.google.type.Date.newBuilder().apply {
                year = it.year
                month = it.monthValue
                day = it.dayOfMonth
            }.build()
        }
        productionWeek = LocalDate.now().let {
            WeekDate.newBuilder().apply {
                year = it.year
                week = (it.dayOfYear / 7) + 1
            }.build()
        }
    }.build()

    val value = WeekSkuInventoryDemandForecastVal.newBuilder().apply {
        forecastedDemandedQty = Decimal.newBuilder().setValue(demandQty.toString()).build()
        unit = UOM_UNIT
    }.build()

    return getConsumerRecord(i, key, value)
}

private fun generateUsTestDataForUpsert(
    i: Int,
    testSkuId: String,
    dcCode: String,
    testDate: Date,
    testProductionWeek: WeekDate,
    demandQty: Int = 10,
): ConsumerRecord<WeekSkuInventoryDemandForecastKey, WeekSkuInventoryDemandForecastVal?> {
    val key = WeekSkuInventoryDemandForecastKey.newBuilder().apply {
        skuId = testSkuId
        distributionCenterBobCode = dcCode
        date = testDate
        productionWeek = testProductionWeek
    }.build()

    val value = WeekSkuInventoryDemandForecastVal.newBuilder().apply {
        forecastedDemandedQty = Decimal.newBuilder().setValue(demandQty.toString()).build()
        unit = UOM_UNIT
    }.build()

    return getConsumerRecord(i, key, value)
}

private fun getConsumerRecord(
    i: Int,
    key: WeekSkuInventoryDemandForecastKey,
    value: WeekSkuInventoryDemandForecastVal
): ConsumerRecord<WeekSkuInventoryDemandForecastKey, WeekSkuInventoryDemandForecastVal?> {
    val headers = buildList<RecordHeader?> {}
    return ConsumerRecord(
        "test", 0, i.toLong(),
        Instant.now().toEpochMilli(),
        CREATE_TIME, 1, 1, key, value,
        RecordHeaders(headers),
        Optional.empty(),
    )
}
