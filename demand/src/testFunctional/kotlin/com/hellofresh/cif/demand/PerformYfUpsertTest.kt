package com.hellofresh.cif.demand

import InfraPreparation
import com.google.type.Date
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.demand.schema.Tables
import com.hellofresh.cif.demand.schema.tables.records.DemandRecord
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_FCMS
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.distribution_center_lib.schema.Tables.DC_CONFIG
import com.hellofresh.cif.distribution_center_lib.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.skuSpecificationLib.repo.SkuSpecificationRepositoryImpl
import com.hellofresh.cif.sku_specification_lib.schema.Tables.SKU_SPECIFICATION
import com.hellofresh.cif.sku_specification_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.sku_specification_lib.schema.enums.Uom
import com.hellofresh.cif.sku_specification_lib.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.proto.stream.ye.demand.recipeDemandForecast.v3.RecipeDemandForecastKey
import com.hellofresh.proto.stream.ye.demand.recipeDemandForecast.v3.RecipeDemandForecastVal
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import java.util.concurrent.Executors
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.common.TopicPartition
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class PerformYfUpsertTest {
    private lateinit var dsl: MetricsDSLContext
    private lateinit var skuSpecification: SkuSpecificationService
    private lateinit var dcConfigService: DcConfigService
    private lateinit var update: YfPerformUpsert

    @BeforeEach
    fun setUp() {
        dsl = DSL.using(
            DefaultConfiguration().apply {
                setSQLDialect(SQLDialect.POSTGRES)
                setDataSource(dataSource)
                setExecutor(Executors.newSingleThreadExecutor())
            },
        ).withMetrics(SimpleMeterRegistry())
        skuSpecification = SkuSpecificationService(SimpleMeterRegistry(), SkuSpecificationRepositoryImpl(dsl))
        dcConfigService = DcConfigService(SimpleMeterRegistry(), DcRepositoryImpl(dsl))
        update = YfPerformUpsert(dsl, skuSpecification, dcConfigService)
    }

    @AfterEach
    fun cleanup() {
        dsl.deleteFrom(Tables.US_DEMAND).execute()
        dsl.deleteFrom(Tables.DEMAND).execute()
    }

    @Test
    fun `you foodz demands should be persisted successfully with actual consumption`() {
        val crList = (0..1).map { i -> generateYfTestData(i) }
        val skuId = UUID.randomUUID()
        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to crList))
        persistDcConfig()
        persistSkuSpecification(skuId)
        runBlocking { update(consumerRecords) }
        val demandRecords = dsl.fetch(Tables.DEMAND).sortedBy { it.dcCode }
        val actualConsumptionRecords = dsl.fetch(Tables.ACTUAL_CONSUMPTION).sortedBy { it.dcCode }

        Assertions.assertEquals(1, demandRecords.size)
        Assertions.assertEquals(1, actualConsumptionRecords.size)
        Assertions.assertEquals(actualConsumptionRecords[0].quantity.toLong(), 20L)
        Assertions.assertEquals(
            crList[0].value().departmentSkuQuantitiesList?.first()?.actualQuantity?.toDoubleValue() ?: 0,
            actualConsumptionRecords[0].quantity.toDouble(),
        )
        demandRecords.forEachIndexed { i, r ->
            assertEquals(crList[i].key(), crList[i].value(), r, skuId)
        }
    }

    private fun assertEquals(k: RecipeDemandForecastKey, v: RecipeDemandForecastVal?, record: DemandRecord, skuId: UUID) {
        Assertions.assertEquals(skuId, record.skuId)
        Assertions.assertEquals(k.dc, record.dcCode)
        Assertions.assertEquals(k.productionDay.toLocalDate(), record.date)
        Assertions.assertEquals(v?.departmentSkuQuantitiesList?.first()?.unit?.toString(), record.uom.toString())
        Assertions.assertEquals(
            v?.departmentSkuQuantitiesList?.first()?.forecastQuantity?.toDoubleValue() ?: 0,
            record.quantity.toDouble(),
        )
    }

    private fun Date.toLocalDate(): LocalDate = LocalDate.of(this.year, this.month, this.day)

    private fun persistSkuSpecification(skuId: UUID) = SkuSpecificationRecord().apply {
        id = skuId
        name = "Some Sku"
        code = "skuCode"
        category = ""
        acceptableCodeLife = 0
        coolingType = ""
        packaging = ""
        market = "AU"
        uom = Uom.UOM_LBS
    }.also {
        dsl.insertInto(SKU_SPECIFICATION).set(it)
            .onDuplicateKeyUpdate()
            .set(it)
            .execute()
        refreshSkuView()
    }

    private fun persistDcConfig() {
        dsl
            .insertInto(DC_CONFIG).set(
                DcConfigRecord().apply {
                    dcCode = "dcCode"
                    market = "AU"
                    productionStart = "MONDAY"
                    cleardown = "THURSDAY"
                    zoneId = "Australia/Sydney"
                    enabled = true
                    hasCleardown = true
                    scheduledCleardownTime = LocalTime.now()
                    recordTimestamp_ = LocalDateTime.now()
                    wmsType = WMS_SYSTEM_FCMS.toString()
                },
            ).execute()
    }

    private fun refreshSkuView() =
        dsl.query("refresh materialized view concurrently ${SKU_SPECIFICATION_VIEW.name}").execute()

    companion object {
        private val dataSource = InfraPreparation.getMigratedDataSource()
    }
}
