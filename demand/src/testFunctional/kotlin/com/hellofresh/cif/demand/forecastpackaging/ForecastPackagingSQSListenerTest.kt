package com.hellofresh.cif.demand.forecastpackaging

import com.hellofresh.cif.demand.schema.Tables
import com.hellofresh.cif.s3.S3File
import io.mockk.coEvery
import java.io.ByteArrayInputStream
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

private const val BUCKET_NAME = "cif-packaging-import-files"

class ForecastPackagingSQSListenerTest : FunctionalTest() {

    @Test
    fun `should fetch forecast packaging file and verify demand created`() {
        readAndProcessFile("sku_level_demands/es_packaging_forecast/es_forecast.csv")
        val demandData = dsl.selectFrom(Tables.DEMAND).fetch()
        assertEquals(8, demandData.size)
    }

    @Test
    fun `should fetch forecast packaging file and verify that created one demand for the same brand`() {
        readAndProcessFile("sku_level_demands/es_packaging_forecast/es_forecast_one_sku_dif_brand.csv")
        val demandData = dsl.selectFrom(Tables.DEMAND).fetch()

        assertEquals(1, demandData.size)

        val expectedQuantity = "246"
        demandData.first().apply {
            assertEquals(thirdForecastSkuId, this.skuId)
            assertEquals(expectedQuantity, this.quantity.toString())
        }
    }

    @Test
    fun `should fetch forecast packaging file and verify that created one demand with empty brand`() {
        readAndProcessFile("sku_level_demands/es_packaging_forecast/es_forecast_one_sku_missing_brand.csv")
        val demandData = dsl.selectFrom(Tables.DEMAND).fetch()

        assertEquals(1, demandData.size)

        val expectedQuantity = "369"
        demandData.first().apply {
            assertEquals(thirdForecastSkuId, this.skuId)
            assertEquals(expectedQuantity, this.quantity.toString())
        }
    }

    @ParameterizedTest(name = "{index} => {1}")
    @MethodSource("provideInputsForInvalidForecastPackaging")
    fun `should not process forecast packaging for invalid entries`(key: String) {
        val fileContentInByteArray = readFileContent(key)

        coEvery {
            s3Importer.fetchObjectContent(
                BUCKET_NAME,
                key
            )
        } returns ByteArrayInputStream(fileContentInByteArray)

        runBlocking {
            forecastPackagingService.process(
                S3File(
                    BUCKET_NAME,
                    key
                )
            )
        }

        val demandData = dsl.selectFrom(Tables.DEMAND).fetch()

        assertEquals(0, demandData.size)
    }

    private fun readAndProcessFile(key: String) {
        val fileContentInByteArray = readFileContent(key)

        coEvery {
            s3Importer.fetchObjectContent(
                BUCKET_NAME,
                key
            )
        } returns ByteArrayInputStream(fileContentInByteArray)

        runBlocking {
            forecastPackagingService.process(
                S3File(
                    BUCKET_NAME,
                    key
                )
            )
        }
    }

    companion object {
        @Suppress("unused")
        @JvmStatic
        fun provideInputsForInvalidForecastPackaging(): Stream<Arguments> = Stream.of(
            Arguments.of(
                "sku_level_demands/es_packaging_forecast/es_forecast_not_allowed_blank_columns.csv",
                "should not process the forecast packaging file if the file has invalid number of columns",
            ),
            Arguments.of(
                "sku_level_demands/es_packaging_forecast/es_forecast_wrong_headers.csv",
                "should not process the forecast packaging file if the file has wrong headers",
            ),
            Arguments.of(
                "sku_level_demands/es_packaging_forecast/es_forecast_negative_value.csv",
                "should not process the forecast packaging file if the file has negative value",
            ),
            Arguments.of(
                "sku_level_demands/es_packaging_forecast/es_forecast_invalid_number_of_columns.csv",
                "should not process the forecast packaging file if the file has invalid number of columns",
            ),
            Arguments.of(
                "sku_level_demands/es_packaging_forecast/es_forecast_not_a_csv_format.csv",
                "should not process the forecast packaging file if the file is not in CSV format",
            ),
        )
    }
}
