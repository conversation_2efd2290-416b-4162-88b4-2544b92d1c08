package com.hellofresh.cif.demand.ukpackaging

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.sku.models.SkuSpecification
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID

fun getDcConfig(dcCode: String) =
    DistributionCenterConfiguration(
        dcCode = dcCode,
        productionStart = LocalDate.now().dayOfWeek,
        cleardown = LocalDate.now().dayOfWeek,
        market = "UK",
        zoneId = ZoneOffset.UTC,
        enabled = true,
        wmsType = WmsSystem.WMS_SYSTEM_FCMS,
    )

private val firstSkuId = UUID.randomUUID()
private val secondSkuId = UUID.randomUUID()
val thirdSkuId: UUID = UUID.randomUUID()

private val firstSkuSpec = getSkuSpec("PCK-10-121326-1", "HF Large box 2023")
private val secondSkuSpec = getSkuSpec("PCK-10-002482-1", "GC Cool Pouch Label")
private val thirdSkuSpec = getSkuSpec("PCK-10-11207-2", "Large PET Light")

fun getSkuSpec(skuCode: String, skuName: String) = SkuSpecification(
    category = "PCK",
    skuCode = skuCode,
    name = skuName,
    coolingType = "test1",
    packaging = "test2",
    acceptableCodeLife = 0,
    market = "UK",
)

val skus = listOf(
    Pair(firstSkuId, firstSkuSpec),
    Pair(secondSkuId, secondSkuSpec),
    Pair(thirdSkuId, thirdSkuSpec)
)
