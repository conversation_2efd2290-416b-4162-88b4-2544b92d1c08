package com.hellofresh.cif.demand.forecastpackaging

import InfraPreparation
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.demand.repository.DemandRepository
import com.hellofresh.cif.demand.repository.DemandRepositoryImpl
import com.hellofresh.cif.demand.service.ForecastPackagingService
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.sku_inputs_lib.schema.Tables
import com.hellofresh.cif.sku_inputs_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.cif.skuinput.service.SkuInputService
import com.hellofresh.sku.models.SkuSpecification
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.mockk
import java.io.File
import java.nio.file.Files
import java.util.UUID
import java.util.concurrent.Executors
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach

open class FunctionalTest {
    fun readFileContent(fileName: String): ByteArray = with(this::class.java.classLoader) {
        File(getResource(fileName)!!.toURI())
    }.let {
        Files.readAllBytes(it.toPath())
    }

    fun insertSkuSpecification(skuCodes: List<Pair<UUID, SkuSpecification>>) {
        skuCodes.forEach { (id, skuItem) ->
            dsl.insertInto(
                Tables.SKU_SPECIFICATION,
                Tables.SKU_SPECIFICATION.ID,
                Tables.SKU_SPECIFICATION.PARENT_ID,
                Tables.SKU_SPECIFICATION.CATEGORY,
                Tables.SKU_SPECIFICATION.CODE,
                Tables.SKU_SPECIFICATION.NAME,
                Tables.SKU_SPECIFICATION.COOLING_TYPE,
                Tables.SKU_SPECIFICATION.PACKAGING,
                Tables.SKU_SPECIFICATION.ACCEPTABLE_CODE_LIFE,
                Tables.SKU_SPECIFICATION.MARKET,
            )
                .values(
                    id,
                    UUID.randomUUID(),
                    skuItem.category,
                    skuItem.skuCode,
                    skuItem.name,
                    skuItem.coolingType,
                    skuItem.packaging,
                    skuItem.acceptableCodeLife,
                    skuItem.market,
                )
                .execute()
        }.also {
            refreshSkuView()
        }
    }

    fun refreshSkuView() =
        dsl.query("refresh materialized view concurrently ${SKU_SPECIFICATION_VIEW.name}").execute()

    @BeforeEach
    fun beforeEach() {
        insertSkuSpecification(skus)
    }

    @AfterEach
    fun tearDown() {
        dsl.deleteFrom(Tables.SKU_SPECIFICATION).execute()
        refreshSkuView()
        dsl.deleteFrom(Tables.DEMAND).execute()
    }

    companion object {
        lateinit var s3Importer: S3Importer
        private lateinit var demandRepository: DemandRepository
        private lateinit var skuInputDataRepository: SkuInputDataRepository
        private lateinit var skuInputService: SkuInputService
        lateinit var forecastPackagingService: ForecastPackagingService
        private val dataSource = InfraPreparation.getMigratedDataSource()
        lateinit var dsl: MetricsDSLContext
        private lateinit var dcRepository: DcRepository
        private lateinit var dcConfigService: DcConfigService

        @BeforeAll
        @JvmStatic
        fun setUp() {
            val defaultConfiguration = DefaultConfiguration().apply {
                setSQLDialect(POSTGRES)
                setDataSource(dataSource)
                setExecutor(Executors.newSingleThreadExecutor())
            }
            dsl = DSL.using(
                defaultConfiguration,
            ).withMetrics(SimpleMeterRegistry())

            s3Importer = mockk<S3Importer>()
            demandRepository = DemandRepositoryImpl(dsl)
            dcRepository = DcRepositoryImpl(dsl)
            dcConfigService = DcConfigService(
                SimpleMeterRegistry(), repo = {
                    listOf(getDcConfig("BV"), getDcConfig("GR"), getDcConfig("SP", "ES"))
                }
            )
            skuInputDataRepository = SkuInputDataRepositoryImpl(dsl, dcConfigService)
            skuInputService = SkuInputService(skuInputDataRepository)
            forecastPackagingService = ForecastPackagingService(s3Importer, demandRepository, skuInputService)
        }
    }
}
