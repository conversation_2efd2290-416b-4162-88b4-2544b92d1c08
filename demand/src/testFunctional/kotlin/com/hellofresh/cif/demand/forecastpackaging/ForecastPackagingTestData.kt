package com.hellofresh.cif.demand.forecastpackaging

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.sku.models.SkuSpecification
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID

fun getDcConfig(dcCode: String, market: String = "UK") =
    DistributionCenterConfiguration(
        dcCode = dcCode,
        productionStart = LocalDate.now().dayOfWeek,
        cleardown = LocalDate.now().dayOfWeek,
        market = market,
        zoneId = ZoneOffset.UTC,
        enabled = true,
        wmsType = WmsSystem.WMS_SYSTEM_FCMS,
    )

private val firstSkuId = UUID.randomUUID()
private val secondSkuId = UUID.randomUUID()
val thirdSkuId: UUID = UUID.randomUUID()

private val firstForecastSkuId = UUID.randomUUID()
private val secondForecastSkuId = UUID.randomUUID()
val thirdForecastSkuId: UUID = UUID.randomUUID()

private val firstSkuSpec = getSkuSpec("PCK-10-121326-1", "HF Large box 2023")
private val secondSkuSpec = getSkuSpec("PCK-10-002482-1", "GC Cool Pouch Label")
private val thirdSkuSpec = getSkuSpec("PCK-10-11207-2", "Large PET Light")

private val firstForecastSkuSpec = getSkuSpec("PCK-10-121326-1", "HF Large box 2023", "ES")
private val secondForecastSkuSpec = getSkuSpec("PCK-10-002482-1", "GC Cool Pouch Label", "ES")
private val thirdForecastSkuSpec = getSkuSpec("PCK-10-11207-2", "Large PET Light", "ES")

fun getSkuSpec(skuCode: String, skuName: String, market: String = "UK") = SkuSpecification(
    category = "PCK",
    skuCode = skuCode,
    name = skuName,
    coolingType = "test1",
    packaging = "test2",
    acceptableCodeLife = 0,
    market = market,
)

val skus = listOf(
    Pair(firstSkuId, firstSkuSpec),
    Pair(secondSkuId, secondSkuSpec),
    Pair(thirdSkuId, thirdSkuSpec),
    Pair(firstForecastSkuId, firstForecastSkuSpec),
    Pair(secondForecastSkuId, secondForecastSkuSpec),
    Pair(thirdForecastSkuId, thirdForecastSkuSpec),
)
