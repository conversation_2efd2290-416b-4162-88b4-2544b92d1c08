package com.hellofresh.cif.demand

import com.google.type.Decimal
import com.hellofresh.cif.demand.schema.enums.SubbedType
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastKey
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.CrossDocking
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.CrossDockingAction.CROSS_DOCKING_ACTION_REPLACE
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.CrossDockingAction.CROSS_DOCKING_ACTION_UNSPECIFIED
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.CrossDockings
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Prekitting
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Prekittings
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.RecipesBreakdown
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Substitution
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Substitutions
import com.hellofresh.proto.stream.ye.demand.recipeDemandForecast.v3.RecipeDemandForecastKey
import com.hellofresh.proto.stream.ye.demand.recipeDemandForecast.v3.RecipeDemandForecastVal
import com.hellofresh.proto.stream.ye.demand.recipeDemandForecast.v3.RecipeDemandForecastVal.DepartmentSkuQuantity
import java.time.Instant
import java.time.LocalDate
import java.util.Optional
import java.util.UUID
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.common.header.internals.RecordHeader
import org.apache.kafka.common.header.internals.RecordHeaders
import org.apache.kafka.common.record.TimestampType.CREATE_TIME

@Suppress("LongParameterList")
fun generateTestData(
    i: Int,
    demandQty: Int = 10,
    subbedType: SubbedType = SubbedType.NONE,
    subInQty: Long = 0,
    subOutQty: Long = 0,
    prekitting: Long = 0,
    crossDockingIn: Long = 0,
    crossDockingOut: Long = 0,
    skuDemandForecastValBlock: SkuDemandForecastVal.Builder.() -> Unit = {}
): ConsumerRecord<SkuDemandForecastKey, SkuDemandForecastVal?> {
    val key = SkuDemandForecastKey.newBuilder().apply {
        skuId = UUID.randomUUID().toString()
        distributionCenterBobCode = i.toString()
        date = LocalDate.now().let {
            com.google.type.Date.newBuilder().apply {
                year = it.year
                month = it.monthValue
                day = it.dayOfMonth
            }.build()
        }
    }.build()

    val value = SkuDemandForecastVal.newBuilder().apply {
        totalQty = Decimal.newBuilder().setValue(demandQty.toString()).build()
        addRecipesBreakdown(
            RecipesBreakdown.newBuilder().apply {
                brand = "UNKNOWN"
                recipeIndex = "1"
                qty = Decimal.newBuilder().setValue(demandQty.toString()).build()
            },
        )

        preKittings = Prekittings.newBuilder().addPrekittingIn(
            Prekitting.newBuilder().setQty(
                Decimal.newBuilder().setValue(prekitting.toString()).build(),
            ),
        ).build()

        substitutions = Substitutions.newBuilder().apply {
            addSubIn(
                Substitution.newBuilder().apply {
                    brand = "UNKNOWN"
                    recipeIndex = "1"
                    qty = Decimal.newBuilder().setValue(subInQty.toString()).build()
                },
            )

            addSubOut(
                Substitution.newBuilder().apply {
                    brand = "UNKNOWN"
                    recipeIndex = "2"
                    qty = Decimal.newBuilder().setValue(subOutQty.toString()).build()
                },
            )
        }.build()

        crossDockings = crossDockingsBuilder(crossDockingIn, crossDockingOut).build()

        skuCode = UUID.randomUUID().toString()
        skuDemandForecastValBlock()
    }.build()

    val headers = buildList<RecordHeader?> {
        when (subbedType) {
            SubbedType.NONE -> {}
            SubbedType.SUB_IN -> add(RecordHeader(SUBBED_IN_KAFKA_HEADER_KEY, true.toString().toByteArray()))
            SubbedType.SUB_OUT -> add(RecordHeader(SUBBED_OUT_KAFKA_HEADER_KEY, true.toString().toByteArray()))
            SubbedType.SUB_IN_AND_OUT -> {
                add(RecordHeader(SUBBED_IN_KAFKA_HEADER_KEY, true.toString().toByteArray()))
                add(RecordHeader(SUBBED_OUT_KAFKA_HEADER_KEY, true.toString().toByteArray()))
            }
        }
    }
    return ConsumerRecord(
        "test", 0, i.toLong(),
        Instant.now().toEpochMilli(),
        CREATE_TIME, 1, 1, key, value,
        RecordHeaders(headers),
        Optional.empty(),
    )
}

fun generateYfTestData(
    i: Int,
    demandQty: Int = 10,
    actualQuantity: Int = 20,
    skuDemandForecastValBlock: RecipeDemandForecastVal.Builder.() -> Unit = {}
): ConsumerRecord<RecipeDemandForecastKey, RecipeDemandForecastVal> {
    val key = RecipeDemandForecastKey.newBuilder().apply {
        skuCode = "skuCode"
        dc = "dcCode"
        productionDay = LocalDate.now().let {
            com.google.type.Date.newBuilder().apply {
                year = it.year
                month = it.monthValue
                day = it.dayOfMonth
            }.build()
        }
    }.build()

    val value = RecipeDemandForecastVal.newBuilder().apply {
        skuCode = UUID.randomUUID().toString()
        skuDemandForecastValBlock()
        this.addDepartmentSkuQuantities(
            DepartmentSkuQuantity.newBuilder().apply {
                forecastQuantity = Decimal.newBuilder().apply {
                    this.value = demandQty.toString()
                }.build()
                this.actualQuantity = Decimal.newBuilder().apply {
                    this.value = actualQuantity.toString()
                }.build()
            }.build(),
        )
    }.build()

    return ConsumerRecord(
        "test", 0, i.toLong(),
        Instant.now().toEpochMilli(),
        CREATE_TIME, 1, 1, key, value,
        RecordHeaders(),
        Optional.empty(),
    )
}

private fun crossDockingsBuilder(
    crossDockingIn: Long,
    crossDockingOut: Long
) = CrossDockings.newBuilder().apply {
    addCrossdockingIn(
        CrossDocking.newBuilder().apply {
            distributionCenterCode = UUID.randomUUID().toString()
            action = CROSS_DOCKING_ACTION_REPLACE
            qty = Decimal.newBuilder().setValue(crossDockingIn.toString()).build()
        },
    )

    addCrossdockingOut(
        CrossDocking.newBuilder().apply {
            distributionCenterCode = UUID.randomUUID().toString()
            action = CROSS_DOCKING_ACTION_UNSPECIFIED
            qty = Decimal.newBuilder().setValue(crossDockingOut.toString()).build()
        },
    )
}
