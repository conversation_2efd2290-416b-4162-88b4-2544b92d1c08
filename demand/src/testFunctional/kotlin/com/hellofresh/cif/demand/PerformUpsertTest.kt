package com.hellofresh.cif.demand

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.type.Date
import com.google.type.Decimal
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.demand.schema.Tables.DEMAND
import com.hellofresh.cif.demand.schema.enums.SubbedType.NONE
import com.hellofresh.cif.demand.schema.enums.SubbedType.SUB_IN
import com.hellofresh.cif.demand.schema.enums.SubbedType.SUB_IN_AND_OUT
import com.hellofresh.cif.demand.schema.enums.SubbedType.SUB_OUT
import com.hellofresh.cif.demand.schema.tables.records.DemandRecord
import com.hellofresh.cif.lib.kafka.serde.objectMapper
import com.hellofresh.demand.models.ConsumptionDetails
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastKey
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.RecipesBreakdown
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Substitution
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.Substitutions
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.Instant
import java.time.LocalDate
import java.util.Optional
import java.util.concurrent.Executors
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.common.TopicPartition
import org.apache.kafka.common.header.internals.RecordHeaders
import org.apache.kafka.common.record.TimestampType.CREATE_TIME
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class PerformUpsertTest {
    private lateinit var dsl: MetricsDSLContext
    private lateinit var update: PerformUpsert

    @BeforeEach
    fun setUp() {
        dsl = DSL.using(
            DefaultConfiguration().apply {
                setSQLDialect(POSTGRES)
                setDataSource(dataSource)
                setExecutor(Executors.newSingleThreadExecutor())
            },
        ).withMetrics(SimpleMeterRegistry())
        update = PerformUpsert(dsl)
    }

    @AfterEach
    fun cleanup() {
        dsl.deleteFrom(DEMAND).execute()
    }

    @Test
    fun `insert new rows`() {
        val crList = (0..2).map { i -> generateTestData(i) }

        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to crList))
        runBlocking { update(consumerRecords) }
        val records = dsl.fetch(DEMAND).sortedBy { it.dcCode }

        assertEquals(3, records.size)
        records.forEachIndexed { i, r ->
            assertEquals(crList[i].key(), crList[i].value(), r)
        }
    }

    @Test
    fun `consumption details are persisted correctly`() {
        val consumerRecord = generateTestData(1)

        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to listOf(consumerRecord)))
        runBlocking { update(consumerRecords) }

        val record = dsl.fetch(DEMAND).first()

        assertEquals(
            consumerRecord.value()!!.consumptionDetails(),
            objectMapper.readValue<ConsumptionDetails>(
                record.consumptionDetails.data(),
            ),
        )
    }

    @Test
    fun `set the subbed column correctly`() {
        val states = listOf(SUB_IN, SUB_OUT, NONE, SUB_IN_AND_OUT)
        val subInQty = 10
        val subOutQty = 20
        val subOut = Substitution.newBuilder().apply {
            brand = "HF"
            recipeIndex = "10"
            qty = Decimal.newBuilder().setValue(subOutQty.toString()).build()
        }.build()

        val subIn = Substitution.newBuilder().apply {
            brand = "HF"
            recipeIndex = "10"
            qty = Decimal.newBuilder().setValue(subInQty.toString()).build()
        }.build()

        runBlocking {
            update(
                ConsumerRecords(
                    mapOf(
                        TopicPartition("test", 0) to
                            states.mapIndexed { i, state ->
                                generateTestData(i) {
                                    when (state) {
                                        NONE -> substitutions = Substitutions.newBuilder().build()
                                        SUB_OUT -> substitutions = Substitutions.newBuilder().apply {
                                            addSubOut(subOut)
                                        }.build()

                                        SUB_IN -> substitutions = Substitutions.newBuilder().apply {
                                            addSubIn(subIn)
                                        }.build()

                                        SUB_IN_AND_OUT -> substitutions = Substitutions.newBuilder().apply {
                                            addSubIn(subIn)
                                            addSubOut(subOut)
                                        }.build()
                                    }
                                }
                            },
                    ),
                ),
            )
        }

        val records = dsl.fetch(DEMAND).sortedBy { it.dcCode } // dcCode is 1-4

        assertEquals(4, records.size)
        records.forEachIndexed { i, r ->
            assertEquals(states[i], r.subbed)
            when (r.subbed!!) {
                NONE -> {
                    assertEquals(0, r.substitutedInQty.toInt())
                    assertEquals(0, r.substitutedOutQty.toInt())
                }

                SUB_OUT -> {
                    assertEquals(0, r.substitutedInQty.toInt())
                    assertEquals(subOutQty, r.substitutedOutQty.toInt())
                }

                SUB_IN -> {
                    assertEquals(subInQty, r.substitutedInQty.toInt())
                    assertEquals(0, r.substitutedOutQty.toInt())
                }

                SUB_IN_AND_OUT -> {
                    assertEquals(subInQty, r.substitutedInQty.toInt())
                    assertEquals(subOutQty, r.substitutedOutQty.toInt())
                }
            }
        }
    }

    @Test
    fun `updates records`() {
        val input = (0..3).map { i -> generateTestData(i) }

        val consumerRecordsBegin = ConsumerRecords(mapOf(TopicPartition("test", 0) to input))
        runBlocking { update(consumerRecordsBegin) }

        val newInput = mutableListOf<ConsumerRecord<SkuDemandForecastKey, SkuDemandForecastVal?>>()

        newInput.add(
            newConsumerRecord(
                input[0].key(),
                generateTestData(0, subInQty = 200).value(),
            ),
        )

        newInput.add(
            newConsumerRecord(
                input[1].key(),
                generateTestData(1, subOutQty = 300).value(),
            ),
        )

        newInput.add(
            newConsumerRecord(
                input[2].key(),
                generateTestData(2, demandQty = 300).value(),
            ),
        )

        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to newInput))
        runBlocking { update(consumerRecords) }
        val records = dsl.fetch(DEMAND).sortedBy { it.dcCode }

        assertEquals(4, records.size)
        assertEquals(newInput[0].key(), newInput[0].value(), records[0])
        assertEquals(newInput[1].key(), newInput[1].value(), records[1])
        assertEquals(newInput[2].key(), newInput[2].value(), records[2])

        // old timestamped record was not updated
        assertEquals(input[3].key(), input[3].value(), records[3])
    }

    @Test
    fun `null values are ignored`() {
        val rec = generateTestData(1)
        // insert null
        val consumerRecNullValue = newConsumerRecord(rec.key(), null)
        val consumerRecordsNullVal = ConsumerRecords(mapOf(TopicPartition("test", 0) to listOf(consumerRecNullValue)))
        runBlocking { update(consumerRecordsNullVal) }
        val records = dsl.fetch(DEMAND)
        assertTrue { records.isEmpty() }
    }

    @Test fun `zero values are inserted`() {
        val rec = generateTestData(1)
        // insert null
        val consumerRecNullValue = newConsumerRecord(rec.key(), SkuDemandForecastVal.newBuilder().build())
        val consumerRecordsNullVal = ConsumerRecords(mapOf(TopicPartition("test", 0) to listOf(consumerRecNullValue)))
        runBlocking { update(consumerRecordsNullVal) }
        val records = dsl.fetch(DEMAND)
        assertEquals(1, records.size)
        assertEquals(0L, records[0].quantity.toLong())
        assertEquals(
            ConsumptionDetails.empty,
            objectMapper.readValue<ConsumptionDetails>(records[0].consumptionDetails.data()),
        )
    }

    @Test
    fun `insert new rows with sub in and out quantites`() {
        val inQty = 100L
        val outQty = 200L
        val crList = (0..1)
            .map { i -> generateTestData(i = i, subOutQty = outQty, subInQty = inQty) }

        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to crList))
        runBlocking { update(consumerRecords) }
        val records = dsl.fetch(DEMAND).sortedBy { it.dcCode }

        assertEquals(2, records.size)
        assertEquals(inQty, records[0].substitutedInQty)
        assertEquals(outQty, records[0].substitutedOutQty)
    }

    @Test
    fun `insert new rows with sub in and out quantites when message has incorrect number stores zeros`() {
        val crList = (0..1)
            .map { i ->
                generateTestData(i = i) {
                    Substitutions.newBuilder().apply {
                        addSubIn(
                            Substitution.newBuilder().setQty(Decimal.newBuilder().setValue("incorrectLong").build()),
                        )
                        addSubOut(
                            Substitution.newBuilder().setQty(Decimal.newBuilder().setValue("incorrectLong").build()),
                        )
                    }.build()
                }
            }

        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to crList))
        runBlocking { update(consumerRecords) }
        val records = dsl.fetch(DEMAND).sortedBy { it.dcCode }

        assertEquals(2, records.size)
        assertEquals(records[0].substitutedOutQty, 0)
        assertEquals(records[0].substitutedInQty, 0)
    }

    @Test fun `don't Use recipeBreakdown to get the demand`() {
        val recipeDemand = 100L
        val totalDemand = 10
        val toInsert = listOf(
            generateTestData(i = 0, demandQty = totalDemand) {
                addRecipesBreakdown(
                    RecipesBreakdown.newBuilder().apply {
                        brand = "UNKNOWN"
                        recipeIndex = "1"
                        qty = Decimal.newBuilder().setValue(recipeDemand.toString()).build()
                    },
                )
            },
        )

        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to toInsert))
        runBlocking { update(consumerRecords) }
        val records = dsl.fetch(DEMAND)

        assertEquals(1, records.size)

        // demand in recipe breakdown is ignored
        assertEquals(totalDemand, records[0].quantity.toInt())
    }

    private fun assertEquals(k: SkuDemandForecastKey, v: SkuDemandForecastVal?, record: DemandRecord) {
        assertEquals(k.skuId, record.skuId.toString())
        assertEquals(k.distributionCenterBobCode, record.dcCode)
        assertEquals(k.date.toLocalDate(), record.date)
        assertEquals(v?.totalQty?.longValue() ?: 0, record.quantity.toLong())
    }

    private fun Date.toLocalDate(): LocalDate = LocalDate.of(this.year, this.month, this.day)

    fun newConsumerRecord(key: SkuDemandForecastKey, value: SkuDemandForecastVal?) =
        ConsumerRecord(
            "test", 0, 0,
            Instant.now().toEpochMilli(),
            CREATE_TIME, 1, 1, key, value,
            RecordHeaders(), Optional.empty(),
        )

    companion object {
        private val dataSource = getMigratedDataSource()
    }
}
