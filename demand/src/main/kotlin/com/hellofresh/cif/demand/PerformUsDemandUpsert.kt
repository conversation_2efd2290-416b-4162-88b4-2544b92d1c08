package com.hellofresh.cif.demand

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.demand.repository.DemandRepositoryImpl
import com.hellofresh.cif.demand.repository.UsDemandRepositoryImpl
import com.hellofresh.cif.demand.schema.enums.SubbedType
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.demand.models.ConsumptionDetails
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastKey
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastVal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.logging.log4j.kotlin.Logging

class PerformUsDemandUpsert(
    private val metricsDSLContext: MetricsDSLContext
) : suspend (ConsumerRecords<WeekSkuInventoryDemandForecastKey, WeekSkuInventoryDemandForecastVal?>) -> Unit {

    override suspend fun invoke(records: ConsumerRecords<WeekSkuInventoryDemandForecastKey, WeekSkuInventoryDemandForecastVal?>) {
        metricsDSLContext
            .withTagName("perform-us-demand-upsert")
            .transactionAsync { config ->
                val tx = metricsDSLContext.withMeteredConfiguration(config)
                val usDemandRepository = UsDemandRepositoryImpl(tx)
                val demandRepository = DemandRepositoryImpl(tx)

                usDemandRepository.insertInBatch(records)
                val aggregatedDemands = usDemandRepository.getAggregatedUsDemand()
                demandRepository.insertDemandInBatch(aggregatedDemands)
            }.await()
    }

    companion object : Logging
}

data class DemandData(
    val skuId: UUID,
    val dcCode: String,
    val date: LocalDate,
    val quantity: SkuQuantity,
    val subbed: SubbedType = SubbedType.NONE,
    val recordTimestamp: LocalDateTime = LocalDateTime.now(),
    val substitutedInQty: Long? = 0,
    val substitutedOutQty: Long? = 0,
    val consumptionDetails: ConsumptionDetails? = null,
)

data class UsDemandData(
    val skuId: UUID,
    val dcCode: String,
    val date: LocalDate,
    val week: String,
    val quantity: SkuQuantity,
    val recordTimestamp: LocalDateTime,
)
