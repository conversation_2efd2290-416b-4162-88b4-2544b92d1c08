package com.hellofresh.cif.demand.repository

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.demand.DemandData
import com.hellofresh.cif.demand.schema.enums.Uom
import com.hellofresh.cif.demand.schema.tables.Demand.DEMAND
import org.jooq.JSONB

class DemandRepositoryImpl(
    private val dslContext: MetricsDSLContext
) : DemandRepository {
    override fun insertDemandInBatch(demands: List<DemandData>) {
        val insertDemandQuery = demands.map { row ->
            dslContext
                .insertInto(DEMAND)
                .columns(
                    DEMAND.SKU_ID,
                    DEMAND.DC_CODE,
                    DEMAND.DATE,
                    DEMAND.QUANTITY,
                    DEMAND.SUBBED,
                    DEMAND.RECORD_TIMESTAMP,
                    DEMAND.SUBSTITUTED_IN_QTY,
                    DEMAND.SUBSTITUTED_OUT_QTY,
                    DEMAND.CONSUMPTION_DETAILS,
                    DEMAND.UOM
                )
                .values(
                    row.skuId,
                    row.dcCode,
                    row.date,
                    row.quantity.getValue(),
                    row.subbed,
                    row.recordTimestamp,
                    row.substitutedInQty,
                    row.substitutedOutQty,
                    JSONB.jsonbOrNull(row.consumptionDetails?.toString()),
                    Uom.valueOf(row.quantity.unitOfMeasure.toString())
                ).onDuplicateKeyUpdate()
                .set(DEMAND.QUANTITY, row.quantity.getValue())
        }

        dslContext
            .withTagName("insert-demand-from-us")
            .batch(insertDemandQuery)
            .execute()
    }
}
