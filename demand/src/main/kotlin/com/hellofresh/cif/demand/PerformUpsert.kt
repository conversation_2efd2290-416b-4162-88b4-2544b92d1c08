package com.hellofresh.cif.demand

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.type.Decimal
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.demand.schema.Tables.DEMAND
import com.hellofresh.cif.demand.schema.enums.SubbedType
import com.hellofresh.cif.lib.instantTimestamp
import com.hellofresh.dateUtil.models.toLocalDate
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastKey
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Batch
import org.jooq.JSONB
import org.jooq.impl.DSL.insertInto

class PerformUpsert(
    private val metricsDSLContext: MetricsDSLContext
) : suspend (ConsumerRecords<SkuDemandForecastKey, SkuDemandForecastVal?>) -> Unit {

    override suspend fun invoke(records: ConsumerRecords<SkuDemandForecastKey, SkuDemandForecastVal?>) {
        BatchQueryBuilder(metricsDSLContext).let {
            records.filter {
                val isNull = it.value() == null
                if (isNull) logger.error("Unexpected null value for the key:${it.key()}")
                !isNull
            }
                .map { v -> it.bind(v) }
                .lastOrNull()
                ?.executeAsync()
        }?.await()
    }

    /**
     * Provides a safe interface to build PreparedStatement
     */
    private class BatchQueryBuilder(dsl: MetricsDSLContext) {
        private val query = dsl.withTagName("insert-demand")
            .batch(
                DEMAND.run {
                    insertInto(this)
                        .columns(
                            SKU_ID,
                            DC_CODE,
                            DATE,
                            QUANTITY,
                            SUBBED,
                            RECORD_TIMESTAMP,
                            SUBSTITUTED_IN_QTY,
                            SUBSTITUTED_OUT_QTY,
                            CONSUMPTION_DETAILS,
                        )
                        .values(
                            null, null, LocalDate.now(), BigDecimal(0), SubbedType.NONE, LocalDateTime.now(), 0, 0,
                            JSONB.jsonbOrNull(
                                null,
                            ),
                        )
                        .onDuplicateKeyUpdate()
                        .set(SKU_ID, UUID.randomUUID())
                        .set(DC_CODE, "")
                        .set(DATE, LocalDate.now())
                        .set(QUANTITY, BigDecimal(0))
                        .set(SUBBED, SubbedType.NONE)
                        .set(RECORD_TIMESTAMP, LocalDateTime.now())
                        .set(SUBSTITUTED_IN_QTY, 0)
                        .set(SUBSTITUTED_OUT_QTY, 0)
                        .set(CONSUMPTION_DETAILS, JSONB.jsonbOrNull(null))
                },
            )

        lateinit var runnableQuery: RunnableQuery

        fun bind(record: ConsumerRecord<SkuDemandForecastKey, SkuDemandForecastVal?>): RunnableQuery {
            if (!this::runnableQuery.isInitialized) {
                runnableQuery = RunnableQuery(query)
            }

            val key = record.key()
            val value = record.value()
            if (value == null) {
                logger.error("Unexpected null value for the key:$key")
                return runnableQuery
            }

            val demandQuantity = value.totalQty?.toLongValue() ?: 0
            val recordTimestamp = LocalDateTime.ofInstant(record.instantTimestamp(), ZoneOffset.UTC)
            val totalSubIn = value.substitutions?.subInList?.sumOf { it.qty.toLongValue() } ?: 0
            val totalSubOut = value.substitutions?.subOutList?.sumOf { it.qty.toLongValue() } ?: 0
            val consumptionDetails = JSONB.jsonbOrNull(
                record.value()?.consumptionDetails()?.let {
                    objectMapper.writeValueAsString(it)
                },
            )

            val subbedType = when {
                totalSubIn != 0L && totalSubOut != 0L -> SubbedType.SUB_IN_AND_OUT
                totalSubIn != 0L -> SubbedType.SUB_IN
                totalSubOut != 0L -> SubbedType.SUB_OUT
                else -> SubbedType.NONE
            }

            query.bind(
                UUID.fromString(key.skuId),
                key.distributionCenterBobCode,
                key.date.toLocalDate(),
                demandQuantity,
                subbedType,
                recordTimestamp,
                totalSubIn,
                totalSubOut,
                consumptionDetails,
                UUID.fromString(key.skuId),
                key.distributionCenterBobCode,
                key.date.toLocalDate(),
                demandQuantity,
                subbedType,
                recordTimestamp,
                totalSubIn,
                totalSubOut,
                consumptionDetails,
            )

            return runnableQuery
        }

        class RunnableQuery(val query: Batch) {
            fun executeAsync() = query.executeAsync()
        }
    }

    companion object : Logging {
        private val objectMapper = ObjectMapper().findAndRegisterModules()
    }
}

private fun Decimal.isValueMissing() = this == Decimal.getDefaultInstance()

fun Decimal.toLongValue() = if (isValueMissing()) 0 else value.toLongOrNull() ?: logErrorReturnZero()

fun Decimal.toDoubleValue() = if (isValueMissing()) 0 else value.toDoubleOrNull() ?: logErrorReturnZero()

private fun Decimal.logErrorReturnZero(): Long {
    PerformUpsert.logger.error("Unable to parse decimal value: $value")
    return 0
}
