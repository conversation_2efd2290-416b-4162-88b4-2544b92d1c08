package com.hellofresh.cif.demand.repository

import com.hellofresh.cif.demand.DemandData
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastKey
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastVal
import org.apache.kafka.clients.consumer.ConsumerRecords

interface UsDemandRepository {
    fun insertInBatch(records: ConsumerRecords<WeekSkuInventoryDemandForecastKey, WeekSkuInventoryDemandForecastVal?>)
    fun getAggregatedUsDemand(): List<DemandData>
}
