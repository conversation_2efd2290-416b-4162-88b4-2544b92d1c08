package com.hellofresh.cif.demand

import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.demand.repository.DemandRepositoryImpl
import com.hellofresh.cif.demand.service.ForecastPackagingService
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.s3.S3EventMessageParser
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.cif.skuinput.service.SkuInputService
import com.hellofresh.cif.sqs.SQSClientBuilder
import com.hellofresh.cif.sqs.SQSListener
import com.hellofresh.cif.sqs.SQSMessageProxy
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import software.amazon.awssdk.services.sqs.SqsClient

private const val WRITE_NUMBER_OF_THREADS = 2
private const val READ_NUMBER_OF_THREADS = 4

object ForecastPackagingSQSListener {
    private val sqsClient: SqsClient = SQSClientBuilder.getSqsClient(sessionName = "forecast-packaging-session")

    private fun getForecastPackagingSqsUrl() = ConfigurationLoader.getStringOrFail("aws.sqs.forecast.url")

    fun launch(
        meterRegistry: HelloFreshMeterRegistry
    ) {
        val readWriteMetricsDSLContext = DBConfiguration.jooqMasterDslContext(
            WRITE_NUMBER_OF_THREADS,
            meterRegistry,
        )

        val readMetricsDSLContext = DBConfiguration.jooqReadOnlyDslContext(
            READ_NUMBER_OF_THREADS,
            meterRegistry,
        )

        val s3Importer = S3Importer(sessionName = "forecast-packaging-session")
        val s3EventMessageParser = S3EventMessageParser()
        val demandRepository = DemandRepositoryImpl(readWriteMetricsDSLContext)
        val dcRepository = DcRepositoryImpl(readMetricsDSLContext)
        val dcConfigService = DcConfigService(meterRegistry, dcRepository)
        val skuInputDataRepository = SkuInputDataRepositoryImpl(readMetricsDSLContext, dcConfigService)
        val skuInputService = SkuInputService(skuInputDataRepository)

        val forecastPackagingService =
            ForecastPackagingService(s3Importer, demandRepository, skuInputService)

        val forecastPackagingSqsService = SQSMessageProxy(
            forecastPackagingService,
            s3EventMessageParser,
        )

        val forecastPackagingSqsListener = shutdownNeeded {
            SQSListener(
                forecastPackagingSqsService,
                sqsClient,
                getForecastPackagingSqsUrl(),
            )
        }

        CoroutineScope(Dispatchers.IO).launch {
            forecastPackagingSqsListener.run()
        }
    }
}
