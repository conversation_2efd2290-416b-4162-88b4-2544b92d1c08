package com.hellofresh.cif.demand.repository

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.demand.DemandData
import com.hellofresh.cif.demand.PerformUsDemandUpsert.Companion.logger
import com.hellofresh.cif.demand.UsDemandData
import com.hellofresh.cif.demand.schema.enums.Uom
import com.hellofresh.cif.demand.schema.tables.UsDemand.US_DEMAND
import com.hellofresh.cif.lib.instantTimestamp
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.dateUtil.models.toLocalDate
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastKey
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastVal
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastVal.UOM.UOM_GAL
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastVal.UOM.UOM_KG
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastVal.UOM.UOM_LBS
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastVal.UOM.UOM_LITRE
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastVal.UOM.UOM_OZ
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastVal.UOM.UOM_UNIT
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastVal.UOM.UOM_UNSPECIFIED
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.jooq.impl.DSL
import org.jooq.impl.DSL.row

class UsDemandRepositoryImpl(
    private val dslContext: MetricsDSLContext
) : UsDemandRepository {
    private val usDemandList: MutableList<UsDemandData> = mutableListOf()

    override fun insertInBatch(records: ConsumerRecords<WeekSkuInventoryDemandForecastKey, WeekSkuInventoryDemandForecastVal?>) {
        records.filter {
            val isNull = it.key() == null
            if (isNull) {
                logger.error("Unexpected null value for the key:${it.key()}")
            }
            !isNull
        }

        val batchQuery = records.map { record ->
            getUsDemand(record)?.let {
                usDemandList.add(it)
                dslContext
                    .insertInto(US_DEMAND)
                    .set(US_DEMAND.SKU_ID, it.skuId)
                    .set(US_DEMAND.DC_CODE, it.dcCode)
                    .set(US_DEMAND.DATE, it.date)
                    .set(US_DEMAND.WEEK, it.week)
                    .set(US_DEMAND.QUANTITY, it.quantity.getValue())
                    .set(US_DEMAND.UOM, Uom.valueOf(it.quantity.unitOfMeasure.toString()))
                    .set(US_DEMAND.RECORD_TIMESTAMP, it.recordTimestamp)
                    .onDuplicateKeyUpdate()
                    .set(US_DEMAND.SKU_ID, it.skuId)
                    .set(US_DEMAND.DC_CODE, it.dcCode)
                    .set(US_DEMAND.DATE, it.date)
                    .set(US_DEMAND.WEEK, it.week)
                    .set(US_DEMAND.QUANTITY, it.quantity.getValue())
                    .set(US_DEMAND.UOM, Uom.valueOf(it.quantity.unitOfMeasure.toString()))
                    .set(US_DEMAND.RECORD_TIMESTAMP, it.recordTimestamp)
            }
        }

        dslContext
            .withTagName("insert-us-demand")
            .batch(batchQuery)
            .execute()
    }

    override fun getAggregatedUsDemand(): List<DemandData> {
        val result = dslContext
            .withTagName("get-aggregated-us-demand")
            .select(
                US_DEMAND.SKU_ID,
                US_DEMAND.DC_CODE,
                US_DEMAND.DATE,
                US_DEMAND.UOM,
                DSL.sum(US_DEMAND.QUANTITY).`as`("total_quantity"),
            )
            .from(US_DEMAND)
            .where(
                row(US_DEMAND.SKU_ID, US_DEMAND.DC_CODE, US_DEMAND.DATE).`in`(
                    usDemandList.map {
                        row(it.skuId, it.dcCode, it.date)
                    },
                ),
            )
            .groupBy(
                US_DEMAND.SKU_ID,
                US_DEMAND.DC_CODE,
                US_DEMAND.DATE,
                US_DEMAND.UOM,
            )

        result
            .execute()

        return result.map { record ->
            DemandData(
                skuId = record.get(US_DEMAND.SKU_ID, UUID::class.java),
                dcCode = record.get(US_DEMAND.DC_CODE, String::class.java),
                date = record.get(US_DEMAND.DATE, LocalDate::class.java),
                quantity = SkuQuantity.fromBigDecimal(
                    record.get("total_quantity", BigDecimal::class.java),
                    SkuUOM.valueOf(record.get(US_DEMAND.UOM, Uom::class.java).literal),
                ),
            )
        }
    }

    private fun getUsDemand(record: ConsumerRecord<WeekSkuInventoryDemandForecastKey, WeekSkuInventoryDemandForecastVal?>): UsDemandData? {
        val key = record.key()
        val value = record.value()
        if (value == null) {
            logger.error("Unexpected null value for the key:$key")
            return null
        }

        val recordTimestamp = LocalDateTime.ofInstant(record.instantTimestamp(), ZoneOffset.UTC)
        val skuId = UUID.fromString(key.skuId)
        val week = "${key.productionWeek.year}-W${key.productionWeek.week}"
        val uom = getUom(value.unit)

        val demandQuantity: BigDecimal = value.forecastedDemandedQty?.value?.takeIf {
            it.isNotEmpty()
        }?.let {
            BigDecimal(it)
        } ?: BigDecimal.ZERO

        return UsDemandData(
            skuId = skuId,
            dcCode = key.distributionCenterBobCode,
            date = key.date.toLocalDate(),
            week = week,
            quantity = SkuQuantity.fromBigDecimal(demandQuantity, SkuUOM.valueOf(uom.literal)),
            recordTimestamp = recordTimestamp,
        )
    }

    private fun getUom(value: WeekSkuInventoryDemandForecastVal.UOM): Uom =
        when (value) {
            UOM_UNSPECIFIED -> Uom.UOM_UNSPECIFIED
            UOM_UNIT -> Uom.UOM_UNIT
            UOM_KG -> Uom.UOM_KG
            UOM_LBS -> Uom.UOM_LBS
            UOM_GAL -> Uom.UOM_GAL
            UOM_LITRE -> Uom.UOM_LITRE
            UOM_OZ -> Uom.UOM_OZ
            else -> error("Unrecognised enum value for UOM")
        }
}
