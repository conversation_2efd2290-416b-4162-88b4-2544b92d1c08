package com.hellofresh.cif.demand.model

import com.hellofresh.cif.fileconsumer.model.ParsedFile
import org.apache.commons.csv.CSVRecord

class ForecastPackagingParsedFile(records: List<CSVRecord>) : ParsedFile {
    override val columns = Companion.columns
    override val data = data(records)
    override val pKey: List<String> = listOf()
    override val numericColumnDataTypes: Set<String> = Companion.numericColumnDataTypes
    override val allowedBlank: Set<String> = Companion.allowedBlank

    companion object {
        const val FORECAST_DATE_HEADER = "forecast_date"
        const val HELLOFRESH_WEEK_HEADER = "hellofresh_week"
        const val PRODUCTION_DATE_HEADER = "production_date"
        const val DC_HEADER = "dc"
        const val BRAND_HEADER = "brand"
        const val SKU_CATEGORY_HEADER = "sku_category"
        const val SKU_SIZE_HEADER = "sku_size"
        const val QUANTITY_HEADER = "quantity"
        const val SKU_CODE_HEADER = "sku_code"
        const val SKU_NAME_HEADER = "sku_name"

        val columns: List<String> = listOf(
            FORECAST_DATE_HEADER,
            HELLOFRESH_WEEK_HEADER,
            PRODUCTION_DATE_HEADER,
            DC_HEADER,
            BRAND_HEADER,
            SKU_CATEGORY_HEADER,
            SKU_SIZE_HEADER,
            QUANTITY_HEADER,
            SKU_CODE_HEADER,
            SKU_NAME_HEADER,
        )
        val numericColumnDataTypes = setOf(
            QUANTITY_HEADER
        )
        val allowedBlank = setOf(
            SKU_SIZE_HEADER,
            BRAND_HEADER,
        )
    }
}
