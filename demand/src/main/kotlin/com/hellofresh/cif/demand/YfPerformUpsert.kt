package com.hellofresh.cif.demand

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.demand.PerformUsDemandUpsert.Companion.logger
import com.hellofresh.cif.demand.schema.Tables.ACTUAL_CONSUMPTION
import com.hellofresh.cif.demand.schema.enums.SubbedType
import com.hellofresh.cif.demand.schema.enums.Uom
import com.hellofresh.cif.demand.schema.tables.Demand.DEMAND
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.lib.instantTimestamp
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.SkuUOM.UOM_GAL
import com.hellofresh.cif.models.SkuUOM.UOM_KG
import com.hellofresh.cif.models.SkuUOM.UOM_LBS
import com.hellofresh.cif.models.SkuUOM.UOM_LITRE
import com.hellofresh.cif.models.SkuUOM.UOM_OZ
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.cif.models.SkuUOM.UOM_UNSPECIFIED
import com.hellofresh.cif.models.sumOf
import com.hellofresh.cif.skuSpecificationLib.SkuCodeDcKey
import com.hellofresh.cif.skuSpecificationLib.SkuCodeLookUp
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.dateUtil.models.toLocalDate
import com.hellofresh.proto.stream.ye.demand.recipeDemandForecast.v3.RecipeDemandForecastKey
import com.hellofresh.proto.stream.ye.demand.recipeDemandForecast.v3.RecipeDemandForecastVal
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.jooq.impl.DSL

class YfPerformUpsert(
    private val dslContext: MetricsDSLContext,
    private val skuSpecificationService: SkuSpecificationService,
    private val dcConfigService: DcConfigService,
) : suspend (ConsumerRecords<RecipeDemandForecastKey, RecipeDemandForecastVal>) -> Unit {

    override suspend fun invoke(records: ConsumerRecords<RecipeDemandForecastKey, RecipeDemandForecastVal>) {
        records.filter {
            val isNull = it.key() == null
            if (isNull) {
                logger.error("Unexpected null value for the key:${it.key()}")
            }
            !isNull
        }

        val batchDcs = records.map { it.key().dc }.toSet()
        val dcConfigs = dcConfigService.dcConfigurations.filterKeys { it in batchDcs }

        val skuSpecifications = skuSpecificationService
            .skuCodeLookUp(dcConfigs.values.toList())

        val demands = records.mapNotNull { record ->
            getYfDemand(record, skuSpecifications)
        }

        dslContext.transactionAsync {
            val tx = dslContext.withMeteredConfiguration(it)
            tx.saveActualConsumption(demands)
            tx.saveDemand(demands)
        }.await()
    }

    private fun MetricsDSLContext.saveDemand(demands: List<YfDemand>) =
        with(
            withTagName("insert-yf-demand")
                .batch(
                    demands.map {
                        dslContext
                            .insertInto(DEMAND)
                            .set(DEMAND.SKU_ID, it.skuId)
                            .set(DEMAND.DC_CODE, it.dcCode)
                            .set(DEMAND.DATE, it.date)
                            .set(DEMAND.QUANTITY, it.quantity.getValue())
                            .set(DEMAND.UOM, getUom(it.quantity.unitOfMeasure))
                            .set(DEMAND.RECORD_TIMESTAMP, it.recordTimestamp)
                            .set(DEMAND.SUBBED, SubbedType.NONE)
                            .onDuplicateKeyUpdate()
                            .set(DEMAND.SKU_ID, DSL.excluded(DEMAND.SKU_ID))
                            .set(DEMAND.DC_CODE, DSL.excluded(DEMAND.DC_CODE))
                            .set(DEMAND.DATE, DSL.excluded(DEMAND.DATE))
                            .set(DEMAND.QUANTITY, DSL.excluded(DEMAND.QUANTITY))
                            .set(DEMAND.UOM, DSL.excluded(DEMAND.UOM))
                            .set(DEMAND.RECORD_TIMESTAMP, DSL.excluded(DEMAND.RECORD_TIMESTAMP))
                    },
                ),
        ) {
            execute()
        }

    private fun MetricsDSLContext.saveActualConsumption(demands: List<YfDemand>) =
        with(
            withTagName("insert-yf-actual-consumption")
                .batch(
                    demands.map {
                        dslContext.insertInto(ACTUAL_CONSUMPTION)
                            .set(ACTUAL_CONSUMPTION.SKU_ID, it.skuId)
                            .set(ACTUAL_CONSUMPTION.DC_CODE, it.dcCode)
                            .set(ACTUAL_CONSUMPTION.DATE, it.date)
                            .set(ACTUAL_CONSUMPTION.QUANTITY, it.actualConsumption)
                            .set(ACTUAL_CONSUMPTION.UOM, getUom(it.quantity.unitOfMeasure))
                            .onDuplicateKeyUpdate()
                            .set(ACTUAL_CONSUMPTION.QUANTITY, DSL.excluded(ACTUAL_CONSUMPTION.QUANTITY))
                            .set(ACTUAL_CONSUMPTION.UOM, DSL.excluded(ACTUAL_CONSUMPTION.UOM))
                    },
                ),
        ) {
            execute()
        }

    @Suppress("ReturnCount")
    private fun getYfDemand(
        record: ConsumerRecord<RecipeDemandForecastKey, RecipeDemandForecastVal?>,
        skuSpecifications: SkuCodeLookUp
    ): YfDemand? {
        val key = record.key()
        val value = record.value()
        if (value == null) {
            logger.error("Unexpected null value for the key:$key")
            return null
        }

        val skuId = skuSpecifications[SkuCodeDcKey(key.skuCode, key.dc)]?.first

        if (skuId == null) {
            logger.warn(
                "skuCode: ${key.skuCode} for this dc: ${key.dc} doesn't exist in IP",
            )
            return null
        }
        val recordTimestamp = LocalDateTime.ofInstant(record.instantTimestamp(), ZoneOffset.UTC)

        val demandQuantity = value.departmentSkuQuantitiesList.sumOf {
            SkuQuantity.fromBigDecimal(BigDecimal(it.forecastQuantity.value), SkuUOM.valueOf(it.unit.name))
        }

        val actualConsumption = value.departmentSkuQuantitiesList.sumOf {
            SkuQuantity.fromBigDecimal(BigDecimal(it.actualQuantity.value))
        }

        return YfDemand(
            skuId = skuId,
            dcCode = key.dc,
            date = key.productionDay.toLocalDate(),
            quantity = demandQuantity,
            recordTimestamp = recordTimestamp,
            actualConsumption = actualConsumption.getValue(),
        )
    }
}

data class YfDemand(
    val skuId: UUID,
    val dcCode: String,
    val date: LocalDate,
    val quantity: SkuQuantity,
    val recordTimestamp: LocalDateTime,
    val actualConsumption: BigDecimal,
)

private fun getUom(value: SkuUOM): Uom =
    when (value) {
        UOM_UNSPECIFIED -> Uom.UOM_UNSPECIFIED
        UOM_UNIT -> Uom.UOM_UNIT
        UOM_KG -> Uom.UOM_KG
        UOM_LBS -> Uom.UOM_LBS
        UOM_GAL -> Uom.UOM_GAL
        UOM_LITRE -> Uom.UOM_LITRE
        UOM_OZ -> Uom.UOM_OZ
        else -> error("Unrecognised enum value for UOM")
    }
