package com.hellofresh.cif.demand

import com.hellofresh.cif.demand.schema.enums.SubbedType
import org.apache.kafka.common.header.Headers

const val SUBBED_OUT_KAFKA_HEADER_KEY = "isSubbedOut"
const val SUBBED_IN_KAFKA_HEADER_KEY = "isSubbedIn"

fun Headers.extractSubbedState(): SubbedType {
    val isSubbedOut = getBooleanOrFalse(SUBBED_OUT_KAFKA_HEADER_KEY)
    val isSubbedIn = getBooleanOrFalse(SUBBED_IN_KAFKA_HEADER_KEY)

    return when {
        isSubbedOut && isSubbedIn -> SubbedType.SUB_IN_AND_OUT
        isSubbedOut -> SubbedType.SUB_OUT
        isSubbedIn -> SubbedType.SUB_IN
        else -> SubbedType.NONE
    }
}

private fun Headers.getBooleanOrFalse(key: String) =
    find { it.key() == key }
        ?.value()
        ?.toString(Charsets.UTF_8)
        ?.toBoolean()
        ?: false
