package com.hellofresh.cif.demand

import com.google.type.Decimal
import com.hellofresh.cif.demand.PerformUpsert.Companion.logger
import com.hellofresh.demand.models.ConsumptionDetails
import com.hellofresh.demand.models.CrossDocking
import com.hellofresh.demand.models.CrossDockingAction
import com.hellofresh.demand.models.CrossDockings
import com.hellofresh.demand.models.DemandTypeMapper
import com.hellofresh.demand.models.Prekitting
import com.hellofresh.demand.models.Prekittings
import com.hellofresh.demand.models.RecipeBreakdown
import com.hellofresh.demand.models.Substitution
import com.hellofresh.demand.models.Substitutions
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.CrossDockingAction.CROSS_DOCKING_ACTION_DUPLICATE
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.CrossDockingAction.CROSS_DOCKING_ACTION_REPLACE
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.CrossDockingAction.CROSS_DOCKING_ACTION_UNSPECIFIED
import com.hellofresh.proto.stream.demand.skuDemandForecast.v2.SkuDemandForecastVal.CrossDockingAction.UNRECOGNIZED

fun Decimal.longValue() = if (this != Decimal.getDefaultInstance()) this.value.toLongOrNull() ?: 0 else 0

fun SkuDemandForecastVal.consumptionDetails() = ConsumptionDetails(
    prekitting =
    if (preKittings.prekittingInList.isNotEmpty() || preKittings.prekittingOutList.isNotEmpty()) {
        Prekittings(
            `in` = preKittings.prekittingInList.map {
                Prekitting(it.qty.longValue(), DemandTypeMapper.mapDemandType(it.demandType))
            },
            out = preKittings.prekittingOutList.map {
                Prekitting(it.qty.longValue(), DemandTypeMapper.mapDemandType(it.demandType))
            },
        )
    } else {
        null
    },

    substitutions =
    if (substitutions.subInList.isNotEmpty() || substitutions.subOutList.isNotEmpty()) {
        Substitutions(
            `in` = substitutions.subInList.map {
                Substitution(
                    it.brand,
                    it.recipeIndex,
                    it.qty.longValue(),
                    DemandTypeMapper.mapDemandType(it.demandType),
                )
            },
            out = substitutions.subOutList.map {
                Substitution(
                    it.brand,
                    it.recipeIndex,
                    it.qty.longValue(),
                    DemandTypeMapper.mapDemandType(it.demandType),
                )
            },
        )
    } else {
        null
    },

    crossDockings =
    if (crossDockings.crossdockingInList.isNotEmpty() || crossDockings.crossdockingOutList.isNotEmpty()) {
        CrossDockings(
            `in` = crossDockings.crossdockingInList.map {
                CrossDocking(it.distributionCenterCode, it.action.toAction(), it.qty.longValue())
            },
            out = crossDockings.crossdockingOutList.map {
                CrossDocking(it.distributionCenterCode, it.action.toAction(), it.qty.longValue())
            },
        )
    } else {
        null
    },

    recipeBreakdowns = recipesBreakdownList.map {
        RecipeBreakdown(
            recipeIndex = it.recipeIndex,
            brand = it.brand,
            qty = it.qty.longValue(),
            demandType = DemandTypeMapper.mapDemandType(it.demandType),
        )
    },
)

private fun SkuDemandForecastVal.CrossDockingAction.toAction() =
    when (this) {
        CROSS_DOCKING_ACTION_REPLACE -> CrossDockingAction.REPLACE
        CROSS_DOCKING_ACTION_DUPLICATE -> CrossDockingAction.DUPLICATE
        CROSS_DOCKING_ACTION_UNSPECIFIED, UNRECOGNIZED -> {
            logger.error("Unknown Cross Docking Action Received")
            CrossDockingAction.UNKNOWN
        }
    }
