package com.hellofresh.cif.demand.deserializer

import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastKey
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.weekBased.v1.WeekSkuInventoryDemandForecastVal
import org.apache.kafka.common.serialization.Deserializer
import org.apache.kafka.common.serialization.Serde
import org.apache.kafka.common.serialization.Serializer

class WeekSkuInventoryDemandForecastKeySerde : Serde<WeekSkuInventoryDemandForecastKey> {
    override fun serializer() = Serializer<WeekSkuInventoryDemandForecastKey> { _, value ->
        value.toByteArray()
    }

    override fun deserializer() = Deserializer { _, data ->
        check(data != null) { "Key can not be null" }
        WeekSkuInventoryDemandForecastKey.parseFrom(data)
    }
}

class WeekSkuInventoryDemandForecastValSerde : Serde<WeekSkuInventoryDemandForecastVal> {
    override fun serializer() = Serializer<WeekSkuInventoryDemandForecastVal> { _, value ->
        value.toByteArray()
    }

    override fun deserializer() = Deserializer { _, data ->
        data?.let { WeekSkuInventoryDemandForecastVal.parseFrom(data) }
    }
}
