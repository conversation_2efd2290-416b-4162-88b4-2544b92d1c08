package com.hellofresh.cif.demand.service

import com.hellofresh.cif.demand.DemandData
import com.hellofresh.cif.demand.model.ForecastPackagingParsedFile
import com.hellofresh.cif.demand.repository.DemandRepository
import com.hellofresh.cif.fileconsumer.ProcessFile
import com.hellofresh.cif.fileconsumer.model.FileTypeDescriptor
import com.hellofresh.cif.fileconsumer.model.ProcessFileResult
import com.hellofresh.cif.fileconsumer.model.ViolationHandlersConfig
import com.hellofresh.cif.fileconsumer.violations.ViolationHandlersPerFile
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.skuinput.service.SkuInputService
import com.hellofresh.cif.sqs.MessageHandlerServiceInterface
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging
import org.jetbrains.annotations.VisibleForTesting

class ForecastPackagingService(
    private val importer: S3Importer,
    private val demandRepository: DemandRepository,
    private val skuInputService: SkuInputService
) :
    MessageHandlerServiceInterface {
    override suspend fun process(s3File: S3File) {
        logger.info("Request to download file from S3: bucket='${s3File.bucket}', key='${s3File.key}'")
        val allBytes = withContext(Dispatchers.IO) {
            importer.fetchObjectContent(s3File.bucket, s3File.key).readAllBytes()
        }

        logger.info("Starting CSV file parsing")
        val content = processContent(allBytes, s3File.key)

        if (content.isNotEmpty()) {
            logger.info("Saving objects to demand storage: total ${content.size} objects")
            demandRepository.insertDemandInBatch(content)
        }
    }

    override suspend fun name(): String = "Forecast Packaging SQS Handler"

    @VisibleForTesting
    suspend fun processContent(content: ByteArray, fileName: String): List<DemandData> {
        val file = ForecastPackagingFile(
            ProcessFile.processFileContent(
                fileName,
                content,
                listOf(','),
                fileTypeDescriptor = FileTypeDescriptor(
                    ForecastPackagingParsedFile.columns,
                    ::ForecastPackagingParsedFile
                ),
                violationHandlersConfig = ViolationHandlersConfig(
                    preViolationHandlers = ViolationHandlersPerFile.createInstances(),
                ),
            ),
        )

        if (file.processFileResult.hasSevereViolations()) {
            val message = file.processFileResult.violations.joinToString { it.message }
            logger.error(
                "Error while processing the forecast packaging file $fileName, because: $message.",
            )
        } else if (file.processFileResult.violations.isNotEmpty()) {
            val message = file.processFileResult.violations.joinToString { it.message }
            logger.warn(
                "Warning while processing the forecast packaging file $fileName, because: $message.",
            )
        }
        return if (file.processFileResult.run { !hasSevereViolations() && violations.isEmpty() }) {
            file.toDemandData()
        } else {
            emptyList()
        }
    }

    private suspend fun ForecastPackagingFile.toDemandData(): List<DemandData> {
        val dateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm")
        val dateFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy")
        val file = this.processFileResult.parsedFile
        return file?.data
            ?.mapNotNull {
                val dcCode = it[ForecastPackagingParsedFile.DC_HEADER]
                val shortSkuSpec = skuInputService.getSkuId(dcCode, it[ForecastPackagingParsedFile.SKU_CODE_HEADER])

                shortSkuSpec?.let { skuSpec ->
                    DemandData(
                        skuId = skuSpec.id,
                        dcCode = dcCode,
                        date = LocalDate.parse(it[ForecastPackagingParsedFile.PRODUCTION_DATE_HEADER], dateFormatter),
                        recordTimestamp = LocalDateTime.parse(
                            it[ForecastPackagingParsedFile.FORECAST_DATE_HEADER],
                            dateTimeFormatter
                        ),
                        quantity = SkuQuantity.fromBigDecimal(
                            BigDecimal(it[ForecastPackagingParsedFile.QUANTITY_HEADER]),
                            skuSpec.uom
                        ),
                        substitutedInQty = null,
                        substitutedOutQty = null,
                        consumptionDetails = null
                    )
                }
            }
            ?.groupBy { Triple(it.skuId, it.date, it.dcCode) }
            ?.map { (_, value) ->
                val targetDemand = value.first()
                val totalQuantity = value
                    .map { it.quantity.getValue() }
                    .fold(BigDecimal.ZERO) { acc, quantVal -> acc + quantVal }
                targetDemand.copy(
                    quantity = SkuQuantity.fromBigDecimal(
                        totalQuantity,
                        targetDemand.quantity.unitOfMeasure
                    )
                )
            } ?: emptyList()
    }

    companion object : Logging

    data class ForecastPackagingFile(
        val processFileResult: ProcessFileResult
    )
}
