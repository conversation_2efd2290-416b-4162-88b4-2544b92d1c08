package com.hellofresh.cif.demand

import com.google.common.util.concurrent.ThreadFactoryBuilder
import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.AbstractConfigurationLoader
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.demand.deserializer.SkuDemandForecastKeySerde
import com.hellofresh.cif.demand.deserializer.SkuDemandForecastValSerde
import com.hellofresh.cif.demand.deserializer.WeekSkuInventoryDemandForecastKeySerde
import com.hellofresh.cif.demand.deserializer.WeekSkuInventoryDemandForecastValSerde
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.kafka.ConsumerProcessorConfig
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategy
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategyType.LOG_ERROR_IGNORE
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.kafka.serde.ProtoDeserializer
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.shutdown.shutdownHook
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.skuSpecificationLib.repo.SkuSpecificationRepositoryImpl
import com.hellofresh.proto.stream.ye.demand.recipeDemandForecast.v3.RecipeDemandForecastKey
import com.hellofresh.proto.stream.ye.demand.recipeDemandForecast.v3.RecipeDemandForecastVal
import java.util.concurrent.Executors
import kotlin.time.Duration
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.common.serialization.Deserializer
import org.apache.logging.log4j.kotlin.Logging

private const val HTTP_PORT = 8081
const val TOPIC_NAME = "public.demand.sku-demand-forecast.v2"
const val US_TOPIC_NAME = "public.ordering.sku-inventory-demand-forecast.week-based.v1"
const val YF_TOPIC_NAME = "public.ye.demand.recipe-demand-forecast.v3"
private val logger = (object : Logging {}).logger

data class Topic<K, V>(
    val name: String,
    val keyDeserializer: Deserializer<K>,
    val valDeserializer: Deserializer<V>
)

private const val DEFAULT_POLL_INTERVAL_MS = 20

@Suppress("LongMethod")
suspend fun main() {
    val meterRegistry = createMeterRegistry()
    StatusServer.run(meterRegistry, HTTP_PORT)

    val topic = Topic(
        name = TOPIC_NAME,
        keyDeserializer = SkuDemandForecastKeySerde().deserializer(),
        valDeserializer = SkuDemandForecastValSerde().deserializer(),
    )

    val usTopic = Topic(
        name = US_TOPIC_NAME,
        keyDeserializer = WeekSkuInventoryDemandForecastKeySerde().deserializer(),
        valDeserializer = WeekSkuInventoryDemandForecastValSerde().deserializer(),
    )

    val yfTopic = Topic(
        name = YF_TOPIC_NAME,
        keyDeserializer = ProtoDeserializer<RecipeDemandForecastKey>(),
        valDeserializer = ProtoDeserializer<RecipeDemandForecastVal>(),
    )
    val parallelism = ConfigurationLoader.getIntegerOrDefault("parallelism", 1)
    val metricsDSLContext = DBConfiguration.jooqMasterDslContext(parallelism, meterRegistry)
    val skuSpecificationService = SkuSpecificationService(
        meterRegistry,
        SkuSpecificationRepositoryImpl(metricsDSLContext),
    )
    val dcConfiguration = DcConfigService(meterRegistry, DcRepositoryImpl(metricsDSLContext))

    UKPackagingSQSListener.launch(meterRegistry)
    ForecastPackagingSQSListener.launch(meterRegistry)

    val demandTasks = runProcessor(
        ConfigurationLoader,
        meterRegistry,
        topic,
        { dsl, _, _ -> PerformUpsert(dsl) },
        skuSpecificationService,
        dcConfiguration,
        metricsDSLContext,
    )
    val usDemandTasks = ConfigurationLoader.withProfile("us") {
        runProcessor(
            it,
            meterRegistry,
            usTopic,
            { dsl, _, _ -> PerformUsDemandUpsert(dsl) },
            skuSpecificationService,
            dcConfiguration,
            metricsDSLContext,
        )
    }
    val yfDemandTasks = ConfigurationLoader.withProfile("yf") {
        runProcessor(
            it,
            meterRegistry,
            yfTopic,
            { dsl, _, _ -> YfPerformUpsert(dsl, skuSpecificationService, dcConfiguration) },
            skuSpecificationService,
            dcConfiguration,
            metricsDSLContext,
        )
    }

    yfDemandTasks.awaitAll().let {
        logger.warn("Exiting YF Demand consumer")
    }

    demandTasks.awaitAll().let {
        logger.warn("Exiting the Demand consumer")
    }

    usDemandTasks.awaitAll().let {
        logger.warn("Exiting US Demand consumer")
    }
}

@Suppress("LongParameterList")
suspend fun <K, V> runProcessor(
    configLoader: AbstractConfigurationLoader,
    meterRegistry: HelloFreshMeterRegistry,
    topic: Topic<K, V>,
    upsert: (
        MetricsDSLContext,
        SkuSpecificationService?,
        DcConfigService?
    ) -> suspend (ConsumerRecords<K, V>) -> Unit,
    skuSpecificationService: SkuSpecificationService?,
    dcConfigService: DcConfigService?,
    metricsDSLContext: MetricsDSLContext,
): List<Deferred<Unit>> {
    val parallelism = configLoader.getIntegerOrDefault("parallelism", 1)
    val pollTimeout = Duration.parse(configLoader.getStringOrFail("poll.timeout"))
    val pollInterval = configLoader.getIntegerOrDefault("poll.interval_ms", DEFAULT_POLL_INTERVAL_MS).toLong()
    val processTimeout = Duration.parse(configLoader.getStringOrFail("process.timeout"))
    val consumerProcessorConfig = ConsumerProcessorConfig(
        configLoader.loadKafkaConsumerConfigurations() +
            mapOf(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG to "false"),
        topic.keyDeserializer,
        topic.valDeserializer,
        listOf(topic.name),
    )
    val coroutineDispatcher = Executors.newFixedThreadPool(
        parallelism,
        ThreadFactoryBuilder().setNameFormat("producer-thread-%d").build(),
    ).asCoroutineDispatcher()

    val pollConfig = PollConfig(pollTimeout, pollInterval, processTimeout)

    return (0 until parallelism).map {
        CoroutineScope(coroutineDispatcher).async {
            CoroutinesProcessor(
                pollConfig = pollConfig,
                consumerProcessorConfig = consumerProcessorConfig,
                meterRegistry = meterRegistry,
                process = upsert(metricsDSLContext, skuSpecificationService, dcConfigService),
                recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                    meterRegistry,
                    "demand_processor_write_failure",
                ),
                handleDeserializationException = DeserializationExceptionStrategy.create(
                    LOG_ERROR_IGNORE,
                    meterRegistry,
                ),
            ).also {
                shutdownHook(it)
                StartUpChecks.add(it)
                HealthChecks.add(it)
            }.run()
        }
    }
}
