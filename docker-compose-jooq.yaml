---
version: '3.8'

services:
  postgres-jooq-cif:
      image: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/supply-automation-postgres:15.5
      container_name: postgres-jooq-cif
      ports:
        - '${INVENTORY_DB_JOOQ_PORT:-5551}:5432'
      healthcheck:
        test: [ "CMD-SHELL",
                "pg_isready -q -U cif -d inventory
                 && psql -c '\\x' -c 'SELECT (count(*) > 0) as flyway FROM flyway_schema_history' -d inventory -U cif --csv -q
                 | grep 'flyway,t'
                "
        ]
        interval: 1s
        timeout: 2s
        retries: 5
      environment:
        LC_ALL: C.UTF-8
        POSTGRES_USER: ${INVENTORY_DB_JOOQ_USER:-cif}
        POSTGRES_PASSWORD: ${INVENTORY_DB_JOOQ_PASSWORD:-123456}
        POSTGRES_DB: inventory

  db-migration-jooq-cif:
    image: flyway/flyway:9.22
    container_name: db-migration-jooq-cif
    depends_on:
      - postgres-jooq-cif
    command: -locations=filesystem:/flyway/sql -connectRetries=60 -connectRetriesInterval=1 -placeholders.cifReadonlyPassword="'123456'" migrate
    volumes:
      - ./inventory-db/src/main/resources/migrations:/flyway/sql/migration
    environment:
      FLYWAY_URL: '**************************************************=${INVENTORY_DB_JOOQ_USER:-cif}&password=${INVENTORY_DB_JOOQ_PASSWORD:-123456}'
      FLYWAY_USER: ${INVENTORY_DB_JOOQ_USER:-cif}
      FLYWAY_PASSWORD: ${INVENTORY_DB_JOOQ_PASSWORD:-123456}
      FLYWAY_DRIVER: org.postgresql.Driver
