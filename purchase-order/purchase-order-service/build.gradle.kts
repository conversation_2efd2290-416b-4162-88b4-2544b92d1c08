plugins {
    id("com.hellofresh.cif.application-conventions")
    alias(libs.plugins.jooq)
}

description = "Process Purchase Order from Kafka topics"
group = "$group.purchaseOrder"

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(projects.distributionCenterLib)
    implementation(projects.skuSpecificationLib)
    implementation(projects.lib)
    implementation(projects.lib.db)
    implementation(projects.lib.featureflags)
    implementation(projects.lib.models)
    implementation(projects.dateUtilModels)
    implementation(projects.skuModels)

    implementation(libs.protobuf.grpc)
    testImplementation(libs.mockk)
    testImplementation(testFixtures(projects.distributionCenterModels))
    testImplementation(testFixtures(projects.lib.featureflags))
    testImplementation(testFixtures(projects.skuModels))
    testImplementation(projects.libTests)
    testImplementation(libs.testcontainers.core)
    testImplementation(projects.lib.db)
}

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.JavaGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        includes = "purchase_order|purchase_order_sku|advanced_shipping_notice|advanced_shipping_notice_sku|purchase_order_status|uom"
                        inputSchema = "public"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    target.apply {
                        packageName = "${group.toString().lowercase()}.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}
