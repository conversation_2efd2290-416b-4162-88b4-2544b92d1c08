# CSKU Inventory Forecast
[![Roll-out](https://github.com/hellofresh/csku-inventory-forecast/actions/workflows/cd.yaml/badge.svg)](https://github.com/hellofresh/csku-inventory-forecast/actions/workflows/cd.yaml)

Kafka pipeline to calculate the Culinary SKU Inventory Forecast (CIF) that is
then made available via
the [Systematic Inventory Visibility (SIV) dashboard](https://operations.hellofresh.com/siv/stock-management?consumptionAhead=&dCCode=VE&market=DE&page=1&weeklyView=false&weeks=2022-W49).

- [Architecture](#architecture)
- [ADR](#architecture-decision-record)
- [Confluence](https://hellofresh.atlassian.net/wiki/spaces/CIF/pages/3617522440/SIV+Simplified)
- [Jira](https://hellofresh.atlassian.net/browse/CIF)
- [WhiteSource](https://saas.whitesourcesoftware.com/Wss/WSS.html#!project;id=2788236) (HelloFresh Security Checker)
- [FeatureFlags](#feature-flags)
- **Slack**
  - [General](https://hellofresh.slack.com/messages/proj-csku-inventory-forecast)
  - [Team](https://hellofresh.slack.com/messages/proj-csku-inventory-forecast-standup)
  - [Releases](https://hellofresh.slack.com/messages/proj-csku-inventory-forecast-releases)
  - [Alerts](https://hellofresh.slack.com/archives/C01NEG7HADS)
  - [Alerts](https://hellofresh.slack.com/archives/C02TG72KBL1)
- **GitHub Actions**
  - [CI](https://github.com/hellofresh/csku-inventory-forecast/actions/workflows/ci.yaml)
  - [CD](https://github.com/hellofresh/csku-inventory-forecast/actions/workflows/cd.yaml)
- **Live**
  - [Grafana](https://grafana.live-k8s.hellofresh.io/d/d229Q85Mk/csku-inventory-forecast)
  - [Loki](https://prdhellofresh.grafana.net/explore?orgId=1&left=%7B%22datasource%22:%22DbAZrmOVz%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22datasource%22:%7B%22type%22:%22loki%22,%22uid%22:%22DbAZrmOVz%22%7D,%22editorMode%22:%22code%22,%22expr%22:%22%7Bsquad%3D%5C%22inventory%5C%22,%20app%3D~%5C%22cif.%2A%5C%22%7D%22,%22queryType%22:%22range%22,%22legendFormat%22:%22%22%7D%5D,%22range%22:%7B%22from%22:%221681974691512%22,%22to%22:%221681974991512%22%7D%7D)
  - [SLO](#slos)
- **Staging**
  - [Grafana](https://grafana.staging-k8s.hellofresh.io/d/I3G9tvOGy/csku-inventory-forecast-dev)
  - [Loki](https://prdhellofresh.grafana.net/explore?orgId=1&left=%7B%22datasource%22:%22DbAZrmOVz%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22datasource%22:%7B%22type%22:%22loki%22,%22uid%22:%22DbAZrmOVz%22%7D,%22editorMode%22:%22code%22,%22expr%22:%22%7Bsquad%3D%5C%22inventory%5C%22,%20app%3D~%5C%22cif.%2A%5C%22%7D%22,%22queryType%22:%22range%22,%22legendFormat%22:%22%22%7D%5D,%22range%22:%7B%22from%22:%221681974691512%22,%22to%22:%221681974991512%22%7D%7D)

[Components block diagrams](https://hellofresh.atlassian.net/wiki/spaces/CIF/pages/2995128386/CSKU-Inventory-Forecast)

## Architecture

### Architecture decision record

See the [Authenticate Integration ADRs](./docs/architecture/decisions).

We want to keep track of our functional and non-functional decisions that would impact this project architecture. To achieve that, we have chosen the
Architectural Decision Record ([ADR](https://adr.github.io/)), which is a document that captures an important architectural decision made along with its context
and consequences.

If you want to create a new ADR, please install the [adr-tools](https://github.com/npryce/adr-tools), add a new ADR via command-line `adr new <Title>`, and
raise a pull request.

## Distribution centers management

We have introduced the distribution center management API [distribution-center-api service] to CREATE, UPDATE, DELETE DC's, whenever a new DC has to be
created then POST API could be used `https://operations.hellofresh.com/gw/scm/cif/distribution-center/EU` to create the
dc in cif config/distribution_center database table, which then will be published to `csku-inventory-forecast.intermediate.distribution-center.v*` topic
by a scheduled poller,and consumed by benthos [distribution-center-resources] to persist in the dc_config table finally.

## Development

You need a [JDK](#jdk) (see [`.java-version`](.java-version)) and afterwards you
can check out the code to a place of your liking and run:

```shell
./gradlew build
./gradlew idea
```

Get yourself a copy of [IntelliJ], Community or Ultimate does not matter, and
import the project.

### Code generation
After the [ADR decision](./docs/architecture/decisions/0002-generate-dtos-from-the-openapi-definition.md) we now use an OpenApi kotlin generator
to generate DTOs for use in the API presentation layer. The gradle task to generate the models is `./gradlew openApiGenerate`
and is dependent on the compilation task. Importantly, the generated models are not checked in the source tree (see ADR).


> It is important to run the Gradle commands before importing because of the
> Avro Gradle plugin, otherwise IntelliJ might not recognize the generated code
> correctly. This is a problem of the plugin, not IntelliJ.

### JOOQ
This project uses the [JOOQ](https://www.jooq.org/) library to manage entities from the persistence layer.

In order to generate the JOOQ metadata for all modules, run the following command:
```bash
./gradlew check
```
Jooq Generated classes are located in the build/ module directories and should not be checked into the source tree.
Note that updates to the DB schema will be immediately reflected in the code.

## Contributing

We recommend that you install the [detekt-intellij-plugin] and use our
[detekt config](.detekt.yaml), this ensures that you will directly be notified
about any issues that would otherwise fail in CI. You can also autoformat your
code:

```shell
./gradlew detektFormat
```

## Deployment

Whenever you added a new application and want to deploy it you have to extend
the following file:

- [new application modules](https://github.com/hellofresh/csku-inventory-forecast/blob/master/.github/config/modules)

## Partial deployments

Partial deployment is achieved by using the hash of the docker images called
`jib-image.digest` - so if hash is not changed that means the docker image is the
same and we won't redeploy. In our pipeline, we build all the projects on every
merge to master. It includes the build of docker images(using JIB) and generation
of helm charts. The very important trick is to copy the digest of built docker
image into the helm chart see [here](https://github.com/hellofresh/csku-inventory-forecast/blob/6943eef672253ef3b16e51d4e433a80c81152c3e/build.gradle.kts#L28).
The digest value replaces `tag` in helm chart -
see [./ci/helm-chart-gen.sh](https://github.com/hellofresh/csku-inventory-forecast/blob/b748349fe088d060ffb4a1ae50bcca0e5b2453fe/ci/helm-chart-gen.sh#L28).
It also means that the `tag` value from values files is not used.

## Running helm locally

Most likely, you won't need this instruction, but it also serves for educational
purpose here to demonstrate how it works on CI.

```shell
# copies content of projects `<specific app>/src/main/helm` folder to `./build` directory
./gradlew generateHelmChart

# install helm@v3 and helm-chart-gen plugin - can copy binary from https://github.com/hellofresh/helm-chart-gen/releases
# (on linux it expected to be here ~/.local/share/helm/plugins/helm-chart-gen/bin/chart-gen)

# very important step(nothing will work without it), because one of the artifacts this task provides is `jib-image.digest`
# which is required in the next step. BTW it's also important that a built images are available on ECR.
make docker-build

# applies hellofresh Helm plugin(which equip charts with templates) and change app version to `jib-image.digest` value
./ci/helm-chart-gen.sh

# deploy to staging or live
helm upgrade cif-calculator ./build/helm/chart/cif-calculator -f build/helm/chart/cif-calculator/values-staging.yaml --cleanup-on-fail --install --namespace scm
```

## Tests

Use Gradle as follows or IntelliJ:

```shell
./gradlew check
```

## Aiven QA

Performing a final quality assurance before deploying a processor is a good idea
but there are a lot of things to watch out for. Most importantly are:

1. Do not use an ACL with admin privileges because you would start creating
   resources (e.g. topics).
2. Disable everything that would create resources (e.g. changelogs for your
   state stores).
3. Do not use the version of your application but rather something random so
   that you are not committing offsets that would later be picked up when you
   run the application.

All of these things are prepared for you if you enable the `qa` profile with
either `staging` or `live` as your tier. All that is left then is that you set
the correct password for the QA user, you can get them from Vault:

- [QA password staging](https://vault.secrets.hellofresh.io/ui/vault/secrets/staging%2Fkey-value/kv/kafka%2Fcsku-inventory-forecast/details?namespace=services%2Fcsku-inventory-forecast)
- [QA password live](https://vault.secrets.hellofresh.io/ui/vault/secrets/live%2Fkey-value/show/kafka-credentials-qa?namespace=services%2Fcsku-inventory-forecast)

## End-to-end

To find detailed information follow [end-to-end-test/readme](./mvp/end-to-end-test/README.md)

## Local Environment

Make sure to provide following secret values in env - values can be found in Vault - or just ask you teammate to share:

Please note that
- `HF_KAFKA_PASSWORD_LIVE` is reused as `HF_KAFKA_PASSWORD_READONLY_LIVE` and `HF_AIVEN_PASSWORD`,
- `HF_KAFKA_PASSWORD_STAGING` is reused as `HF_KAFKA_PASSWORD_READONLY_STAGING`.
```shell
export HF_KAFKA_TRUSTSTORE_PASSWORD=<https://vault.secrets.hellofresh.io/ui/vault/secrets/live%2Fkey-value/kv/kafka/details?namespace=services%2Fcsku-inventory-forecast&version=4>
export HF_KAFKA_PASSWORD_LIVE=<https://vault.secrets.hellofresh.io/ui/vault/secrets/live%2Fkey-value/kv/kafka/details?namespace=services%2Fcsku-inventory-forecast&version=4>
export HF_KAFKA_PASSWORD_READONLY_LIVE=<https://vault.secrets.hellofresh.io/ui/vault/secrets/live%2Fkey-value/kv/kafka/details?namespace=services%2Fcsku-inventory-forecast&version=4>
export HF_KAFKA_PASSWORD_STAGING=<https://vault.secrets.hellofresh.io/ui/vault/secrets/staging%2Fkey-value/kv/kafka%2Fcsku-inventory-forecast/details?namespace=services%2Fcsku-inventory-forecast&version=12>
export HF_KAFKA_PASSWORD_READONLY_STAGING=<https://vault.secrets.hellofresh.io/ui/vault/secrets/staging%2Fkey-value/kv/kafka%2Fcsku-inventory-forecast/details?namespace=services%2Fcsku-inventory-forecast&version=12>
export HF_AIVEN_PASSWORD=<https://vault.secrets.hellofresh.io/ui/vault/secrets/live%2Fkey-value/kv/kafka/details?namespace=services%2Fcsku-inventory-forecast&version=4>
```

It is possible to deploy all services and their dependencies at once in a local Docker cluster. You will need to run

```shell
make local-up-with-data
```

This will build docker images for all services, push them to the local docker registry, start core components
(e.g Kafka broker, databases, etc.) and, after all container dependencies are healthy, start all CIF services.

Once you finished using the local environment, you can simply run the following command to have all containers
destroyed, and their orphaned volumes removed:

```shell
make local-down
```

_Kafdrop_ is available at <http://localhost:19000/> - for browsing topics, reading messages, etc.
and also kafkacat(running in docker container) at your disposal: `docker-compose exec kafkacat kafkacat -b broker:9092 -L`.

Environment variables:

```shell
HF_KAFKA_BOOTSTRAP_SERVERS=localhost:29092;HF_PROFILES=live,private;HF_TIER=local;HF_SCHEMA_REGISTRY_URL=https://kafka-live-hellofresh-live.aivencloud.com:23411;HF_AIVEN_PASSWORD=<https://vault.hellofresh.io/ui/vault/secrets/aiven/show/live/credentials/csku-inventory-forecast>
```

For calculator application `HF_PROFILES` variable should be with additional `production-mode` or `pre-production-mode`.

## Mirroring data with Kafka Mirror Maker

1. Mirror data from all CIF topics (i.e. internal and external) to gain visibility.

    ```shell
    make kafka-mirror-all env=live
    ```

   This will mirror data from all topics until `Ctrl+C` is pressed, keeping all topics in sync.

2. QA code currently only available locally (i.e. in development) using staging/live data.

    ```shell
    make kafka-mirror-external env=live
    ```

   This will mirror data from all topics until `Ctrl+C` is pressed, keeping all topics in sync.

   This approach only mirrors external topics so CIF services in development can use them as input, process and
   generate its own data, given the developers a way to verify that code changes are working as intended.

3. It is also possible to filter data while mirroring. If any of the key or value contains the value, we mirror the record

    ```shell
        # e.g. make kafka-mirror topic="rawevents.culinarysku.dach" filter="ab812d67-d787-479e-83b2-f6d7d8595f85 SPI-00-90489-5" env=live
        make kafka-mirror topic=<topic> filter="<string>" env=live
    ```

**Known issue with Mirror Maker**

Mirror Maker creates a consumer group when consuming from the source cluster. This means :-

1. Between local cluster restarts we will consume only newer updates. This means we cannot QA on the older data.
1. Mirroring by one instance will block the other instance from consuming messages, or might get the message from a newer offset.
2. We each could use different consumer group, but that means creating too many consumer groups at the cluster.

_Workarounds_

1. We can `docker commit` the image after mirroring and then share it with the team.
2. We can have a job to take periodic snapshots of the cluster.
3. We could have a dev cluster where we run the Mirror Maker service to keep it in sync with the kafka cluster.

## Using kafkacat

A script is provided to inject authentication and avro configuration.

Use the script as -

```shell
make kafkacat topic="rawevents.culinarysku.dach" env=local
```

It is also possible to specify broker address in make run

```shell
make kafkacat topic="rawevents.culinarysku.dach" env=staging
```

Note that the script -

- Apply credentials if there is `aiven` in the `BROKER_ADDRESS`
- Apply avro deserializers if there is `rawevents` in the `topic_name`

## Data Sink

To get more insight around the data transiting through the many topics we currently have for CIF, it is possible
to have data from topics dump into a database. To achieve this you can do the following:

```shell
make sink-up
make kafka-mirror-all
```

By doing so all data from all configured topics will be dumped into a local instance of a Postgres database.

Please note that all prerequisites seen on [Mirroring data with Kafka Mirror Maker](#mirroring-data-with-kafka-mirror-maker)
still applies for the `make kafka-mirror-all` command.

It is also possible to sink a specific topic by doing:

```shell
make sink-up
make kafka-mirror topic="<topic_name>"
```

## Scripting

`:mvp:scripts` contain kotlin scripts to automate common debugging/data analysis task required by the dev team.

_Instructions to use:_

1. Scripts should be quick to write.
2. Scripts shall be executed from local and only on local environment.
3. Execute scripts either from the IDE(right click the file and then run) or from ci - `kotlinc -script foo.kts <args>`

## JDK

We generally recommend either [AdoptOpenJDK] or [Azul Zulu] but any JDK will
work. Adopt is a community project that provide free OpenJDK builds and Azul is
a free commercial offering that passes all [TCK] tests.

We also recommend [jEnv] for automatic JDK selection based on `.java-version`
files. You can also use it along [SDKMAN!] if you use it. You also want to
install the [Mad-jEnv IntelliJ Plugin] for perfect integration. Refer to the
documentation of the tools for more information.

These tools are going to improve your JVM workflow in general on not only for
working with this project, promise. 😊

### Creating Resources with Terraform

Application specific external resources are grouped under the folder `/terraform`.
To apply changes on such resources we have a dedicated pipeline to help us through it:

<https://github.com/hellofresh/csku-inventory-forecast/actions/workflows/terraform-apply.yaml>

Please note that we currently have two jobs:

- **pr-terraform-plan** - triggered automatically for each PR. The PR can still be merged
  regardless of the outcome of this job.
- **master-terraform-apply** - triggered manually when required. There's no confirmation
  prompt during this process, which means it will run plan and apply it regardless of
  the planning outcome.

## Inventory database

Inventory DB provides all the datasets which is used as input for calculations.

## Composite types vs JSONB

Initially, it was thought that we will benefit from using composite types and arrays from PostgreSQL
to store lists of objects like inventory items. The main advantage of it would be having
a static schema for values. But that's also a disadvantage if we want to change something
in schema, either add or remove columns, without introducing a breaking change.
Obviously, removing columns would violate backward-compatability - old clients won't be able
to write data. But also adding a new column to the structure will require to create a new type
and additional column in the table. Possible scenario(some columns is hidden for simplicity):

```sql
CREATE TYPE inventory_item AS
(
  qty integer
);
CREATE TABLE inventory
(
  items inventory_item[] NOT NULL
);
INSERT INTO inventory
VALUES (array [row (10)::inventory_item]); -- works
ALTER TYPE inventory_item ADD ATTRIBUTE expiry_date date; -- previous insert will fail after this alter
```

Using JSONB instead of composite types gives more flexibility and allows us to introduce new fields
painlessly and have rolling updates, but moving the complexity to the readers(mainly the Calculator).
All tables have a similar structure - three columns `(sku_id, dc_code, date)` as primary key, `value` contains
value of incoming kafka message, additional timestamp columns.

## DB migrations

We use `flyway` to manage database migrations.
To run migrations locally you can:

```bash
ci/scripts/migrate.sh
```

which is automated in local dev environment, but for staging and live there is a
[dedicated job on CI](https://github.com/hellofresh/csku-inventory-forecast/actions/workflows/ci.yaml) that can be triggered
manually.


[AdoptOpenJDK]: https://adoptopenjdk.net/

[Azul Zulu]: https://www.azul.com/downloads/zulu/

[detekt-intellij-plugin]: https://github.com/detekt/detekt-intellij-plugin

[IntelliJ]: https://www.jetbrains.com/idea/

[jEnv]: https://www.jenv.be/

[Mad-jEnv IntelliJ Plugin]: https://plugins.jetbrains.com/plugin/12761-mad-jenv

[SDKMAN!]: https://sdkman.io/

[TCK]: https://en.wikipedia.org/wiki/Technology_Compatibility_Kit


## Feature Flags

Feature flags logic implemented using ([Statsig](https://console.statsig.com/1LYjXY4aLTlnkxLr1pYyCv/gates?Target+Application=4V6HJdDGRWRX1FfAWU1zT7))<br>

## SLOs

| Service                                             |
|-----------------------------------------------------|
| [forecast-api][forecast-api]                        |
| [distribution-center-api][distribution-center-api]  |

[forecast-api]: https://grafana.live-k8s.hellofresh.io/d/slo-per-service/slo-per-service?orgId=1&var-tribe=procurement&var-service=cif-forecast-api&var-slo=All&var-sli_window=6h&var-min_burning_rate=0

[distribution-center-api]: https://grafana.live-k8s.hellofresh.io/d/slo-per-service/slo-per-service?orgId=1&var-tribe=procurement&var-service=cif-distribution-center-api&var-slo=All&var-sli_window=6h&var-min_burning_rate=0
