import com.hellofresh.cif.safetystock.Configuration
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystock.model.SafetyStockMultiplier
import com.hellofresh.cif.safetystock.model.SkuRiskRating
import java.math.BigDecimal
import java.util.UUID

fun SafetyStock.Companion.random(dcCode: String = "DC", skuId: UUID = UUID.randomUUID(), week: String = "2024-W21") =
    SafetyStock(
        dcCode = dcCode,
        skuId = skuId,
        week = week,
        value = kotlin.random.Random.nextLong(),
        configuration = Configuration(
            riskMultiplier = BigDecimal.valueOf(kotlin.random.Random.nextLong()),
            skuRiskRating = SkuRiskRating.entries.random(),
            bufferPercentage = BigDecimal.valueOf(kotlin.random.Random.nextLong()),
        ),
        strategy = setOf("WEEKS_COVERAGE", "TARGET_INVENTORY", "ALGORITHM_FORECASTVARIANCE").random()
    )

fun SafetyStockMultiplier.Companion.random(dcCode: String = "DC", skuId: UUID = UUID.randomUUID()) =
    SafetyStockMultiplier(
        dcCode = dcCode,
        skuId = skuId,
        skuRiskRating = SkuRiskRating.MEDIUM,
        targetWeek = 1,
        weekCover = 1,
        targetWeekMultiplier = BigDecimal(.23),
        weekCoverMultiplier = BigDecimal(.1433),
    )
