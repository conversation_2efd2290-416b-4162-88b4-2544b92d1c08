package com.hellofresh.cif.safetystock.model

import java.math.BigDecimal
import java.math.BigDecimal.ZERO
import java.util.UUID

data class SafetyStockMultiplier(
    val dcCode: String,
    val skuId: UUID,
    val skuRiskRating: SkuRiskRating,
    val targetWeek: Long,
    val weekCover: Long,
    val targetWeekMultiplier: BigDecimal,
    val weekCoverMultiplier: BigDecimal,
) {

    fun toKey() = SafetyStockMultiplierKey(skuId, dcCode, skuRiskRating, targetWeek, weekCover)

    companion object
}

data class SafetyStockMultiplierKey(
    val skuId: UUID,
    val dcCode: String,
    val skuRiskRating: SkuRiskRating,
    val targetWeek: Long,
    val weekCover: Long,
)

class SafetyStockMultipliers(safetyStockMultipliers: List<SafetyStockMultiplier>) {

    private val safetyStockMultipliersGrouped =
        safetyStockMultipliers.groupBy { it.dcCode }
            .mapValues { (_, values) ->
                values.groupBy { it.skuId }
                    .mapValues { (_, skuValues) ->
                        skuValues.groupBy { it.skuRiskRating }
                    }
            }

    val skus: Set<UUID>
        get() = safetyStockMultipliersGrouped.flatMap { it.value.keys }.toSet()

    val dcs: Set<String>
        get() = safetyStockMultipliersGrouped.keys

    fun getSafetyStockMultiplier(
        dc: String,
        skuId: UUID,
        skuRiskRating: SkuRiskRating,
        targetWeek: Long,
        weekCover: Long
    ): SafetyStockMultiplier =
        safetyStockMultipliersGrouped[dc]
            ?.get(skuId)
            ?.get(skuRiskRating)
            ?.firstOrNull { it.targetWeek == targetWeek && it.weekCover == weekCover }
            ?: SafetyStockMultiplier(dc, skuId, skuRiskRating, targetWeek, weekCover, DEFAULT_TARGET_WEED_RISK_MULTIPLIER, DEFAULT_WEEK_COVER_RISK_MULTIPLIER)

    companion object {
        val DEFAULT_TARGET_WEED_RISK_MULTIPLIER: BigDecimal = ZERO
        val DEFAULT_WEEK_COVER_RISK_MULTIPLIER: BigDecimal = ZERO
    }
}
