package com.hellofresh.cif.safetystock.model

import java.math.BigDecimal
import java.util.UUID

data class SafetyStockBuffer(
    val skuId: UUID,
    val dcCode: String,
    val week: String,
    val buffer: BigDecimal,
) {

    fun toKey() = SafetyStockBufferKey(skuId, dcCode, week)

    companion object
}

data class SafetyStockBufferKey(
    val skuId: UUID,
    val dcCode: String,
    val week: String,
)
