package com.hellofresh.cif.safetystock.repository

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.safetystock.SafetyStockConfiguration
import com.hellofresh.cif.safetystock.SafetyStockConfigurations
import com.hellofresh.cif.safetystock.model.SkuRiskRating
import com.hellofresh.cif.safetystocklib.schema.Tables.SAFETY_STOCK_CONF
import com.hellofresh.cif.safetystocklib.schema.enums.SkuRiskRating as SkuRiskRatingDb
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.future.asDeferred
import org.jooq.impl.DSL

class SafetyStockConfigurationRepository(private val metricsDSLContext: MetricsDSLContext) {

    suspend fun fetchSafetyStockConfigurations(distributionCenters: Set<DistributionCenterConfiguration>): SafetyStockConfigurations {
        if (distributionCenters.isEmpty()) {
            return SafetyStockConfigurations(emptyList())
        }

        val configurations = distributionCenters.groupBy {
            DcWeek(
                it.getLatestProductionStart(),
                it.productionStart,
            ).toString()
        }
            .map { (week, distributionCenters) ->
                fetchSafetyStockConfigurationsFromWeek(week, distributionCenters)
            }.awaitAll()
            .flatten()

        return SafetyStockConfigurations(configurations)
    }

    suspend fun fetchSafetyStockConfigurations(
        week: String,
        distributionCenter: DistributionCenterConfiguration
    ): SafetyStockConfigurations =
        SafetyStockConfigurations(
            fetchSafetyStockConfigurationsFromWeek(week, listOf(distributionCenter)).await(),
        )

    private fun fetchSafetyStockConfigurationsFromWeek(
        week: String,
        distributionCenters: List<DistributionCenterConfiguration>
    ): Deferred<List<SafetyStockConfiguration>> {
        val dcMap = distributionCenters.associateBy { it.dcCode }

        val safetyStockMinWeek = safetyStockMinFromWeekTable(dcMap.keys, week)

        return metricsDSLContext.withTagName("fetch-safety-stock-config")
            .select(
                SAFETY_STOCK_CONF.DC_CODE,
                SAFETY_STOCK_CONF.SKU_ID,
                SAFETY_STOCK_CONF.WEEK,
                SAFETY_STOCK_CONF.RISK_MULTIPLIER,
                SAFETY_STOCK_CONF.SKU_RISK_RATING,
            )
            .from(SAFETY_STOCK_CONF)
            .leftJoin(safetyStockMinWeek).on(
                SAFETY_STOCK_CONF.DC_CODE.eq(
                    safetyStockMinWeek.field(SAFETY_STOCK_CONF.DC_CODE),
                ),
                SAFETY_STOCK_CONF.SKU_ID.eq(
                    safetyStockMinWeek.field(SAFETY_STOCK_CONF.SKU_ID),
                ),
            )
            .where(
                SAFETY_STOCK_CONF.DC_CODE.`in`(dcMap.keys)
                    .and(
                        SAFETY_STOCK_CONF.WEEK.ge(
                            DSL.coalesce(
                                safetyStockMinWeek.field(SAFETY_STOCK_CONF.WEEK),
                                week,
                            ),
                        ),
                    ),
            )
            .fetchAsync()
            .thenApply { results ->
                results.mapNotNull { record ->
                    val dcCode = record[SAFETY_STOCK_CONF.DC_CODE]
                    dcMap[dcCode]?.let { dc ->
                        SafetyStockConfiguration(
                            dcCode = dcCode,
                            skuId = record[SAFETY_STOCK_CONF.SKU_ID],
                            productionWeek = ProductionWeek(
                                record[SAFETY_STOCK_CONF.WEEK],
                                dc.productionStart,
                                dc.zoneId,
                            ),
                            riskMultiplier = record[SAFETY_STOCK_CONF.RISK_MULTIPLIER],
                            skuRiskRating = mapToSkuRiskRating(record[SAFETY_STOCK_CONF.SKU_RISK_RATING]),
                        )
                    }
                }
            }.asDeferred()
    }

    private fun safetyStockMinFromWeekTable(dcCodes: Set<String>, requestedFromWeek: String) =
        metricsDSLContext.select(
            SAFETY_STOCK_CONF.DC_CODE,
            SAFETY_STOCK_CONF.SKU_ID,
            SAFETY_STOCK_CONF.WEEK,
        ).distinctOn(
            SAFETY_STOCK_CONF.DC_CODE,
            SAFETY_STOCK_CONF.SKU_ID,
        ).from(SAFETY_STOCK_CONF)
            .where(
                SAFETY_STOCK_CONF.DC_CODE.`in`(dcCodes)
                    .and(
                        SAFETY_STOCK_CONF.WEEK.le(requestedFromWeek),
                    ),
            ).asTable("safety_stock_min_week")

    companion object {

        fun mapToSkuRiskRating(skuRiskRating: SkuRiskRatingDb): SkuRiskRating =
            when (skuRiskRating) {
                SkuRiskRatingDb.LOW -> SkuRiskRating.LOW
                SkuRiskRatingDb.MEDIUM -> SkuRiskRating.MEDIUM
                SkuRiskRatingDb.MEDIUM_LOW -> SkuRiskRating.MEDIUM_LOW
                SkuRiskRatingDb.HIGH -> SkuRiskRating.HIGH
                SkuRiskRatingDb.CRITICAL -> SkuRiskRating.CRITICAL
            }
    }
}
