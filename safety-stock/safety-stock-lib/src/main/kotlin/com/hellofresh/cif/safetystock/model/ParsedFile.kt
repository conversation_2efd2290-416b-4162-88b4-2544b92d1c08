package com.hellofresh.cif.safetystock.model

import org.apache.commons.csv.CSVRecord

enum class FileType {
    SAFETY_STOCK_MULTIPLIER, SAFETY_STOCK_IMPORT, TARGET_SAFETY_STOCK_IMPORT
}

sealed interface ParsedFile {
    val columns: List<String>
    val data: List<Row>
    val pKey: List<String>
    fun data(records: List<CSVRecord>): List<Row> = records
        .map { csvRecord ->
            Row(
                recordNumber = csvRecord.recordNumber + 1,
                columns = columns.associateWith { csvRecord[it] },
            )
        }

    class SafetyStockMultiplier(records: List<CSVRecord>) : ParsedFile {
        override val columns = Companion.columns
        override val data = data(records)
        override val pKey: List<String> = listOf(
            SKU_HEADER,
            DC_HEADER,
            SKU_RISK_RATING_HEADER,
            TARGET_WEEK_HEADER,
            WEEK_COVER_HEADER,
        )

        companion object {
            const val SKU_HEADER = "sku"
            const val DC_HEADER: String = "dc"
            const val SKU_RISK_RATING_HEADER = "risk_index"
            const val TARGET_WEEK_HEADER = "tw"
            const val WEEK_COVER_HEADER = "wc"
            const val TARGET_WEEK_MULTIPLIER_HEADER = "tw_multiplier"
            const val WEEK_COVER_MULTIPLIER_HEADER = "wc_multiplier"

            val columns: List<String> = listOf(
                SKU_HEADER,
                DC_HEADER,
                SKU_RISK_RATING_HEADER,
                TARGET_WEEK_HEADER,
                WEEK_COVER_HEADER,
                TARGET_WEEK_MULTIPLIER_HEADER,
                WEEK_COVER_MULTIPLIER_HEADER,
            )
            val intColumnDataTypes = setOf(
                TARGET_WEEK_HEADER,
                WEEK_COVER_HEADER,
            )
        }
    }

    class SafetyStockImport(records: List<CSVRecord>) : ParsedFile {
        override val columns = Companion.columns
        override val data = data(records)
        override val pKey: List<String> = listOf(
            SKU_HEADER,
            DC_HEADER,
            WEEK_HEADER,
        )

        companion object {
            const val SKU_HEADER = "sku"
            const val DC_HEADER: String = "dc"
            const val WEEK_HEADER = "production_week"
            const val SAFETY_STOCK_HEADER = "safety_stock"

            val columns: List<String> = listOf(
                SKU_HEADER,
                DC_HEADER,
                WEEK_HEADER,
                SAFETY_STOCK_HEADER,
            )
            val doubleColumnDataTypes = setOf(
                SAFETY_STOCK_HEADER,
            )
        }
    }

    class TargetSafetyStockImport(records: List<CSVRecord>) : ParsedFile {
        override val columns = Companion.columns
        override val data = data(records)
        override val pKey: List<String> = listOf(
            SKU_HEADER,
            DC_HEADER,
            WEEK_HEADER,
        )

        companion object {
            const val SKU_HEADER = "sku_code"
            const val DC_HEADER: String = "dc_code"
            const val WEEK_HEADER = "production_week"
            const val STRATEGY = "strategy"
            const val SAFETY_STOCK_HEADER = "safety_stock"

            val columns: List<String> = listOf(
                SKU_HEADER,
                DC_HEADER,
                WEEK_HEADER,
                STRATEGY,
                SAFETY_STOCK_HEADER,
            )
            val doubleColumnDataTypes = setOf(
                SAFETY_STOCK_HEADER,
            )
        }
    }
}

data class Row(val recordNumber: Long, val columns: Map<String, String>) {
    operator fun get(name: String) = columns[name]!!
}
