package com.hellofresh.cif.safetystock.reader

import com.hellofresh.cif.safetystock.model.FileType
import com.hellofresh.cif.safetystock.model.ParsedFile
import com.hellofresh.cif.safetystock.model.ParsedFile.SafetyStockImport
import com.hellofresh.cif.safetystock.model.ParsedFile.SafetyStockMultiplier
import com.hellofresh.cif.safetystock.model.ParsedFile.TargetSafetyStockImport
import com.hellofresh.cif.safetystock.violations.ReaderViolation
import com.hellofresh.cif.safetystock.violations.ReaderViolation.DelimiterExtractionFailed
import com.hellofresh.cif.safetystock.violations.ReaderViolation.HeadersNotMatching
import com.hellofresh.cif.safetystock.violations.ReaderViolation.NotACsv
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.apache.commons.csv.CSVRecord
import org.apache.logging.log4j.kotlin.Logging

object CSVFileUploadReader : Logging {
    private val csvFormat: CSVFormat = CSVFormat.DEFAULT.builder()
        .setAllowMissingColumnNames(true)
        .setIgnoreHeaderCase(true)
        .setTrim(true)
        .build()

    @Suppress("SpreadOperator")
    fun toCsvRecords(byteArray: ByteArray, fileType: FileType): CsvFileUploadResult {
        val violations = mutableListOf<ReaderViolation>()
        var parsedFile: ParsedFile? = null

        return byteArray.inputStream().bufferedReader().use { reader ->

            runCatching {
                val delimiters = listOf(',', ';')
                val header = reader.readLine().replace("[^\\w.${delimiters.joinToString()}]".toRegex(), "")
                val delimiter = detectDelimiter(header, delimiters)

                delimiter?.let {
                    val columns = header.split(delimiter)
                    val records = CSVParser.parse(
                        reader,
                        csvFormat.builder()
                            .setDelimiter(delimiter)
                            .setHeader(*header.split(delimiter).toTypedArray())
                            .build(),
                    ).records
                    parsedFile = resolveParsedFileFromHeader(fileType, columns, records)
                        .also {
                            if (it == null) violations.add(HeadersNotMatching())
                        }
                } ?: run {
                    violations.add(DelimiterExtractionFailed())
                }
            }.onFailure { violations.add(NotACsv()) }
            CsvFileUploadResult(parsedFile, violations)
        }
    }

    private fun resolveParsedFileFromHeader(fileType: FileType, columns: List<String>, records: List<CSVRecord>): ParsedFile? =
        if (columns.isNotEmpty()) {
            when (fileType) {
                FileType.SAFETY_STOCK_MULTIPLIER -> {
                    if (columns.containsAll(SafetyStockMultiplier.columns)) {
                        SafetyStockMultiplier(records)
                    } else {
                        null
                    }
                }

                FileType.SAFETY_STOCK_IMPORT -> {
                    if (columns.containsAll(SafetyStockImport.columns)) {
                        SafetyStockImport(records)
                    } else {
                        null
                    }
                }

                FileType.TARGET_SAFETY_STOCK_IMPORT -> {
                    if (columns.containsAll(TargetSafetyStockImport.columns)) {
                        TargetSafetyStockImport(records)
                    } else {
                        null
                    }
                }
            }
        } else {
            null
        }

    private fun detectDelimiter(csvLine: String, delimiters: List<Char>): Char? =
        delimiters.map { it to csvLine.count { char -> char == it } }
            .filter { (_, count) -> count > 0 }
            .maxByOrNull { (_, count) -> count > 0 }
            ?.first
}

data class CsvFileUploadResult(
    val parsedFile: ParsedFile?,
    val errors: List<ReaderViolation>
)
