package com.hellofresh.cif.safetystock.violations

import com.hellofresh.cif.safetystock.model.ParsedFile
import com.hellofresh.cif.safetystock.model.ProcessFileResult
import com.hellofresh.cif.safetystock.model.ProcessFileResult.Invalid
import com.hellofresh.cif.safetystock.model.ProcessFileResult.Valid
import com.hellofresh.cif.safetystock.model.Row

class ProcessChain(private val violationHandlers: List<ViolationHandler>) {
    fun process(file: ParsedFile, fileName: String): ProcessFileResult {
        val violation = mutableListOf<Violation>()
        file.data.map { record ->
            violationHandlers.forEach { v ->
                val res = v.handle(ProcessingUnit(fileName, file, record))
                if (res !is NoViolation) {
                    violation.add(res)
                    return@map
                } else if (res is WarnViolation) {
                    violation.add(res)
                }
            }
        }
        return createProcessResult(
            parsedFile = file,
            violations = violation
                .groupBy { it.javaClass.simpleName }
                .values
                .map { it.first().add(it) },
        )
    }

    companion object {
        fun createProcessResult(
            parsedFile: ParsedFile,
            violations: List<Violation>,
        ) = if (violations.any { it !is NoViolation }) {
            Invalid(
                parsedFile = parsedFile,
                violations = violations,
            )
        } else {
            Valid(
                parsedFile = parsedFile,
                violations = violations.filterIsInstance<WarnViolation>().toList(),
            )
        }
    }
}

data class ProcessingUnit(val fileName: String, val file: ParsedFile, val record: Row)
