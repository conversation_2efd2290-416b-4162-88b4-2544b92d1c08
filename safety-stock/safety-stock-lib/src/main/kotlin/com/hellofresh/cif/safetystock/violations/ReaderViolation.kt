package com.hellofresh.cif.safetystock.violations

sealed class ReaderViolation {
    abstract val message: String
    data class HeadersNotMatching(
        override val message: String = "Header format error",
    ) : ReaderViolation()

    data class DelimiterExtractionFailed(
        override val message: String = "Delimiter extraction failed",
    ) : ReaderViolation()

    data class NotACsv(
        override val message: String = "Not matching CSV format",
    ) : ReaderViolation()
}
