package com.hellofresh.cif.safetystock.util

import java.io.ByteArrayInputStream
import java.io.IOException
import org.apache.parquet.io.InputFile
import org.apache.parquet.io.SeekableInputStream

class ByteArrayInputFile(private val data: ByteArray) : InputFile {
    override fun getLength(): Long =
        data.size.toLong()

    @Throws(IOException::class)
    override fun newStream(): SeekableInputStream =
        ByteArraySeekableInputStream(ByteArrayInputStream(data))
}
