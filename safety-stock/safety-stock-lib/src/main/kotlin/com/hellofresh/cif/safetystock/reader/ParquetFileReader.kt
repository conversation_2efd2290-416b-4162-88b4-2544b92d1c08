package com.hellofresh.cif.safetystock.reader

import com.hellofresh.cif.safetystock.util.ByteArrayInputFile
import org.apache.avro.generic.GenericRecord
import org.apache.parquet.avro.AvroParquetReader
import org.apache.parquet.hadoop.ParquetReader

class ParquetFileReader {

    fun readParquetFromByteArray(data: ByteArray): List<GenericRecord> {
        val inputFile = ByteArrayInputFile(data)
        val reader: ParquetReader<GenericRecord> = AvroParquetReader.builder<GenericRecord>(inputFile).build()

        return generateSequence { reader.read() }.toList()
    }
}
