package com.hellofresh.cif.safetystock.violations

sealed class Violation {
    // A violation message is returned if a violation is found
    abstract val message: String
    abstract var lineNumbers: MutableList<Int>

    fun add(violations: List<Violation>): Violation {
        lineNumbers = (lineNumbers + violations.flatMap { it.lineNumbers }).toSet().toMutableList()
        return this
    }

    // Headers in upload files not matching reference CSVs
    data class HeadersNotMatching(
        override val message: String = "Header format error",
    ) : SevereViolation()

    // Delimiter extraction from upload file headers failed
    data class DelimiterExtractionFailed(
        override val message: String = "Delimiter extraction failed",
    ) : SevereViolation()

    // File which does not match CSV format is uploaded
    data class NotACsv(
        override val message: String = "Not matching CSV format",
    ) : SevereViolation()

    // A blank cell is present under a column where other columns in that row have been completed
    data class BlankCell(
        val lineNumber: Int,
        override val message: String = "Blank cell",
    ) : SevereViolation(lineNumber)

    data class InvalidDataTypeFormat(
        val lineNumber: Int,
        override val message: String = "Invalid data type found in the uploaded file",
    ) : SevereViolation(lineNumber)

    class InvalidNumberOfColumns(
        private val lineNumber: Int,
        override val message: String = "Invalid number of columns",
    ) : SevereViolation(lineNumber)

    data class NegativeNumbers(
        val lineNumber: Int,
        override val message: String = "Negative target week or week cover",
    ) : SevereViolation(lineNumber)
}

sealed class NoViolation : Violation() {
    override val message: String = ""
    override var lineNumbers: MutableList<Int> = mutableListOf()
}

object EmptyNoViolation : NoViolation()

val noViolation = EmptyNoViolation

abstract class WarnViolation(lineNumber: Int? = null) : NoViolation() {
    override var lineNumbers = lineNumber?.let { mutableListOf(lineNumber) } ?: mutableListOf()
}

abstract class SevereViolation(lineNumber: Int? = null) : Violation() {
    override var lineNumbers = lineNumber?.let { mutableListOf(lineNumber) } ?: mutableListOf()
}
