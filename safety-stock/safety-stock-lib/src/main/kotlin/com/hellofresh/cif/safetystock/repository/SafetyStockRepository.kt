package com.hellofresh.cif.safetystock.repository

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.safetystock.Configuration
import com.hellofresh.cif.safetystock.SafetyStock
import com.hellofresh.cif.safetystocklib.schema.Tables.SAFETY_STOCKS
import com.hellofresh.cif.safetystocklib.schema.tables.records.SafetyStocksRecord
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.future.asDeferred
import kotlinx.coroutines.future.await

class SafetyStockRepository(private val metricsDSLContext: MetricsDSLContext) {
    suspend fun fetchSafetyStocks(minWeek: String, dcs: Set<String>): List<SafetyStock> =
        metricsDSLContext.withTagName("fetch-safety-stocks")
            .selectFrom(SAFETY_STOCKS)
            .where(
                SAFETY_STOCKS.DC_CODE.`in`(dcs)
                    .and(
                        SAFETY_STOCKS.WEEK.ge(
                            minWeek,
                        ),
                    ),
            )
            .fetchAsync()
            .thenApply { records -> records.map { it.toSafetyStock() } }
            .await()

    suspend fun fetchSafetyStocksFromLatestProductionWeek(distributionCenters: Set<DistributionCenterConfiguration>): List<SafetyStock> {
        if (distributionCenters.isEmpty()) {
            return emptyList()
        }

        val safetyStocks = distributionCenters.groupBy {
            DcWeek(
                it.getLatestProductionStart(),
                it.productionStart,
            ).toString()
        }
            .map { (week, distributionCenters) ->
                fetchSafetyStockFromWeek(week, distributionCenters.map { it.dcCode }.toSet())
            }.awaitAll()
            .flatten()

        return safetyStocks
    }

    private fun fetchSafetyStockFromWeek(
        week: String,
        dcCodes: Set<String>
    ): Deferred<List<SafetyStock>> =
        metricsDSLContext.withTagName("fetch-safety-stock-from-week")
            .selectFrom(SAFETY_STOCKS)
            .where(
                SAFETY_STOCKS.DC_CODE.`in`(dcCodes)
                    .and(
                        SAFETY_STOCKS.WEEK.ge(week),
                    ),
            )
            .fetchAsync()
            .thenApply { results ->
                results.map { it.toSafetyStock() }
            }.asDeferred()

    suspend fun fetchSafetyStock(
        week: String,
        dcCode: String
    ): List<SafetyStock> =
        metricsDSLContext.withTagName("fetch-safety-stock-by-week")
            .selectFrom(SAFETY_STOCKS)
            .where(
                SAFETY_STOCKS.DC_CODE.eq(dcCode)
                    .and(
                        SAFETY_STOCKS.WEEK.eq(week),
                    ),
            )
            .fetchAsync()
            .thenApply { results ->
                results.map { it.toSafetyStock() }
            }.await()

    private fun SafetyStocksRecord.toSafetyStock() =
        SafetyStock(
            dcCode = this[SAFETY_STOCKS.DC_CODE],
            skuId = this[SAFETY_STOCKS.SKU_ID],
            week = this[SAFETY_STOCKS.WEEK],
            value = this[SAFETY_STOCKS.SAFETY_STOCK],
            configuration = objectMapper.readValue<Configuration>(this[SAFETY_STOCKS.CONFIGURATION].data()),
            strategy = this[SAFETY_STOCKS.STRATEGY],
        )

    companion object {
        val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
