package com.hellofresh.cif.safetystock

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonInclude.Include
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.cif.safetystock.model.SkuRiskRating
import java.math.BigDecimal
import java.math.BigDecimal.ZERO
import java.util.UUID

const val DEFAULT_TARGET_SAFETY_STOCK_STRATEGY = "ALGORITHM_FORECASTVARIANCE"

data class SafetyStock(
    val dcCode: String,
    val skuId: UUID,
    val week: String,
    val value: Long,
    val configuration: Configuration,
    val strategy: String = DEFAULT_TARGET_SAFETY_STOCK_STRATEGY,
) {

    constructor(
        dcCode: String,
        skuId: UUID,
        week: String,
        value: Long,
        riskMultiplier: BigDecimal,
        skuRiskRating: SkuRiskRating,
        strategy: String,
    ) : this(
        dcCode,
        skuId,
        week,
        value,
        Configuration(riskMultiplier, skuRiskRating, ZERO),
        strategy,
    )

    fun toKey() = SafetyStockKey(dcCode, DcWeek(week), skuId)

    companion object
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
@JsonNaming(SnakeCaseStrategy::class)
data class Configuration(
    val riskMultiplier: BigDecimal,
    val skuRiskRating: SkuRiskRating,
    val bufferPercentage: BigDecimal,
)

fun SafetyStockConfiguration.toConfiguration() =
    Configuration(
        this.riskMultiplier,
        this.skuRiskRating,
        ZERO,
    )
