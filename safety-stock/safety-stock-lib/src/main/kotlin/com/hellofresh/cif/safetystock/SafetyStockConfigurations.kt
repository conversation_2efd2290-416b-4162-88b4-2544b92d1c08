package com.hellofresh.cif.safetystock

import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.safetystock.SafetyStockConfiguration.Companion.DEFAULT_RISK_MULTIPLIER
import com.hellofresh.cif.safetystock.SafetyStockConfiguration.Companion.DEFAULT_SKU_RISK_RATING
import com.hellofresh.cif.safetystock.model.SkuRiskRating
import com.hellofresh.cif.safetystock.model.SkuRiskRating.MEDIUM
import java.math.BigDecimal
import java.util.UUID

class SafetyStockConfigurations(
    safetyStockConfigurations: List<SafetyStockConfiguration>,
) {

    private val dcConfigurations = safetyStockConfigurations
        .groupBy { it.dcCode }
        .mapValues { (_, skus) ->
            skus.groupBy { it.skuId }
                .mapValues { (_, weeks) -> weeks.associateBy { it.productionWeek } }
        }

    fun getConfiguration(dcCode: String, skuId: UUID, productionWeek: ProductionWeek): SafetyStockConfiguration =
        dcConfigurations[dcCode]
            ?.get(skuId)
            ?.let { weekConfiguration ->
                weekConfiguration[productionWeek]
                    ?: (
                        weekConfiguration
                            .filter { (week, _) -> week < productionWeek }
                            .maxByOrNull { (week, _) -> week }
                            ?.value
                        )
            }
            ?: SafetyStockConfiguration(
                dcCode, skuId, productionWeek, DEFAULT_RISK_MULTIPLIER, DEFAULT_SKU_RISK_RATING,
            )
}

data class SafetyStockConfiguration(
    val dcCode: String,
    val skuId: UUID,
    val productionWeek: ProductionWeek,
    val riskMultiplier: BigDecimal,
    val skuRiskRating: SkuRiskRating
) {
    companion object {
        val DEFAULT_RISK_MULTIPLIER: BigDecimal = BigDecimal.ONE
        val DEFAULT_SKU_RISK_RATING: SkuRiskRating = MEDIUM
    }
}
