package com.hellofresh.cif.safetystock.violations

import com.hellofresh.cif.safetystock.model.ParsedFile.SafetyStockImport
import com.hellofresh.cif.safetystock.model.ParsedFile.SafetyStockMultiplier
import com.hellofresh.cif.safetystock.model.ParsedFile.TargetSafetyStockImport
import com.hellofresh.cif.safetystock.violations.Violation.BlankCell
import com.hellofresh.cif.safetystock.violations.Violation.InvalidDataTypeFormat
import com.hellofresh.cif.safetystock.violations.Violation.InvalidNumberOfColumns
import com.hellofresh.cif.safetystock.violations.Violation.NegativeNumbers
import kotlin.reflect.full.createInstance
import kotlin.reflect.full.primaryConstructor
import org.apache.logging.log4j.kotlin.Logging

interface ViolationHandler {
    fun handle(data: ProcessingUnit): Violation

    companion object : Logging
}

sealed interface ViolationHandlersPerFile : ViolationHandler {
    companion object {
        fun createInstances(
            violationsHandlersPerFile: List<ViolationHandlersPerFile> = emptyList()
        ): List<ViolationHandlersPerFile> =
            ViolationHandlersPerFile::class.sealedSubclasses
                .filter { it.primaryConstructor?.parameters?.size ?: 0 == 0 }
                .map { it.objectInstance ?: it.createInstance() }
                .plus(violationsHandlersPerFile)
    }
}

object DataTypeVerificationHandler : ViolationHandlersPerFile {
    override fun handle(data: ProcessingUnit): Violation {
        val isValid = when (data.file) {
            is SafetyStockMultiplier ->
                checkDataType(data, SafetyStockMultiplier.intColumnDataTypes) { it.toIntOrNull() }

            is SafetyStockImport ->
                checkDataType(data, SafetyStockImport.doubleColumnDataTypes) { it.toDoubleOrNull() }

            is TargetSafetyStockImport ->
                checkDataType(data, TargetSafetyStockImport.doubleColumnDataTypes) { it.toDoubleOrNull() }
        }

        return if (isValid) noViolation else InvalidDataTypeFormat(data.record.recordNumber.toInt())
    }

    private fun checkDataType(data: ProcessingUnit, headers: Set<String>, conversion: (String) -> Number?): Boolean =
        headers.all { header -> conversion(data.record[header]) != null }
}

object NegativeNumbersViolationHandler : ViolationHandlersPerFile {
    @Suppress("UnnecessaryParentheses")
    override fun handle(data: ProcessingUnit): Violation =
        if (data.record.columns.values.any { (it.toIntOrNull() ?: 0) < 0 }) {
            NegativeNumbers(data.record.recordNumber.toInt())
        } else {
            noViolation
        }
}

class InvalidNumberOfColumnsViolationHandler : ViolationHandlersPerFile {
    var size: Int? = null
    override fun handle(data: ProcessingUnit): Violation =
        if (size != null && size != data.record.columns.size) {
            InvalidNumberOfColumns(data.record.recordNumber.toInt())
        } else {
            size = data.record.columns.size
            noViolation
        }
}

object BlankCellViolationHandler : ViolationHandlersPerFile {
    override fun handle(data: ProcessingUnit): Violation =
        if (data.record.columns.values.any { it.isBlank() }) {
            BlankCell(data.record.recordNumber.toInt())
        } else {
            noViolation
        }
}
