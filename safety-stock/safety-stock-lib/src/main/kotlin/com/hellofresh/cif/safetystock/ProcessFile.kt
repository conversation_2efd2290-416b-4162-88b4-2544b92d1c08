package com.hellofresh.cif.safetystock

import com.hellofresh.cif.safetystock.model.FileType
import com.hellofresh.cif.safetystock.model.ProcessFileResult
import com.hellofresh.cif.safetystock.model.ProcessFileResult.Invalid
import com.hellofresh.cif.safetystock.reader.CSVFileUploadReader
import com.hellofresh.cif.safetystock.violations.ProcessChain
import com.hellofresh.cif.safetystock.violations.ReaderViolation
import com.hellofresh.cif.safetystock.violations.Violation
import com.hellofresh.cif.safetystock.violations.ViolationHandler
import com.hellofresh.cif.safetystock.violations.ViolationHandlersPerFile
import org.apache.logging.log4j.kotlin.Logging

object ProcessFile : Logging {
    fun processFileContent(
        fileName: String,
        fileType: FileType,
        content: ByteArray,
        preViolationHandlers: List<ViolationHandler> = emptyList()
    ): ProcessFileResult {
        val (parsedFile, readerViolations) = CSVFileUploadReader.toCsvRecords(content, fileType)

        if (parsedFile == null || readerViolations.isNotEmpty()) {
            return Invalid(
                parsedFile,
                readerViolations.map {
                    when (it) {
                        is ReaderViolation.DelimiterExtractionFailed -> Violation.DelimiterExtractionFailed()
                        is ReaderViolation.HeadersNotMatching -> Violation.HeadersNotMatching()
                        is ReaderViolation.NotACsv -> Violation.NotACsv()
                    }
                },
            )
        }
        return ProcessChain(preViolationHandlers + ViolationHandlersPerFile.createInstances())
            .process(parsedFile, fileName)
    }
}
