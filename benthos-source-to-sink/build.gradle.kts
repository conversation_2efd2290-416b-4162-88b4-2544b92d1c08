import com.google.cloud.tools.jib.gradle.JibTask

plugins {
    id("com.hellofresh.cif.application-conventions")
    hellofresh.`test-integration`
}

tasks.withType<JibTask>().configureEach {
    enabled = false
}

description = "Consumes source data (inventory, demand and actual inbound status saves them into a database"
group = "$group.${project.name}"

dependencies {
    testIntegrationRuntimeOnly(libs.postgresql.driver)
    testIntegrationRuntimeOnly(libs.slf4j.simple)

    testIntegrationImplementation(projects.distributionCenterModels)
    testIntegrationImplementation(projects.inventoryModels)
    testIntegrationImplementation(projects.libTests)
    testIntegrationImplementation(libs.jackson.kotlin)
    testIntegrationImplementation(libs.jackson.databind)
    testIntegrationImplementation(libs.jackson.jsr310)
    testIntegrationImplementation(libs.awaitility)
    testIntegrationImplementation(libs.flyway.core)
    testIntegrationImplementation(libs.kafka.clients)
    testIntegrationImplementation(libs.testcontainers.core)
    testIntegrationImplementation(libs.testcontainers.kafka)
    testIntegrationImplementation(libs.testcontainers.postgresql)
}
