# steps to start the services locally

How to start the benthos-distribution-center-to-sink locally ?

1. Run docker compose up broker kafdrop
2. Run docker compose up inventory-postgres db-migration
3. Create a topic “csku-inventory-forecast.intermediate.distribution-center.v4” in the broker [http://localhost:19000]
4. Mirror the topic \
   docker exec --env-file kafka-live-su.env -it broker sh -c '/usr/src/kafka-mirror.sh "csku-inventory-forecast.intermediate.distribution-center.v4"'
5. export DISTRIBUTION_CENTER_TOPIC_VERSION=4
6. docker compose up  benthos-distribution-center-to-sink
7. Connect to localhost:5432 database with cif/123456, database = inventory
8. dc_config table should be populated with data from csku-inventory-forecast.intermediate.distribution-center.v4

How to start the benthos-calculations-db-sink locally ?

1. Run docker compose up broker kafdrop
2. Run docker compose up inventory-postgres db-migration
3. Create a topic “csku-inventory-forecast.intermediate.calculations.v1” in the broker [http://localhost:19000]
4. Create a topic “csku-inventory-forecast.intermediate.pre-production.calculations.v1” in the broker [http://localhost:19000]
5. Mirror the following the topics \
   docker exec --env-file kafka-live-su.env -it broker sh -c '/usr/src/kafka-mirror.sh "csku-inventory-forecast.intermediate.calculations.v1"'
   docker exec --env-file kafka-live-su.env -it broker sh -c '/usr/src/kafka-mirror.sh "csku-inventory-forecast.intermediate.pre-production.calculations.v1"'
6. docker compose up  calculations-db-sink
7. Connect to localhost:5432 database with cif/123456, database = inventory
8. calculation table should be populated with data from csku-inventory-forecast.intermediate.calculations.v1
9. pre_production_calculation table should be populated with data from csku-inventory-forecast.intermediate.pre-production.calculations.v1

