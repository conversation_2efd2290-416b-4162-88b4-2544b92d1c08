@file:Suppress("UnstableApiUsage")

import java.net.URI


dependencyResolutionManagement {
    repositories {
        mavenCentral()
        maven("https://packages.confluent.io/maven/")
        maven("https://artifactory.tools-k8s.hellofresh.io/artifactory/maven-local/")
    }
}

enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")

buildCache {
    buildCache {
        local {
            isEnabled = true
        }

        remote(HttpBuildCache::class.java) {
            isEnabled = true
            url =
                URI("https://artifactory.tools-k8s.hellofresh.io/artifactory/gradle-build-caches/csku-inventory-forecast/")
            credentials {
                username = System.getenv("ARTIFACTORY_USERNAME")
                password = System.getenv("ARTIFACTORY_PASSWORD")
            }
            // Only push to the remote cache when running in CI
            isPush = System.getenv("CI") == "true"
            println("Build cache push is enabled: $isPush")
        }
    }
}

include(
    "actual-consumption-service",
    "benthos-source-to-sink",
    "calculator-job",
    "calculator-models",
    "calculator-rule",
    "file-consumer-service",
    "safety-stock-job",
    "safety-stock:safety-stock-lib",
    "supply-quantity-recommendation-job",
    "supply-quantity-recommendation-lib",
    "date-util-models",
    "demand",
    "demand-lib",
    "demand-models",
    "distribution-center-api",
    "distribution-center-lib",
    "distribution-center-models",
    "forecast-api",
    "forecast-api-db",
    "goods-received-note-service",
    "inventory-db",
    "inventory:inventory-lib",
    "inventory-models",
    "inventory:inventory-snapshot-service",
    "inventory:inventory-activity-service",
    "inventory:inventory-live-job",
    "inventory-variance-job",
    "kafka-db-sink",
    "lib",
    "lib:configuration",
    "lib:checks",
    "lib:shutdown",
    "lib:logging",
    "lib:db",
    "lib:file-consumer",
    "lib:stock-update",
    "lib:file-upload",
    "lib:s3",
    "lib:sqs",
    "lib:featureflags",
    "lib:models",
    "lib-tests",
    "mvp:end-to-end-test",
    "mvp:kafka-mirror-plugin",
    "mvp:sanity-checker",
    "mvp:scripts",
    "output-demand-forecast",
    "purchase-order:purchase-order-service",
    "purchase-order:purchase-order-models",
    "purchase-order:purchase-order-lib",
    "sku-inputs-lib",
    "sku-models",
    "sku-specification-lib",
    "sku-specification-service",
    "supplier",
    "topic-config",
    "transfer-order:transfer-order-service",
    "transfer-order:transfer-order-lib",
)
