package com.hellofresh.cif.supplier

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.supplier.model.Supplier
import com.hellofresh.cif.supplier.schema.Tables.SUPPLIER
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.util.UUID
import java.util.concurrent.Executors
import javax.sql.DataSource
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.common.TopicPartition
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test

class PerformUpsertTest {
    private val update = PerformUpsert(dsl)

    @AfterEach
    fun cleanup() {
        dsl.deleteFrom(SUPPLIER).execute()
    }

    @Test
    fun `insert new rows`() {
        val crList = (0..2).map { i ->
            ConsumerRecord(
                "test",
                0,
                0,
                Unit,
                listOf(generateTestData(i)),
            )
        }

        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to crList))
        runBlocking { update(consumerRecords) }
        val records = dsl.fetch(SUPPLIER).sortedBy { it.name }

        assertEquals(3, records.size)
        records.forEachIndexed { i, r ->
            val expectedSupplier = crList[i].value()!![0]
            assertEquals(expectedSupplier.id, r.id)
            assertEquals(expectedSupplier.name, r.name)
            assertEquals(expectedSupplier.parentId, r.parentId)
        }
    }

    private fun generateTestData(i: Int): Supplier = Supplier(
        id = UUID.randomUUID(),
        name = "$i",
        parentId = UUID.randomUUID(),
    )

    companion object {
        private lateinit var dataSource: DataSource
        private lateinit var dsl: MetricsDSLContext

        @JvmStatic
        @BeforeAll
        fun init() {
            dataSource = getMigratedDataSource()
            dsl = DSL.using(
                DefaultConfiguration().apply {
                    setSQLDialect(POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                },
            ).withMetrics(SimpleMeterRegistry())
        }
    }
}
