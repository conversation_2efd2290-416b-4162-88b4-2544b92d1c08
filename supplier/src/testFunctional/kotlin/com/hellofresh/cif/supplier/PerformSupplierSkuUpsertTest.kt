package com.hellofresh.cif.supplier

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.supplier.model.SupplierSku
import com.hellofresh.cif.supplier.schema.Tables.SUPPLIER_CULINARY_SKU
import com.hellofresh.cif.supplier.sku.PerformSupplierSkuUpsert
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.util.UUID
import java.util.concurrent.Executors
import javax.sql.DataSource
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.common.TopicPartition
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test

class PerformSupplierSkuUpsertTest {
    private val update = PerformSupplierSkuUpsert(dsl)

    @AfterEach
    fun cleanup() {
        dsl.deleteFrom(SUPPLIER_CULINARY_SKU).execute()
    }

    @Test
    fun `insert new supplier culinary sku rows`() {
        val consumerRecordList = getConsumerRecords(generateTestData())
        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to consumerRecordList))
        runBlocking { update(consumerRecords) }
        val records = dsl.fetch(SUPPLIER_CULINARY_SKU)

        assertEquals(1, records.size)
        records.forEachIndexed { i, r ->
            val expectedSupplier = consumerRecordList[i].value()!![0]
            assertEquals(expectedSupplier.id, r.id)
            assertEquals(expectedSupplier.supplierId, r.supplierId)
            assertEquals(expectedSupplier.culinarySkuId, r.culinarySkuId)
            assertEquals(expectedSupplier.market, r.market)
            assertEquals(expectedSupplier.status, r.status)
        }
    }

    @Test
    fun `update supplier culinary sku rows on duplicate key`() {
        val testData = generateTestData()
        val consumerRecordList = getConsumerRecords(testData)
        val consumerRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to consumerRecordList))
        runBlocking { update(consumerRecords) }

        val updatedSupplierSku = testData.copy(status = "InActive")
        val consumerUpdatedRecordList = getConsumerRecords(updatedSupplierSku)
        val consumerUpdatedRecords = ConsumerRecords(mapOf(TopicPartition("test", 0) to consumerUpdatedRecordList))
        runBlocking { update(consumerUpdatedRecords) }
        val updatedRecords = dsl.fetch(SUPPLIER_CULINARY_SKU)
        updatedRecords.forEachIndexed { i, r ->
            val expectedSupplier = consumerUpdatedRecordList[i].value()!![0]
            assertEquals(expectedSupplier.id, r.id)
            assertEquals(expectedSupplier.supplierId, r.supplierId)
            assertEquals(expectedSupplier.culinarySkuId, r.culinarySkuId)
            assertEquals(expectedSupplier.market, r.market)
            assertEquals(expectedSupplier.status, r.status)
        }
    }
    private fun getConsumerRecords(supplierSku: SupplierSku) = (0..2).map { _ ->
        ConsumerRecord(
            "test",
            0,
            0,
            Unit,
            listOf(supplierSku),
        )
    }
    private fun generateTestData(): SupplierSku = SupplierSku(
        id = UUID.randomUUID(),
        supplierId = UUID.randomUUID(),
        culinarySkuId = UUID.randomUUID(),
        market = "dach",
        status = "Active",
    )
    companion object {
        private lateinit var dataSource: DataSource
        private lateinit var dsl: MetricsDSLContext

        @JvmStatic
        @BeforeAll
        fun init() {
            dataSource = getMigratedDataSource()
            dsl = DSL.using(
                DefaultConfiguration().apply {
                    setSQLDialect(POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                },
            ).withMetrics(SimpleMeterRegistry())
        }
    }
}
