package com.hellofresh.cif.sqr

import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.sqrlib.schema.Tables
import com.hellofresh.sku.models.SkuSpecification
import io.mockk.coEvery
import java.io.ByteArrayInputStream
import java.util.stream.Stream
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

private const val BUCKET_NAME = "hf-bi-dwh-uploader"

class BNLBufferCalculationSQSListenerTest : FunctionalTest() {

    private val skuSpec = SkuSpecification(
        category = "PCK",
        skuCode = "BAK-10-003072-1",
        name = "Test",
        coolingType = "test1",
        packaging = "test2",
        acceptableCodeLife = 2,
        market = "BENELUXFR",
    )

    @BeforeEach
    fun beforeEach() {
        insertSkuSpecification(listOf(skuSpec))
    }

    @Test
    fun `should fetch bnl buffer calculation file and verify ssl conf created`() {
        val key = "bnl_buffer_calculations/bnl_buffers.csv"
        val fileContentInByteArray = readFileContent(key)

        coEvery {
            s3Importer.fetchObjectContent(
                BUCKET_NAME,
                key
            )
        } returns ByteArrayInputStream(fileContentInByteArray)

        runBlocking {
            bufferCalculationService.process(
                S3File(
                    BUCKET_NAME,
                    key
                )
            )
        }

        val shelfLifeConfRecords = dsl.selectFrom(Tables.SQR_SHORT_SHELF_LIFE_CONF).fetch()

        assertEquals(3, shelfLifeConfRecords.size)
    }

    @ParameterizedTest(name = "{index} => {1}")
    @MethodSource("provideInputsForInvalidFileContents")
    fun `should not process bnl_buffers for invalid entries`(key: String) {
        val fileContentInByteArray = readFileContent(key)

        coEvery {
            s3Importer.fetchObjectContent(
                BUCKET_NAME,
                key
            )
        } returns ByteArrayInputStream(fileContentInByteArray)

        runBlocking {
            bufferCalculationService.process(
                S3File(
                    BUCKET_NAME,
                    key
                )
            )
        }

        val shelfLifeConfRecords = dsl.selectFrom(Tables.SQR_SHORT_SHELF_LIFE_CONF).fetch()

        assertEquals(0, shelfLifeConfRecords.size)
    }

    companion object {
        @Suppress("unused")
        @JvmStatic
        fun provideInputsForInvalidFileContents(): Stream<Arguments> = Stream.of(
            Arguments.of(
                "bnl_buffer_calculations/bnl_buffers_not_allowed_blank_columns.csv",
                "should not process the bnl_buffers file if the file has invalid number of columns",
            ),
            Arguments.of(
                "bnl_buffer_calculations/bnl_buffers_wrong_headers.csv",
                "should not process the bnl_buffers file if the file has wrong headers",
            ),
            Arguments.of(
                "bnl_buffer_calculations/bnl_buffers_negative_value.csv",
                "should not process the bnl_buffers file if the file has negative value",
            ),
            Arguments.of(
                "bnl_buffer_calculations/bnl_buffers_invalid_number_of_columns.csv",
                "should not process the bnl_buffers file if the file has invalid number of columns",
            ),
            Arguments.of(
                "bnl_buffer_calculations/bnl_buffers_not_a_csv_format.csv",
                "should not process the bnl_buffers file if the file is not in CSV format",
            ),
        )
    }
}
