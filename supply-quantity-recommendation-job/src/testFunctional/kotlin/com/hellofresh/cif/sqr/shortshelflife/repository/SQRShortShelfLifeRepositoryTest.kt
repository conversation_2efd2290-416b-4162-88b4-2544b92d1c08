package com.hellofresh.cif.sqr.shortshelflife.repository

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.sqr.FunctionalTest
import com.hellofresh.cif.sqr.random
import com.hellofresh.cif.sqr.schema.Tables
import com.hellofresh.cif.sqr.schema.tables.records.SqrShortShelfLifeRecord
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeRepository.upsertSqrShortShelfLifeRecommendations
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class SQRShortShelfLifeRepositoryTest : FunctionalTest() {

    @Test
    fun `short shelf life sqr are successfully persisted`() {
        val sqr1 = SQRShortShelfLife.random()
        val sqr2 = SQRShortShelfLife.random(dcCode = "D2", date = sqr1.date.plusDays(1))

        runBlocking { dsl.upsertSqrShortShelfLifeRecommendations(listOf(sqr1, sqr2)) }

        assertSqr(listOf(sqr1, sqr2))
    }

    @ParameterizedTest
    @CsvSource(
        "openingStock",
        "unusable",
        "consumption",
        "stockUpdate",
        "bufferPercentage",
        "bufferAdditional",
        "sqr",
        "productionWeek",
        "poData",
    )
    fun `short shelf life sqr is updated when fields are different`(fieldName: String) {
        val sqr = SQRShortShelfLife.random()

        runBlocking { dsl.upsertSqrShortShelfLifeRecommendations(listOf(sqr)) }

        val record = assertSqr(sqr)

        val updatedSqr = sqr.copy()

        // Updates copy field
        var randomSqr = SQRShortShelfLife.random()
        val originalField = updatedSqr::class.java.getDeclaredField(fieldName)
        originalField.isAccessible = true
        val originalFieldValue = originalField.get(updatedSqr)
        var randomField = randomSqr::class.java.getDeclaredField(fieldName)
        randomField.isAccessible = true
        while (originalFieldValue == randomField.get(randomSqr)) {
            randomSqr = SQRShortShelfLife.random()
            randomField = randomSqr::class.java.getDeclaredField(fieldName)
            randomField.isAccessible = true
        }

        originalField.set(updatedSqr, randomField.get(randomSqr))
        originalField.isAccessible = false

        assertNotEquals(sqr, updatedSqr)

        runBlocking { dsl.upsertSqrShortShelfLifeRecommendations(listOf(updatedSqr)) }

        val updatedRecord = assertSqr(updatedSqr)

        assertEquals(record.createdAt, updatedRecord.createdAt)
        assertTrue(record.updatedAt.isBefore(updatedRecord.updatedAt))
    }

    @Test
    fun `short shelf life sqr is updated when uom from sqr value is different`() {
        val sqr = SQRShortShelfLife.random()

        runBlocking { dsl.upsertSqrShortShelfLifeRecommendations(listOf(sqr)) }

        val record = assertSqr(sqr)

        val updatedSqr = sqr.copy(
            sqr = SkuQuantity.fromBigDecimal(
                value = sqr.sqr.getValue(),
                unitOfMeasure = SkuUOM.entries.first {
                    it != sqr.uom
                },
            ),
        )

        assertNotEquals(sqr, updatedSqr)

        runBlocking { dsl.upsertSqrShortShelfLifeRecommendations(listOf(updatedSqr)) }

        val updatedRecord = assertSqr(updatedSqr)

        assertEquals(record.createdAt, updatedRecord.createdAt)
        assertTrue(record.updatedAt.isBefore(updatedRecord.updatedAt))
    }

    @Test
    fun `short shelf life sqr is not updated when fields are exactly the same`() {
        val sqr = SQRShortShelfLife.random()

        runBlocking { dsl.upsertSqrShortShelfLifeRecommendations(listOf(sqr)) }

        val record = assertSqr(sqr)

        runBlocking { dsl.upsertSqrShortShelfLifeRecommendations(listOf(sqr)) }

        val updatedRecord = assertSqr(sqr)

        assertEquals(record.createdAt, updatedRecord.createdAt)
        assertEquals(record.updatedAt, updatedRecord.updatedAt)
    }

    private fun assertSqr(sqrShortShelfLife: SQRShortShelfLife) = assertSqr(
        listOf(sqrShortShelfLife),
    ).first()

    private fun assertSqr(sqrShortShelfLifes: List<SQRShortShelfLife>): List<SqrShortShelfLifeRecord> {
        val records = dsl.selectFrom(Tables.SQR_SHORT_SHELF_LIFE).fetch()
        assertEquals(sqrShortShelfLifes.size, records.size)
        sqrShortShelfLifes.forEach { sqr ->
            val record = records.first { it.skuId == sqr.skuId }
            assertSqr(sqr, record)
        }
        return records
    }

    private fun assertSqr(
        sqrShortShelfLife: SQRShortShelfLife,
        record: SqrShortShelfLifeRecord
    ) {
        assertEquals(sqrShortShelfLife.dcCode, record.dcCode)
        assertEquals(sqrShortShelfLife.skuId, record.skuId)
        assertEquals(sqrShortShelfLife.date, record.date)
        assertEquals(sqrShortShelfLife.uom.name, record.uom.name)
        assertEquals(sqrShortShelfLife.sqr.getValue(), record.sqr)
        assertEquals(sqrShortShelfLife.openingStock.getValue(), record.openingSock)
        assertEquals(sqrShortShelfLife.unusable.getValue(), record.unusableStock)
        assertEquals(sqrShortShelfLife.consumption.getValue(), record.consumption)
        assertEquals(sqrShortShelfLife.stockUpdate?.getValue(), record.stockUpdate)
        assertEquals(sqrShortShelfLife.bufferPercentage, record.bufferPercentage)
        assertEquals(sqrShortShelfLife.bufferAdditional, record.bufferAdditional)
    }
}
