package com.hellofresh.cif.sqr.repository

import com.hellofresh.cif.sqr.FunctionalTest
import com.hellofresh.cif.sqr.schema.tables.records.DcConfigRecord
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneId
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class DcWeightRepositoryImplTest : FunctionalTest() {

    @Test
    fun `fetch dc return enabled dcs with dcWeight greater than 0`() {
        val enabledDcs: Map<DcConfigRecord, Long> = (0..5L).associate {
            insertDcRecord(UUID.randomUUID().toString(), true) to it + 1L
        }.onEach { (dcRecord, count) ->
            (0 until count).map {
                insertCalculation(
                    dcRecord.dcCode,
                    DayOfWeek.valueOf(dcRecord.productionStart),
                    LocalDate.now(ZoneId.of(dcRecord.zoneId)).plusWeeks(1),
                    UUID.randomUUID(),
                )
            }
        }

        val disabledDcs = (0..2).map {
            insertDcRecord(UUID.randomUUID().toString(), false)
        }

        val fetchDcsWithWeight = runBlocking { DcWeightRepositoryImpl(dsl).fetchDcsWithWeight() }

        assertEquals(enabledDcs.map { it.key.dcCode }, fetchDcsWithWeight.map { it.dcCode })
        fetchDcsWithWeight.forEach { assertTrue(it.weight > 0) }

        with(fetchDcsWithWeight.map { it.dcCode }.toSet()) {
            disabledDcs.forEach { assertTrue(!contains(it.dcCode)) }
        }
    }

    @Test
    fun `fetch dc return dcs with weight using calculations`() {
        val dcs = (0..5L).associate {
            insertDcRecord(UUID.randomUUID().toString(), true) to it + 1L
        }.onEach { (dcRecord, count) ->
            (0 until count).map {
                insertCalculation(
                    dcRecord.dcCode,
                    DayOfWeek.valueOf(dcRecord.productionStart),
                    LocalDate.now(ZoneId.of(dcRecord.zoneId)).plusWeeks(1),
                    UUID.randomUUID(),
                )
            }
        }

        val fetchDcsWithWeight = runBlocking { DcWeightRepositoryImpl(dsl).fetchDcsWithWeight() }

        dcs.forEach { (dcRecord, count) ->
            with(fetchDcsWithWeight.first { it.dcCode == dcRecord.dcCode }) {
                assertEquals(count, this.weight)
            }
        }
    }
}
