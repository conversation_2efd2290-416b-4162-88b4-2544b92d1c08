package com.hellofresh.cif.sqr

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.purchaseorder.DeliveryInfo
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus
import com.hellofresh.cif.models.purchaseorder.PoStatus
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.TimeRange
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeService
import com.hellofresh.cif.sqr.shortshelflife.SqrShortShelfLifeDcParam
import com.hellofresh.cif.sqr.shortshelflife.SqrShortShelfLifeParams
import com.hellofresh.cif.sqr.shortshelflife.repository.DailyCalculation
import com.hellofresh.cif.sqr.shortshelflife.repository.DailyCalculationsRepository
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLife
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeConfRepository
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.producer.Producer
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class SQRShortShelfLifeServiceTest {
    private val purchaseOrderRepository = mockk<PurchaseOrderRepository>(relaxed = true)
    private val metricsDSLContext = mockk<MetricsDSLContext>(relaxed = true)
    private val dcConfigService = mockk<DcConfigService>(relaxed = true)
    private val dailyCalculationsRepository = mockk<DailyCalculationsRepository>(relaxed = true)
    private val sqrShortShelfLifeConfRepository = mockk<SQRShortShelfLifeConfRepository>(relaxed = true)
    private val producer = mockk<
        Producer<
            SupplyQuantityRecommendationDailyKey,
            SupplyQuantityRecommendationDailyVal,
            >,
        >(relaxed = true)
    private val sqrShortShelfLifeService = SQRShortShelfLifeService(
        sqrShortShelfLifeParams = SqrShortShelfLifeParams(marketParam = mapOf()),
        readWriteDslContext = metricsDSLContext,
        dcConfigService = dcConfigService,
        dailyCalculationsRepository = dailyCalculationsRepository,
        sqrShortShelfLifeConfRepository = sqrShortShelfLifeConfRepository,
        purchaseOrderRepository = purchaseOrderRepository,
        producer = producer,
    )

    @Test
    fun `should fill poData when getPurchaseOrders returns purchase orders`() = runBlocking {
        val dcCode = "BV"
        val skuId = UUID.randomUUID()
        val sqrShortShelfLife = SQRShortShelfLife.Companion.random(
            dcCode = dcCode,
            date = LocalDate.now(),
            skuId = skuId,
        )

        val purchaseOrder = PurchaseOrder(
            number = "PO123",
            poReference = "REF123",
            poId = UUID.randomUUID(),
            dcCode = dcCode,
            expectedDeliveryTimeslot = TimeRange(ZonedDateTime.now(), ZonedDateTime.now().plusDays(1)),
            supplier = null,
            purchaseOrderSkus = listOf(
                PurchaseOrderSku(
                    skuId = skuId,
                    expectedQuantity = SkuQuantity.fromBigDecimal(BigDecimal(500)),
                    deliveries = listOf(
                        DeliveryInfo("ID1", ZonedDateTime.now(), DeliveryInfoStatus.CLOSED, SkuQuantity.fromLong(99)),
                    ),
                    expiryDate = LocalDate.now().plusDays(50),
                ),
            ),
            asns = emptyList(),
            poStatus = PoStatus.APPROVED,
        )

        coEvery { dcConfigService.dcConfigurations } returns mapOf(dcCode to getDcConfig(dcCode))
        coEvery { purchaseOrderRepository.findPurchaseOrders(any(), any()) } returns listOf(purchaseOrder)

        val result = sqrShortShelfLifeService.fillSQRShortShelfLifeWithPODetails(listOf(sqrShortShelfLife))

        assertEquals(1, result[0].poData.poDetails.size)
        assertEquals(BigDecimal(500), result[0].poData.poDetails[0].qty)
        assertEquals("PO123", result[0].poData.poDetails[0].poNumber)
    }

    @Test
    fun `should have empty poData when getPurchaseOrders returns empty list`() = runBlocking {
        val dcCode = "BV"
        val skuId = UUID.randomUUID()
        val sqrShortShelfLife = SQRShortShelfLife.Companion.random(
            dcCode = dcCode,
            date = LocalDate.now(),
            skuId = skuId,
        )

        coEvery { dcConfigService.dcConfigurations } returns mapOf(dcCode to getDcConfig(dcCode))
        coEvery { purchaseOrderRepository.findPurchaseOrders(any(), any()) } returns emptyList()

        val result = sqrShortShelfLifeService.fillSQRShortShelfLifeWithPODetails(listOf(sqrShortShelfLife))

        assertEquals(0, result[0].poData.poDetails.size)
        assertEquals(0, result[0].poData.inboundDetails.size)
    }

    private fun getDcConfig(dcCode: String, poCutoffTime: LocalTime = LocalTime.now()) =
        DistributionCenterConfiguration(
            dcCode = dcCode,
            productionStart = LocalDate.now().dayOfWeek,
            cleardown = LocalDate.now().dayOfWeek,
            market = "DACH",
            zoneId = ZoneOffset.UTC,
            enabled = true,
            wmsType = WmsSystem.WMS_SYSTEM_FCMS,
            poCutoffTime = poCutoffTime
        )

    @ParameterizedTest(name = "isValid test for dcCode: {0}, market: {1}, skuCategory: {2}, expected: {3}")
    @CsvSource(
        "BV, GB, PRO, false",
        "BV, GB, DRY, false",
        "BV, GB, SPI, false",
        "BV, GB, PTN, true",
        "NJ, US, PRO, true",
        "NJ, US, DRY, true",
        "NJ, US, SPI, true",
        "NJ, US, PTN, true",
    )
    fun `should validate market and sku category to be processed`(
        dcCode: String,
        market: String,
        skuCategory: String,
        expected: Boolean
    ) {
        val dailyCalculation = DailyCalculation(
            dcCode = dcCode,
            date = LocalDate.now(),
            skuId = UUID.randomUUID(),
            skuCategory = skuCategory,
            openingStock = SkuQuantity.fromLong(100),
            unusable = SkuQuantity.fromLong(10),
            consumption = SkuQuantity.fromLong(20),
            stockUpdate = SkuQuantity.fromLong(5),
            productionWeek = "2025-W20",
        )

        val distributionCenterConfig = DistributionCenterConfiguration(
            dcCode = dcCode,
            productionStart = MONDAY,
            cleardown = FRIDAY,
            market = market,
            zoneId = ZoneId.of("Europe/Berlin"),
            enabled = true,
            hasCleardown = true,
            wmsType = WmsSystem.WMS_SYSTEM_FCMS,
            poCutoffTime = LocalTime.now(),
        )
        val sqrShortShelfLifeParams = mapOf(
            "US" to SqrShortShelfLifeDcParam(
                skuCategoriesIncluded = emptySet(),
                skuCategoriesExcluded = emptySet(),
            ),
            "GB" to SqrShortShelfLifeDcParam(
                skuCategoriesIncluded = emptySet(),
                skuCategoriesExcluded = setOf("PRO", "DRY", "SPI"),
            ),
        )
        val sqrShortShelfLifeService = SQRShortShelfLifeService(
            sqrShortShelfLifeParams = SqrShortShelfLifeParams(sqrShortShelfLifeParams),
            readWriteDslContext = metricsDSLContext,
            dcConfigService = dcConfigService,
            dailyCalculationsRepository = dailyCalculationsRepository,
            sqrShortShelfLifeConfRepository = sqrShortShelfLifeConfRepository,
            purchaseOrderRepository = purchaseOrderRepository,
            producer = producer,
        )

        val dcs = mapOf(
            dcCode to distributionCenterConfig,
        )

        val actual = sqrShortShelfLifeService.isValid(dailyCalculation, dcs)

        assertEquals(expected, actual)
    }

    @Test
    fun `sqr calculation result is not negative`() = runBlocking {
        val dcCode = "BV"
        val skuId = UUID.randomUUID()
        val prevSqr = SkuQuantity.fromDouble(20.0, SkuUOM.UOM_UNIT)
        val sqrShortShelfLife = SQRShortShelfLife.Companion.random(
            dcCode = dcCode,
            date = LocalDate.now(),
            skuId = skuId,
            sqr = prevSqr
        )
        val poCutOff = ZonedDateTime.now()

        val purchaseOrder = PurchaseOrder(
            number = "PO123",
            poReference = "REF123",
            poId = UUID.randomUUID(),
            dcCode = dcCode,
            expectedDeliveryTimeslot = TimeRange(poCutOff.minusHours(2), poCutOff.minusHours(1)),
            supplier = null,
            purchaseOrderSkus = listOf(
                PurchaseOrderSku(
                    skuId = skuId,
                    expectedQuantity = SkuQuantity.fromBigDecimal(BigDecimal(200)),
                    deliveries = listOf(),
                ),
            ),
            asns = emptyList(),
            poStatus = PoStatus.APPROVED,
        )

        coEvery { dcConfigService.dcConfigurations } returns mapOf(dcCode to getDcConfig(dcCode, poCutOff.toLocalTime()))
        coEvery { purchaseOrderRepository.findPurchaseOrders(any(), any()) } returns listOf(purchaseOrder)

        val result = sqrShortShelfLifeService.fillSQRShortShelfLifeWithPODetails(listOf(sqrShortShelfLife))

        assertEquals(1, result[0].poData.poDetails.size)
        assertEquals(BigDecimal(200), result[0].poData.poDetails[0].qty)
        assertEquals("PO123", result[0].poData.poDetails[0].poNumber)

        // SkuQuantity.max(0, 20.0 (prevSqr) - 200 (po before po cut off))
        assertEquals(SkuQuantity.ZERO, result[0].sqr)
    }
}
