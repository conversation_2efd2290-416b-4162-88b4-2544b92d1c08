package com.hellofresh.cif.sqr

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.type.Decimal
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.valueOf
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.sqr.Main.dcBalancer
import com.hellofresh.cif.sqr.schema.Tables
import com.hellofresh.cif.sqr.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.sqr.schema.tables.records.CalculationRecord
import com.hellofresh.cif.sqr.schema.tables.records.SqrShortShelfLifeRecord
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeConfigurations.Companion.default
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeService
import com.hellofresh.cif.sqr.shortshelflife.SqrShortShelfLifeDcParam
import com.hellofresh.cif.sqr.shortshelflife.SqrShortShelfLifeParams
import com.hellofresh.cif.sqr.shortshelflife.repository.PoData
import com.hellofresh.cif.sqrlib.schema.tables.records.SqrShortShelfLifeConfRecord
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal
import io.mockk.coEvery
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.math.BigDecimal.TEN
import java.math.BigDecimal.ZERO
import java.nio.charset.StandardCharsets
import java.time.DayOfWeek
import java.time.Duration
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.apache.commons.lang.ObjectUtils.max
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.testcontainers.shaded.org.awaitility.Awaitility

private val objectMapper: ObjectMapper = ObjectMapper().findAndRegisterModules()
private const val BUCKET_NAME = "hf-bi-dwh-uploader"

class SQRShortShelfLifeJobTest : FunctionalTest() {
    private val poNumber = "2305DH046273"
    private val poRef = "2305DH046273_01"
    private val poId = UUID.randomUUID()
    private val poQuantity = 12345L
    private val dcCode = "DC"
    private val market = "DACH"
    private val skuId = UUID.randomUUID()
    private val skuCategory = "PRO"
    private val skuCode = UUID.randomUUID()
    private val expectedArrivalStartTime = OffsetDateTime.now(UTC)
    private val expectedArrivalEndTime = expectedArrivalStartTime.plusHours(2)
    private val supplierId: UUID = UUID.randomUUID()
    private val productionStart = DayOfWeek.THURSDAY

    private val defaultPurchaseOrder = PurchaseOrderRecord(
        poRef = poRef,
        poId = poId,
        poNumber = poNumber,
        dcCode = dcCode,
        expectedDateStartTime = expectedArrivalStartTime,
        expectedDateEndTime = expectedArrivalEndTime,
        supplierId = UUID.randomUUID(),
        supplierName = UUID.randomUUID().toString(),
        skuId = skuId,
        quantity = poQuantity,
    )

    @Test
    fun `sqr ssl job produces recommendations`() {
        // given
        val poRef = "2508WM027948"
        val dcConfigRecord = insertDcRecord("BV")
        val productionStart = DayOfWeek.valueOf(dcConfigRecord.productionStart)
        val dcWeek = DcWeek(LocalDate.now(UTC), productionStart)
        val startDateInDcWeek = dcWeek.getStartDateInDcWeek(productionStart, UTC)

        val calculationRecord = insertCalculation(
            dcConfigRecord.dcCode,
            productionStart,
            startDateInDcWeek,
            UUID.randomUUID(),
            uom = UOM_UNIT,
        )
        insertCalculation(
            dcConfigRecord.dcCode,
            productionStart,
            LocalDate.now().plusDays(5L),
            UUID.randomUUID(),
            uom = UOM_UNIT,
        )
        insertSKU(skuID = calculationRecord.cskuId)

        insertSQRShortShelfLife(
            dcConfigRecord.dcCode,
            startDateInDcWeek,
            calculationRecord.cskuId,
        )

        val configuration = insertSQRShortShelfLifeConfiguration(
            dcConfigRecord.dcCode,
            startDateInDcWeek,
            calculationRecord.cskuId,
            TEN,
            calculationRecord.openingStock,
            true,
        )

        val grnClosed = insertGrn(
            dcCode = dcConfigRecord.dcCode,
            poRef = poRef,
            skuId = calculationRecord.cskuId,
            deliveryInfoStatus = CLOSED,
            deliveryDate = startDateInDcWeek.atStartOfDay().atOffset(UTC),
        )
        insertGoodsReceivedNotes(setOf(grnClosed))
        insertPurchaseOrderAndRefreshPoInfoView(
            defaultPurchaseOrder.copy(
                supplierId = supplierId,
                dcCode = dcConfigRecord.dcCode,
                skus = listOf(PurchaseOrderSkuRecord(calculationRecord.cskuId, 500)),
                expectedDateStartTime = startDateInDcWeek.atStartOfDay().atOffset(UTC),
                expectedDateEndTime = startDateInDcWeek.plusDays(1).atStartOfDay().atOffset(UTC),
            ),
        )

        refreshDcConfigWeightView()
        // when
        runJob(setOf(dcConfigRecord.market))

        // then
        val recommendationRecords = dsl.selectFrom(Tables.SQR_SHORT_SHELF_LIFE).fetch()
        val poCutOffQty = BigDecimal.valueOf(9)

        assertEquals(1, recommendationRecords.size)

        assertSqr(
            calculationRecord,
            configuration,
            recommendationRecords.first(),
            poCutOffQty
        )
        val poDetails = objectMapper.readValue<PoData>(
            recommendationRecords.first().poDetails.data(),
        )
        assertEquals("2305DH046273", poDetails.poDetails.first().poNumber)
        assertEquals(BigDecimal("500"), poDetails.poDetails.first().qty)
        assertEquals("2305DH046273", poDetails.inboundDetails.first().poNumber)
        assertEquals(BigDecimal("9"), poDetails.inboundDetails.first().qty)

        assertTopicRecords(recommendationRecords)
    }

    @Test
    fun `sqr ssl job does not produces recommendations if the production week does not have any consumption`() {
        // given
        val poRef = "2508WM027948"
        val dcConfigRecord = insertDcRecord("BV")
        val productionStart = DayOfWeek.valueOf(dcConfigRecord.productionStart)
        val dcWeek = DcWeek(LocalDate.now(UTC), productionStart)
        val startDateInDcWeek = dcWeek.getStartDateInDcWeek(productionStart, UTC)

        val calculationRecord = insertCalculation(
            dcConfigRecord.dcCode,
            productionStart,
            startDateInDcWeek,
            UUID.randomUUID(),
            uom = UOM_UNIT,
            demand = ZERO,
        )
        insertCalculation(
            dcConfigRecord.dcCode,
            productionStart,
            LocalDate.now().plusDays(5L),
            UUID.randomUUID(),
            uom = UOM_UNIT,
        )
        insertSKU(skuID = calculationRecord.cskuId)

        insertSQRShortShelfLife(
            dcConfigRecord.dcCode,
            startDateInDcWeek,
            calculationRecord.cskuId,
        )

        val configuration = insertSQRShortShelfLifeConfiguration(
            dcConfigRecord.dcCode,
            startDateInDcWeek,
            calculationRecord.cskuId,
            TEN,
            calculationRecord.openingStock,
            true,
        )

        val grnClosed = insertGrn(
            dcCode = dcConfigRecord.dcCode,
            poRef = poRef,
            skuId = calculationRecord.cskuId,
            deliveryInfoStatus = CLOSED,
            deliveryDate = startDateInDcWeek.atStartOfDay().atOffset(UTC),
        )
        insertGoodsReceivedNotes(setOf(grnClosed))
        insertPurchaseOrderAndRefreshPoInfoView(
            defaultPurchaseOrder.copy(
                supplierId = supplierId,
                dcCode = dcConfigRecord.dcCode,
                skus = listOf(PurchaseOrderSkuRecord(calculationRecord.cskuId, 500)),
                expectedDateStartTime = startDateInDcWeek.atStartOfDay().atOffset(UTC),
                expectedDateEndTime = startDateInDcWeek.plusDays(1).atStartOfDay().atOffset(UTC),
            ),
        )
        refreshDcConfigWeightView()
        // when
        runJob(setOf(dcConfigRecord.market))

        // then
        val recommendationRecords = dsl.selectFrom(Tables.SQR_SHORT_SHELF_LIFE).fetch()
        val poCutOffQty = BigDecimal.valueOf(9)

        assertEquals(1, recommendationRecords.size)

        assertSqr(calculationRecord, configuration, recommendationRecords.first(), poCutOffQty)
        val poDetails = objectMapper.readValue<PoData>(
            recommendationRecords.first().poDetails.data(),
        )
        assertEquals("2305DH046273", poDetails.poDetails.first().poNumber)
        assertEquals(BigDecimal("500"), poDetails.poDetails.first().qty)
        assertEquals("2305DH046273", poDetails.inboundDetails.first().poNumber)
        assertEquals(BigDecimal("9"), poDetails.inboundDetails.first().qty)

        assertTopicRecords(recommendationRecords, BigDecimal("100"))
    }

    @Test
    fun `sqr ssl job produces from different dcs`() {
        // given
        val dcConfig1 = insertDcRecord("DC")
        val dcConfig2 = insertDcRecord("ZZ")

        val productionStart = DayOfWeek.valueOf(dcConfig1.productionStart)
        val dcWeek = DcWeek(LocalDate.now(UTC), productionStart)
        val startDateInDcWeek = dcWeek.getStartDateInDcWeek(productionStart, UTC)

        val calculationRecord1 = insertCalculation(
            dcConfig1.dcCode,
            productionStart,
            startDateInDcWeek,
            UUID.randomUUID(),
            uom = UOM_UNIT,
        )
        insertSKU(skuID = calculationRecord1.cskuId)

        val calculationRecord2 = insertCalculation(
            dcConfig2.dcCode,
            productionStart,
            startDateInDcWeek.plusDays(1),
            UUID.randomUUID(),
            uom = UOM_UNIT,
        )
        insertCalculation(
            dcConfig1.dcCode,
            productionStart,
            LocalDate.now().plusDays(5L),
            UUID.randomUUID(),
            uom = UOM_UNIT,
        )
        insertCalculation(
            dcConfig2.dcCode,
            productionStart,
            LocalDate.now().plusDays(5L),
            UUID.randomUUID(),
            uom = UOM_UNIT,
        )
        insertSKU(skuID = calculationRecord2.cskuId)
        refreshDcConfigWeightView()
        // when
        runJob(setOf(dcConfig1.market, dcConfig2.market))

        // then
        val recommendationRecords = dsl.selectFrom(Tables.SQR_SHORT_SHELF_LIFE).fetch()

        assertEquals(2, recommendationRecords.size)

        assertSqr(calculationRecord1, null, recommendationRecords.first { it.dcCode == calculationRecord1.dcCode })
        assertSqr(calculationRecord2, null, recommendationRecords.first { it.dcCode == calculationRecord2.dcCode })
    }

    @ParameterizedTest
    @CsvSource(
        value = [
            "700, 500, 100, 10, 50, true, 0",
            "1000, 500, 100, 10, 50, true, 0",
            "0, 10, 1, 0, 10, true, 19",
            "100, 1500, null, 10, 10, true, 1560",
        ],
        nullValues = ["null"],
    )
    fun `sqr ssl job produces recommendations for different values`(
        openingStock: BigDecimal,
        demand: BigDecimal,
        stockUpdate: BigDecimal?,
        bufferPercentage: BigDecimal,
        bufferAdditional: BigDecimal,
        touchlessOrderingEnabled: Boolean,
        expectedSqr: BigDecimal,
    ) {
        // given
        val dcConfigRecord = insertDcRecord("DC")

        val productionStart = DayOfWeek.valueOf(dcConfigRecord.productionStart)
        val dcWeek = DcWeek(LocalDate.now(UTC), productionStart)
        val startDateInDcWeek = dcWeek.getStartDateInDcWeek(productionStart, UTC)

        val calculationRecord = insertCalculation(
            dcConfigRecord.dcCode,
            productionStart,
            startDateInDcWeek,
            UUID.randomUUID(),
            openingStock = openingStock,
            demand = demand,
            stockUpdate = stockUpdate,
            uom = UOM_UNIT,
        )

        insertCalculation(
            dcConfigRecord.dcCode,
            productionStart,
            LocalDate.now().plusDays(5L),
            UUID.randomUUID(),
            uom = UOM_UNIT,
        )

        insertSKU(skuID = calculationRecord.cskuId)

        val configuration = insertSQRShortShelfLifeConfiguration(
            dcConfigRecord.dcCode,
            startDateInDcWeek,
            calculationRecord.cskuId,
            bufferPercentage,
            bufferAdditional,
            touchlessOrderingEnabled,
        )
        refreshDcConfigWeightView()
        // when
        runJob(setOf(dcConfigRecord.market))

        // then
        val recommendationRecords = dsl.selectFrom(Tables.SQR_SHORT_SHELF_LIFE).fetch()

        assertEquals(1, recommendationRecords.size)

        with(recommendationRecords.first { it.dcCode == calculationRecord.dcCode }) {
            assertSqr(calculationRecord, configuration, this)
            assertTrue(touchlessOrderingEnabled)
            assertEquals(expectedSqr, sqr)
        }
    }

    @Test
    fun `recommendations are produced for included sku categories`() {
        val dcConfigRecord = insertDcRecord("DC")
        val productionStart = DayOfWeek.valueOf(dcConfigRecord.productionStart)
        val dcWeek = DcWeek(LocalDate.now(UTC), productionStart)
        val startDateInDcWeek = dcWeek.getStartDateInDcWeek(productionStart, UTC)
        val calculationRecord1 = insertCalculation(
            dcConfigRecord.dcCode,
            productionStart,
            startDateInDcWeek,
            UUID.randomUUID(),
            uom = UOM_UNIT,
        )
        insertCalculation(
            dcConfigRecord.dcCode,
            productionStart,
            LocalDate.now().plusDays(5L),
            UUID.randomUUID(),
            uom = UOM_UNIT,
        )
        val sku1 = insertSKU(skuID = calculationRecord1.cskuId)

        val calculationRecord2 = insertCalculation(
            dcConfigRecord.dcCode,
            productionStart,
            startDateInDcWeek,
            UUID.randomUUID(),
            uom = UOM_UNIT,
        )
        insertSKU(skuID = calculationRecord2.cskuId, skuCategory = "OTH")

        refreshDcConfigWeightView()
        // when
        runJob(
            mapOf(
                dcConfigRecord.market to SqrShortShelfLifeDcParam(
                    skuCategoriesIncluded = setOf(sku1.category),
                    emptySet()
                ),
            ),
        )

        // then
        val recommendationRecords = dsl.selectFrom(Tables.SQR_SHORT_SHELF_LIFE).fetch()

        assertEquals(1, recommendationRecords.size)

        assertSqr(calculationRecord1, null, recommendationRecords.first())
    }

    // case 1. in ssl config, we have full weak
    // case 2. in ssl config, we have only 4 days
    @ParameterizedTest
    @CsvSource(
        value = [
            "0",
            "3",
        ]
    )
    fun `supply quantity recommendations are produced and flag is set correctly if config exists only for current week`(
        since: Int
    ) {
        val (currentWeek, _, _) = prepareSQRShortShelfLifeTests()

        insertSQRShortShelfLifeConfForProductionWeek(
            currentWeek.getStartDateInDcWeek(productionStart, UTC),
            true,
            since
        )

        runJob(
            mapOf(
                market to SqrShortShelfLifeDcParam(
                    skuCategoriesIncluded = setOf(skuCategory),
                    emptySet()
                ),
            ),
        )

        val recommendationRecords = dsl.selectFrom(Tables.SQR_SHORT_SHELF_LIFE).fetch()

        val sqrSsls = recommendationRecords.map {
            SQRShortShelfLife(
                dcCode = it.dcCode,
                date = it.date,
                skuId = it.skuId,
                touchlessOrderingEnabled = it.touchlessOrderingEnabled,
                productionWeek = it.productionWeek,
            )
        }

        assertTopicRecords(recommendationRecords, BigDecimal("0"))
        assertEquals(true, sqrSsls.all { it.touchlessOrderingEnabled })
    }

    @Test
    fun `supply quantity recommendations are produced and flag is set correctly if config exists for current week and week after next`() {
        val (currentWeek, nextWeek, weekAfterNext) = prepareSQRShortShelfLifeTests()

        insertSQRShortShelfLifeConfForProductionWeek(
            currentWeek.getStartDateInDcWeek(productionStart, UTC),
            true
        )

        insertSQRShortShelfLifeConfForProductionWeek(
            weekAfterNext.getStartDateInDcWeek(productionStart, UTC),
            false
        )

        runJob(
            mapOf(
                market to SqrShortShelfLifeDcParam(
                    skuCategoriesIncluded = setOf(skuCategory),
                    emptySet()
                ),
            ),
        )

        val recommendationRecords = dsl.selectFrom(Tables.SQR_SHORT_SHELF_LIFE).fetch()

        val sqrSsls = recommendationRecords.map {
            SQRShortShelfLife(
                dcCode = it.dcCode,
                date = it.date,
                skuId = it.skuId,
                touchlessOrderingEnabled = it.touchlessOrderingEnabled,
                productionWeek = it.productionWeek,
            )
        }.groupBy { it.productionWeek }

        assertTopicRecords(recommendationRecords, BigDecimal("0"))
        assertEquals(true, sqrSsls[currentWeek.value]?.all { it.touchlessOrderingEnabled })
        assertEquals(true, sqrSsls[nextWeek.value]?.all { it.touchlessOrderingEnabled })
        assertEquals(false, sqrSsls[weekAfterNext.value]?.all { it.touchlessOrderingEnabled })
    }

    @Test
    fun `supply quantity recommendations are produced and flag is set correctly if bnl file uploaded for next week`() {
        val (currentWeek, nextWeek, _) = prepareSQRShortShelfLifeTests()

        insertSQRShortShelfLifeConfForProductionWeek(
            currentWeek.getStartDateInDcWeek(productionStart, UTC),
            true
        )

        val key = "bnl_buffer_calculations/bnl_buffers.csv"

        val csvHeader = "Week;Date;DC;SKU Code;Buffer Value"
        val csvRows = listOf(
            "$nextWeek;${nextWeek.getStartDateInDcWeek(productionStart, UTC)};$dcCode;$skuCode;4",
            "$nextWeek;${nextWeek.getStartDateInDcWeek(productionStart, UTC).plusDays(1)};$dcCode;$skuCode;4",
            "$nextWeek;${nextWeek.getStartDateInDcWeek(productionStart, UTC).plusDays(2)};$dcCode;$skuCode;4"
        )
        val csvContent = (listOf(csvHeader) + csvRows).joinToString("\n")
        val toByteArray = csvContent.toByteArray(StandardCharsets.UTF_8)

        coEvery {
            s3Importer.fetchObjectContent(
                BUCKET_NAME,
                key
            )
        } returns ByteArrayInputStream(toByteArray)

        // first run to create data in ssl table
        runJob(
            mapOf(
                market to SqrShortShelfLifeDcParam(
                    skuCategoriesIncluded = setOf(skuCategory),
                    emptySet()
                ),
            ),
        )

        runBlocking {
            bufferCalculationService.process(
                S3File(
                    BUCKET_NAME,
                    key
                )
            )
        }

        // second run to update ssl data in ssl table after s3 import
        runJob(
            mapOf(
                market to SqrShortShelfLifeDcParam(
                    skuCategoriesIncluded = setOf(skuCategory),
                    emptySet()
                ),
            ),
        )

        val recommendationRecords = dsl.selectFrom(Tables.SQR_SHORT_SHELF_LIFE).fetch()

        val sqrSsls = recommendationRecords.map {
            SQRShortShelfLife(
                dcCode = it.dcCode,
                date = it.date,
                skuId = it.skuId,
                touchlessOrderingEnabled = it.touchlessOrderingEnabled,
                productionWeek = it.productionWeek,
            )
        }

        assertEquals(true, sqrSsls.all { it.touchlessOrderingEnabled })
    }

    @Test
    fun `supply quantity recommendations are produced for excluded sku categories`() {
        val dcConfigRecord = insertDcRecord("DC")
        val productionStart = DayOfWeek.valueOf(dcConfigRecord.productionStart)
        val dcWeek = DcWeek(LocalDate.now(UTC), productionStart)
        val startDateInDcWeek = dcWeek.getStartDateInDcWeek(productionStart, UTC)
        val calculationRecord1 = insertCalculation(
            dcConfigRecord.dcCode,
            productionStart,
            startDateInDcWeek,
            UUID.randomUUID(),
            uom = UOM_UNIT,
        )
        insertSKU(skuID = calculationRecord1.cskuId)

        val calculationRecord2 = insertCalculation(
            dcConfigRecord.dcCode,
            productionStart,
            startDateInDcWeek,
            UUID.randomUUID(),
            uom = UOM_UNIT,
        )
        insertCalculation(
            dcConfigRecord.dcCode,
            productionStart,
            LocalDate.now().plusDays(5L),
            UUID.randomUUID(),
            uom = UOM_UNIT,
        )
        val sku2 = insertSKU(skuID = calculationRecord2.cskuId, skuCategory = "OTH")
        refreshDcConfigWeightView()
        // when
        runJob(
            mapOf(
                dcConfigRecord.market to SqrShortShelfLifeDcParam(
                    skuCategoriesExcluded = setOf(sku2.category),
                    skuCategoriesIncluded = emptySet()
                ),
            ),
        )

        // then
        val recommendationRecords = dsl.selectFrom(Tables.SQR_SHORT_SHELF_LIFE).fetch()

        assertEquals(1, recommendationRecords.size)

        assertSqr(calculationRecord1, null, recommendationRecords.first())
    }

    private fun assertTopicRecords(
        recommendationRecords: List<SqrShortShelfLifeRecord>,
        sqrDetailsQty: BigDecimal = BigDecimal("200")
    ): List<ConsumerRecord<SupplyQuantityRecommendationDailyKey, SupplyQuantityRecommendationDailyVal>> {
        val records =
            mutableListOf<ConsumerRecord<SupplyQuantityRecommendationDailyKey, SupplyQuantityRecommendationDailyVal>>()
        Awaitility
            .await()
            .atMost(Duration.ofSeconds(30))
            .until {
                val consumerRecords = expectedSqrSSLTopicConsumer.poll(Duration.ofMillis(100))
                records.addAll(consumerRecords)
                records.count() == recommendationRecords.size
            }
        recommendationRecords.forEach { sqr ->
            assertTopicRecord(sqr, records.first { isSameKey(sqr, it.key()) }, sqrDetailsQty)
        }
        return records
    }

    private fun assertTopicRecord(
        sqr: SqrShortShelfLifeRecord,
        consumerRecord: ConsumerRecord<SupplyQuantityRecommendationDailyKey, SupplyQuantityRecommendationDailyVal>,
        sqrDetailsQty: BigDecimal = BigDecimal("200")
    ) {
        with(consumerRecord) {
            assertEquals(sqr.dcCode, key().dcCode)
            assertEquals(sqr.date.year, key().date.year)
            assertEquals(sqr.date.monthValue, key().date.month)
            assertEquals(sqr.date.dayOfMonth, key().date.day)
            assertEquals(sqr.skuId, UUID.fromString(key().skuId))
            assertEquals(sqr.openingSock, value().inventoryRollover.qty.toBigDecimal())
            assertEquals(sqr.consumption, value().forecastedDemanded.qty.toBigDecimal())
            assertEquals(sqrDetailsQty, value().bufferDetails.qty.toBigDecimal())
            assertEquals(DcWeek(sqr.productionWeek).year, value().productionWeek.year)
            assertEquals(DcWeek(sqr.productionWeek).week, value().productionWeek.week)
            assertEquals(sqr.sqr, value().supplyQuantityRecommendation.qty.toBigDecimal())
            assertEquals(sqr.date.year, value().date.year)
            assertEquals(sqr.date.monthValue, value().date.month)
            assertEquals(sqr.date.dayOfMonth, value().date.day)
            assertEquals(sqr.touchlessOrderingEnabled, value().touchlessOrderingEnabled)

            val poNumbers = value().purchaseOrder.takeIf { it.poNumbersList.isNotEmpty() }
            poNumbers?.getPoNumbers(0)?.qty?.toBigDecimal()?.let { qty ->
                assertEquals(BigDecimal(500), qty)
            }
            poNumbers?.getPoNumbers(0)?.poNumber?.let { price ->
                assertEquals("2305DH046273", poNumber)
            }

            val inbounds = value().inbounds.takeIf { it.poNumbersList.isNotEmpty() }
            inbounds?.getPoNumbers(0)?.qty?.toBigDecimal()?.let { qty ->
                assertEquals(BigDecimal(9), qty)
            }
            inbounds?.getPoNumbers(0)?.poNumber?.let { price ->
                assertEquals("2305DH046273", poNumber)
            }
        }
    }

    private fun Decimal.toBigDecimal() = BigDecimal(this.value)

    private fun assertSqr(
        calculationRecord: CalculationRecord,
        sqrShortShelfLifeConfRecord: SqrShortShelfLifeConfRecord?,
        sqrShortShelfLifeRecord: SqrShortShelfLifeRecord,
        poCutOffQty: BigDecimal = BigDecimal.valueOf(0),
    ) {
        with(sqrShortShelfLifeRecord) {
            assertEquals(calculationRecord.dcCode, dcCode)
            assertEquals(calculationRecord.cskuId, skuId)
            assertEquals(calculationRecord.date, date)
            assertEquals(calculationRecord.uom, uom)
            assertEquals(calculationRecord.productionWeek, productionWeek)
            assertEquals(
                SkuQuantity.fromBigDecimal(
                    max(
                        ZERO,
                        calculationRecord.demanded +
                            (
                                sqrShortShelfLifeConfRecord?.let {
                                    calculationRecord.demanded.multiply(it.bufferPercentage.movePointLeft(2)) + it.bufferAdditional
                                } ?: ZERO
                                ) -
                            (calculationRecord.openingStock + (calculationRecord.stockUpdate ?: ZERO)) - poCutOffQty,
                    ) as BigDecimal,
                    valueOf(calculationRecord.uom.name),
                ).getValue(),
                sqr,
            )
            assertEquals(calculationRecord.openingStock, openingSock)
            assertEquals(calculationRecord.demanded, consumption)
            assertEquals(calculationRecord.stockUpdate, stockUpdate)
            if (sqrShortShelfLifeConfRecord != null) {
                assertEquals(sqrShortShelfLifeConfRecord.bufferPercentage, bufferPercentage)
                assertEquals(sqrShortShelfLifeConfRecord.bufferAdditional, bufferAdditional)
                assertTrue(sqrShortShelfLifeConfRecord.touchlessOrderingEnabled)
            } else {
                assertEquals(
                    default(
                        calculationRecord.dcCode,
                        calculationRecord.cskuId,
                        calculationRecord.date,
                    ).bufferPercentage,
                    bufferPercentage,
                )
                assertEquals(
                    default(
                        calculationRecord.dcCode,
                        calculationRecord.cskuId,
                        calculationRecord.date,
                    ).bufferAdditional,
                    bufferAdditional,
                )
            }
        }
    }

    private fun isSameKey(
        sqr: SqrShortShelfLifeRecord,
        key: SupplyQuantityRecommendationDailyKey
    ) = key.skuId == sqr.skuId.toString() &&
        key.dcCode == sqr.dcCode &&
        key.date.year == sqr.date.year &&
        key.date.month == sqr.date.monthValue &&
        key.date.day == sqr.date.dayOfMonth

    private fun runJob(markets: Set<String>) {
        runJob(markets.associateWith { SqrShortShelfLifeDcParam(emptySet(), emptySet()) })
    }

    private fun runJob(params: Map<String, SqrShortShelfLifeDcParam>) {
        runBlocking {
            Main.createSQRShortShelfLifeJob(
                SqrShortShelfLifeParams(params),
                meterRegistry,
                dcBalancer(1, "whatevername-0", dsl),
                dsl,
                dsl,
                SQRShortShelfLifeService.createProducer(producerConfig),
                statsigFeatureFlagClient,
            ).execute()
        }
    }

    private fun prepareSQRShortShelfLifeTests(): Triple<DcWeek, DcWeek, DcWeek> {
        val dcConfigRecord = insertDcRecord(dcCode, market = market, productionStart = productionStart)
        val productionStart = DayOfWeek.valueOf(dcConfigRecord.productionStart)

        val currentWeek = DcWeek(LocalDate.now(), productionStart)
        val nextWeek = DcWeek(LocalDate.now().plusWeeks(1), productionStart)
        val weekAfterNext = DcWeek(LocalDate.now().plusWeeks(2), productionStart)

        insertSKU(skuId, skuCategory, skuCode, market)

        insertCalculationForProductionWeek(productionStart, currentWeek.getStartDateInDcWeek(productionStart, UTC))
        insertCalculationForProductionWeek(productionStart, nextWeek.getStartDateInDcWeek(productionStart, UTC))
        insertCalculationForProductionWeek(productionStart, weekAfterNext.getStartDateInDcWeek(productionStart, UTC))

        return Triple(currentWeek, nextWeek, weekAfterNext)
    }

    private fun insertCalculationForProductionWeek(
        productionStart: DayOfWeek,
        startDateInDcWeek: LocalDate
    ) {
        for (i in 0 until 7) {
            val date = startDateInDcWeek.plusDays(i.toLong())
            insertCalculation(
                dcCode,
                productionStart,
                date,
                skuId,
                uom = UOM_UNIT,
            )
        }
    }

    private fun insertSQRShortShelfLifeConfForProductionWeek(
        date: LocalDate,
        touchlessOrderingEnabled: Boolean,
        since: Int = 0
    ) {
        for (i in since until 7) {
            val date = date.plusDays(i.toLong())
            insertSQRShortShelfLifeConfiguration(
                dcCode,
                date,
                skuId,
                ZERO,
                ZERO,
                touchlessOrderingEnabled
            )
        }
    }

    @SuppressWarnings("LongParameterList")
    fun insertSQRShortShelfLife(
        dcCode: String,
        date: LocalDate,
        skuId: UUID,
        bufferPercentage: BigDecimal = TEN,
        bufferAdditional: BigDecimal = TEN,
        touchlessOrderingEnabled: Boolean = false
    ) =
        SqrShortShelfLifeRecord().apply {
            this.dcCode = dcCode
            this.skuId = skuId
            this.date = date
            this.bufferPercentage = bufferPercentage
            this.bufferAdditional = bufferAdditional
            this.touchlessOrderingEnabled = touchlessOrderingEnabled
            this.openingSock = BigDecimal("10000")
            this.unusableStock = TEN
            this.stockUpdate = TEN
            this.consumption = TEN
            this.uom = UOM_UNIT
            this.sqr = TEN
        }.also {
            dsl.batchInsert(it).execute()
        }

    @SuppressWarnings("LongParameterList")
    fun insertSQRShortShelfLifeConfiguration(
        dcCode: String,
        date: LocalDate,
        skuId: UUID,
        bufferPercentage: BigDecimal,
        bufferAdditional: BigDecimal,
        touchlessOrderingEnabled: Boolean = false
    ) =
        SqrShortShelfLifeConfRecord().apply {
            this.dcCode = dcCode
            this.skuId = skuId
            this.date = date
            this.bufferPercentage = bufferPercentage
            this.bufferAdditional = bufferAdditional
            this.touchlessOrderingEnabled = touchlessOrderingEnabled
        }.also {
            dsl.batchInsert(it).execute()
        }
}

data class SQRShortShelfLife(
    val dcCode: String,
    val date: LocalDate,
    val skuId: UUID,
    val touchlessOrderingEnabled: Boolean,
    val productionWeek: String,
)
