package com.hellofresh.cif.sqr.repository

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.sqr.FunctionalTest
import com.hellofresh.cif.sqr.schema.enums.Uom
import java.math.BigDecimal
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.WEDNESDAY
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class WeeklyCalculationsRepositoryTest : FunctionalTest() {

    @Test
    fun `weekly calculations are fetched from latest production date`() {
        val distributionCenterConfiguration1 = DistributionCenterConfiguration.Companion.default()
            .copy(
                productionStart = FRIDAY,
                zoneId = UTC,
            )
        val distributionCenterConfiguration2 = DistributionCenterConfiguration.Companion.default().copy(
            dcCode = "DC",
            productionStart = WEDNESDAY,
            zoneId = UTC,
        )

        val today = LocalDate.now(UTC)
        val skuId1 = UUID.randomUUID()
        val uom = Uom.UOM_GAL

        val calc1Day1 = insertCalculation(
            distributionCenterConfiguration1,
            distributionCenterConfiguration1.getProductionStartDate(today),
            skuId1,
            openingStock = BigDecimal(100),
            demand = BigDecimal(10),
            uom = uom,
        )
        val calc1Day2 = insertCalculation(
            distributionCenterConfiguration1,
            distributionCenterConfiguration1.getProductionStartDate(today).plusDays(1),
            skuId1,
            openingStock = BigDecimal(1000),
            demand = BigDecimal(50),
            uom = uom,
        )
        val calc1Day3 = insertCalculation(
            distributionCenterConfiguration1,
            distributionCenterConfiguration1.getProductionStartDate(today).plusWeeks(1),
            skuId1,
            openingStock = BigDecimal(200),
            demand = BigDecimal(500),
            uom = uom,
        )

        val skuId2 = UUID.randomUUID()
        val calc2Day1 = insertCalculation(
            distributionCenterConfiguration2,
            distributionCenterConfiguration2.getLatestProductionStart(),
            skuId2,
            openingStock = BigDecimal(400),
            demand = BigDecimal(500),
            uom = uom,
        )

        insertCalculation(
            DistributionCenterConfiguration.Companion.default("XX"),
            today,
            skuId1,
            openingStock = BigDecimal(400),
            demand = BigDecimal(500),
            uom = uom,
        )

        val weeklyCalculations =
            runBlocking {
                weeklyCalculationsRepository.fetchWeeklyCalculations(
                    setOf(distributionCenterConfiguration1, distributionCenterConfiguration2),
                )
            }

        assertEquals(3, weeklyCalculations.size)

        assertWeeklyCalculation(
            distributionCenterConfiguration1,
            calc1Day1.productionWeek,
            skuId1,
            calc1Day1.openingStock,
            calc1Day1.demanded + calc1Day2.demanded,
            uom,
            weeklyCalculations,
        )

        assertWeeklyCalculation(
            distributionCenterConfiguration1,
            calc1Day3.productionWeek,
            skuId1,
            calc1Day3.openingStock,
            calc1Day3.demanded,
            uom,
            weeklyCalculations,
        )

        assertWeeklyCalculation(
            distributionCenterConfiguration2,
            calc2Day1.productionWeek,
            skuId2,
            calc2Day1.openingStock,
            calc2Day1.demanded,
            uom,
            weeklyCalculations,
        )
    }

    @Test
    fun `return empty weekly calculations when  dc list is empty`() {
        assertTrue(
            runBlocking { weeklyCalculationsRepository.fetchWeeklyCalculations(emptySet()) }
                .isEmpty(),
        )
    }

    @SuppressWarnings("LongParameterList")
    private fun assertWeeklyCalculation(
        distributionCenterConfiguration: DistributionCenterConfiguration,
        week: String,
        skuId: UUID,
        expectedOpeningStock: BigDecimal,
        expectedAggregatedDemand: BigDecimal,
        uom: Uom,
        weeklyCalculations: List<WeeklyCalculation>
    ) {
        with(
            weeklyCalculations.first {
                it.skuId == skuId && it.dcCode == distributionCenterConfiguration.dcCode && it.week == week
            },
        ) {
            assertEquals(expectedOpeningStock, productionStartOpeningStock.getValue())
            assertEquals(uom.name, productionStartOpeningStock.unitOfMeasure.name)
            assertEquals(expectedAggregatedDemand, aggregatedDemand.getValue())
            assertEquals(uom.name, aggregatedDemand.unitOfMeasure.name)
        }
    }
}
