package com.hellofresh.cif.sqr

import com.google.type.Decimal
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.cif.models.SkuUOM.valueOf
import com.hellofresh.cif.safetystocklib.schema.tables.records.SafetyStocksRecord
import com.hellofresh.cif.sqr.Main.dcBalancer
import com.hellofresh.cif.sqr.SQRConfiguration.Companion.DEFAULT_MULTI_WEEK_ENABLED
import com.hellofresh.cif.sqr.SQRConfiguration.Companion.DEFAULT_RECOMMENDATION_ENABLED
import com.hellofresh.cif.sqr.repository.WeeklyCalculation
import com.hellofresh.cif.sqr.schema.Tables
import com.hellofresh.cif.sqr.schema.enums.Uom
import com.hellofresh.cif.sqr.schema.tables.records.CalculationRecord
import com.hellofresh.cif.sqr.schema.tables.records.SupplyQuantityRecommendationRecord
import com.hellofresh.cif.sqr.service.SupplyQuantityRecommendationService
import com.hellofresh.cif.sqrlib.schema.tables.records.SupplyQuantityRecommendationConfRecord
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.SupplyQuantityRecommendationKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.SupplyQuantityRecommendationVal
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.Duration
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.random.Random
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlinx.coroutines.runBlocking
import org.apache.commons.lang.ObjectUtils.max
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.junit.jupiter.api.Test
import org.testcontainers.shaded.org.awaitility.Awaitility
import random

class SupplyQuantityRecommendationJobTest : FunctionalTest() {

    @Test
    fun `sqr job produces recommendations`() {
        // given
        val dcConfigRecord = insertDcRecord("DC")
        val productionStart = DayOfWeek.valueOf(dcConfigRecord.productionStart)
        val dcWeek = DcWeek(LocalDate.now(UTC), productionStart)
        val startDateInDcWeek = dcWeek.getStartDateInDcWeek(productionStart, UTC)
        val incomingPos = Random.nextLong()
        val actualInbound = Random.nextLong()
        val skuId = UUID.randomUUID()
        val calculationRecord = insertCalculation(
            dcConfigRecord.dcCode,
            productionStart,
            startDateInDcWeek,
            skuId,
            uom = Uom.UOM_UNIT,
            incomingPos = incomingPos.toBigDecimal(),
            actualInbound = actualInbound.toBigDecimal(),
        )

        insertCalculation(
            dcConfigRecord.dcCode,
            productionStart,
            LocalDate.now().plusDays(5),
            UUID.randomUUID(),
            uom = Uom.UOM_UNIT,
        )

        val sqrConfigurationRecord =
            insertSQRConfiguration(
                dcConfigRecord.dcCode,
                productionStart,
                startDateInDcWeek,
                calculationRecord.cskuId,
                !DEFAULT_RECOMMENDATION_ENABLED,
                !DEFAULT_MULTI_WEEK_ENABLED,
            )
        val safetyStockRecord = insertSafetyStock(
            dcConfigRecord.dcCode,
            productionStart,
            startDateInDcWeek,
            calculationRecord.cskuId,
            2322,
        )

        refreshDcConfigWeightView()

        // when
        runJob()

        // then
        val recommendationRecords = dsl.selectFrom(Tables.SUPPLY_QUANTITY_RECOMMENDATION).fetch()

        assertEquals(2, recommendationRecords.size)

        assertSqr(
            calculationRecord,
            sqrConfigurationRecord,
            safetyStockRecord,
            recommendationRecords.find { it.skuId == skuId }!!,
        )
    }

    @Test
    fun `supply quantity recommendations are produced from different sources for given dcs`() {
        // given
        val dcConfig1 = insertDcRecord("DC")
        val dcConfig2 = insertDcRecord("ZZ")

        val productionStart = DayOfWeek.valueOf(dcConfig1.productionStart)
        val dcWeek = DcWeek(LocalDate.now(UTC), productionStart)
        val startDateInDcWeek = dcWeek.getStartDateInDcWeek(productionStart, UTC)

        val weeklyCalculation1 = WeeklyCalculation.random(dcCode = dcConfig1.dcCode, uom = UOM_UNIT)
        val calculationRecord1 = createCalculationRecord(weeklyCalculation1, productionStart, startDateInDcWeek)
        createCalculationRecord(weeklyCalculation1, productionStart, LocalDate.now().plusDays(5))
        val weeklyCalculation2 = WeeklyCalculation.random(dcCode = dcConfig2.dcCode, uom = UOM_UNIT)

        val calculationRecord2 = createCalculationRecord(
            weeklyCalculation2,
            productionStart,
            startDateInDcWeek.plusWeeks(1),
        )
        createCalculationRecord(
            weeklyCalculation2,
            productionStart,
            LocalDate.now().plusWeeks(1),
        )
        val sqrConfigurationRecord =
            insertSQRConfiguration(
                dcConfig1.dcCode,
                productionStart,
                startDateInDcWeek,
                calculationRecord1.cskuId,
                !DEFAULT_RECOMMENDATION_ENABLED,
                !DEFAULT_MULTI_WEEK_ENABLED,
            )

        val safetyStockRecord = insertSafetyStock(
            dcConfig1.dcCode,
            productionStart,
            startDateInDcWeek,
            calculationRecord1.cskuId,
            Random.nextLong(),
        )
        refreshDcConfigWeightView()
        // when
        runJob()

        // then
        val recommendationRecords = dsl.selectFrom(Tables.SUPPLY_QUANTITY_RECOMMENDATION).fetch()

        assertEquals(4, recommendationRecords.size)

        assertSqr(
            calculationRecord1,
            sqrConfigurationRecord,
            safetyStockRecord,
            recommendationRecords.first { it.dcCode == calculationRecord1.dcCode && it.skuId == calculationRecord1.cskuId },
        )
        assertSqr(
            calculationRecord2,
            null,
            null,
            recommendationRecords.first { it.dcCode == calculationRecord2.dcCode && it.skuId == calculationRecord2.cskuId },
        )

        assertTopicRecords(recommendationRecords.toList())
    }

    private fun assertTopicRecords(
        recommendationRecords: List<SupplyQuantityRecommendationRecord>
    ): List<ConsumerRecord<SupplyQuantityRecommendationKey, SupplyQuantityRecommendationVal>> {
        val records = mutableListOf<ConsumerRecord<SupplyQuantityRecommendationKey, SupplyQuantityRecommendationVal>>()
        Awaitility
            .await()
            .atMost(Duration.ofSeconds(20))
            .until {
                val consumerRecords = expectedSqrTopicConsumer.poll(Duration.ofMillis(100))
                records.addAll(consumerRecords)
                records.count() == recommendationRecords.size
            }
        recommendationRecords.forEach { sqr ->
            assertTopicRecord(sqr, records.first { isSameKey(sqr, it.key()) })
        }
        return records
    }

    private fun isSameKey(
        sqr: SupplyQuantityRecommendationRecord,
        key: SupplyQuantityRecommendationKey
    ) = key.skuId == sqr.skuId.toString() &&
        key.distributionCenterBobCode == sqr.dcCode &&
        key.productionWeek.week == DcWeek(sqr.week).week &&
        key.productionWeek.year == DcWeek(sqr.week).year

    private fun assertTopicRecord(
        sqr: SupplyQuantityRecommendationRecord,
        consumerRecord: ConsumerRecord<SupplyQuantityRecommendationKey, SupplyQuantityRecommendationVal>
    ) {
        with(consumerRecord) {
            val dcWeek = DcWeek(sqr.week)
            assertEquals(sqr.dcCode, key().distributionCenterBobCode)
            assertEquals(dcWeek.week, key().productionWeek.week)
            assertEquals(dcWeek.year, key().productionWeek.year)
            assertEquals(sqr.skuId, UUID.fromString(key().skuId))
            assertEquals(sqr.inbound, value().inbound.toBigDecimal())
            assertEquals(sqr.incomingPos, value().incomingPos.toBigDecimal())
            assertEquals(sqr.unusableStock, value().unusableStock.toBigDecimal())

            assertEquals(key().distributionCenterBobCode, key().distributionCenterBobCode)
            assertEquals(key().productionWeek.week, value().productionWeek.week)
            assertEquals(key().productionWeek.year, key().productionWeek.year)
            assertEquals(key().skuId, value().skuId)

            assertEquals(sqr.sqr, value().supplyQuantityRecommendation.toBigDecimal())
            assertEquals(sqr.inventoryRollover, value().inventoryRollover.toBigDecimal())
            assertEquals(sqr.demand, value().demand.toBigDecimal())
            if (sqr.safetyStock != null) {
                assertEquals(sqr.safetyStock, value().safetyStock.toBigDecimal())
            } else {
                assertFalse(value().hasSafetyStock())
            }

            assertEquals(sqr.recommendationEnabled, value().recommendationEnabled)
            assertEquals(sqr.multiWeekEnabled, value().multiWeekEnabled)
        }
    }

    private fun Decimal.toBigDecimal() = BigDecimal(this.value)

    private fun assertSqr(
        calculationRecord: CalculationRecord,
        supplyQuantityRecommendationConfRecord: SupplyQuantityRecommendationConfRecord?,
        safetyStockRecord: SafetyStocksRecord?,
        supplyQuantityRecommendationRecord: SupplyQuantityRecommendationRecord
    ) {
        with(supplyQuantityRecommendationRecord) {
            assertEquals(calculationRecord.dcCode, dcCode)
            assertEquals(calculationRecord.cskuId, skuId)
            assertEquals(calculationRecord.productionWeek, week)
            assertEquals(calculationRecord.uom, uom)
            assertEquals(
                SkuQuantity.fromBigDecimal(
                    max(
                        BigDecimal.ZERO,
                        (safetyStockRecord?.safetyStock?.toBigDecimal() ?: BigDecimal.ZERO) + calculationRecord.demanded - calculationRecord.openingStock,
                    ) as BigDecimal,
                    valueOf(calculationRecord.uom.name),
                ).getValue(),
                sqr,
            )
            assertEquals(calculationRecord.openingStock, inventoryRollover)
            assertEquals(calculationRecord.demanded, demand)
            assertEquals(calculationRecord.actualInbound, inbound)
            assertEquals(calculationRecord.expectedInbound, incomingPos)
            assertEquals(safetyStockRecord?.safetyStock?.toBigDecimal(), safetyStock)
            if (supplyQuantityRecommendationConfRecord != null) {
                assertEquals(supplyQuantityRecommendationConfRecord.recommendationEnabled, recommendationEnabled)
                assertEquals(supplyQuantityRecommendationConfRecord.multiWeekEnabled, multiWeekEnabled)
            } else {
                assertEquals(DEFAULT_RECOMMENDATION_ENABLED, recommendationEnabled)
                assertEquals(DEFAULT_MULTI_WEEK_ENABLED, multiWeekEnabled)
            }
        }
    }

    private fun runJob() {
        runBlocking {
            Main.createSupplyQuantityRecommendationJob(
                meterRegistry,
                dcBalancer(1, "whatevername-0", dsl),
                dsl,
                dsl,
                SupplyQuantityRecommendationService.createProducer(producerConfig),
            ).execute()
        }
    }

    private fun createCalculationRecord(
        weeklyCalculation: WeeklyCalculation,
        productionStart: DayOfWeek,
        startDateInDcWeek: LocalDate
    ): CalculationRecord =
        with(weeklyCalculation) {
            insertCalculation(
                dcCode,
                productionStart,
                startDateInDcWeek,
                UUID.randomUUID(),
                productionStartOpeningStock.getValue(),
                aggregatedDemand.getValue(),
                uom = Uom.valueOf(productionStartOpeningStock.unitOfMeasure.name),
            )
        }
}
