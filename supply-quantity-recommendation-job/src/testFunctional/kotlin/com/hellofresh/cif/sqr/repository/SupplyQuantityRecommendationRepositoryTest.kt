package com.hellofresh.cif.sqr.repository

import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.sqr.FunctionalTest
import com.hellofresh.cif.sqr.random
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationRepository.upsertSupplyQuantityRecommendations
import com.hellofresh.cif.sqr.schema.Tables
import com.hellofresh.cif.sqr.schema.tables.records.SupplyQuantityRecommendationRecord
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class SupplyQuantityRecommendationRepositoryTest : FunctionalTest() {

    @Test
    fun `supply quantity recommendations are successfully persisted`() {
        val sqr1 = SupplyQuantityRecommendation.random()
        val sqr2 = SupplyQuantityRecommendation.random(dcCode = "D2", week = "2024-W03")

        runBlocking { dsl.upsertSupplyQuantityRecommendations(listOf(sqr1, sqr2)) }

        assertSqr(listOf(sqr1, sqr2))
    }

    @ParameterizedTest
    @CsvSource(
        "supplyQuantityRecommendationValue",
        "aggregatedDemand",
        "inventoryRollover",
        "safetyStock",
        "recommendationEnabled",
    )
    fun `supply quantity recommendation is updated when fields are different`(fieldName: String) {
        val sqr = SupplyQuantityRecommendation.random()

        runBlocking { dsl.upsertSupplyQuantityRecommendations(listOf(sqr)) }

        val record = assertSqr(sqr)

        val updatedSqr = sqr.copy()

        // Updates copy field
        var randomSqr = SupplyQuantityRecommendation.random()
        val originalField = updatedSqr::class.java.getDeclaredField(fieldName)
        originalField.isAccessible = true
        val originalFieldValue = originalField.get(updatedSqr)
        var randomField = randomSqr::class.java.getDeclaredField(fieldName)
        randomField.isAccessible = true
        while (originalFieldValue == randomField.get(randomSqr)) {
            randomSqr = SupplyQuantityRecommendation.random()
            randomField = randomSqr::class.java.getDeclaredField(fieldName)
            randomField.isAccessible = true
        }

        originalField.set(updatedSqr, randomField.get(randomSqr))
        originalField.isAccessible = false

        assertNotEquals(sqr, updatedSqr)

        runBlocking { dsl.upsertSupplyQuantityRecommendations(listOf(updatedSqr)) }

        val updatedRecord = assertSqr(updatedSqr)

        assertEquals(record.createdAt, updatedRecord.createdAt)
        assertTrue(record.updatedAt.isBefore(updatedRecord.updatedAt))
    }

    @Test
    fun `supply quantity recommendation is updated when uom from sqr value is different`() {
        val sqr = SupplyQuantityRecommendation.random()

        runBlocking { dsl.upsertSupplyQuantityRecommendations(listOf(sqr)) }

        val record = assertSqr(sqr)

        val updatedSqr = sqr.copy(
            supplyQuantityRecommendationValue =
            sqr.supplyQuantityRecommendationValue.copy(unitOfMeasure = SkuUOM.entries.first { it != sqr.uom }),
        )

        assertNotEquals(sqr, updatedSqr)

        runBlocking { dsl.upsertSupplyQuantityRecommendations(listOf(updatedSqr)) }

        val updatedRecord = assertSqr(updatedSqr)

        assertEquals(record.createdAt, updatedRecord.createdAt)
        assertTrue(record.updatedAt.isBefore(updatedRecord.updatedAt))
    }

    @Test
    fun `supply quantity recommendation is not updated when fields are exactly the same`() {
        val sqr = SupplyQuantityRecommendation.random()

        runBlocking { dsl.upsertSupplyQuantityRecommendations(listOf(sqr)) }

        val record = assertSqr(sqr)

        runBlocking { dsl.upsertSupplyQuantityRecommendations(listOf(sqr)) }

        val updatedRecord = assertSqr(sqr)

        assertEquals(record.createdAt, updatedRecord.createdAt)
        assertEquals(record.updatedAt, updatedRecord.updatedAt)
    }

    private fun assertSqr(supplyQuantityRecommendation: SupplyQuantityRecommendation) = assertSqr(
        listOf(supplyQuantityRecommendation),
    ).first()

    private fun assertSqr(supplyQuantityRecommendations: List<SupplyQuantityRecommendation>): List<SupplyQuantityRecommendationRecord> {
        val records = dsl.selectFrom(Tables.SUPPLY_QUANTITY_RECOMMENDATION).fetch()
        assertEquals(supplyQuantityRecommendations.size, records.size)
        supplyQuantityRecommendations.forEach { sqr ->
            val record = records.first { it.skuId == sqr.skuId }
            assertSqr(sqr, record)
        }
        return records
    }

    private fun assertSqr(
        supplyQuantityRecommendation: SupplyQuantityRecommendation,
        record: SupplyQuantityRecommendationRecord
    ) {
        assertEquals(supplyQuantityRecommendation.dcCode, record.dcCode)
        assertEquals(supplyQuantityRecommendation.skuId, record.skuId)
        assertEquals(supplyQuantityRecommendation.productionWeek, record.week)
        assertEquals(supplyQuantityRecommendation.uom.name, record.uom.name)
        assertEquals(supplyQuantityRecommendation.supplyQuantityRecommendationValue.getValue(), record.sqr)
        assertEquals(supplyQuantityRecommendation.supplyQuantityRecommendationValue.unitOfMeasure.name, record.uom.name)
        assertEquals(supplyQuantityRecommendation.inventoryRollover.getValue(), record.inventoryRollover)
        assertEquals(supplyQuantityRecommendation.aggregatedDemand.getValue(), record.demand)
        assertEquals(supplyQuantityRecommendation.safetyStock?.getValue(), record.safetyStock)
        assertEquals(supplyQuantityRecommendation.recommendationEnabled, record.recommendationEnabled)
        assertEquals(supplyQuantityRecommendation.multiWeekEnabled, record.multiWeekEnabled)
    }
}
