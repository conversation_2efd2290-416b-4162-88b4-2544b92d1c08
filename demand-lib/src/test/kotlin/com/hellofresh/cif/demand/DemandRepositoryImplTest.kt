package com.hellofresh.cif.demand

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.demand.lib.schema.Tables.ACTUAL_CONSUMPTION_INVENTORY_ACTIVITY
import com.hellofresh.cif.demand.lib.schema.Tables.ACTUAL_CONSUMPTION_VIEW
import com.hellofresh.cif.demand.lib.schema.Tables.DC_CONFIG
import com.hellofresh.cif.demand.lib.schema.Tables.DEMAND
import com.hellofresh.cif.demand.lib.schema.Tables.INVENTORY_ACTIVITY
import com.hellofresh.cif.demand.lib.schema.Tables.PICK_2_LIGHT
import com.hellofresh.cif.demand.lib.schema.Tables.SKU_SPECIFICATION
import com.hellofresh.cif.demand.lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.demand.lib.schema.enums.InventoryActivityType.ADJ
import com.hellofresh.cif.demand.lib.schema.enums.Uom
import com.hellofresh.cif.demand.lib.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.demand.lib.schema.tables.records.DemandRecord
import com.hellofresh.cif.demand.lib.schema.tables.records.InventoryActivityRecord
import com.hellofresh.cif.demand.lib.schema.tables.records.Pick_2LightRecord
import com.hellofresh.cif.demand.lib.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.Context.PACKAGING
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag
import com.hellofresh.cif.featureflags.FeatureFlag.UsableActualConsumptionDemandDomain
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.lib.kafka.serde.objectMapper
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.demand.models.ConsumptionDetails
import com.hellofresh.demand.models.Demands
import com.hellofresh.demand.models.Prekitting
import com.hellofresh.demand.models.Prekittings
import com.hellofresh.demand.models.RecipeBreakdown
import com.hellofresh.demand.models.Substitution
import com.hellofresh.demand.models.Substitutions
import com.hellofresh.inventory.models.InventoryAdjustmentTypeId.PCK
import com.hellofresh.inventory.models.InventoryAdjustmentValue
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_PRODUCTION
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.util.UUID
import java.util.concurrent.Executors
import java.util.stream.Stream
import kotlin.random.Random
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.jooq.JSONB
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments.of
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource

private const val IT = "IT"
private const val ALLOWED_PACKAGING_TYPE = "coolpouch"
private const val GB = "GB"
private const val DACH = "DACH"
private const val CA = "CA"
private const val AU = "AU"
private const val DKSE = "DKSE"
private const val DC_CODE = "VE"

internal class DemandRepositoryImplTest {

    private val skuId = UUID.randomUUID()
    private val demandQuantity = BigDecimal(7.1)
    private val actualConsumptionQty: Short = 11
    private val skuSpecRecord = createSkuSpecificationRecord()
    private val dcConfigRecord = createDcConfig()
    private val demandRecord = DemandRecord(
        skuId,
        DC_CODE,
        LocalDate.now(UTC),
        LocalDateTime.now(UTC),
        LocalDateTime.now(UTC),
        LocalDateTime.now(UTC),
        null,
        demandQuantity,
        null,
        null,
        null,
        Uom.UOM_UNIT,
    )
    private val p2lRecord = Pick_2LightRecord(
        "abc123",
        OffsetDateTime.now(UTC),
        actualConsumptionQty,
        skuSpecRecord.market,
        DC_CODE,
        skuSpecRecord.code,
    )

    @BeforeEach
    fun initData() {
        dsl.batchInsert(skuSpecRecord).execute()
        dsl.batchInsert(dcConfigRecord).execute()
        refreshSkuView()
    }

    @AfterEach
    fun afterEach() {
        dsl.deleteFrom(SKU_SPECIFICATION).execute()
        dsl.deleteFrom(DEMAND).execute()
        dsl.deleteFrom(PICK_2_LIGHT).execute()
        dsl.deleteFrom(DC_CONFIG).execute()
        dsl.deleteFrom(INVENTORY_ACTIVITY).execute()
        refreshSkuView()
    }

    fun refreshSkuView() =
        dsl.query("refresh materialized view ${SKU_SPECIFICATION_VIEW.name}").execute()

    @Test
    fun `returns empty demand records when there are no matching demands`() {
        val demandRecord = createDemand(10)
        dsl.batchInsert(demandRecord).execute()
        runBlocking {
            val demands = demandRepository.findDemands(
                setOf(DC_CODE),
                DateRange(
                    LocalDate.now(UTC).minusDays(3L),
                    LocalDate.now(UTC),
                ),
            )
            assertTrue(demands.isEmpty())
        }
    }

    @Test
    fun `returns demand records with consumption details`() {
        val expectedConsumptionDetails = ConsumptionDetails(
            recipeBreakdowns = listOf(
                RecipeBreakdown(
                    UUID.randomUUID().toString(),
                    UUID.randomUUID().toString(),
                    Random.nextLong(),
                ),
            ),
            prekitting = Prekittings(
                `in` = listOf(Prekitting(Random.nextLong())),
                out = listOf(Prekitting(Random.nextLong())),

            ),
            substitutions = Substitutions(
                `in` = listOf(
                    Substitution(
                        UUID.randomUUID().toString(),
                        UUID.randomUUID().toString(),
                        Random.nextLong(),
                    ),
                ),
                out = listOf(
                    Substitution(
                        UUID.randomUUID().toString(),
                        UUID.randomUUID().toString(),
                        Random.nextLong(),
                    ),
                ),
            ),
        )
        val consumptionDetails = objectMapper.writeValueAsString(expectedConsumptionDetails)
        val demandRecord = createDemand(10, consumptionDetails = JSONB.valueOf(consumptionDetails))
        dsl.batchInsert(demandRecord).execute()
        runBlocking {
            val demandConsumptionDetails = demandRepository.findDemandConsumptionDetails(
                setOf(DC_CODE),
                DateRange(
                    LocalDate.now(UTC).minusDays(10),
                    LocalDate.now(UTC),
                ),
                skuId,
            )[
                com.hellofresh.demand.models.DemandKey(
                    skuId,
                    DC_CODE,
                    LocalDate.now(UTC).minusDays(10),
                ),
            ]
            assertEquals(1, demandConsumptionDetails?.substitutions?.out?.size)
            assertEquals(1, demandConsumptionDetails?.substitutions?.`in`?.size)
            assertEquals(1, demandConsumptionDetails?.recipeBreakdowns?.size)
            assertEquals(1, demandConsumptionDetails?.prekitting?.`in`?.size)
            assertEquals(1, demandConsumptionDetails?.prekitting?.out?.size)
        }
    }

    @Test
    fun `returns demand records with empty consumption details`() {
        val demandRecord = createDemand(10, consumptionDetails = null)
        dsl.batchInsert(demandRecord).execute()
        runBlocking {
            val demandConsumptionDetails = demandRepository.findDemandConsumptionDetails(
                setOf(DC_CODE),
                DateRange(
                    LocalDate.now(UTC).minusDays(10),
                    LocalDate.now(UTC),
                ),
                skuId,
            )
            assertTrue(demandConsumptionDetails.isEmpty())
        }
    }

    @Test
    fun `returns demand records for the given dc codes when there is no matching actual consumption`() {
        // given
        val pick2LightRecordOne = createPick2LightRecord("abc345", 3)
        val pick2LightRecordTwo = createPick2LightRecord("abc678", 10)
        dsl.batchInsert(p2lRecord, pick2LightRecordOne, pick2LightRecordTwo).execute()
        refreshActualConsumptionMatView()

        val demandRecordOne = createDemand(3)
        val nonExistingSkuId = UUID.randomUUID()
        val demandRecordTwo = createDemand(10, nonExistingSkuId)
        dsl.batchInsert(demandRecord, demandRecordOne, demandRecordTwo).execute()

        // when
        runBlocking {
            val demands = Demands(
                demandRepository.findDemands(
                    setOf(DC_CODE),
                    DateRange(
                        LocalDate.now(UTC).minusDays(7L),
                        LocalDate.now(UTC),
                    ),
                ),
            )
            val actualDemand = demands.getDemand(
                demandRecord.skuId,
                demandRecord.dcCode,
                demandRecord.date,
                UTC,
            )?.forecastedQty?.getValue()?.toLong()
            assertEquals(demandRecord.quantity.toLong(), actualDemand)
            val actualDemandOne = demands.getDemand(
                demandRecordOne.skuId,
                demandRecordOne.dcCode,
                demandRecordOne.date,
                UTC,
            )
            assertEquals(demandRecord.quantity.toLong(), actualDemandOne?.forecastedQty?.getValue()?.toLong())
        }
    }

    @ParameterizedTest
    @MethodSource("getDcMarket")
    fun `should return actual consumption for all the markets`(expectedDcCode: String, expectedMarket: String) {
        // given
        dsl.deleteFrom(DC_CONFIG).execute()
        val dcConfigRecord = createDcConfig(dcCode = expectedDcCode, market = expectedMarket)
        dsl.batchInsert(dcConfigRecord).execute()
        val skuSpecRecord = SkuSpecificationRecord().apply {
            id = UUID.randomUUID()
            parentId = UUID.randomUUID()
            category = "SPI"
            code = "SPI-0000"
            name = "Name"
            acceptableCodeLife = 0
            coolingType = ""
            packaging = ""
            market = expectedMarket.lowercase()
            uom = Uom.UOM_LBS
        }
        dsl.batchInsert(skuSpecRecord).execute()
        val pick2LightRecord = createPick2LightRecord(
            "abc345",
            3,
            market = expectedMarket,
            skuSpec = skuSpecRecord,
            dcCodeParam = expectedDcCode,
        )
        dsl.batchInsert(pick2LightRecord).execute()
        refreshActualConsumptionMatView()

        // when
        runBlocking {
            val result = withContext(Dispatchers.IO) {
                dsl.select(ACTUAL_CONSUMPTION_VIEW.DC_CODE).from(ACTUAL_CONSUMPTION_VIEW)
                    .where(ACTUAL_CONSUMPTION_VIEW.DC_CODE.eq(expectedDcCode))
                    .fetch()
            }.first()

            assertEquals(expectedDcCode, result[ACTUAL_CONSUMPTION_VIEW.DC_CODE])
        }
    }

    @Test
    fun `should use inventory adjustment picks as actual consumption for automated dcs`() {
        // given

        val now = OffsetDateTime.now(UTC)
        val inventoryActivityRecords =
            (1..2).map {
                InventoryActivityRecord().apply {
                    hashMessage = it.toString()
                    activityId = UUID.randomUUID()
                    activityTime = now
                    dcCode = dcConfigRecord.dcCode
                    skuId = skuSpecRecord.id
                    type = ADJ
                    typeId = PCK.name
                    value = JSONB.valueOf(
                        objectMapper.writeValueAsString(
                            InventoryAdjustmentValue(
                                quantity = SkuQuantity.fromBigDecimal(BigDecimal(-100 * it), SkuUOM.UOM_KG), locationId = "",
                                locationType = LOCATION_TYPE_PRODUCTION, expirationDate = null,
                                remainingQuantity = SkuQuantity.fromBigDecimal(BigDecimal.ZERO),
                                transportModuleId = null, poNumber = null,
                            ),
                        ),
                    )
                    publishedTime = now
                }
            }

        dsl.batchInsert(inventoryActivityRecords).execute()
        refreshActualConsumptionMatView()

        val demandRepository = DemandRepositoryImpl(
            dsl,
            StatsigTestFeatureFlagClient(
                setOf(FeatureFlag.AutomatedDcLiveRules(setOf(ContextData(DC, dcConfigRecord.dcCode)))),
            ),
        )

        val demands =
            runBlocking {
                demandRepository.findDemands(
                    setOf(dcConfigRecord.dcCode),
                    DateRange.oneDay(now.toLocalDate())
                )
            }

        // when

        assertEquals(300.toBigDecimal(), demands.first().actualConsumption.actualConsumptionQty.getValue())
        assertEquals(SkuUOM.UOM_KG, demands.first().actualConsumption.actualConsumptionQty.unitOfMeasure)
    }

    @Test
    fun `returns demand records for the given dc codes when there is no matching actual consumption (empty sku packaging)`() {
        // given
        dsl.batchInsert(p2lRecord).execute()
        refreshActualConsumptionMatView()
        dsl.batchInsert(demandRecord).execute()

        // when
        runBlocking {
            val demands = demandRepository.findDemands(
                setOf(DC_CODE),
                DateRange(
                    LocalDate.now(UTC).minusDays(7L),
                    LocalDate.now(UTC),
                ),
            )

            val demandValue = demands.first()
            assertEquals(demandRecord.date, demandValue.date)
            assertEquals(p2lRecord.quantity, demandValue.actualConsumption.actualConsumptionQty.getValue().toShort())
            assertFalse(demandValue.actualConsumption.usable)
            assertEquals(demandRecord.quantity.toLong(), demandValue.forecastedQty.getValue().toLong())
        }
    }

    @Test
    fun `returns demand records for a given skuId and all the skus if not provided`() {
        val demandRecord = createDemand(3)
        val demandRecord1 = createDemand(3, skuIdParam = UUID.randomUUID())
        val skuSpecRecord1 = createSkuSpecificationRecord(skuIdParam = demandRecord1.skuId)

        // insert for 2 sku
        dsl.batchInsert(demandRecord, demandRecord1, skuSpecRecord1).execute()
        refreshSkuView()

        runBlocking {
            val demands = Demands(
                demandRepository.findDemands(
                    setOf(DC_CODE),
                    DateRange(
                        LocalDate.now(UTC).minusDays(3L),
                        LocalDate.now(UTC),
                    ),
                    skuId,
                ),
            )
            // returns demand only for the requested sku
            assertEquals(1, demands.demandList.size)
            assertEquals(skuId, demands.demandList.first().skuId)

            val demandsAllSku = Demands(
                demandRepository.findDemands(
                    setOf(DC_CODE),
                    DateRange(LocalDate.now(UTC).minusDays(3L), LocalDate.now(UTC)),
                ),
            )
            // returns demand only all the sku
            assertEquals(2, demandsAllSku.demandList.size)
        }
    }

    @Test
    fun `returns demand records with actual consumption for a given skuId and all the skus if not provided`() {
        // given
        val skuId = UUID.randomUUID()
        val skuId1 = UUID.randomUUID()
        val skuCode1 = UUID.randomUUID().toString()
        val skuSpecRecord = createSkuSpecificationRecord("Coolpouch", skuId)
        val skuSpecRecord1 = createSkuSpecificationRecord("Coolpouch", skuId1, skuCode1)
        val dcConfigRecord = createDcConfig(IT)
        val demandRecord = createDemand(0, skuId, IT)
        val demandRecord1 = createDemand(0, skuId1, IT)

        val p2lRecord = createPick2LightRecord("abc123", 0, IT)
        val p2lRecord1 = createPick2LightRecord("abc234", 0, IT, skuSpecRecord1)

        dsl.batchInsert(skuSpecRecord, skuSpecRecord1, dcConfigRecord).execute()
        refreshSkuView()
        dsl.batchInsert(p2lRecord, p2lRecord1).execute()
        dsl.batchInsert(demandRecord, demandRecord1).execute()

        refreshActualConsumptionMatView()

        // when
        runBlocking {
            val demands = Demands(
                demandRepository.findDemands(
                    setOf(IT),
                    DateRange(LocalDate.now(UTC).minusDays(7L), LocalDate.now(UTC)),
                    skuId,
                ),
            )
            // returns demand only for the requested sku
            assertEquals(1, demands.demandList.size)
            assertEquals(skuId, demands.demandList.first().skuId)

            val demandsAllSku = Demands(
                demandRepository.findDemands(
                    setOf(IT),
                    DateRange(LocalDate.now(UTC).minusDays(3L), LocalDate.now(UTC)),
                ),
            )
            // returns demand only all the sku
            assertEquals(3, demandsAllSku.demandList.size)
        }
    }

    @Test
    fun `returns always demand with actual consumption values for the given dc codes when there matching actual consumption (coolpouch sku packaging)`() {
        // given
        val skuId = UUID.randomUUID()
        val skuSpecRecord = createSkuSpecificationRecord("Coolpouch", skuId)
        val p2lRecord = createPick2LightRecord("abc123", 0, IT, skuSpec = skuSpecRecord)
        val demandRecord = createDemand(0, skuId, IT)
        val dcConfigRecord = createDcConfig(IT)
        dsl.batchInsert(skuSpecRecord, dcConfigRecord, p2lRecord, demandRecord).execute()
        refreshActualConsumptionMatView()
        refreshSkuView()

        val dateRange = DateRange(LocalDate.now(UTC).minusDays(7L), LocalDate.now(UTC))

        // when
        runBlocking {
            val demands = demandRepository.findDemands(setOf(IT), dateRange)

            val demandValue = demands.first()
            assertEquals(demandRecord.date, demandValue.date)
            assertEquals(p2lRecord.quantity, demandValue.actualConsumption.actualConsumptionQty.getValue().toShort())
            assertTrue(demandValue.actualConsumption.usable)
            assertEquals(demandRecord.quantity.toLong(), demandValue.forecastedQty.getValue().toLong())
        }
    }

    @Test
    fun `returns demand records for the given dc codes when there matching actual consumption (coolpouch sku packaging) with dc local date`() {
        // given
        val skuId = UUID.randomUUID()
        val market = "DACH"
        val skuSpecRecord = createSkuSpecificationRecord(
            "Coolpouch",
            skuId,
            skuCode = "PHF-1010",
            market = market.lowercase(),
        )
        val dcCode = IT
        val mockItalyTimeZoneForTesting = "America/Winnipeg"
        val today = OffsetDateTime.now(ZoneId.of(mockItalyTimeZoneForTesting))
        val p2lRecord = createPick2LightRecord(
            "abc1234",
            0,
            dcCode,
            createdAt = today,
            market = market,
            skuSpec = skuSpecRecord,
        )
        dsl.deleteFrom(DC_CONFIG).execute()
        val demandRecord = DemandRecord(
            skuId,
            dcCode,
            LocalDate.now(ZoneId.of(mockItalyTimeZoneForTesting)).minusDays(0),
            today.toLocalDateTime(),
            today.toLocalDateTime(),
            today.toLocalDateTime(),
            null,
            demandQuantity,
            null,
            null,
            null,
            Uom.UOM_UNIT,
        )

        val dcConfigRecord = createDcConfig(dcCode = dcCode, market = market, zoneId = mockItalyTimeZoneForTesting)
        dsl.batchInsert(dcConfigRecord).execute()
        dsl.batchInsert(skuSpecRecord).execute()
        refreshSkuView()
        dsl.batchInsert(p2lRecord).execute()
        refreshActualConsumptionMatView()
        dsl.batchInsert(demandRecord).execute()

        // when
        runBlocking {
            val demands = Demands(
                demandRepository.findDemands(
                    setOf(dcCode),
                    DateRange(
                        LocalDate.now(ZoneId.of(mockItalyTimeZoneForTesting)).minusDays(7L),
                        LocalDate.now(ZoneId.of(mockItalyTimeZoneForTesting)),
                    ),
                ),
            )
            val actualDemandValue = demands.getDemand(
                demandRecord.skuId,
                demandRecord.dcCode,
                demandRecord.date,
                UTC,
            )
            assertEquals(p2lRecord.quantity, actualDemandValue?.actualConsumptionQty?.getValue()?.toShort())
            assertEquals(demandRecord.quantity.toLong(), actualDemandValue?.forecastedQty?.getValue()?.toLong())
        }
    }

    @Test
    fun `returns demand records for the given dc codes when there not matching actual consumption (meal kit sku packaging)`() {
        // given
        val skuId = UUID.randomUUID()
        val skuSpecRecord = createSkuSpecificationRecord("MealKit", skuId)
        val p2lRecord = createPick2LightRecord("abc123", 0, IT, skuSpec = skuSpecRecord)
        val demandRecord = createDemand(0, skuId, IT)
        val dcConfigRecord = createDcConfig(IT)
        dsl.batchInsert(skuSpecRecord, p2lRecord, dcConfigRecord).execute()
        refreshActualConsumptionMatView()
        refreshSkuView()
        dsl.batchInsert(demandRecord).execute()

        // when
        runBlocking {
            val demands = Demands(
                demandRepository.findDemands(
                    setOf(IT),
                    DateRange(
                        LocalDate.now(UTC).minusDays(7L),
                        LocalDate.now(UTC),
                    ),
                ),
            )
            val actualDemandValue = demands.getDemand(
                demandRecord.skuId,
                demandRecord.dcCode,
                demandRecord.date,
                UTC,
            )
            assertEquals(demandRecord.quantity.toLong(), actualDemandValue?.forecastedQty?.getValue()?.toLong())
            assertEquals(actualConsumptionQty, actualDemandValue?.actualConsumptionQty?.getValue()?.toShort())
        }
    }

    @ParameterizedTest
    @CsvSource("true", "false")
    fun `returns demand records with usable actual consumption when matches feature flag`(flagMatching: Boolean) {
        // given
        val skuId = UUID.randomUUID()
        val skuSpecRecord = createSkuSpecificationRecord("Coolpouch", skuId, skuId.toString())
        val dcConfigRecord = createDcConfig(IT)
        val p2lRecord = createPick2LightRecord("abc123", 0, IT, skuSpecRecord)
        val demandRecord = createDemand(0, skuId, IT)
        val demandRecord1 = createDemand(1, skuId, IT)
        dsl.batchInsert(skuSpecRecord, dcConfigRecord, p2lRecord, demandRecord, demandRecord1).execute()
        refreshActualConsumptionMatView()
        refreshSkuView()

        val demandRepository = DemandRepositoryImpl(
            dsl,
            StatsigTestFeatureFlagClient(
                if (flagMatching) {
                    setOf(
                        UsableActualConsumptionDemandDomain(
                            setOf(
                                ContextData(DC, IT),
                                ContextData(PACKAGING, skuSpecRecord.packaging.lowercase()),
                            ),
                        ),
                    )
                } else {
                    emptySet()
                },
            ),
        )

        // when
        runBlocking {
            val demands = demandRepository.findDemands(
                setOf(IT),
                DateRange(
                    LocalDate.now(UTC).minusDays(7L),
                    LocalDate.now(UTC),
                ),
            )

            assertEquals(2, demands.size)

            demands.forEach { demand ->
                val expectedActualConsumption = if (demand.date == p2lRecord.createdAt.toLocalDate()) p2lRecord.quantity.toLong() else 0
                assertEquals(expectedActualConsumption, demand.actualConsumptionQty.getValue().toLong())
                assertEquals(
                    expectedActualConsumption,
                    demand.actualConsumption.actualConsumptionQty.getValue().toLong(),
                )
                assertEquals(7, demand.forecastedQty.getValue().toLong())
                assertEquals(flagMatching, demand.actualConsumption.usable)
            }
        }
    }

    private fun refreshActualConsumptionMatView() {
        dsl.query("refresh materialized view ${ACTUAL_CONSUMPTION_VIEW.name}").execute()
        dsl.query("refresh materialized view ${ACTUAL_CONSUMPTION_INVENTORY_ACTIVITY.name}").execute()
    }

    private fun createSkuSpecificationRecord(
        packaging: String = "",
        skuIdParam: UUID = skuId,
        skuCode: String? = null,
        market: String = "dach"
    ) = SkuSpecificationRecord().apply {
        id = skuIdParam
        parentId = UUID.randomUUID()
        category = "SPI"
        code = skuCode ?: skuIdParam.toString()
        name = "Name"
        acceptableCodeLife = 0
        coolingType = ""
        this.packaging = packaging
        this.market = market
        uom = Uom.UOM_LBS
    }

    @Suppress("LongParameterList")
    private fun createPick2LightRecord(
        messageKey: String,
        pastNumberOfDays: Long,
        dcCodeParam: String = DC_CODE,
        skuSpec: SkuSpecificationRecord = skuSpecRecord,
        createdAt: OffsetDateTime = OffsetDateTime.now(UTC).minusDays(pastNumberOfDays),
        market: String = skuSpec.market
    ) = Pick_2LightRecord(
        messageKey,
        createdAt,
        actualConsumptionQty,
        market,
        dcCodeParam,
        skuSpec.code,
    )

    @Suppress("LongParameterList")
    private fun createDemand(
        pastNumberOfDays: Long,
        skuIdParam: UUID = skuId,
        dcCodeParam: String = DC_CODE,
        substitutedInQty: Long? = null,
        substitutedOutQty: Long? = null,
        consumptionDetails: JSONB? = null
    ) = DemandRecord(
        skuIdParam,
        dcCodeParam,
        LocalDate.now(UTC).minusDays(pastNumberOfDays),
        LocalDateTime.now(UTC),
        LocalDateTime.now(UTC),
        LocalDateTime.now(UTC),
        null,
        demandQuantity,
        substitutedInQty,
        substitutedOutQty,
        consumptionDetails,
        Uom.UOM_UNIT,
    )

    fun createDcConfig(dcCode: String = DC_CODE, market: String = "DACH", zoneId: String = "Europe/Berlin") =
        DcConfigRecord(
            dcCode, market, "MONDAY", "FRIDAY", zoneId,
            true, LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, null, null,
            LocalTime.now(), arrayOf("brand1", "brand2")
        )

    companion object {
        lateinit var demandRepository: DemandRepository
        lateinit var dsl: MetricsDSLContext
        private val dataSource = getMigratedDataSource()

        @JvmStatic
        @Suppress("unused")
        private fun getDcMarket() = Stream.of(
            of("IT", IT),
            of("GR", GB),
            of("BV", GB),
            of("TO", GB),
            of("VE", DACH),
            of("CH", DACH),
            of("OH", CA),
            of("BL", CA),
            of("AE", CA),
            of("OE", CA),
            of("PH", AU),
            of("PH", AU),
            of("ML", AU),
            of("SY", AU),
            of("SK", DKSE),
            of("SK", DKSE),
            of("MO", DKSE),
            of("LI", "BENELUXFR"),
            of("IE", "IE"),
            of("DH", "NL"),
            of("NZ", "NZ"),
            of("SP", "ES"),
        )

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            demandRepository = DemandRepositoryImpl(
                dsl,
                StatsigTestFeatureFlagClient(
                    setOf(
                        UsableActualConsumptionDemandDomain(
                            setOf(
                                ContextData(DC, IT),
                                ContextData(PACKAGING, ALLOWED_PACKAGING_TYPE),
                            ),
                        ),
                    ),
                ),
            )
        }
    }
}
