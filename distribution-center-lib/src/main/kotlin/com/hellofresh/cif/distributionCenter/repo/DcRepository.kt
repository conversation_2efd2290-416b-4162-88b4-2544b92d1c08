package com.hellofresh.cif.distributionCenterLib.repo

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distribution_center_lib.schema.Tables
import java.time.DayOfWeek
import java.time.ZoneId
import kotlinx.coroutines.future.await

fun interface DcRepository {
    suspend fun fetchDcConfigurations(): List<DistributionCenterConfiguration>
}

class DcRepositoryImpl(private val metricsDSLContext: MetricsDSLContext) : DcRepository {

    private val findDcConfigurations = "find-dc-configurations"

    override suspend fun fetchDcConfigurations(): List<DistributionCenterConfiguration> =
        metricsDSLContext.withTagName(findDcConfigurations)
            .fetchAsync(Tables.DC_CONFIG)
            .thenApply {
                it.map { dc ->
                    DistributionCenterConfiguration(
                        dcCode = dc.dcCode,
                        productionStart = DayOfWeek.valueOf(dc.productionStart),
                        cleardown = DayOfWeek.valueOf(dc.cleardown),
                        market = dc.market,
                        zoneId = ZoneId.of(dc.zoneId),
                        enabled = dc.enabled,
                        hasCleardown = dc.hasCleardown,
                        wmsType = WmsSystem.valueOf(dc.wmsType ?: WmsSystem.UNRECOGNIZED.value),
                        poCutoffTime = dc.poCutoffTime,
                        brands = dc.brands.toList()
                    )
                }
            }.await()
}
