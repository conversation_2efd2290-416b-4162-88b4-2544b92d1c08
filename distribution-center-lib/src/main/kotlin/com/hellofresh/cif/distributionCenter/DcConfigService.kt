package com.hellofresh.cif.distributionCenterLib

import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.lib.KrontabScheduler
import com.hellofresh.cif.lib.MeteredJob
import com.hellofresh.cif.lib.Scheduler
import com.hellofresh.cif.shutdown.shutdownNeeded
import io.micrometer.core.instrument.MeterRegistry
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy
import java.util.concurrent.TimeUnit.HOURS
import java.util.concurrent.TimeUnit.SECONDS
import kotlinx.coroutines.runBlocking

private const val POOL_SIZE = 1

/**
 * Reads `DC_CONFIG` table in a background thread and caches the result. By
 * default, it uses the [KrontabScheduler] to schedule the read from  the
 * [DcRepositoryImpl]. However, this could be overwritten by the caller.
 *
 * **The class is threadsafe.**
 */

class DcConfigService(
    private val meterRegistry: MeterRegistry,
    private val repo: DcRepository = DcRepositoryImpl(
        DBConfiguration.jooqReadOnlyDslContext(
            POOL_SIZE,
            meterRegistry,
        ),
    ),
    jobTimePeriodSeconds: Int = 60,
    private val scheduler: Scheduler = shutdownNeeded {
        KrontabScheduler(
            jobTimePeriodSeconds,
            SECONDS,
            ThreadPoolExecutor(
                POOL_SIZE,
                POOL_SIZE,
                Long.MAX_VALUE,
                HOURS,
                ArrayBlockingQueue(POOL_SIZE, true),
                CallerRunsPolicy(),
            ),
        )
    }
) {

    /**
     * Assignment and read operations are atomic hence volatile is enough to make
     * the critical section threadsafe
     */
    @Volatile
    private var _dcConfigurations: Map<String, DistributionCenterConfiguration> = emptyMap()

    init {
        scheduler.schedule {
            MeteredJob(meterRegistry, "dcConfigService") { _dcConfigurations = loadDcConfigurations() }.execute()
        }
    }

    /**
     * Mapping of DC Configurations. Note that this mapping is
     * refreshed by a background task periodically. Use [fetchOnDemand] if stale
     * data is not favourable.
     */
    val dcConfigurations: Map<String, DistributionCenterConfiguration>
        get() = readDcConfigurations()

    /**
     * Fetch the mapping of DC code to Timezone from the source. This performs
     * a DB query. Use [dcTimezones] if cached data is enough for the use.
     */
    fun fetchOnDemand(): Map<String, DistributionCenterConfiguration> = loadDcConfigurations()

    /**
     * Reads dc configurations if present and fetch them from db if needed
     */
    private fun readDcConfigurations() =
        _dcConfigurations.ifEmpty {
            loadDcConfigurations()
        }

    private fun loadDcConfigurations() = runBlocking {
        repo.fetchDcConfigurations()
            .associateBy { it.dcCode }
            .also {
                _dcConfigurations = it
            }
    }
}
