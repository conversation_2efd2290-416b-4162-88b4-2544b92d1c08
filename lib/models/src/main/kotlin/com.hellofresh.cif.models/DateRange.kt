package com.hellofresh.cif.models

import java.time.LocalDate

data class DateRange(
    val fromDate: LocalDate,
    val toDate: LocalDate
) :
    Iterable<LocalDate>,
    ClosedRange<LocalDate> {

    override val start
        get() = fromDate
    override val endInclusive
        get() = toDate

    init {
        require(fromDate.isBefore(toDate) || fromDate == toDate) {
            "fromDate must be before or equal to toDate. Got $fromDate - $toDate"
        }
    }

    fun dates(): List<LocalDate> = iterator().asSequence().toList()

    override fun iterator(): Iterator<LocalDate> =
        object : Iterator<LocalDate> {
            var next = fromDate

            override fun hasNext(): Boolean = next <= toDate
            override fun next(): LocalDate {
                if (!hasNext()) throw NoSuchElementException()
                val result = next
                return result.also {
                    next = next.plusDays(1)
                }
            }
        }

    companion object {

        fun oneDay(date: LocalDate) = DateRange(date, date)
    }
}

fun DateRange.validateDates(dcCode: String, dates: Set<LocalDate>) =
    require(dates.all { this.contains(it) }) {
        "Simulation with stock updates out of valid range: dc: $dcCode, " +
            "DateRange: $this - Requested: $dates"
    }

fun Set<LocalDate>.validateDates(dcCode: String, dates: Set<LocalDate>) {
    if (this.isNotEmpty()) {
        DateRange(this.min(), this.max()).validateDates(dcCode, dates)
    } else {
        require(dates.isEmpty()) { "Validating $dates against empty Date Range, dc: $dcCode" }
    }
}
