package com.hellofresh.cif.models

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import java.math.BigDecimal
import java.math.RoundingMode
import java.math.RoundingMode.HALF_UP

@Suppress("TooManyFunctions")
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
@JsonDeserialize(using = InventoryQuantityDeserializer::class)
data class SkuQuantity private constructor(
    private val value: BigDecimal,
    @JsonProperty("uom")
    val unitOfMeasure: SkuUOM = UOM_UNIT
) : Comparable<SkuQuantity> {

    fun getValue(): BigDecimal = roundValue(value).stripTrailingZeros().toPlainString().toBigDecimal()

    operator fun plus(other: SkuQuantity): SkuQuantity {
        val resolvedUOM = resolveUnitOfMeasure(other)
        return SkuQuantity(this.value + other.value, resolvedUOM)
    }

    operator fun minus(other: SkuQuantity): SkuQuantity {
        val resolvedUOM = resolveUnitOfMeasure(other)
        return SkuQuantity(this.value - other.value, resolvedUOM)
    }

    operator fun times(other: SkuQuantity): SkuQuantity {
        val resolvedUOM = resolveUnitOfMeasure(other)
        return SkuQuantity(this.value * other.value, resolvedUOM)
    }

    operator fun div(other: SkuQuantity): SkuQuantity {
        val resolvedUOM = resolveUnitOfMeasure(other)
        return SkuQuantity(this.value / other.value, resolvedUOM)
    }

    fun multiply(multiplicand: BigDecimal): SkuQuantity =
        SkuQuantity(this.value.multiply(multiplicand), this.unitOfMeasure)

    operator fun unaryMinus() = SkuQuantity(-this.value, this.unitOfMeasure)

    operator fun inc() = SkuQuantity(this.value + BigDecimal.ONE, this.unitOfMeasure)

    operator fun dec() = SkuQuantity(this.value - BigDecimal.ONE, this.unitOfMeasure)

    fun abs() = SkuQuantity(this.value.abs(), this.unitOfMeasure)

    @JsonIgnore
    fun isZero() = this.value.compareTo(BigDecimal.ZERO) == 0

    @JsonIgnore
    fun isNotZero() = !this.isZero()

    @JsonIgnore
    fun isNegative() = this.value < BigDecimal.ZERO

    @JsonIgnore
    fun isPositive() = this.value > BigDecimal.ZERO

    override fun compareTo(other: SkuQuantity): Int {
        checkUnitOfMeasureCompatibility(other)
        return this.value.compareTo(other.value)
    }

    override fun toString(): String = "${value.toPlainString()} ${unitOfMeasure.name}"

    private fun checkUnitOfMeasureCompatibility(other: SkuQuantity) {
        if (isNotZero() && other.isNotZero()) {
            require(this.unitOfMeasure == other.unitOfMeasure) {
                "SkuUOM mismatch: cannot perform operation between ${this.unitOfMeasure} and ${other.unitOfMeasure}"
            }
        }
    }

    private fun resolveUnitOfMeasure(other: SkuQuantity): SkuUOM {
        checkUnitOfMeasureCompatibility(other)

        return if (isNotZero()) {
            this.unitOfMeasure
        } else {
            other.unitOfMeasure
        }
    }

    companion object {
        val ZERO = fromBigDecimal(BigDecimal.ZERO, UOM_UNIT)

        private const val SCALE = 5
        private val MINIMUM_VALUE = BigDecimal("0.00001")

        private fun roundValue(input: BigDecimal): BigDecimal {
            if (input <= BigDecimal.ZERO) {
                return input
            }
            val scaledValue = input.setScale(SCALE + 1, RoundingMode.HALF_UP)
            val roundingThreshold = BigDecimal("0.5").movePointLeft(SCALE + 1)
            val roundedValue = if (scaledValue.remainder(BigDecimal.ONE).abs() < roundingThreshold) {
                scaledValue.setScale(SCALE, RoundingMode.DOWN)
            } else {
                scaledValue.setScale(SCALE, RoundingMode.UP)
            }

            return if (roundedValue < MINIMUM_VALUE) MINIMUM_VALUE else roundedValue
        }

        fun fromLong(value: Long, unitOfMeasure: SkuUOM = UOM_UNIT): SkuQuantity = getSkuQuantity(value, unitOfMeasure)

        fun fromDouble(value: Double, unitOfMeasure: SkuUOM = UOM_UNIT): SkuQuantity = getSkuQuantity(
            value,
            unitOfMeasure,
        )

        fun fromString(value: String, unitOfMeasure: SkuUOM = UOM_UNIT): SkuQuantity = getSkuQuantity(
            value,
            unitOfMeasure,
        )

        fun fromBigDecimal(value: BigDecimal, unitOfMeasure: SkuUOM = UOM_UNIT): SkuQuantity = getSkuQuantity(
            value,
            unitOfMeasure,
        )

        fun max(valueA: SkuQuantity, valueB: SkuQuantity): SkuQuantity = if (valueA >= valueB) valueA else valueB

        private fun getSkuQuantity(value: Any, uom: SkuUOM = UOM_UNIT) =
            when (value) {
                is Long -> SkuQuantity(BigDecimal(value).setScale(SCALE, HALF_UP), uom)
                is Double -> SkuQuantity(BigDecimal(value).setScale(SCALE, HALF_UP), uom)
                is String -> SkuQuantity(BigDecimal(value).setScale(SCALE, HALF_UP), uom)
                is BigDecimal -> SkuQuantity(value.setScale(SCALE, HALF_UP), uom)
                else -> error("Invalid Sku Quantity Provided.")
            }
    }
}

inline fun <T> Iterable<T>.sumOf(selector: (T) -> SkuQuantity): SkuQuantity {
    var sum = SkuQuantity.fromBigDecimal(BigDecimal.ZERO, UOM_UNIT)
    for (skuQuantity in this) {
        sum = sum.plus(selector(skuQuantity))
    }
    return sum
}

fun Iterable<SkuQuantity>.sum(): SkuQuantity {
    var sum = SkuQuantity.fromBigDecimal(BigDecimal.ZERO, UOM_UNIT)
    for (skuQuantity in this) {
        sum = sum.plus(skuQuantity)
    }
    return sum
}
