package com.hellofresh.cif.models

import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.JsonNode

class InventoryQuantityDeserializer : JsonDeserializer<SkuQuantity>() {
    override fun deserialize(p: <PERSON>sonParser, ctxt: DeserializationContext): SkuQuantity {
        val node: JsonNode = p.codec.readTree(p)

        return when {
            node.isObject && node.has(VALUE) && node.has(UNIT_OF_MEASUREMENT) -> {
                val value = node.get(VALUE).decimalValue()
                val uom = node.get(UNIT_OF_MEASUREMENT).asText()
                val unitOfMeasure = SkuUOM.valueOf(uom)
                SkuQuantity.fromBigDecimal(value, unitOfMeasure)
            }

            node.isNumber -> {
                SkuQuantity.fromLong(node.asLong(), SkuUOM.UOM_UNIT)
            }

            else -> {
                error("Unknown format for SkuQuantity, exception while parsing Inventory SkuQuantity : $node")
            }
        }
    }
    companion object {
        private const val VALUE = "value"
        private const val UNIT_OF_MEASUREMENT = "uom"
    }
}
