package com.hellofresh.com.cif.business.fileupload.model

import com.hellofresh.cif.business.file_upload.schema.enums.FileType
import com.hellofresh.cif.business.file_upload.schema.enums.FileUploadStatus
import java.util.UUID

data class FileUpload(
    val id: UUID,
    val filename: String,
    val market: String,
    val dcs: Set<String>,
    val authorName: String,
    val authorEmail: String,
    val message: String? = null,
    val status: FileUploadStatus,
    val fileType: FileType,
)
