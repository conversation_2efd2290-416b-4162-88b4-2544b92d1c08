package com.hellofresh.com.cif.business.fileupload.repository

import com.hellofresh.cif.business.file_upload.schema.tables.FileUploads.FILE_UPLOADS
import com.hellofresh.com.cif.business.fileupload.model.FileUpload
import org.jooq.DSLContext

class FileUploadRepositoryImpl : FileUploadRepository {
    override fun insert(dslContext: DSLContext, file: FileUpload) {
        with(FILE_UPLOADS) {
            dslContext
                .insertInto(this,)
                .set(ID, file.id)
                .set(FILE_NAME, file.filename)
                .set(MARKET, file.market)
                .set(DCS, file.dcs.toTypedArray())
                .set(STATUS, file.status)
                .set(MESSAGE, file.message)
                .set(AUTHOR_NAME, file.authorName)
                .set(AUTHOR_EMAIL, file.authorEmail)
                .set(FILE_TYPE, file.fileType)
                .executeAsync()
        }
    }
}
