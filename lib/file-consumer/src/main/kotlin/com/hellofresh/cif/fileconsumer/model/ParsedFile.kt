package com.hellofresh.cif.fileconsumer.model

import com.hellofresh.cif.fileconsumer.violations.ViolationHandler
import kotlin.reflect.KClass
import org.apache.commons.csv.CSVRecord

interface ParsedFile {
    val columns: List<String>
    val data: List<Row>
    val pKey: List<String>
    val numericColumnDataTypes: Set<String>
    val allowedBlank: Set<String>

    fun data(records: List<CSVRecord>): List<Row> = records
        .map { csvRecord ->
            Row(
                recordNumber = csvRecord.recordNumber + 1,
                columns = columns.associateWith { csvRecord[it] },
            )
        }
}

data class Row(val recordNumber: Long, val columns: Map<String, String>) {
    operator fun get(name: String) = columns[name] ?: throw IllegalArgumentException("Column $name not found")
}

data class FileTypeDescriptor<T : ParsedFile>(
    val headers: List<String>,
    val constructor: (List<CSVRecord>) -> T
)

data class ViolationHandlersConfig(
    val preViolationHandlers: List<ViolationHandler> = emptyList(),
    val excludeViolationHandlers: List<KClass<out ViolationHandler>> = emptyList()
) {
    companion object {
        val empty = ViolationHandlersConfig()
    }
}
