package com.hellofresh.cif.fileconsumer.violations

import com.hellofresh.cif.fileconsumer.violations.Violation.BlankCell
import com.hellofresh.cif.fileconsumer.violations.Violation.InvalidDataTypeFormat
import com.hellofresh.cif.fileconsumer.violations.Violation.InvalidNumberOfColumns
import com.hellofresh.cif.fileconsumer.violations.Violation.NegativeNumbers
import kotlin.reflect.full.createInstance
import kotlin.reflect.full.primaryConstructor
import org.apache.logging.log4j.kotlin.Logging

interface ViolationHandler {
    fun handle(data: ProcessingUnit): Violation

    companion object : Logging
}

sealed interface ViolationHandlersPerFile : ViolationHandler {
    companion object {
        fun createInstances(
            violationsHandlersPerFile: List<ViolationHandlersPerFile> = emptyList()
        ): List<ViolationHandlersPerFile> =
            ViolationHandlersPerFile::class.sealedSubclasses
                .filter { it.primaryConstructor?.parameters?.size ?: 0 == 0 }
                .map { it.objectInstance ?: it.createInstance() }
                .plus(violationsHandlersPerFile)
    }
}

object DataTypeVerificationHandler : ViolationHandlersPerFile {
    override fun handle(data: ProcessingUnit): Violation {
        val isValid = checkDataType(data, data.file.numericColumnDataTypes) { it.isNumeric() }
        return if (isValid) noViolation else InvalidDataTypeFormat(data.record.recordNumber.toInt())
    }

    private fun String.isNumeric(): Boolean = this.toIntOrNull() != null || this.toDoubleOrNull() != null

    private fun checkDataType(data: ProcessingUnit, headers: Set<String>, conversion: (String) -> Boolean): Boolean =
        headers.all { header -> conversion(data.record[header]) }
}

object NegativeNumbersViolationHandler : ViolationHandlersPerFile {
    @Suppress("UnnecessaryParentheses")
    override fun handle(data: ProcessingUnit): Violation =
        if (data.record.columns.values.any { (it.toIntOrNull() ?: 0) < 0 }) {
            NegativeNumbers(data.record.recordNumber.toInt())
        } else {
            noViolation
        }
}

class InvalidNumberOfColumnsViolationHandler : ViolationHandlersPerFile {
    var size: Int? = null
    override fun handle(data: ProcessingUnit): Violation =
        if (size != null && size != data.record.columns.size) {
            InvalidNumberOfColumns(data.record.recordNumber.toInt())
        } else {
            size = data.record.columns.size
            noViolation
        }
}

object BlankCellViolationHandler : ViolationHandlersPerFile {
    override fun handle(data: ProcessingUnit): Violation {
        data.record.columns.map { (key, value) ->
            if (!data.file.allowedBlank.contains(key) && value.isBlank()) {
                return BlankCell(data.record.recordNumber.toInt())
            }
        }
        return noViolation
    }
}
