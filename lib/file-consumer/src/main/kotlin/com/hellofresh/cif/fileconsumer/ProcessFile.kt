package com.hellofresh.cif.fileconsumer

import com.hellofresh.cif.fileconsumer.model.FileTypeDescriptor
import com.hellofresh.cif.fileconsumer.model.ParsedFile
import com.hellofresh.cif.fileconsumer.model.ProcessFileResult
import com.hellofresh.cif.fileconsumer.model.ProcessFileResult.Invalid
import com.hellofresh.cif.fileconsumer.model.ViolationHandlersConfig
import com.hellofresh.cif.fileconsumer.reader.CSVFileUploadReader
import com.hellofresh.cif.fileconsumer.violations.ProcessChain
import com.hellofresh.cif.fileconsumer.violations.ReaderViolation
import com.hellofresh.cif.fileconsumer.violations.Violation
import org.apache.logging.log4j.kotlin.Logging

object ProcessFile : Logging {
    fun <T : ParsedFile> processFileContent(
        fileName: String,
        content: ByteArray,
        delimiters: List<Char>,
        fileTypeDescriptor: FileTypeDescriptor<T>,
        violationHandlersConfig: ViolationHandlersConfig = ViolationHandlersConfig.empty,
    ): ProcessFileResult {
        val (parsedFile, readerViolations) = CSVFileUploadReader.toCsvRecords(content, delimiters, fileTypeDescriptor)

        if (parsedFile == null || readerViolations.isNotEmpty()) {
            return Invalid(
                parsedFile,
                readerViolations.map {
                    when (it) {
                        is ReaderViolation.DelimiterExtractionFailed -> Violation.DelimiterExtractionFailed()
                        is ReaderViolation.HeadersNotMatching -> Violation.HeadersNotMatching()
                        is ReaderViolation.NotACsv -> Violation.NotACsv()
                    }
                },
            )
        }
        return ProcessChain(
            violationHandlers = violationHandlersConfig.preViolationHandlers
                .filter { !violationHandlersConfig.excludeViolationHandlers.contains(it::class) }
        ).process(parsedFile, fileName)
    }
}
