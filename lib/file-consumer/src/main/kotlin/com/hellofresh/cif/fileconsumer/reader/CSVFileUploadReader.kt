package com.hellofresh.cif.fileconsumer.reader

import com.hellofresh.cif.fileconsumer.model.FileTypeDescriptor
import com.hellofresh.cif.fileconsumer.model.ParsedFile
import com.hellofresh.cif.fileconsumer.violations.ReaderViolation
import com.hellofresh.cif.fileconsumer.violations.ReaderViolation.DelimiterExtractionFailed
import com.hellofresh.cif.fileconsumer.violations.ReaderViolation.HeadersNotMatching
import com.hellofresh.cif.fileconsumer.violations.ReaderViolation.NotACsv
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.apache.commons.csv.CSVRecord
import org.apache.logging.log4j.kotlin.Logging

object CSVFileUploadReader : Logging {
    private val csvFormat: CSVFormat = CSVFormat.DEFAULT.builder()
        .setAllowMissingColumnNames(true)
        .setIgnoreHeaderCase(true)
        .setTrim(true)
        .build()

    @Suppress("SpreadOperator")
    fun <T : ParsedFile> toCsvRecords(
        byteArray: ByteArray,
        delimiters: List<Char>,
        fileTypeDescriptor: FileTypeDescriptor<T>
    ): CsvFileUploadResult {
        val violations = mutableListOf<ReaderViolation>()
        var parsedFile: ParsedFile? = null

        return byteArray.inputStream().bufferedReader().use { reader ->

            runCatching {
                val header = reader.readLine().replace("[^\\w.${delimiters.joinToString()}]".toRegex(), "")
                val delimiter = detectDelimiter(header, delimiters)

                delimiter?.let {
                    val columns = header.split(delimiter)
                    val records = CSVParser.parse(
                        reader,
                        csvFormat.builder()
                            .setDelimiter(delimiter)
                            .setHeader(*header.split(delimiter).toTypedArray())
                            .build(),
                    ).records
                    parsedFile = resolveParsedFileFromHeader(records, columns, fileTypeDescriptor)
                        .also {
                            if (it == null) violations.add(HeadersNotMatching())
                        }
                } ?: run {
                    violations.add(DelimiterExtractionFailed())
                }
            }.onFailure { violations.add(NotACsv()) }
            CsvFileUploadResult(parsedFile, violations)
        }
    }

    private fun <T : ParsedFile> resolveParsedFileFromHeader(
        records: List<CSVRecord>,
        columns: List<String>,
        fileTypeDescriptor: FileTypeDescriptor<T>
    ): T? = columns.takeIf {
        it.isNotEmpty() && it.containsAll(fileTypeDescriptor.headers)
    }
        ?.let { fileTypeDescriptor.constructor(records) }

    private fun detectDelimiter(csvLine: String, delimiters: List<Char>): Char? =
        delimiters.map { it to csvLine.count { char -> char == it } }
            .filter { (_, count) -> count > 0 }
            .maxByOrNull { (_, count) -> count > 0 }
            ?.first
}

data class CsvFileUploadResult(
    val parsedFile: ParsedFile?,
    val errors: List<ReaderViolation>
)
