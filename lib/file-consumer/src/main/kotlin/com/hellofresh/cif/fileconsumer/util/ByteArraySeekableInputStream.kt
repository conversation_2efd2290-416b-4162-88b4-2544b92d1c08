package com.hellofresh.cif.fileconsumer.util

import java.io.ByteArrayInputStream
import java.io.IOException
import java.nio.ByteBuffer
import org.apache.parquet.io.SeekableInputStream

class ByteArraySeekableInputStream(private val inputStream: ByteArrayInputStream) : SeekableInputStream() {
    private var position: Long = 0

    @Suppress("ReturnCount")
    override fun read(p0: ByteBuffer?): Int {
        if (p0 == null) return 0
        val bytes = ByteArray(p0.remaining())
        val bytesRead = inputStream.read(bytes)
        if (bytesRead == -1) return -1
        p0.put(bytes, 0, bytesRead)
        position += bytesRead
        return bytesRead
    }

    @Throws(IOException::class)
    override fun read(): Int {
        val res = inputStream.read()
        if (res != -1) {
            position++
        }
        return res
    }

    @Throws(IOException::class)
    override fun read(b: ByteArray, off: Int, len: Int): Int {
        val res = inputStream.read(b, off, len)
        if (res != -1) {
            position += res.toLong()
        }
        return res
    }

    @Throws(IOException::class)
    override fun getPos(): Long =
        position

    @Throws(IOException::class)
    override fun seek(newPos: Long) {
        inputStream.reset()
        inputStream.skip(newPos)
        position = newPos
    }

    override fun readFully(b: ByteArray) {
        val res = inputStream.read(b, 0, b.size)
        position += res.toLong()
    }

    override fun readFully(b: ByteArray, off: Int, len: Int) {
        val res = inputStream.read(b, off, len)
        position += res.toLong()
    }

    override fun readFully(p0: ByteBuffer?) {
        if (p0 == null) return
        val bytes = ByteArray(p0.remaining())
        inputStream.read(bytes)
        p0.put(bytes)
        position += bytes.size
    }
}
