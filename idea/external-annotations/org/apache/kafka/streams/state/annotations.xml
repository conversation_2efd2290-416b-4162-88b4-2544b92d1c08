<root>
    <item name='org.apache.kafka.streams.state.KeyValueStore V delete(K)'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.state.KeyValueStore V delete(K) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.streams.state.KeyValueStore V putIfAbsent(K, V) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.streams.state.KeyValueStore V putIfAbsent(K, V) 1'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.state.KeyValueStore void put(K, V) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.streams.state.KeyValueStore void put(K, V) 1'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.state.KeyValueStore void putAll(java.util.List&lt;org.apache.kafka.streams.KeyValue&lt;K,V&gt;&gt;) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.streams.state.ReadOnlyKeyValueStore V get(K)'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
    </item>
    <item name='org.apache.kafka.streams.state.ReadOnlyKeyValueStore V get(K) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.streams.state.ReadOnlyKeyValueStore long approximateNumEntries()'>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
    </item>
    <item name='org.apache.kafka.streams.state.ReadOnlyKeyValueStore org.apache.kafka.streams.state.KeyValueIterator&lt;K,V&gt; all()'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
    </item>
    <item name='org.apache.kafka.streams.state.ReadOnlyKeyValueStore org.apache.kafka.streams.state.KeyValueIterator&lt;K,V&gt; range(K, K)'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
    </item>
    <item name='org.apache.kafka.streams.state.ReadOnlyKeyValueStore org.apache.kafka.streams.state.KeyValueIterator&lt;K,V&gt; range(K, K) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.streams.state.ReadOnlyKeyValueStore org.apache.kafka.streams.state.KeyValueIterator&lt;K,V&gt; range(K, K) 1'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
</root>
