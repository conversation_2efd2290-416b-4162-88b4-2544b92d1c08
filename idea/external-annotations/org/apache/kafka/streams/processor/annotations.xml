<root>
    <item name='org.apache.kafka.streams.processor.Processor void init(org.apache.kafka.streams.processor.ProcessorContext) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.streams.processor.ProcessorContext java.lang.String topic()'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.streams.processor.StateStore java.lang.String name()'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
    </item>
    <item
        name='org.apache.kafka.streams.processor.StateStore void init(org.apache.kafka.streams.processor.ProcessorContext, org.apache.kafka.streams.processor.StateStore) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item
        name='org.apache.kafka.streams.processor.StateStore void init(org.apache.kafka.streams.processor.ProcessorContext, org.apache.kafka.streams.processor.StateStore) 1'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
</root>
