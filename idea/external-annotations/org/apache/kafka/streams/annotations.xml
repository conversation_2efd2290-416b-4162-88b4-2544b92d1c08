<root>
    <item name='org.apache.kafka.streams.KeyValue KeyValue(K, V) 0'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.KeyValue KeyValue(K, V) 1'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.KeyValue boolean equals(java.lang.Object) 0'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.KeyValue key'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.KeyValue org.apache.kafka.streams.KeyValue&lt;K,V&gt; pair(K, V)'>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="value" val="&quot;_,_-&gt;new&quot;"/>
            <val name="pure" val="true"/>
        </annotation>
    </item>
    <item name='org.apache.kafka.streams.KeyValue org.apache.kafka.streams.KeyValue&lt;K,V&gt; pair(K, V) 0'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.KeyValue org.apache.kafka.streams.KeyValue&lt;K,V&gt; pair(K, V) 1'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.KeyValue value'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.TestOutputTopic java.util.List&lt;V&gt; readValuesToList()'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.streams.TestOutputTopic java.util.List&lt;org.apache.kafka.streams.KeyValue&lt;K,V&gt;&gt; readKeyValuesToList()'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.streams.TestOutputTopic java.util.List&lt;org.apache.kafka.streams.test.TestRecord&lt;K,V&gt;&gt; readRecordsToList()'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.streams.TestOutputTopic java.util.Map&lt;K,V&gt; readKeyValuesToMap()'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
</root>
