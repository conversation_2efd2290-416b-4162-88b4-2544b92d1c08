<root>
    <item name='org.apache.kafka.streams.test.TestRecord K getKey()'>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord K key()'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V) 0'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V) 1'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V, java.time.Instant) 0'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V, java.time.Instant) 1'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V, java.time.Instant) 2'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V, org.apache.kafka.common.header.Headers) 0'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V, org.apache.kafka.common.header.Headers) 1'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V, org.apache.kafka.common.header.Headers) 2'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V, org.apache.kafka.common.header.Headers, java.lang.Long) 0'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V, org.apache.kafka.common.header.Headers, java.lang.Long) 1'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V, org.apache.kafka.common.header.Headers, java.lang.Long) 2'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V, org.apache.kafka.common.header.Headers, java.lang.Long) 3'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
        <annotation name='org.jetbrains.annotations.Range'>
            <val name="from" val="0"/>
            <val name="to" val="java.lang.Long.MAX_VALUE"/>
        </annotation>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V, org.apache.kafka.common.header.Headers, java.time.Instant) 0'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V, org.apache.kafka.common.header.Headers, java.time.Instant) 1'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V, org.apache.kafka.common.header.Headers, java.time.Instant) 2'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(K, V, org.apache.kafka.common.header.Headers, java.time.Instant) 3'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(V) 0'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(org.apache.kafka.clients.consumer.ConsumerRecord&lt;K,V&gt;) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord TestRecord(org.apache.kafka.clients.producer.ProducerRecord&lt;K,V&gt;) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord V getValue()'>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord V value()'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord boolean equals(java.lang.Object) 0'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord headers'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord int hashCode()'>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord java.lang.Long timestamp()'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord java.lang.String toString()'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord java.time.Instant getRecordTime()'>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord key'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord org.apache.kafka.common.header.Headers getHeaders()'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord org.apache.kafka.common.header.Headers headers()'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
        <annotation name='org.jetbrains.annotations.Contract'>
            <val name="pure" val="true"/>
        </annotation>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord recordTime'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.streams.test.TestRecord value'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
</root>
