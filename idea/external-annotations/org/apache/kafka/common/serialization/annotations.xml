<root>
    <item name='org.apache.kafka.common.serialization.Deserializer T deserialize(java.lang.String, byte[])'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Deserializer T deserialize(java.lang.String, byte[]) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Deserializer T deserialize(java.lang.String, byte[]) 1'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Deserializer T deserialize(java.lang.String, org.apache.kafka.common.header.Headers, byte[])'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Deserializer T deserialize(java.lang.String, org.apache.kafka.common.header.Headers, byte[]) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Deserializer T deserialize(java.lang.String, org.apache.kafka.common.header.Headers, byte[]) 1'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Deserializer T deserialize(java.lang.String, org.apache.kafka.common.header.Headers, byte[]) 2'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Deserializer void configure(java.util.Map&lt;java.lang.String,?&gt;, boolean) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Serde org.apache.kafka.common.serialization.Deserializer&lt;T&gt; deserializer()'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Serde org.apache.kafka.common.serialization.Serializer&lt;T&gt; serializer()'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Serde void configure(java.util.Map&lt;java.lang.String,?&gt;, boolean) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Serializer byte[] serialize(java.lang.String, T)'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Serializer byte[] serialize(java.lang.String, T) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Serializer byte[] serialize(java.lang.String, T) 1'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Serializer byte[] serialize(java.lang.String, org.apache.kafka.common.header.Headers, T)'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Serializer byte[] serialize(java.lang.String, org.apache.kafka.common.header.Headers, T) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Serializer byte[] serialize(java.lang.String, org.apache.kafka.common.header.Headers, T) 1'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Serializer byte[] serialize(java.lang.String, org.apache.kafka.common.header.Headers, T) 2'>
        <annotation name='org.jetbrains.annotations.Nullable'/>
    </item>
    <item name='org.apache.kafka.common.serialization.Serializer void configure(java.util.Map&lt;java.lang.String,?&gt;, boolean) 0'>
        <annotation name='org.jetbrains.annotations.NotNull'/>
    </item>
</root>
