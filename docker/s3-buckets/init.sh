#!/usr/bin/env bash
echo "creating S3 bucket hf-bi-dwh-uploader"
awslocal --endpoint-url=http://localhost:4566 s3 mb "s3://hf-bi-dwh-uploader" --region eu-west-1

echo "copying safety stock multiplier to S3 bucket hf-bi-dwh-uploader"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader/ip_safety_stock/DACH/VE/safety_stock_multiplier.csv s3://hf-bi-dwh-uploader/ip_safety_stock/DACH/VE/safety_stock_multiplier.csv

echo "Creating S3 bucket [cif-stock-files-export-local]"
awslocal --endpoint-url=http://localhost:4566 s3 mb "s3://cif-stock-files-export-local" --region eu-west-1

echo "--> copying stock files export folder to S3 bucket cif-stock-files-export-local"
awslocal --endpoint-url=http://localhost:4566 s3 sync /docker/s3-buckets/cif-stock-files-export-local s3://cif-stock-files-export-local --metadata market=EU,author_name=John,author_email=<EMAIL>

echo "--> copying bulk stock update csv to S3 bucket cif-stock-files-export-local/stock_update"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/cif-stock-files-export-local/stock_update/sample_file.csv s3://cif-stock-files-export-local/stock_update/sample_file.csv

echo "creating S3 bucket hf-group-limesync-gsheet-local-nonsensitive"
awslocal --endpoint-url=http://localhost:4566 s3 mb "s3://hf-group-limesync-gsheet-local-nonsensitive" --region eu-west-1

echo "copying US safety stock files to S3 bucket hf-group-limesync-gsheet-local-nonsensitive"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-group-limesync-gsheet-local-nonsensitive/raw/us-bta/overwrite/calculated_buffers/test_file.parquet s3://hf-group-limesync-gsheet-local-nonsensitive/raw/us-bta/overwrite/calculated_buffers/test_file.parquet

echo "copying UK packaging to S3 bucket hf-bi-dwh-uploader/gb_packaging_forecast"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader/gb_packaging_forecast/gb_forecast.csv s3://hf-bi-dwh-uploader/gb_packaging_forecast/gb_forecast.csv

echo "copying DACH BX target safety stock to S3 bucket ip_safety_stock/target_safety_stock/DACH/BX"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/DACH/BX/safety_stock_calculated.csv s3://hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/DACH/BX/safety_stock_calculated.csv

echo "copying DACH VE target safety stock to S3 bucket ip_safety_stock/target_safety_stock/DACH/VE"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/DACH/VE/safety_stock_calculated.csv s3://hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/DACH/VE/safety_stock_calculated.csv

echo "copying FR LI target safety stock to S3 bucket ip_safety_stock/target_safety_stock/FR/LI"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/FR/LI/safety_stock_calculated.csv s3://hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/FR/LI/safety_stock_calculated.csv

echo "copying ES SP target safety stock to S3 bucket ip_safety_stock/target_safety_stock/ES/SP"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/ES/SP/safety_stock_calculated.csv s3://hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/ES/SP/safety_stock_calculated.csv

echo "copying GB BV target safety stock to S3 bucket ip_safety_stock/target_safety_stock/GB/BV"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/GB/BV/safety_stock_calculated.csv s3://hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/GB/BV/safety_stock_calculated.csv

echo "copying IE IE target safety stock to S3 bucket ip_safety_stock/target_safety_stock/IE/IE"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/IE/IE/safety_stock_calculated.csv s3://hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/IE/IE/safety_stock_calculated.csv

echo "copying IT IT target safety stock to S3 bucket ip_safety_stock/target_safety_stock/IT/IT"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/IT/IT/safety_stock_calculated.csv s3://hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/IT/IT/safety_stock_calculated.csv

echo "copying NEWZEALAND NZ target safety stock to S3 bucket ip_safety_stock/target_safety_stock/NEWZEALAND/NZ"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/NEWZEALAND/NZ/safety_stock_calculated.csv s3://hf-bi-dwh-uploader/ip_safety_stock/target_safety_stock/NEWZEALAND/NZ/safety_stock_calculated.csv

echo "creating S3 bucket hf-bi-dwh-uploader-staging"
awslocal --endpoint-url=http://localhost:4566 s3 mb "s3://hf-bi-dwh-uploader-staging" --region eu-west-1

echo "copying DACH BX target safety stock to S3 bucket ip_safety_stock/target_safety_stock/DACH/BX"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/DACH/BX/safety_stock_calculated.csv s3://hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/DACH/BX/safety_stock_calculated.csv

echo "copying DACH VE target safety stock to S3 bucket ip_safety_stock/target_safety_stock/DACH/VE"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/DACH/VE/safety_stock_calculated.csv s3://hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/DACH/VE/safety_stock_calculated.csv

echo "copying FR LI target safety stock to S3 bucket ip_safety_stock/target_safety_stock/FR/LI"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/FR/LI/safety_stock_calculated.csv s3://hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/FR/LI/safety_stock_calculated.csv

echo "copying ES SP target safety stock to S3 bucket ip_safety_stock/target_safety_stock/ES/SP"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/ES/SP/safety_stock_calculated.csv s3://hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/ES/SP/safety_stock_calculated.csv

echo "copying GB BV target safety stock to S3 bucket ip_safety_stock/target_safety_stock/GB/BV"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/GB/BV/safety_stock_calculated.csv s3://hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/GB/BV/safety_stock_calculated.csv

echo "copying IE IE target safety stock to S3 bucket ip_safety_stock/target_safety_stock/IE/IE"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/IE/IE/safety_stock_calculated.csv s3://hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/IE/IE/safety_stock_calculated.csv

echo "copying IT IT target safety stock to S3 bucket ip_safety_stock/target_safety_stock/IT/IT"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/IT/IT/safety_stock_calculated.csv s3://hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/IT/IT/safety_stock_calculated.csv

echo "copying NEWZEALAND NZ target safety stock to S3 bucket ip_safety_stock/target_safety_stock/NEWZEALAND/NZ"
awslocal --endpoint-url=http://localhost:4566 s3 cp /docker/s3-buckets/hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/NEWZEALAND/NZ/safety_stock_calculated.csv s3://hf-bi-dwh-uploader-staging/ip_safety_stock/target_safety_stock/NEWZEALAND/NZ/safety_stock_calculated.csv

echo "all buckets"
awslocal --endpoint-url=http://localhost:4566 s3 ls

#SQS
