CREATE DATABASE bv;
CREATE DATABASE ve;
CREATE DATABASE gr;

DROP TABLE IF EXISTS `bv`.`x_stock`;
DROP TABLE IF EXISTS `bv`.`x_delivery_line`;

CREATE TABLE `bv`.`x_stock` (
    record_number int(10),
    site_id varchar(512),
    stock_state varchar(50),
    prod_code varchar(33),
    qty int(10),
    receipt_date datetime,
    po_id varchar(20),
    use_by_date datetime,
    tm_location varchar(31),
    PRIMARY KEY (record_number, site_id)
);

CREATE TABLE `bv`.`x_delivery_line` (
    record_number int(10),
    site_id varchar(512),
    prod_code VARCHAR(33),
    delivery_id VARCHAR(33),
    delivery_line_state VARCHAR(50),
    palletised_usable_qty int(11),
    state_change_time datetime,
    PRIMARY KEY (record_number, site_id)
);

DROP TABLE IF EXISTS `ve`.`x_stock`;
DROP TABLE IF EXISTS `ve`.`x_delivery_line`;

CREATE TABLE `ve`.`x_stock` (
    record_number int(10),
    site_id varchar(512),
    stock_state varchar(50),
    prod_code varchar(33),
    qty int(10),
    receipt_date datetime,
    po_id varchar(20),
    use_by_date datetime,
    tm_location varchar(31),
    PRIMARY KEY (record_number, site_id)
);

CREATE TABLE `ve`.`x_delivery_line` (
    record_number int(10),
    site_id varchar(512),
    prod_code VARCHAR(33),
    delivery_id VARCHAR(33),
    delivery_line_state VARCHAR(50),
    palletised_usable_qty int(11),
    state_change_time datetime,
    PRIMARY KEY (record_number, site_id)
);

DROP TABLE IF EXISTS `gr`.`x_stock`;
DROP TABLE IF EXISTS `gr`.`x_delivery_line`;

CREATE TABLE `gr`.`x_stock` (
    record_number int(10),
    site_id varchar(512),
    stock_state varchar(50),
    prod_code varchar(33),
    qty int(10),
    receipt_date datetime,
    po_id varchar(20),
    use_by_date datetime,
    tm_location varchar(31),
    PRIMARY KEY (record_number, site_id)
);

CREATE TABLE `gr`.`x_delivery_line` (
    record_number int(10),
    site_id varchar(512),
    prod_code VARCHAR(33),
    delivery_id VARCHAR(33),
    delivery_line_state VARCHAR(50),
    palletised_usable_qty int(11),
    state_change_time datetime,
    PRIMARY KEY (record_number, site_id)
);

CREATE USER 'fcms'@'%' IDENTIFIED BY '123456';
GRANT ALL ON *.* TO 'fcms'@'%';
