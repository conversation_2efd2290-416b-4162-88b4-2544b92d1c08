#!/usr/bin/env bash
set -o errexit -o posix -o nounset -o pipefail -o errtrace

topics="$1"
filter=${2:-}

cd "$(dirname "$0")"

eval "echo \"$(cat consumer.properties)\"" >/tmp/consumer.properties
eval "echo \"$(cat producer.properties)\"" >/tmp/producer.properties

if [ -z "$filter" ]; then
    echo "Mirroring everything"

    kafka-mirror-maker \
        --consumer.config /tmp/consumer.properties \
        --producer.config /tmp/producer.properties \
        --whitelist "$topics"
else
    echo "Mirroring only messages with values for \"$filter\""

    kafka-mirror-maker \
        --consumer.config /tmp/consumer.properties \
        --producer.config /tmp/producer.properties \
        --message.handler com.hellofresh.cif.kafkaMirrorPlugin.MessageFilter \
        --message.handler.args "$filter" \
        --whitelist "$topics"
fi
