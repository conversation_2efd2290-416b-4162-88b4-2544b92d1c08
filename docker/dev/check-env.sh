#!/usr/bin/env bash
set -o errexit -o posix -o nounset -o pipefail -o errtrace

env="${1}"

if [ "$env" == "live" ]; then
    if [ -z "${HF_KAFKA_PASSWORD_LIVE:-}" ]; then
        echo "HF_KAFKA_PASSWORD_LIVE is undefined"
        exit 1
    fi
    if [ -z "${HF_KAFKA_PASSWORD_READONLY_LIVE:-}" ]; then
        echo "HF_KAFKA_PASSWORD_READONLY_LIVE is undefined"
        exit 1
    fi
fi

if [ "$env" == "staging" ]; then
    if [ -z "${HF_KAFKA_PASSWORD_STAGING:-}" ]; then
        echo "HF_KAFKA_PASSWORD_STAGING is undefined"
        exit 1
    fi
    if [ -z "${HF_KAFKA_PASSWORD_READONLY_STAGING:-}" ]; then
        echo "HF_KAFKA_PASSWORD_READONLY_STAGING is undefined"
        exit 1
    fi
fi

if [ "$env" != "local" ]; then
    if [ -z "${HF_KAFKA_TRUSTSTORE_PASSWORD:-}" ]; then
        echo "HF_KAFKA_TRUSTSTORE_PASSWORD is undefined"
        exit 1
    fi
fi
