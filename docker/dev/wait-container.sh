#!/usr/bin/env bash
set -o errexit -o posix -o nounset -o pipefail -o errtrace

TIMEOUT=100
ATTEMPT=0
CONTAINER=""

echoerr() {
  printf "%s\n" "$*" 1>&2
}

usage() {
  exitcode="$1"
  cat << USAGE >&2
Usage:
  cmd -c container [-t timeout]
  -t TIMEOUT | --timeout=timeout            Timeout in seconds, zero for no timeout
  -c CONTAINER | --container=container      Container name to be health checked
USAGE
  exit "$exitcode"
}

health_check() {
  until [ "$ATTEMPT" -eq "$TIMEOUT" ]; do
    if [ "$ATTEMPT" -eq 0 ]; then
      echo "Waiting for container '$CONTAINER' to be healthy..."
    fi;
    CONTAINER_ID=$(docker ps -f health=healthy -f name="$CONTAINER" --format "{{.ID}}")
    if [ -n "$CONTAINER_ID" ]; then
      exit 0
    fi;
    sleep 1
    ((ATTEMPT+=1))
  done
  echo "Operation timed out" >&2
  exit 1
}

while [ $# -gt 0 ]; do
  case "$1" in
  -c)
    CONTAINER="$2"
    shift 2
    ;;
  --container=*)
    CONTAINER="${1#*=}"
    shift 1
    ;;
  -t)
    TIMEOUT="$2"
    if [ "$TIMEOUT" = "" ]; then break; fi
    shift 2
    ;;
  --timeout=*)
    TIMEOUT="${1#*=}"
    shift 1
    ;;
  --help)
    usage 0
    ;;
  *)
    echoerr "Unknown argument: $1"
    usage 1
    ;;
  esac
done


health_check "$@"
