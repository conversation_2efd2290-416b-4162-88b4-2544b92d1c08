#!/usr/bin/env bash
set -e

topics="$1"
if [[ -z "$topics" ]]; then
    echo "Missing argument 'topics'"
    topics=","
fi

entries=$(echo $topics | tr "|" "\n")

for topic in $entries; do
    echo "Creating a topic: $topic"
    kafka-topics --create --topic "$topic" --partitions 1 --replication-factor 1 --config cleanup.policy=compact --config retention.ms=7776000000 --if-not-exists --bootstrap-server localhost:9092
done

# empty topic has to be with one partition - that's why creating it outside the loop
kafka-topics --create --topic public.empty.v1 --partitions 1 --replication-factor 1 --if-not-exists --bootstrap-server localhost:9092
