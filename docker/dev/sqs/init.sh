#!/usr/bin/env bash
set -euo pipefail

create_queue() {
    local QUEUE_NAME_TO_CREATE=$1
    awslocal --endpoint-url=http://localhost:4566 --region="${DEFAULT_REGION}" sqs create-queue --queue-name "${QUEUE_NAME_TO_CREATE}"
}

get_all_queues() {
    awslocal --endpoint-url=http://localhost:4566 --region="${DEFAULT_REGION}" sqs list-queues
}
# Queue Specific here

get_queue_url() {
    local QUEUE_URL
    QUEUE_URL=$(awslocal --endpoint-url=http://localhost:4566 --region="${DEFAULT_REGION}" \
        sqs list-queues --query "QueueUrls[?ends_with(@, \`${QUEUE_NAME}\`)]" --output text)
    # Check if the queue URL is empty
    if [ -z "$QUEUE_URL" ]; then
        echo "Queue not found"
        return 1
    fi
    echo "$QUEUE_URL"
}

get_queue_us_safety_stock_url() {
    local QUEUE_US_SAFETY_STOCK_URL
    QUEUE_US_SAFETY_STOCK_URL=$(awslocal --endpoint-url=http://localhost:4566 --region="${DEFAULT_REGION}" \
        sqs list-queues --query "QueueUrls[?ends_with(@, \`${QUEUE_NAME_US_SAFETY_STOCK}\`)]" --output text)
    # Check if the queue URL is empty
    if [ -z "$QUEUE_US_SAFETY_STOCK_URL" ]; then
        echo "Queue QUEUE_US_SAFETY_STOCK_URL not found"
        return 1
    fi
    echo "$QUEUE_US_SAFETY_STOCK_URL"
}

get_queue_3pw_stock_uploads_url() {
    local QUEUE_3PW_STOCK_UPLOADS_URL
    QUEUE_3PW_STOCK_UPLOADS_URL=$(awslocal --endpoint-url=http://localhost:4566 --region="${DEFAULT_REGION}" \
        sqs list-queues --query "QueueUrls[?ends_with(@, \`${QUEUE_NAME_3PW_STOCK_UPLOADS}\`)]" --output text)
    # Check if the queue URL is empty
    if [ -z "$QUEUE_3PW_STOCK_UPLOADS_URL" ]; then
        echo "Queue QUEUE_3PW_STOCK_UPLOADS_URL not found"
        return 1
    fi
    echo "$QUEUE_3PW_STOCK_UPLOADS_URL"
}

get_queue_uk_packaging_url() {
    local QUEUE_UK_PACKAGING_URL
    QUEUE_UK_PACKAGING_URL=$(awslocal --endpoint-url=http://localhost:4566 --region="${DEFAULT_REGION}" \
        sqs list-queues --query "QueueUrls[?ends_with(@, \`${QUEUE_NAME_UK_PACKAGING}\`)]" --output text)
    # Check if the queue URL is empty
    if [ -z "$QUEUE_UK_PACKAGING_URL" ]; then
        echo "Queue QUEUE_UK_PACKAGING_URL not found"
        return 1
    fi
    echo "$QUEUE_UK_PACKAGING_URL"
}

get_queue_stock_update_url() {
    local QUEUE_STOCK_UPDATE_URL
    QUEUE_STOCK_UPDATE_URL=$(awslocal --endpoint-url=http://localhost:4566 --region="${DEFAULT_REGION}" \
        sqs list-queues --query "QueueUrls[?ends_with(@, \`${QUEUE_NAME_STOCK_UPDATE}\`)]" --output text)
    # Check if the queue URL is empty
    if [ -z "$QUEUE_STOCK_UPDATE_URL" ]; then
        echo "Queue QUEUE_STOCK_UPDATE_URL not found"
        return 1
    fi
    echo "$QUEUE_STOCK_UPDATE_URL"
}

get_queue_forecast_packaging_url() {
    local QUEUE_FORECAST_PACKAGING_URL
    QUEUE_FORECAST_PACKAGING_URL=$(awslocal --endpoint-url=http://localhost:4566 --region="${DEFAULT_REGION}" \
        sqs list-queues --query "QueueUrls[?ends_with(@, \`${QUEUE_NAME_FORECAST_PACKAGING}\`)]" --output text)
    # Check if the queue URL is empty
    if [ -z "$QUEUE_FORECAST_PACKAGING_URL" ]; then
        echo "Queue QUEUE_FORECAST_PACKAGING_URL not found"
        return 1
    fi
    echo "$QUEUE_FORECAST_PACKAGING_URL"
}

# Put message in queue
put_message_in_queue() {
    QUEUE_URL=$(get_queue_url)
    echo "put_message_in_queue queue url $QUEUE_NAME"
    awslocal --endpoint-url=http://localhost:4566 sqs send-message --region="${DEFAULT_REGION}" --queue-url="$QUEUE_URL" --message-body="{\"Records\":[{\"eventVersion\":\"2.1\",\"eventSource\":\"aws:s3\",\"awsRegion\":\"eu-west-11\",\"eventTime\":\"2024-10-08T09:51:04.773Z\",\"eventName\":\"ObjectCreated:Put\",\"userIdentity\":{\"principalId\":\"AWS:AROA6K4FMOAP7ZHSXRYTK:<EMAIL>\"},\"requestParameters\":{\"sourceIPAddress\":\"*******\"},\"responseElements\":{\"x-amz-request-id\":\"sdfsfsf\",\"x-amz-id-2\":\"FrdbMaeOVgoaGrPMhwhj1kiX2izdmLHHfvVYCbGCHqwJ8NnqSUcOG6dsdu2AiqXf8/J2c5HBBf7MqiZRRSrql3vPr6JekxNt\"},\"s3\":{\"s3SchemaVersion\":\"1.0\",\"configurationId\":\"tf-s3-queue-23424242343242423\",\"bucket\":{\"name\":\"hf-bi-dwh-uploader\",\"ownerIdentity\":{\"principalId\":\"A13H6PT4GJ3OU5\"},\"arn\":\"arn:aws:s3:::hf-bi-dwh-uploader\"},\"object\":{\"key\":\"ip_safety_stock/DACH/VE/safety_stock_multiplier.csv\",\"size\":9350280,\"eTag\":\"bf2463ba179d218f0f8e4191c150d843342342\",\"versionId\":\"PUr.Hf8aZVRqfXoCG8qjw_Yw7pW9p0NQfsssfs\",\"sequencer\":\"006705008894842A9Dsfsfsf\"}}}]}"
}

put_us_safety_stock_in_queue() {
    QUEUE_US_SAFETY_STOCK_URL=$(get_queue_us_safety_stock_url)
    echo "put_message_in_queue queue 2 url $QUEUE_NAME_US_SAFETY_STOCK"
    awslocal --endpoint-url=http://localhost:4566 sqs send-message --region="${DEFAULT_REGION}" --queue-url="$QUEUE_US_SAFETY_STOCK_URL" --message-body="{\"Type\":\"Notification\",\"MessageId\":\"0000-363d-576d-a7ab-59f0000\",\"TopicArn\":\"arn:aws:sns:eu-west-1:0000000:hf_ip_us_safety_stock_calculated_buffer_topic_staging\",\"Subject\":\"AmazonS3Notification\",\"Message\":\"{\\\"Records\\\":[{\\\"eventVersion\\\":\\\"2.1\\\",\\\"eventSource\\\":\\\"aws:s3\\\",\\\"awsRegion\\\":\\\"eu-west-11\\\",\\\"eventTime\\\":\\\"2025-03-20T22:58:00.483Z\\\",\\\"eventName\\\":\\\"ObjectCreated:Put\\\",\\\"userIdentity\\\":{\\\"principalId\\\":\\\"AWS:AROA6K4FMOAP7ZHSXRYTK:<EMAIL>\\\"},\\\"requestParameters\\\":{\\\"sourceIPAddress\\\":\\\"*******\\\"},\\\"responseElements\\\":{\\\"x-amz-request-id\\\":\\\"SQK0Q0000347QRN\\\",\\\"x-amz-id-2\\\":\\\"ChRH3xKkKDYQ5ZWFONP9DpvbnfSCuEp/ooDD/DvrgfPFH4sNCB08Cz+G/cwluVO7/nPJwfVl\\\"},\\\"s3\\\":{\\\"s3SchemaVersion\\\":\\\"1.0\\\",\\\"configurationId\\\":\\\"tf-s3-topic-2025000124326526700000001\\\",\\\"bucket\\\":{\\\"name\\\":\\\"hf-group-limesync-gsheet-local-nonsensitive\\\",\\\"ownerIdentity\\\":{\\\"principalId\\\":\\\"A13H00T4G03OU5\\\"},\\\"arn\\\":\\\"arn:aws:s3:::hf-group-limesync-gsheet-local-nonsensitive\\\"},\\\"object\\\":{\\\"key\\\":\\\"raw/us-bta/overwrite/calculated_buffers/test_file.parquet\\\",\\\"size\\\":3769,\\\"eTag\\\":\\\"6633a8b3a79000bdc1fbf6dbe61\\\",\\\"sequencer\\\":\\\"0067DC0006A502\\\"}}}]}\",\"Timestamp\":\"2025-03-20T22:58:01.150Z\",\"SignatureVersion\":\"1\",\"Signature\":\"gdPuDQvTmzBxvJic7koHKDTZQTu+CgB3ltNZpVnRYvzLNFCXkyohQHyuX6A6u1xvyOjTY8yLma/eTSa55a3WaJL3gbaO9NccLawGedY1GOjgMrM3a3/aHHLs7Iye5jg/4oZhau7tqAvdVO5IIhxZ5GlIwbroEG4j5nq6CbbUbG6iCsN2G8rFpJPBVTxuWhS4mSg0XcE4ga5O/Fwf3+9WWuqmApt4AGEMKs78XntLrgtsFG/oloje6oAiKxNY4RhnQ6nYjyQ==\",\"SigningCertURL\":\"https://sns.eu-west-1.amazonaws.com/SimpleNotificationService-9c0000023014631ec06.pem\",\"UnsubscribeURL\":\"https://sns.eu-west-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:eu-west-1:00000000:hf_ip_us_safety_stock_calculated_buffer_topic_staging:36945d6e-e967-490e-85eb-20006b6bcd\"}"
}

put_3pw_stock_upload_in_queue() {
    QUEUE_3PW_STOCK_UPLOADS_URL=$(get_queue_3pw_stock_uploads_url)
    echo "put_message_in_queue 3PW queue url $QUEUE_NAME_3PW_STOCK_UPLOADS"
    awslocal --endpoint-url=http://localhost:4566 sqs send-message --region="${DEFAULT_REGION}" --queue-url="$QUEUE_3PW_STOCK_UPLOADS_URL" --message-body="{\"Records\":[{\"eventVersion\":\"2.1\",\"eventSource\":\"aws:s3\",\"awsRegion\":\"eu-west-11\",\"eventTime\":\"2024-10-08T09:51:04.773Z\",\"eventName\":\"ObjectCreated:Put\",\"userIdentity\":{\"principalId\":\"AWS:AROA6K4FMOAP7ZHSXRYTK:<EMAIL>\"},\"requestParameters\":{\"sourceIPAddress\":\"*******\"},\"responseElements\":{\"x-amz-request-id\":\"sdfsfsf\",\"x-amz-id-2\":\"FrdbMaeOVgoaGrPMhwhj1kiX2izdmLHHfvVYCbGCHqwJ8NnqSUcOG6dsdu2AiqXf8/J2c5HBBf7MqiZRRSrql3vPr6JekxNt\"},\"s3\":{\"s3SchemaVersion\":\"1.0\",\"configurationId\":\"tf-s3-queue-23424242343242423\",\"bucket\":{\"name\":\"cif-stock-files-export-local\",\"ownerIdentity\":{\"principalId\":\"A13H6PT4GJ3OU5\"},\"arn\":\"arn:aws:s3:::cif-stock-files-export-local\"},\"object\":{\"key\":\"stock_uploads.csv\",\"size\":9350280,\"eTag\":\"bf2463ba179d218f0f8e4191c150d843342344\",\"versionId\":\"PUr.Hf8aZVRqfXoCG8qjw_Yw7pW9p0NQfsssfs\",\"sequencer\":\"006705008894842A9Dsfsfsf\"}}}]}"
}

put_uk_packaging_in_queue() {
    QUEUE_UK_PACKAGING_URL=$(get_queue_uk_packaging_url)
    echo "put_message_in_queue url $QUEUE_NAME_UK_PACKAGING"
    awslocal --endpoint-url=http://localhost:4566 sqs send-message --region="${DEFAULT_REGION}" --queue-url="$QUEUE_UK_PACKAGING_URL" --message-body="{\"Type\": \"Notification\", \"MessageId\": \"c09dac83-c57b-4597-a42a-dc7f6525f08f\", \"TopicArn\": \"arn:aws:sns:eu-west-1:000000000000:hf_bi_dwh_uploader_gb_packaging_topic_staging\", \"Message\": \"{\\\"Records\\\": [{\\\"eventVersion\\\": \\\"2.1\\\", \\\"eventSource\\\": \\\"aws:s3\\\", \\\"awsRegion\\\": \\\"eu-west-1\\\", \\\"eventTime\\\": \\\"2025-04-01T21:05:35.667Z\\\", \\\"eventName\\\": \\\"ObjectCreated:Put\\\", \\\"userIdentity\\\": {\\\"principalId\\\": \\\"AIDAJDPLRKLG7UEXAMPLE\\\"}, \\\"requestParameters\\\": {\\\"sourceIPAddress\\\": \\\"127.0.0.1\\\"}, \\\"responseElements\\\": {\\\"x-amz-request-id\\\": \\\"0c8acf21\\\", \\\"x-amz-id-2\\\": \\\"eftixk72aD6Ap51TnqcoF8eFidJG9Z/2\\\"}, \\\"s3\\\": {\\\"s3SchemaVersion\\\": \\\"1.0\\\", \\\"configurationId\\\": \\\"tf-s3-topic-20250401200640121400000001\\\", \\\"bucket\\\": {\\\"name\\\": \\\"hf-bi-dwh-uploader\\\", \\\"ownerIdentity\\\": {\\\"principalId\\\": \\\"A3NL1KOZZKExample\\\"}, \\\"arn\\\": \\\"arn:aws:s3:::hf-bi-dwh-uploader\\\"}, \\\"object\\\": {\\\"key\\\": \\\"gb_packaging_forecast/gb_forecast.csv\\\", \\\"sequencer\\\": \\\"0055AED6DCD90281E5\\\", \\\"eTag\\\": \\\"e6507eb8181dad2f9b493f2f8e77ef54\\\", \\\"size\\\": 33514}}}]}\", \"Timestamp\": \"2025-04-01T21:05:35.680Z\", \"UnsubscribeURL\": \"http://localhost.localstack.cloud:4566/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:eu-west-1:000000000000:hf_bi_dwh_uploader_gb_packaging_topic_staging:e2e0fc04-bbc2-4189-bf6e-494b962f6f04\", \"Subject\": \"Amazon S3 Notification\", \"SignatureVersion\": \" 1\", \"Signature\": \"F63mU2NMEzocdshxT2qFgpQvMXrPIysqwZF2+FvpAA1r+GiLffrreykOtNXOvMnqsfWKWVgYPvu0gxvnM5/JSPL3ddnX7/BbNra7DT75NeIOF2aokvsvZrCsUOnCOn3cerXVtB32foY6H4ZDkhA5HIge+FlQijhS0H5VP9sZYYptXST8hSKgs0q8UzDp/+6SV3uNkt0X1XUhXs3jnTUt/ZpJS244NbQoAhxWg9WPv0TpR+71+sqYN6soo39n7QyELWkOKHeeEMct6owQ5qsn2p9JdYOQXVBdUNpQpjLlsU1yYtsOi7WeGYpPtCIaWzAl+LG0m1/4T6AvSYRqa4zbyw==\", \"SigningCertURL\": \"http://localhost.localstack.cloud:4566/_aws/sns/SimpleNotificationService-6c6f63616c737461636b69736e696365.pem\"}"
}

put_stock_update_in_queue() {
    QUEUE_STOCK_UPDATE_URL=$(get_queue_stock_update_url)
    echo "put_message_in_queue stock update queue url $QUEUE_NAME_STOCK_UPDATE"
    awslocal --endpoint-url=http://localhost:4566 sqs send-message --region="${DEFAULT_REGION}" --queue-url="$QUEUE_STOCK_UPDATE_URL" --message-body="{\"Records\":[{\"eventVersion\":\"2.1\",\"eventSource\":\"aws:s3\",\"awsRegion\":\"eu-west-11\",\"eventTime\":\"2024-10-08T09:51:04.773Z\",\"eventName\":\"ObjectCreated:Put\",\"userIdentity\":{\"principalId\":\"AWS:AROA6K4FMOAP7ZHSXRYTK:<EMAIL>\"},\"requestParameters\":{\"sourceIPAddress\":\"*******\"},\"responseElements\":{\"x-amz-request-id\":\"sdfsfsf\",\"x-amz-id-2\":\"FrdbMaeOVgoaGrPMhwhj1kiX2izdmLHHfvVYCbGCHqwJ8NnqSUcOG6dsdu2AiqXf8/J2c5HBBf7MqiZRRSrql3vPr6JekxNt\"},\"s3\":{\"s3SchemaVersion\":\"1.0\",\"configurationId\":\"tf-s3-queue-23424242343242424\",\"bucket\":{\"name\":\"cif-stock-files-export-local\",\"ownerIdentity\":{\"principalId\":\"A13H6PT4GJ3OU5\"},\"arn\":\"arn:aws:s3:::cif-stock-files-export-local\"},\"object\":{\"key\":\"stock_update/sample_file.csv\",\"size\":9350280,\"eTag\":\"bf2463ba179d218f0f8e4191c150d843342344\",\"versionId\":\"PUr.Hf8aZVRqfXoCG8qjw_Yw7pW9p0NQfsssfs\",\"sequencer\":\"006705008894842A9Dsfsfsf\"}}}]}"
}

put_forecast_packaging_in_queue() {
    QUEUE_FORECAST_PACKAGING_URL=$(get_queue_forecast_packaging_url)
    echo "put_message_in_queue url $QUEUE_NAME_FORECAST_PACKAGING"
    awslocal --endpoint-url=http://localhost:4566 sqs send-message --region="${DEFAULT_REGION}" --queue-url="$QUEUE_FORECAST_PACKAGING_URL" --message-body="{\"Type\": \"Notification\", \"MessageId\": \"c09dac83-c57b-4597-a42a-dc7f6525f08d\", \"TopicArn\": \"arn:aws:sns:eu-west-1:000000000000:cif-packaging-import-files-staging\", \"Message\": \"{\\\"Records\\\": [{\\\"eventVersion\\\": \\\"2.1\\\", \\\"eventSource\\\": \\\"aws:s3\\\", \\\"awsRegion\\\": \\\"eu-west-1\\\", \\\"eventTime\\\": \\\"2025-04-01T21:05:35.667Z\\\", \\\"eventName\\\": \\\"ObjectCreated:Put\\\", \\\"userIdentity\\\": {\\\"principalId\\\": \\\"AIDAJDPLRKLG7UEXAMPLE\\\"}, \\\"requestParameters\\\": {\\\"sourceIPAddress\\\": \\\"127.0.0.1\\\"}, \\\"responseElements\\\": {\\\"x-amz-request-id\\\": \\\"0c8acf21\\\", \\\"x-amz-id-2\\\": \\\"eftixk72aD6Ap51TnqcoF8eFidJG9Z/2\\\"}, \\\"s3\\\": {\\\"s3SchemaVersion\\\": \\\"1.0\\\", \\\"configurationId\\\": \\\"tf-s3-topic-20250401200640121400000001\\\", \\\"bucket\\\": {\\\"name\\\": \\\"sku_level_demands\\\", \\\"ownerIdentity\\\": {\\\"principalId\\\": \\\"A3NL1KOZZKExample\\\"}, \\\"arn\\\": \\\"arn:aws:s3:::sku_level_demands\\\"}, \\\"object\\\": {\\\"key\\\": \\\"es_packaging_forecast/es_forecast.csv\\\", \\\"sequencer\\\": \\\"0055AED6DCD90281E5\\\", \\\"eTag\\\": \\\"e6507eb8181dad2f9b493f2f8e77ef54\\\", \\\"size\\\": 33514}}}]}\", \"Timestamp\": \"2025-04-01T21:05:35.680Z\", \"UnsubscribeURL\": \"http://localhost.localstack.cloud:4566/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:eu-west-1:000000000000:cif_packaging_import_files_staging_forecast_packaging_topic_staging:e2e0fc04-bbc2-4189-bf6e-494b962f6f04\", \"Subject\": \"Amazon S3 Notification\", \"SignatureVersion\": \" 1\", \"Signature\": \"F63mU2NMEzocdshxT2qFgpQvMXrPIysqwZF2+FvpAA1r+GiLffrreykOtNXOvMnqsfWKWVgYPvu0gxvnM5/JSPL3ddnX7/BbNra7DT75NeIOF2aokvsvZrCsUOnCOn3cerXVtB32foY6H4ZDkhA5HIge+FlQijhS0H5VP9sZYYptXST8hSKgs0q8UzDp/+6SV3uNkt0X1XUhXs3jnTUt/ZpJS244NbQoAhxWg9WPv0TpR+71+sqYN6soo39n7QyELWkOKHeeEMct6owQ5qsn2p9JdYOQXVBdUNpQpjLlsU1yYtsOi7WeGYpPtCIaWzAl+LG0m1/4T6AvSYRqa4zbyw==\", \"SigningCertURL\": \"http://localhost.localstack.cloud:4566/_aws/sns/SimpleNotificationService-6c6f63616c737461636b69736e696365.pem\"}"
}

# Purge queue
purge_queue() {
    local QUEUE_URL=$1
    awslocal --endpoint-url=http://localhost:4566 --region="${DEFAULT_REGION}" sqs purge-queue --queue-url "$QUEUE_URL"
}

purge_queue_us_safety_stock() {
    local QUEUE_US_SAFETY_STOCK_URL=$1
    awslocal --endpoint-url=http://localhost:4566 --region="${DEFAULT_REGION}" sqs purge-queue --queue-url "$QUEUE_US_SAFETY_STOCK_URL"
}

purge_queue_3pw_safety_stock_stock() {
    local QUEUE_3PW_STOCK_UPLOADS_URL=$1
    awslocal --endpoint-url=http://localhost:4566 --region="${DEFAULT_REGION}" sqs purge-queue --queue-url "$QUEUE_3PW_STOCK_UPLOADS_URL"
}

purge_queue_uk_packaging() {
    local QUEUE_UK_PACKAGING_URL=$1
    awslocal --endpoint-url=http://localhost:4566 --region="${DEFAULT_REGION}" sqs purge-queue --queue-url "$QUEUE_UK_PACKAGING_URL"
}

purge_queue_stock_update() {
    local QUEUE_STOCK_UPDATE_URL=$1
    awslocal --endpoint-url=http://localhost:4566 --region="${DEFAULT_REGION}" sqs purge-queue --queue-url "$QUEUE_STOCK_UPDATE_URL"
}

purge_queue_forecast_packaging() {
    local QUEUE_FORECAST_PACKAGING_URL=$1
    awslocal --endpoint-url=http://localhost:4566 --region="${DEFAULT_REGION}" sqs purge-queue --queue-url "$QUEUE_FORECAST_PACKAGING_URL"
}

# Main script
## Create Queue
echo "creating queue $QUEUE_NAME"
create_queue "$QUEUE_NAME"

echo "creating queue 2 $QUEUE_NAME_US_SAFETY_STOCK"
create_queue "$QUEUE_NAME_US_SAFETY_STOCK"

echo "creating 3PW queue $QUEUE_NAME_3PW_STOCK_UPLOADS"
create_queue "$QUEUE_NAME_3PW_STOCK_UPLOADS"

QUEUE_NAME_UK_PACKAGING="uk_packaging-sqs-local-queue"
echo "creating queue $QUEUE_NAME_UK_PACKAGING"
create_queue "$QUEUE_NAME_UK_PACKAGING"

echo "creating stock update queue $QUEUE_NAME_STOCK_UPDATE"
create_queue "$QUEUE_NAME_STOCK_UPDATE"

echo "creating queue $QUEUE_NAME_BNL_CALCULATION"
create_queue "$QUEUE_NAME_BNL_CALCULATION"

QUEUE_NAME_FORECAST_PACKAGING="forecast_packaging-sqs-local-queue"
echo "creating queue $QUEUE_NAME_FORECAST_PACKAGING"
create_queue "$QUEUE_NAME_FORECAST_PACKAGING"

# Set Queue URL
QUEUE_URL=$(get_queue_url)
export QUEUE_URL
echo "Queue URL is set to $QUEUE_URL"
echo "put message in queue:"
put_message_in_queue

QUEUE_US_SAFETY_STOCK_URL=$(get_queue_us_safety_stock_url)
export QUEUE_US_SAFETY_STOCK_URL
echo "Queue URL 2 is set to $QUEUE_US_SAFETY_STOCK_URL"
echo "put message in queue US Safety Stock:"
put_us_safety_stock_in_queue

QUEUE_3PW_STOCK_UPLOADS_URL=$(get_queue_3pw_stock_uploads_url)
export QUEUE_3PW_STOCK_UPLOADS_URL
echo "3PW Queue URL is set to $QUEUE_3PW_STOCK_UPLOADS_URL"
echo "put message in 3PW queue:"
put_3pw_stock_upload_in_queue

QUEUE_UK_PACKAGING_URL=$(get_queue_uk_packaging_url)
export QUEUE_UK_PACKAGING_URL
echo "Queue uk packaging url is set to $QUEUE_UK_PACKAGING_URL"
echo "put message in uk packaging queue:"
put_uk_packaging_in_queue

QUEUE_STOCK_UPDATE_URL=$(get_queue_stock_update_url)
export QUEUE_STOCK_UPDATE_URL
echo "Stock Update Queue URL is set to $QUEUE_STOCK_UPDATE_URL"
echo "put message in Stock Update queue:"
put_stock_update_in_queue

QUEUE_FORECAST_PACKAGING_URL=$(get_queue_forecast_packaging_url)
export QUEUE_FORECAST_PACKAGING_URL
echo "Queue forecast packaging url is set to QUEUE_FORECAST_PACKAGING_URL"

QUEUE_FORECAST_PACKAGING_URL=$(get_queue_forecast_packaging_url)
export QUEUE_FORECAST_PACKAGING_URL
echo "put message in forecast packaging queue:"
put_forecast_packaging_in_queue

# Print all queues
echo "all queues are:"
get_all_queues
