bootstrap.servers=$BROKER_ADDRESS
group.id=scm.inventory.qa.$(date +%s)
partition.assignment.strategy=org.apache.kafka.clients.consumer.RoundRobinAssignor
auto.offset.reset=earliest
security.protocol=SASL_SSL
ssl.truststore.password=$KAFKA_TRUSTSTORE_PASSWORD
ssl.truststore.location=/usr/src/$KAFKA_TRUSTSTORE
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="$KAFKA_USERNAME" password="$KAFKA_PASSWORD";
# fail fast if no connection
connections.max.idle.ms=5000
