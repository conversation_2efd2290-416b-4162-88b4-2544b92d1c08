# CA certificates

Certificates authority are used to connect to <PERSON><PERSON> by running kafkacat's commands.

## Existing certificates

We can use existing certificates from the [kotlin-libs] repository:
- [live.aivencloud.com](https://github.com/hellofresh/kotlin-libs/blob/master/kafka/streams/src/main/resources/kafka/live.aivencloud.com/trust-store.jks)
- [staging.aivencloud.com](https://github.com/hellofresh/kotlin-libs/tree/master/kafka/streams/src/main/resources/kafka/staging.aivencloud.com/trust-store.jks)

To figure out the keystore password, read the generating script [gen-jks].

## How to generate

To generate certificate, use [gen-jks](https://github.com/hellofresh/kotlin-libs/blob/master/kafka/streams/bin/gen-jks) script from the [kotlin-libs] repository.
The command may look like:
```bash
 ./<path to the script location>/gen-jks docker/dev/kafkacat/ca.staging.pem staging aivencloud.com docker/dev
```
To figure out the keystore password, look into the script.


Another way to generate certificate is to use the `keytool` command directly:
```bash
    keytool \
        -import \
        -trustcacerts \
        -file docker/dev/kafkacat/ca.staging.pem \
        -keystore docker/dev/kafka.truststore.staging.jks
```
then enter keystore password of yours

## How to print

In order to print a certificate, run the command:
```bash
    keytool \
        -list \
        -keystore docker/dev/kafka.truststore.staging.jks
```
The [kafka.truststore.staging.jks](./kafka.truststore.staging.jks) and [kafka.truststore.live.jks](./kafka.truststore.live.jks) certs have the same keystore password that can be found in Vault, as [HF_KAFKA_TRUSTSTORE_PASSWORD](https://vault.secrets.hellofresh.io/ui/vault/dashboard?namespace=services%2Fcsku-inventory-forecast).


[kotlin-libs]: https://github.com/hellofresh/kotlin-libs
