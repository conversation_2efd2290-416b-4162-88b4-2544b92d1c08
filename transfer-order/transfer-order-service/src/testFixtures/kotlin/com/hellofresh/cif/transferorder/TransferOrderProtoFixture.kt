package com.hellofresh.cif.transferorder

import com.google.protobuf.Timestamp
import com.google.type.Decimal
import com.google.type.Money
import com.hellofresh.proto.stream.transferOrder.v1.CasePackaging
import com.hellofresh.proto.stream.transferOrder.v1.InventoryInputType
import com.hellofresh.proto.stream.transferOrder.v1.ProductionWeek
import com.hellofresh.proto.stream.transferOrder.v1.State
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrder
import com.hellofresh.proto.stream.transferOrder.v1.TransferOrderItem
import com.hellofresh.proto.stream.transferOrder.v1.UnitPackaging
import java.time.Instant
import java.util.UUID

@Suppress("LongMethod")
object TransferOrderProtoFixture {
    fun getProtoTransferOrder(
        id: UUID = UUID.randomUUID(),
        transferOrderNumber: String = "2552AET01638",
        sourceDcCode: String = "BX",
        destinationDcCode: String = "VE",
        status: String = "STATE_RESERVED",
        creatorEmail: String = "<EMAIL>",
        reasonText: String = "Replenishment",
        marketCode: String = "DACH",
        withSkus: Boolean = true,
        withUnitPackaging: Boolean = false,
    ): TransferOrder {
        val now = Instant.now().epochSecond

        val builder = TransferOrder.newBuilder()
            .setId(id.toString())
            .setSourceDcCode(sourceDcCode)
            .setDestinationDcCode(destinationDcCode)
            .setCreatorEmail(creatorEmail)
            .setReasonText(reasonText)
            .setStatus(State.valueOf(status))
            .setProductionWeek(
                ProductionWeek.newBuilder()
                    .setYear(2025)
                    .setWeek(52)
                    .build()
            )
            .setCreateTime(Timestamp.newBuilder().setSeconds(now).build())
            .setUpdateTime(Timestamp.newBuilder().setSeconds(now).build())
            .setPickupStartTime(
                Timestamp.newBuilder().setSeconds(Instant.parse("2025-12-22T10:00:00Z").epochSecond).build()
            )
            .setPickupEndTime(
                Timestamp.newBuilder().setSeconds(Instant.parse("2025-12-22T11:00:00Z").epochSecond).build()
            )
            .setDeliveryStartTime(
                Timestamp.newBuilder().setSeconds(Instant.parse("2025-12-23T12:00:00Z").epochSecond).build()
            )
            .setDeliveryEndTime(
                Timestamp.newBuilder().setSeconds(Instant.parse("2025-12-23T14:00:00Z").epochSecond).build()
            )
            .setSentTime(Timestamp.newBuilder().setSeconds(now).build())
            .setTransferOrderNumber(transferOrderNumber)
            .setSourceDcName("CA - Valley")
            .setShippingMethod("Vendor delivered")
            .setMarketCode(marketCode)
            .setVersion(1)

        if (withSkus) {
            if (withUnitPackaging) {
                builder.addItems(getProtoTransferOrderItem(packaging = TransferOrderItem.PackagingCase.UNIT_PACKAGING))
            } else {
                builder.addItems(getProtoTransferOrderItem())
            }
        }

        return builder.build()
    }

    fun getProtoTransferOrderItem(
        id: UUID = UUID.randomUUID(),
        cskuCode: String = "PHF-10-88380-4",
        cskuName: String = "X3B- Garlic, Unpeeled Clove",
        supplierId: UUID = UUID.fromString("64ba864f-d7b1-4216-8c98-90fc32f77335"),
        supplierCode: String = "301330",
        supplierSkuId: UUID = UUID.randomUUID(),
        skuId: UUID = UUID.fromString("77946983-7dc1-41fc-9f85-655131fce4a7"),
        orderSize: Int = 99,
        packaging: TransferOrderItem.PackagingCase = TransferOrderItem.PackagingCase.CASE_PACKAGING, // or PackagingCase.CASE_PACKAGING
    ): TransferOrderItem {
        val item = TransferOrderItem.newBuilder()
            .setId(id.toString())
            .setCskuCode(cskuCode)
            .setCskuName(cskuName)
            .setSupplierId(supplierId.toString())
            .setSupplierCode(supplierCode)
            .setSupplierSkuId(supplierSkuId.toString())
            .setSkuId(skuId.toString())
            .setOrderSize(orderSize)
            .setInventoryType(InventoryInputType.INVENTORY_INPUT_TYPE_MANUAL)
            .setPrice(
                Money.newBuilder()
                    .setCurrencyCode("CAD")
                    .setNanos(990000000)
                    .build(),
            )
            .setTotalPrice(
                Money.newBuilder()
                    .setCurrencyCode("CAD")
                    .setUnits(98)
                    .setNanos(10000000)
                    .build(),
            )
            .setQuantity(
                Decimal.newBuilder()
                    .setValue("99")
                    .build(),
            )

        if (packaging == TransferOrderItem.PackagingCase.CASE_PACKAGING) {
            item.setCasePackaging(
                CasePackaging.newBuilder()
                    .setSize(
                        Decimal.newBuilder()
                            .setValue("1")
                            .build(),
                    )
                    .setUnit(CasePackaging.UOM.UOM_UNIT)
                    .build(),
            )
        } else {
            item.setUnitPackaging(
                UnitPackaging
                    .newBuilder()
                    .build()
            )
        }

        return item.build()
    }
}
