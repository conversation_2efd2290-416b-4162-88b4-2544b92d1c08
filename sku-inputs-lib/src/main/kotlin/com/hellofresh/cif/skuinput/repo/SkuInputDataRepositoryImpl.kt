package com.hellofresh.cif.skuinput.repo

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.sku_inputs_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.sku_inputs_lib.schema.enums.Uom
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SkuSpecificationViewRecord
import com.hellofresh.sku.models.SkuSpecification
import java.util.UUID
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.future.await
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.Result
import org.jooq.impl.DSL.lower

@Suppress("TooManyFunctions")
class SkuInputDataRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
    private val dcConfigService: DcConfigService,
) : SkuInputDataRepository {

    private val fetchSkusByDc = "fetch-skus-by-dcs"
    private val fetchSkusById = "fetch-skus-by-id"

    private val supplierRepository = SupplierRepositoryImpl(metricsDSLContext)

    override suspend fun fetchDcConfig() = dcConfigService.dcConfigurations

    private suspend fun fetchSkus(markets: Set<String>, skuId: UUID?) =
        metricsDSLContext.withTagName(skuId?.let { fetchSkusById } ?: fetchSkusByDc)
            .selectFrom(SKU_SPECIFICATION_VIEW)
            .where(lower(SKU_SPECIFICATION_VIEW.MARKET).`in`(markets.map { lower(it) }))
            .also {
                skuId?.let { v -> it.and(SKU_SPECIFICATION_VIEW.ID.eq(v)) }
            }
            .fetchAsync()
            .thenApply(::toSkuSpecification)
            .await()

    override suspend fun fetchSkus(market: String) = fetchSkus(setOf(market), null)

    override suspend fun fetchSkus(skuIds: Set<UUID>): Map<UUID, SkuSpecification> =
        metricsDSLContext.withTagName(fetchSkusByDc)
            .selectFrom(SKU_SPECIFICATION_VIEW)
            .where(SKU_SPECIFICATION_VIEW.ID.`in`(skuIds))
            .fetchAsync()
            .thenApply(::toSkuSpecification)
            .await()

    override suspend fun fetchSkuLookUp(dcCodes: Set<String>): SkuLookLookUp =
        dcCodes.mapNotNull { dcConfigService.dcConfigurations[it] }
            .groupBy { it.market }
            .flatMap { (market, dcs) ->
                val skus = fetchSkus(market)
                dcs.flatMap { dc ->
                    skus.map { (id, skuSpec) -> DcSkuCodeKey(dc.dcCode, skuSpec.skuCode) to (id to skuSpec) }
                }
            }.toMap()

    private fun toSkuSpecification(records: Result<SkuSpecificationViewRecord>): Map<UUID, SkuSpecification> {
        val skus = records.associate {
            it.id to SkuSpecification(
                parentId = it.parentId,
                category = it.category,
                skuCode = it.code,
                name = it.name,
                coolingType = it.coolingType,
                packaging = it.packaging,
                acceptableCodeLife = it.acceptableCodeLife,
                market = it.market,
                uom = it.uom.toSkuUOM(),
            )
        }
        return skus
    }

    override suspend fun fetchByDcs(dcCodes: Set<String>) = fetchBy(dcCodes)

    override suspend fun fetchSkuInputData(dcCode: String, skuId: UUID): SkuInputData {
        val result = fetchBy(setOf(dcCode), skuId)
        val skuSpecEntries = result.skuSpecification.size
        require(skuSpecEntries == 1) {
            "Expected a single Sku with UUID $skuId but found $skuSpecEntries"
        }
        val dcConfigEntries = result.dcConfig.size
        require(dcConfigEntries == 1) {
            "Expected a single DC config for $dcCode but found $dcConfigEntries"
        }
        return result
    }

    /**
     * Fetches all SKU data for a list of DC codes.
     * If a skuId is supplied then it fetches the sku data for this sku only.
     */
    private suspend fun fetchBy(dcCodes: Set<String>, singleSkuId: UUID? = null) =
        withContext(Dispatchers.IO) {
            val dcConfigs = fetchDcConfig().filter { dcCodes.contains(it.key) }
            val markets = dcConfigs.values.map { it.market }.toSet()
            val skus = async { fetchSkus(markets, singleSkuId) }.await()
            val supplierSkuDetails = async {
                supplierRepository.fetchSupplierSkuDetails(
                    markets,
                    singleSkuId,
                )
            }.await()
            SkuInputData(
                dcConfig = dcConfigs,
                skuSpecification = skus,
                supplierSkuDetails = supplierSkuDetails,
            )
        }

    companion object : Logging {
        internal fun Uom.toSkuUOM() = when (this) {
            Uom.UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
            Uom.UOM_UNIT -> SkuUOM.UOM_UNIT
            Uom.UOM_KG -> SkuUOM.UOM_KG
            Uom.UOM_LBS -> SkuUOM.UOM_LBS
            Uom.UOM_GAL -> SkuUOM.UOM_GAL
            Uom.UOM_LITRE -> SkuUOM.UOM_LITRE
            Uom.UOM_OZ -> SkuUOM.UOM_OZ
            Uom.UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
        }
    }
}
