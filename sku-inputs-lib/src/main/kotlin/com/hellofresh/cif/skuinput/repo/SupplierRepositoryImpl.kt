package com.hellofresh.cif.skuinput.repo

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.sku_inputs_lib.schema.Tables.SUPPLIER_DETAILS_VIEW
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SupplierDetailsViewRecord
import com.hellofresh.sku.models.LeadTime
import com.hellofresh.sku.models.SupplierSkuDetail
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.Result
import org.jooq.impl.DSL

class SupplierRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext
) : SupplierRepository {

    private val fetchSupplierSkusBySkuId = "fetch-supplier-sku-by-skuId"
    private val fetchSupplierSkusByMarkets = "fetch-supplier-skus-by-markets"
    override suspend fun fetchSupplierSkuDetails(markets: Set<String>, skuId: UUID?) =
        metricsDSLContext.withTagName(fetchSupplierSkusByMarkets)
            .selectFrom(SUPPLIER_DETAILS_VIEW)
            .where(DSL.lower(SUPPLIER_DETAILS_VIEW.MARKET).`in`(markets.map { DSL.lower(it) }))
            .also {
                skuId?.let { v -> it.and(SUPPLIER_DETAILS_VIEW.SKU_ID.eq(v)) }
            }
            .fetchAsync()
            .thenApply(::toSupplierSkuDetails)
            .await()
    override suspend fun fetchSuppliersBySkuId(
        skuId: UUID,
    ): Map<UUID, List<SupplierSkuDetail>> =
        metricsDSLContext.withTagName(fetchSupplierSkusBySkuId)
            .selectFrom(SUPPLIER_DETAILS_VIEW)
            .where(SUPPLIER_DETAILS_VIEW.SKU_ID.equal(skuId))
            .fetchAsync()
            .thenApply(::toSupplierSkuDetails)
            .await()

    private fun toSupplierSkuDetails(records: Result<SupplierDetailsViewRecord>): Map<UUID, List<SupplierSkuDetail>> =
        records
            .groupBy { it.skuId }
            .mapValues { (_, records) ->
                records.groupBy { it.supplierId }.map { (k, v) ->
                    SupplierSkuDetail(
                        k,
                        v.first { it.supplierName != null }.supplierName,
                        v.first().mlor,
                        v.filter {
                            it.startDate != null
                        }.map {
                            LeadTime(it.leadTime, it.startDate, it.endDate)
                        }.distinct()
                    )
                }
            }
}
