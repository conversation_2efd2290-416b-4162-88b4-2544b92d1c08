package com.hellofresh.cif.skuinput.service

import com.github.benmanes.caffeine.cache.Cache
import com.github.benmanes.caffeine.cache.Caffeine
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import java.util.Locale
import java.util.UUID
import java.util.concurrent.TimeUnit
import org.apache.logging.log4j.kotlin.Logging

@Suppress("MagicNumber")
class SkuInputService(private val skuInputDataRepository: SkuInputDataRepository) {
    private val skuCodeMarketCache: Cache<Pair<String, String>, ShortSkuSpec> = Caffeine.newBuilder()
        .expireAfterWrite(1, TimeUnit.HOURS)
        .maximumSize(100_000)
        .build()

    suspend fun getSkuId(dcCode: String, skuCode: String): ShortSkuSpec? {
        val market =
            skuInputDataRepository.fetchDcConfig()[dcCode]?.market?.lowercase(Locale.getDefault()) ?: run {
                logger.error("Market not found in the dc config for dcCode = $dcCode.")
                return null
            }
        val key = market to skuCode
        return (skuCodeMarketCache.getIfPresent(key) ?: loadAndCacheSkuId(market, skuCode, key))
            ?: run {
                logger.error("SkuId not found for the market: $market and skuCode: $skuCode")
                null
            }
    }

    private suspend fun loadAndCacheSkuId(
        market: String,
        skuCode: String,
        key: Pair<String, String>
    ): ShortSkuSpec? {
        val skuSpecifications = skuInputDataRepository.fetchSkus(market).also {
            if (it.isEmpty()) logger.error("SKU's not found for the market: $market and skuCode: $skuCode")
        }

        skuSpecifications.forEach { (id, spec) ->
            skuCodeMarketCache.put(market to spec.skuCode, ShortSkuSpec(id, spec.uom))
        }
        return skuCodeMarketCache.getIfPresent(key) ?: run {
            logger.error(
                "SkuId not found for the market: $market and skuCode: $skuCode",
            )
            return null
        }
    }

    companion object : Logging
}

data class ShortSkuSpec(val id: UUID, val uom: SkuUOM)
