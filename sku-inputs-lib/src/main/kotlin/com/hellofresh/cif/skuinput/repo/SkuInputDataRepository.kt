package com.hellofresh.cif.skuinput.repo

import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.SupplierSkuDetail
import java.util.UUID

const val DAYS_IN_PAST = 7L
const val NUMBER_OF_DAYS_FOR_CALCULATION = 112L

typealias SkuLookLookUp = Map<DcSkuCodeKey, Pair<UUID, SkuSpecification>>

interface SkuInputDataRepository {
    suspend fun fetchDcConfig(): Map<DcCode, DistributionCenterConfiguration>
    suspend fun fetchByDcs(dcCodes: Set<String>): SkuInputData
    suspend fun fetchSkuInputData(dcCode: String, skuId: UUID): SkuInputData
    suspend fun fetchSkus(skuIds: Set<UUID>): Map<UUID, SkuSpecification>
    suspend fun fetchSkus(market: String): Map<UUID, SkuSpecification>
    suspend fun fetchSkuLookUp(dcCodes: Set<String>): SkuLookLookUp
}

data class SkuInputData(
    val dcConfig: Map<DcCode, DistributionCenterConfiguration>,
    val skuSpecification: Map<UUID, SkuSpecification>,
    val supplierSkuDetails: Map<UUID, List<SupplierSkuDetail>> = emptyMap(),
)

data class DcSkuCodeKey(val dcCode: String, val skuCode: String)
