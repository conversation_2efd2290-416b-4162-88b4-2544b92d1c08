plugins {
    id("com.hellofresh.cif.application-conventions")
    hellofresh.`test-fixtures`
    alias(libs.plugins.jooq)
}

group = "$group.${project.name}"

dependencies {
    jooqGenerator(libs.postgresql.driver)
    api(libs.hellofresh.service)
    implementation(projects.distributionCenterModels)
    implementation(projects.inventory.inventoryLib)
    implementation(projects.lib.featureflags)
    implementation(projects.lib.db)
    implementation(libs.prometheus.pushgateway)
    implementation(libs.aws.java.sdk.s3)
    implementation(libs.apache.commonscsv)
    implementation(libs.coroutines.jdk8)
    implementation(libs.resilience4j.kotlin)
    implementation(libs.resilience4j.retry)
    implementation(libs.cronutils)

    testImplementation(libs.mockk)
    testImplementation(projects.libTests)
}

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "dc_config|inventory_live_snapshot|inventory_snapshot|sku_specification|sku_specification_yf"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}
