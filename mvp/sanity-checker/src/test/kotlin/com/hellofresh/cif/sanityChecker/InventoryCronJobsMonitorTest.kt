package com.hellofresh.cif.sanityChecker

import io.mockk.coEvery
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import java.sql.Timestamp
import java.time.Instant
import java.time.temporal.ChronoUnit
import org.junit.jupiter.api.Test

class InventoryCronJobsMonitorTest {

    private val metrics = mockk<CronJobMetrics>()
    private val inventoryCronJobsDB = mockk<InventoryCronJobsDB>()

    @Test
    fun `should not publish a pg_cron metric if there are no failed inventory cron jobs`() {
        mockMethodCalls(inventoryCronJobsDB, metrics)

        val jobStatus = JobStatus("succeeded", Timestamp.from(Instant.now()))
        coEvery {
            inventoryCronJobsDB.fetchLastJobStatus(any())
        } returns jobStatus

        checkLastJobStatus(metrics, "actual_consumption_schedule", jobStatus)

        verify(exactly = 1) {
            metrics.setFailedJobCount(
                "pg_cron_job",
                0,
                "last_pg_cron_job_execution",
                "actual_consumption_schedule"
            )
        }
    }

    @Test
    fun `should not publish a pg_cron metric if the cron jobs are running as expected`() {
        mockMethodCalls(inventoryCronJobsDB, metrics)
        val jobStatus = JobStatus("succeeded", Timestamp.from(Instant.now()))
        coEvery {
            inventoryCronJobsDB.fetchLastJobStatus(any())
        } returns jobStatus

        checkLastRanJobAndNotify(
            metrics,
            JobDetail(
                "actual_consumption_schedule",
                "0 */2 * * *",
            ),
            jobStatus,
        )

        verify(exactly = 1) {
            metrics.setFailedJobCount(
                "pg_cron_job",
                0,
                "last_pg_cron_scheduled_job_execution_status",
                "actual_consumption_schedule"
            )
        }
    }

    @Test
    fun `should publish a pg_cron metric if there are failed inventory cron jobs`() {
        mockMethodCalls(inventoryCronJobsDB, metrics)
        val jobStatus = JobStatus("failed", Timestamp.from(Instant.now()))
        coEvery {
            inventoryCronJobsDB.fetchLastJobStatus(any())
        } returns jobStatus

        checkLastJobStatus(metrics, "actual_consumption_schedule", jobStatus)

        verify(exactly = 1) {
            metrics.setFailedJobCount(
                "pg_cron_job",
                1,
                "last_pg_cron_job_execution",
                "actual_consumption_schedule",
            )
        }
    }

    @Test
    fun `should publish a pg_cron metric if the job status start_time is null`() {
        mockMethodCalls(inventoryCronJobsDB, metrics)
        val jobStatus = JobStatus("failed", null)
        coEvery {
            inventoryCronJobsDB.fetchLastJobStatus(any())
        } returns jobStatus

        checkLastJobStatus(metrics, "actual_consumption_schedule", jobStatus)

        verify(exactly = 1) {
            metrics.setFailedJobCount(
                "pg_cron_job",
                1,
                "last_pg_cron_job_execution",
                "actual_consumption_schedule",
            )
        }
    }

    @Test
    fun `should publish a pg_cron metric if the cron jobs are not running as expected`() {
        mockMethodCalls(inventoryCronJobsDB, metrics)
        val jobStatus = JobStatus("failed", Timestamp.from(Instant.now().minus(3, ChronoUnit.HOURS)))
        coEvery {
            inventoryCronJobsDB.fetchLastJobStatus(any())
        } returns jobStatus

        checkLastRanJobAndNotify(
            metrics,
            JobDetail(
                "actual_consumption_schedule",
                "0 */2 * * *",
            ),
            jobStatus,
        )

        verify(exactly = 1) {
            metrics.setFailedJobCount(
                "pg_cron_job",
                1,
                "last_pg_cron_scheduled_job_execution_status",
                "actual_consumption_schedule",
            )
        }
    }

    @Test
    fun `should not publish a pg_cron metric if the job status start_time is null`() {
        mockMethodCalls(inventoryCronJobsDB, metrics)
        val jobStatus = JobStatus("failed", null)
        coEvery {
            inventoryCronJobsDB.fetchLastJobStatus(any())
        } returns jobStatus

        checkLastRanJobAndNotify(
            metrics,
            JobDetail(
                "actual_consumption_schedule",
                "0 */2 * * *",
            ),
            jobStatus,
        )

        verify(exactly = 0) {
            metrics.setFailedJobCount(
                "pg_cron_job",
                1,
                "last_pg_cron_scheduled_job_execution_status",
                "actual_consumption_schedule",
            )
        }
    }

    private fun mockMethodCalls(
        inventoryCronJobsDB: InventoryCronJobsDB,
        metrics: CronJobMetrics
    ) {
        coEvery {
            inventoryCronJobsDB.fetchJobData()
        } returns mapOf(
            1 to JobDetail("actual_consumption_schedule", "0 */2 * * *"),
            2 to JobDetail("delete_log_schedule", "0 0 * * *"),
        )

        justRun { metrics.setFailedJobCount(any(), any(), any(), any()) }
        justRun { metrics.push(any()) }
    }
}
