#!/usr/bin/env bash

set -o errexit -o posix -o nounset -o pipefail -o errtrace

env="$1"
topic="$2"

envFile="$(pwd)/.env-$env"
echo "$(pwd)/"
echo "values from $envFile will be used"

if [ -f "$envFile" ]; then
    set -o allexport
    # shellcheck disable=SC1090
    source "$envFile"
    set +o allexport
fi
broker="$BROKER_ADDRESS"

brokerSecure=""
if [[ $broker == *"aiven"* ]]; then
    if [[ $broker == *"live"* ]]; then
        env="live"
    else
        env="staging"
    fi
    brokerSecure=" -X security.protocol=SASL_SSL \
    -X ssl.ca.location=/usr/src/ca.$env.pem \
    -X sasl.mechanisms=SCRAM-SHA-256 -X sasl.username=${KAFKA_USERNAME_READONLY} \
    -X sasl.password=${KAFKA_PASSWORD_READONLY}"
fi

echo >&2 "Broker Address=$broker, Topic=${topic}"
network="cif-network"

offset=0

container_name="kafkacat-proto"
set +o errexit
out=$(docker container inspect -f '{{.State.Running}}' $container_name 2>&1)
set -o errexit
if [ "$out" == "true" ]; then
    echo "Stopping $container_name container"
    docker stop $container_name
fi

# shellcheck disable=SC2086
docker run -it --name $container_name --network=$network --rm -v "$(pwd)"/docker/dev/kafkacat:/usr/src edenhill/kafkacat:1.6.0 -C -b $broker $brokerSecure -t $topic -o $offset -c50 -D "" | protoc --decode_raw offset=$((offset + 50))
