#!/usr/bin/env bash

set -o errexit -o posix -o nounset -o pipefail -o errtrace

if [ -z "$1" ]; then
    echo "env is not set"
    exit 1
fi

if [ -z "$2" ]; then
    echo "topic is not set"
    exit 1
fi

if [ -z "$3" ]; then
    echo "filePath is not set"
    exit 1
fi

env="$1"
topic="$2"
filePath="/usr/src/$3"
formatter="-K;"
partition=""

if [ -n "${4:-}" ]; then
    partition="-p $4"
fi

echo "$formatter"

envFile="$(pwd)/.env-$env"
echo "values from $envFile will be used"

if [ -f "$envFile" ]; then
    set -o allexport
    # shellcheck disable=SC1090
    source "$envFile"
    set +o allexport
fi
broker="$BROKER_ADDRESS"

brokerSecure=""
if [[ $broker == *"aiven"* ]]; then
    if [[ $broker == *"live"* ]]; then
        env="live"
    else
        env="staging"
    fi
    brokerSecure=" -X security.protocol=SASL_SSL \
    -X ssl.ca.location=/usr/src/kafkacat/ca.$env.pem \
    -X sasl.mechanisms=SCRAM-SHA-256 -X sasl.username=${KAFKA_USERNAME} \
    -X sasl.password=${KAFKA_PASSWORD}"
fi

echo >&2 "Broker Address=$broker, Schema Registry=${SCHEMA_ADDRESS}, Topic=${topic}, Partition=${4-any}"
network="cif-network"

container_name="kafkacat-producer"
set +o errexit
out=$(docker container inspect -f '{{.State.Running}}' $container_name 2>&1)
set -o errexit
if [ "$out" == "true" ]; then
    echo "Stopping $container_name container"
    docker stop $container_name
fi

# shellcheck disable=SC2086
docker run -t --name $container_name --network=$network --rm -v "$(pwd)"/docker/dev:/usr/src edenhill/kafkacat:1.6.0 -b $broker -t $topic $partition $brokerSecure -P $formatter -l $filePath
