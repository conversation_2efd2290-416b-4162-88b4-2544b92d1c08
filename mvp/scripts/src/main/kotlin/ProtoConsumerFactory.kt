@Suppress("UseIfInsteadOfWhen")
class ProtoConsumerFactory private constructor() {
    companion object {
        fun getConsumer(arguments: Arguments): ProtoConsumer<*, *>? = when (arguments[Arguments.TOPIC_ARG]) {
            "public.demand.sku-demand-forecast.v1" -> ProtoConsumer(
                arguments,
                SkuDemandForecastKeySerde().deserializer(),
                SkuDemandForecastValSerde().deserializer(),
            )
            else -> null
        }
    }
}
