import Arguments.Companion.ARG_IDENTIFIER
import Arguments.Companion.SPLITTER
import org.apache.logging.log4j.kotlin.Logging

class Arguments(private val args: Array<String>) {
    private val parsedArguments = mutableMapOf<String, String>().apply {
        put(BOOTSTRAP_SERVERS_ARG, "")
        put(TOPIC_ARG, "")
        put(FILTER_OR_ARG, "")
        put(FILTER_AND_ARG, "")
    }

    init {
        parse()
    }

    operator fun get(arg: String) = parsedArguments[arg]

    private fun parse() {
        args.forEach { arg ->
            require(arg.isArgIdentifier() && arg.hasSplitter()) {
                "Command malformed. Expected `command --arg value`; Found  `command value1 value2`"
            }

            val k = arg.split(SPLITTER)[0]
            val v = arg.split(SPLITTER)[1]

            require(parsedArguments.keys.contains(k)) {
                " Argument $k does not exist, please specify correct argument"
            }

            if (!v.isNullOrEmpty()) parsedArguments[k] = v
        }
    }

    companion object : Logging {
        const val ARG_IDENTIFIER: String = "--"
        const val BOOTSTRAP_SERVERS_ARG: String = "--bootstrap-servers"
        const val TOPIC_ARG: String = "--topic"
        const val FILTER_OR_ARG: String = "--filter-or"
        const val FILTER_AND_ARG: String = "--filter-and"
        const val SPLITTER: String = "="
    }
}

fun String.isArgIdentifier() = this.startsWith(ARG_IDENTIFIER)
fun String.hasSplitter() = this.contains(SPLITTER)
