import com.hellofresh.proto.stream.demand.skuDemandForecast.v1.SkuDemandForecastKey
import com.hellofresh.proto.stream.demand.skuDemandForecast.v1.SkuDemandForecastVal
import org.apache.kafka.common.serialization.Deserializer
import org.apache.kafka.common.serialization.Serde
import org.apache.kafka.common.serialization.Serializer

class SkuDemandForecastKeySerde : Serde<SkuDemandForecastKey> {
    override fun serializer() = Serializer<SkuDemandForecastKey> { _, value ->
        value.toByteArray()
    }

    override fun deserializer() = Deserializer { _, data ->
        check(data != null) { "Key can not be null" }
        SkuDemandForecastKey.parseFrom(data)
    }
}

class SkuDemandForecastValSerde : Serde<SkuDemandForecastVal> {
    override fun serializer() = Serializer<SkuDemandForecastVal> { _, value ->
        value?.toByteArray()
    }

    override fun deserializer() = Deserializer { _, data ->
        data?.let { SkuDemandForecastVal.parseFrom(data) }
    }
}
