import java.io.File

val fileName = args[0]
val delimiter = args.getOrElse(1) { ";" }
val outName = "${fileName}_compacted"

println("deduping $fileName at $outName")

val dedup = mutableMapOf<String, String>()
File(fileName).inputStream().use {
    it.bufferedReader().use { reader ->
        reader.forEachLine { line ->
            val (key, value) = line.split(delimiter).let { arr ->
                if (arr.size < 2) arr[0] to null else arr[0] to arr[1]
            }
            dedup[key] = value ?: ""
        }
    }
}
File(outName).printWriter().use {
    dedup.forEach { (k, v) -> it.println("$k$delimiter$v") }
}
