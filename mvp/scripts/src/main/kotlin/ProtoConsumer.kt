import Arguments.Companion.BOOTSTRAP_SERVERS_ARG
import Arguments.Companion.TOPIC_ARG
import com.google.protobuf.Message
import java.time.Duration
import java.time.temporal.ChronoUnit
import java.util.Properties
import kotlin.system.exitProcess
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.common.serialization.Deserializer

class ProtoConsumer<K : Message, V : Message?>(
    private val arguments: Arguments,
    keyDeserializer: Deserializer<K>,
    valueDeserializer: Deserializer<V>
) {
    private var consumer: KafkaConsumer<K, V>

    init {
        val properties = Properties().apply {
            this[ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG] = arguments[BOOTSTRAP_SERVERS_ARG]
            this[ConsumerConfig.GROUP_ID_CONFIG] = "proto-consumer"
            this[ConsumerConfig.AUTO_OFFSET_RESET_CONFIG] = "earliest"
            this[ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG] = "false"
            this[ConsumerConfig.MAX_POLL_RECORDS_CONFIG] = "1000"
        }

        consumer = KafkaConsumer(
            properties,
            keyDeserializer,
            valueDeserializer,
        ).apply {
            subscribe(listOf(arguments[TOPIC_ARG]))
        }
    }

    @Suppress("ComplexMethod", "NestedBlockDepth", "ComplexCondition", "StringLiteralDuplication")
    fun consume() {
        var emptyPolls = 0
        var hasRecords = true
        try {
            do {
                consumer.poll(POLL_INTERVAL).apply {
                    if (emptyPolls >= MAX_EMPTY_POLLS) {
                        hasRecords = false
                    }
                    if (count() == 0) {
                        emptyPolls++
                    } else {
                        emptyPolls = 0
                    }
                }
            } while (hasRecords)
        } finally {
            consumer.close()
            exitProcess(0)
        }
    }

    companion object {
        private const val MAX_EMPTY_POLLS = 10
        private val POLL_INTERVAL = Duration.of(100, ChronoUnit.MILLIS)
    }
}
