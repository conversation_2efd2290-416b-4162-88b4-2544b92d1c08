@file:Suppress("DEPRECATION")

package com.hellofresh.cif.kafkaMirrorPlugin

import kafka.consumer.BaseConsumerRecord
import kafka.tools.MirrorMaker.MirrorMakerMessageHandler
import org.apache.kafka.clients.producer.ProducerRecord

private fun String?.contains(str: String): Boolean = this?.contains(str, false) ?: false

class MessageFilter(filterRaw: String) : MirrorMakerMessageHandler {
    private val filter = filterRaw.split(' ').toSet()

    override fun handle(record: BaseConsumerRecord): List<ProducerRecord<ByteArray, ByteArray>> {
        val k = record.key()?.decodeToString()
        val v = record.value()?.decodeToString()

        if (filter.none { k.contains(it) || v.contains(it) }) {
            return emptyList()
        }

        return listOf(ProducerRecord(record.topic(), record.partition(), record.key(), record.value()))
    }
}
