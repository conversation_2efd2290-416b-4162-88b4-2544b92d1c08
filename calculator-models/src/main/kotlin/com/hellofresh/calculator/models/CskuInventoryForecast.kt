package com.hellofresh.calculator.models

import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonFormat.Shape.STRING
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonInclude.Include
import com.fasterxml.jackson.annotation.JsonPropertyOrder
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.inventory.models.LocationType
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

@JsonNaming(SnakeCaseStrategy::class)
@JsonPropertyOrder(alphabetic = true)
data class CskuInventoryForecastKey(
    val cskuId: UUID,
    val dcCode: String,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "uuuu-MM-dd")
    val date: LocalDate
) {
    companion object
}

@Suppress("LongParameterList")
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
@JsonPropertyOrder(alphabetic = true)
data class CskuInventoryForecastVal(
    val expired: BigDecimal,
    val openingStock: BigDecimal,
    val present: BigDecimal, // aka stock in POC
    val actualInbound: BigDecimal,
    val actualInboundPurchaseOrders: Set<String>?,
    val expectedInbound: BigDecimal,
    val expectedInboundPurchaseOrders: Set<String>?,
    val demanded: BigDecimal,
    val closingStock: BigDecimal,
    val dailyNeeds: BigDecimal,
    val productionWeekStartStock: BigDecimal,
    val productionWeek: String,
    val actualConsumption: BigDecimal,
    val safetyStock: BigDecimal?,
    val strategy: String,
    val safetyStockNeeds: BigDecimal?,
    val stagingStock: BigDecimal = BigDecimal.ZERO,
    val storageStock: BigDecimal = BigDecimal.ZERO,
    val stockUpdate: BigDecimal? = null,
    val purchaseOrderDueInForSuppliers: List<SupplierSkuPoDueInVal>? = null,
    val maxPurchaseOrderDueIn: Int? = null,
    val netNeeds: BigDecimal,
    val unusableInventory: List<ForecastInventory>? = null,
    val uom: SkuUOM = UOM_UNIT,
    val expectedInboundTransferOrders: Set<String>? = null,
    val expectedInboundTransferOrdersQuantity: BigDecimal? = BigDecimal.ZERO,
    val expectedOutboundTransferOrders: Set<String>? = null,
    val expectedOutboundTransferOrdersQuantity: BigDecimal? = BigDecimal.ZERO,
    val actualInboundTransferOrders: Set<String>? = null,
    val actualInboundTransferOrdersQuantity: BigDecimal? = BigDecimal.ZERO,
) {
    companion object
}

@JsonNaming(SnakeCaseStrategy::class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonPropertyOrder(alphabetic = true)
data class SupplierSkuPoDueInVal(
    val supplierId: UUID,
    val poDueIn: Int,
) {
    companion object
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
@JsonNaming(SnakeCaseStrategy::class)
data class ForecastInventory(
    val qty: BigDecimal,
    @JsonFormat(shape = STRING, pattern = "uuuu-MM-dd")
    val expiryDate: LocalDate?,
    // TODO MAKE LOCATION TYPE MANDATORY WHEN POSSIBLE
    val locationType: LocationType? = null
) {
    companion object
}
