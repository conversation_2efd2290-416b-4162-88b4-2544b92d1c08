package com.hellofresh.calculator.models

import com.hellofresh.topic.Topic

val inventoryCalculationsTopic = Topic<CskuInventoryForecastKey, CskuInventoryForecastVal>(
    prefix = "csku-inventory-forecast.intermediate.calculations",
    version = 1,
)
val preProdCalculationsTopic = Topic<CskuInventoryForecastKey, CskuInventoryForecastVal>(
    prefix = "csku-inventory-forecast.intermediate.pre-production.calculations",
    version = 1,
)

val liveInventoryCalculationsTopic = Topic<CskuInventoryForecastKey, CskuInventoryForecastVal>(
    prefix = "csku-inventory-forecast.intermediate.forecast.live-inventory-calculations",
    version = 1,
)
val liveInventoryPreProdCalculationsTopic = Topic<CskuInventoryForecastKey, CskuInventoryForecastVal>(
    prefix = "csku-inventory-forecast.intermediate.forecast.live-inventory-pre-production-calculations",
    version = 1,
)
