package com.hellofresh.cif.api.ingredientdepletion

class IngredientDepletionService(
    private val ingredientDepletionRepository: IngredientDepletionRepository,
) {
    suspend fun getIngredientDepletionData(
        brand: String,
        dcCodes: List<String>,
        week: String
    ): IngredientDepletionResponseDto =
        ingredientDepletionRepository.getIngredientDepletionData(dcCodes, week, brand)
}
