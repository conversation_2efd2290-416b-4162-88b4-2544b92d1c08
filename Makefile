.PHONY: help build clean check-docker-deps check-env core-up clean-docker-volumes \
core-down docker-build kafka-mirror local-apps-up local-apps-down \
local-up core-down print-topics run-e2e-test \
run-local-apps-and-e2e-test set-env \
set-docker-env show-lag wait-broker-container wait-container wait-core-containers \
e2e-apps-up \

.DEFAULT_GOAL := build
SHELL = bash

TOPICS_VERSIONS=$(shell find **/src/* */*/src/* -type f -name "topic-versions.properties" | xargs grep -hv "^\#" | sed "s/=/.v/"; echo $$s)
APP_TOPICS=$(shell echo $(TOPICS_VERSIONS) | tr " " "\n" | grep csku | tr "\n" "|" ; echo $$s)
APP_TOPICS_BEFORE_CALC=$(shell echo $(TOPICS_VERSIONS) | tr " " "\n" | grep csku | grep -v result | tr "\n" "|"; echo $$s)
SDF_TOPICS=$(shell echo $(TOPICS_VERSIONS) | tr " " "\n" | grep sku-demand-forecast; echo $$s)
DISTRIBUTION_CENTER=$(shell echo $(TOPICS_VERSIONS) | tr " " "\n" | grep csku-inventory-forecast.intermediate.distribution-center; echo $$s)
RAWEVENTS_TOPICS=public.planning.culinarysku.v1
SKU_DEMAND_FORECASTV1=public.demand.sku-demand-forecast.v1
PURCHASE_ORDER=public.supply.procurement.purchase-order.v1
CALCULATOR_TOPICS=csku-inventory-forecast.intermediate.calculations.v1|csku-inventory-forecast.intermediate.pre-production.calculations.v1
SKU_INVENTORY_DEMAND_FORECAST=public.ordering.sku-inventory-demand-forecast.v1
SUPPLY_QUANTITY_RECOMMENDATION=public.ordering.supply-quantity-recommendation.v1
EXTERNAL_TOPICS=$(RAWEVENTS_TOPICS)|$(SKU_DEMAND_FORECAST)|$(SDF_TOPICS)|$(DISTRIBUTION_CENTER)|$(CALCULATOR_TOPICS)|$(SKU_DEMAND_FORECASTV1)|${PURCHASE_ORDER}|${SKU_INVENTORY_DEMAND_FORECAST}|${SUPPLY_QUANTITY_RECOMMENDATION}
ALL_TOPICS=$(EXTERNAL_TOPICS)|$(APP_TOPICS)
DISTRIBUTION_CENTER_TOPIC_VERSION=$(shell echo $(TOPICS_VERSIONS) | tr " " "\n" | grep csku-inventory-forecast.intermediate.distribution-center | sed 's/.*v\(.*\)/\1/')
DOCKER_COMPOSE=docker compose --env-file .env-docker

ifeq ($(env),)
env = live # by default
endif

############################################################
#                   Target Dependencies                    #
#           Used as dependency by other targets            #
############################################################

print-topics:
	@echo "TOPICS_FROM_TOPIC-VERSIONS_FILES: $(TOPICS_VERSIONS)"
	@echo
	@echo "APP_TOPICS: $(APP_TOPICS)"
	@echo
	@echo "APP_TOPICS_BEFORE_CALC: $(APP_TOPICS_BEFORE_CALC)"
	@echo
	@echo "RAWEVENTS_TOPICS: $(RAWEVENTS_TOPICS)"
	@echo
	@echo "EXTERNAL_TOPICS: $(EXTERNAL_TOPICS)"
	@echo
	@echo "ALL_TOPICS: $(ALL_TOPICS)"
	@echo
	@echo "INVENTORY_TOPIC_VERSION: $(INVENTORY_TOPIC_VERSION)"
	@echo
	@echo "DISTRIBUTION_CENTER_TOPIC_VERSION: $(DISTRIBUTION_CENTER_TOPIC_VERSION)"

check-env:
	@./docker/dev/check-env.sh $(env)

set-docker-env:
	@echo ALL_TOPICS="$(ALL_TOPICS)" > .env-docker
	@echo INVENTORY_TOPIC_VERSION=$(INVENTORY_TOPIC_VERSION) >> .env-docker
	@echo DISTRIBUTION_CENTER_TOPIC_VERSION=$(DISTRIBUTION_CENTER_TOPIC_VERSION) >> .env-docker
	@echo HF_AIVEN_PASSWORD=${HF_AIVEN_PASSWORD} >> .env-docker
	@echo DOCKER_TAG=$(DOCKER_TAG) >> .env-docker

set-env:
	@make check-env
	@./docker/dev/set-env.sh $(env)

check-docker-deps:
	@echo "docker: $$(which docker || echo 'not found')"
	@echo "docker compose: $$(which docker compose || echo 'not found')"

wait-broker-container:
	@./docker/dev/wait-container.sh -c broker -t 240

wait-container:
	@./docker/dev/wait-container.sh -c $(container)

wait-core-containers:
	@make wait-container container=inventory-postgres
	@make wait-broker-container

############################################################
#                        Gradle Tasks                      #
#       Provides convenience when running Gradle tasks     #
############################################################

clean:
	@./gradlew clean

build:
	@./gradlew clean build

docker-build:
	@HF_TIER=local \
        ./gradlew build \
        -Phf.docker.registry= \
        -Phf.docker.repository= \
        -Phf.docker.tag=local \
        -Phf.docker.default=local \
        jibDockerBuild -x test -x testFunctional -x detekt -x testIntegration

build-mirror-plugins:
	@./gradlew :mvp:kafka-mirror-plugin:shadowJar

build-scripts:
	@./gradlew mvp:scripts:shadowJar

run-e2e-test:
	@./gradlew :mvp:end-to-end-test:exec

############################################################
#                   Environment Creation                   #
# Facilitates the creation and destruction of environments #
############################################################


# core-up starts only the CIF core containers for a local environment.
# It's also possible to start a specific core component by using the
# container flag: make core-up container=<container_name>
core-up: check-docker-deps set-docker-env
	$(DOCKER_COMPOSE) up -d zookeeper broker kafdrop inventory-postgres db-migration prometheus $(container)
	@make wait-core-containers

# core-down stops all CIF core containers that are currently running.
core-down: check-docker-deps
	@$(DOCKER_COMPOSE) down --volumes

# local-up creates a complete CIF local environment from scratch with all necessary containers.
local-up: check-docker-deps set-docker-env docker-build core-up
	@$(DOCKER_COMPOSE) up -d --no-recreate

# local-apps-up brings CIF apps up locally from scratch with all necessary containers.
# Usage:
# make local-apps-up app="app1 app2" env=live
local-apps-up: check-docker-deps docker-build core-up
	@$(DOCKER_COMPOSE) up -d $(app)

# local-apps-up stops CIF apps set by $app
# Usage:
# make local-apps-down app="app1 app2" env=live
local-apps-down: check-docker-deps docker-build core-up
	@$(DOCKER_COMPOSE) rm -s $(app)

# local-down stops all CIF local environment containers that are currently running.
local-down: core-down

# kafka-mirror mirrors specific CIF related topics from a remote Kafka cluster to
# a local Kafka cluster.
# Usage:
# make kafka-mirror topic="<topic_name>" filter="<a list of strings separated by space>" env=live
kafka-mirror: build-mirror-plugins check-docker-deps set-env core-up
	@echo "Running Kafka Mirror. Press Ctrl+C to stop mirroring."
	@docker exec --env-file=.env-$(env) broker sh -c '/usr/src/kafka-mirror.sh "$(topic)" "$(filter)"'

# kafka-mirror-all mirrors all CIF related topics from a remote Kafka cluster to
# a local Kafka cluster.
# make kafka-mirror-all filter="<a list of stings separated by space>" env=live
kafka-mirror-all: build-mirror-plugins check-docker-deps set-env core-up
	@echo "Running Kafka Mirror. Press Ctrl+C to stop mirroring."
	@docker exec --env-file=.env-$(env) broker sh -c '/usr/src/kafka-mirror.sh "$(ALL_TOPICS)" "$(filter)"'

# kafka-mirror-external mirrors external CIF related topics from a remote Kafka cluster to
# a local Kafka cluster.
# make kafka-mirror-external filter="<a list of stings separated by space>" env=live
kafka-mirror-external: build-mirror-plugins check-docker-deps set-env core-up
	@echo "Running Kafka Mirror. Press Ctrl+C to stop mirroring."
	@docker exec --env-file=.env-$(env) broker sh -c '/usr/src/kafka-mirror.sh "$(EXTERNAL_TOPICS)" "$(filter)"'

############################################################
#                      Utilities                           #
############################################################

# sink-up starts only Benthos to consume all configured topics from the local Kafka cluster.
sink-up: check-docker-deps
	$(DOCKER_COMPOSE) up -d zookeeper broker kafdrop inventory-postgres db-migration
	@make wait-broker-container
	@make wait-container container=inventory-postgres
	@$(DOCKER_COMPOSE) up -d benthos

# sink-up stops Benthos from consuming topics from the local Kafka cluster
sink-down: core-down

clean-docker-volumes:
	@docker volume prune

# e.g. make kafkacat topic=public.planning.culinarysku.v1 env=live formatter='part-%p;%T;%o;%k\n'(optional)
kafkacat: set-env core-up
	@./mvp/scripts/src/main/shell/kafkacat $(env) $(topic) "$(formatter)"

# this is same as kafkacat , but should be used for topics having protobuf based schema
# e.g. make kafkacat-proto topic=public.planning.culinarysku.v1 env=live formatter='part-%p;%T;%o;%k\n'(optional)
kafkacat-proto: set-env core-up
	@./mvp/scripts/src/main/shell/kafkacat-proto $(env) $(topic)

# e.g. make kafkacat topic=<topic_name> env=(local|staging|live) filePath=<file_path> partition=<partition_id>
# filePath is the path of a file relative to the mounted directory /usr/src/, e.g:
# /usr/src/${filePath}
# partition is optional
kafkacat-producer: set-env core-up
	@cat .env-local
	@./mvp/scripts/src/main/shell/kafkacat-producer $(env) $(topic) $(filePath) $(partition)

# e.g. make cleardown dc=GR env=live
cleardown: set-env
	@./mvp/scripts/src/main/shell/cleardown $(env) $(dc)

# Consumes protobuf topics and output it in the stdout giving the OPTION to
# filter data with AND and OR, e.g:
# - make proto-consumer bootstrap-servers=localhost:29092 topic=public.demand.sku-demand-forecast.v1 filter-and=1a1b5e01-1326-4521-b774-47584624f78f,VE
# - make proto-consumer bootstrap-servers=localhost:29092 topic=public.demand.sku-demand-forecast.v1 filter-or=1a1b5e01-1326-4521-b774-47584624f78f,VE
proto-consumer: build-scripts
	@java -jar ./mvp/scripts/build/libs/scripts-all.jar --bootstrap-servers=$(bootstrap-servers) --topic="$(topic)" --filter-and="$(filter-and)" --filter-or="$(filter-or)"

ps:
	@watch -n 1 $(DOCKER_COMPOSE) ps

# make logs app=calculator
logs:
	@$(DOCKER_COMPOSE) logs -f $(app)


# make show-lag group=scm.inventory.qa.1617696419 env=live
show-lag:
	@docker exec --env-file=.env-$(env) broker sh -c \
	'kafka-consumer-groups --command-config=/tmp/consumer.properties --bootstrap-server $$BROKER_ADDRESS --group $(group) --describe' \
	| awk '$$6>0 || $$6=="-" {print $$0}'

e2e-apps-up:
	@make docker-build
	@make check-docker-deps
	@make set-docker-env
	@$(DOCKER_COMPOSE) up -d zookeeper broker inventory-postgres db-migration
	@make wait-core-containers
	@$(DOCKER_COMPOSE) up --no-recreate -d calculator-job demand benthos-distribution-center-to-sink calculations-db-sink pre-prod-calculations-db-sink purchase-order-service goods-received-note-service output-demand-forecast supply-quantity-recommendation-job forecast-api transfer-order-service
