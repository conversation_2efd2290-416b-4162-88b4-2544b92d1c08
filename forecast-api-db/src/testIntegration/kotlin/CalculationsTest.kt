import InfraPreparation.createKafkaProducer
import InfraPreparation.startBenthos
import InfraPreparation.startKafkaAndCreateTopics
import InfraPreparation.startPostgresAndRunMigrations
import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.models.SkuUOM
import java.math.BigDecimal
import java.sql.ResultSet
import java.time.Duration
import java.time.LocalDate
import java.util.UUID
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.awaitility.Awaitility
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.testcontainers.containers.KafkaContainer
import org.testcontainers.containers.PostgreSQLContainer

class CalculationsTest {

    @Test
    fun `should consume calculations from topics`() {
        // given

        val key1 = CskuInventoryForecastKey(UUID.randomUUID(), "VE", LocalDate.now())
        val value1 = CskuInventoryForecastVal.random()

        val key2 = key1.copy(cskuId = UUID.randomUUID())
        val value2 = value1.copy(expired = BigDecimal(100))

        // when
        send(calculationsTopic, key1, value1)
        send(preprodCalculationsTopic, key2, value2)

        // then
        assertCalculationResult(key1, value1, isProdCalculation = true)
        assertCalculationResult(key2, value2, isProdCalculation = false)
    }

    @Test
    fun `should consume calculations from topic even net_needs is missing`() {
        // given
        val cskuId = UUID.fromString("4fbf851d-1072-4819-bf28-d77bb11ab53a")
        val key = """
            {"csku_id":"4fbf851d-1072-4819-bf28-d77bb11ab53a","date":"2023-10-16","dc_code":"GR"}
        """.trimIndent()
        val value = """
            {
              "actual_consumption": 0,
              "actual_inbound": 0,
              "actual_inbound_purchase_orders": [

              ],
              "closing_stock": 30,
              "daily_needs": 0,
              "demanded": 100,
              "expected_inbound": 0,
              "expected_inbound_purchase_orders": [
              ],
              "net_needs": null,
              "expired": 0,
              "max_purchase_order_due_in": null,
              "opening_stock": 130,
              "present": 130,
              "production_week": "2023-W42",
              "production_week_start_stock": 0,
              "purchase_order_due_in_for_suppliers": null,
              "safety_stock": null,
              "safety_stock_needs": null,
              "staging_stock": 0,
              "storage_stock": 0
            }
        """.trimIndent()

        // when
        producer.send(ProducerRecord(calculationsTopic, key, value)).get()
        producer.flush()

        // then
        assertNetNeedsIsNull(cskuId)
    }

    @Test
    fun `should consume unusable_inventory field from calculations`() {
        // given
        val cskuId = UUID.fromString("4fbf851d-1072-4819-bf28-d77bb11ab53a")
        val key = """
            {"csku_id":"4fbf851d-1072-4819-bf28-d77bb11ab53a","date":"2023-10-16","dc_code":"GR"}
        """.trimIndent()
        val expectedInventory = """{"inventory": [{"qty": 21900, "expiry_date": "2024-01-20"}]}"""
        val value = """
            {
              "actual_consumption": 0,
              "actual_inbound": 0,
              "actual_inbound_purchase_orders": [

              ],
              "closing_stock": 30,
              "daily_needs": 0,
              "demanded": 100,
              "expected_inbound": 0,
              "expected_inbound_purchase_orders": [
              ],
              "net_needs": null,
              "expired": 0,
              "max_purchase_order_due_in": null,
              "opening_stock": 130,
              "present": 130,
              "production_week": "2023-W42",
              "production_week_start_stock": 0,
              "purchase_order_due_in_for_suppliers": null,
              "safety_stock": null,
              "safety_stock_needs": null,
              "staging_stock": 0,
              "storage_stock": 0,
              "stock_update": 10,
              "unusable_inventory": $expectedInventory
            }
        """.trimIndent()

        // when
        producer.send(ProducerRecord(calculationsTopic, key, value)).get()
        producer.flush()

        // then
        assertUnusableInventoryDetailsExist(cskuId, expectedInventory)
    }

    @Test
    fun `should update calculation updated_at when a subsequent calculation is upserted`() {
        // given
        val key = CskuInventoryForecastKey(UUID.randomUUID(), "VE", LocalDate.now())

        val calculationValue = CskuInventoryForecastVal.random()

        val openingStockUpdate = BigDecimal(99)
        listOf(
            calculationsTopic,
            preprodCalculationsTopic,
            liveInventoryCalculationsTopic,
            liveInventoryPreProdCalculationsTopic,
        ).forEach {
            val prodCalcRecord = ProducerRecord(
                it,
                objectMapper.writeValueAsString(key),
                objectMapper.writeValueAsString(calculationValue),
            )
            val prodCalcRecordUpdate = ProducerRecord(
                it,
                objectMapper.writeValueAsString(key),
                objectMapper.writeValueAsString(calculationValue.copy(openingStock = openingStockUpdate)),
            )
            producer.send(prodCalcRecord).get()
            producer.send(prodCalcRecordUpdate).get()
        }

        // then
        val statement = postgres.createConnection("").createStatement()
        lateinit var resultSet: ResultSet
        listOf(
            "calculation",
            "pre_production_calculation",
            "live_inventory_calculation",
            "live_inventory_pre_production_calculation",
        ).forEach {
            Awaitility.await().atMost(Duration.ofSeconds(15))
                .until {
                    resultSet = statement.executeQuery("select * from $it where csku_id='${key.cskuId}'")
                    resultSet.next() && resultSet.getBigDecimal("opening_stock") == openingStockUpdate
                }
        }
    }

    @Test
    fun `should consume calculations from the live inventory calculation topics`() {
        // given
        val key = CskuInventoryForecastKey(UUID.randomUUID(), "VE", LocalDate.now())

        val calculationValue = CskuInventoryForecastVal.random()
        val calculationValuePreProd = CskuInventoryForecastVal.random().copy(
            demanded = BigDecimal(100),
        )

        val prodLiveInvCalcRecord =
            ProducerRecord(
                liveInventoryCalculationsTopic,
                objectMapper.writeValueAsString(key),
                objectMapper.writeValueAsString(calculationValue),
            )
        val preProdLiveInvCalcRecord =
            ProducerRecord(
                liveInventoryPreProdCalculationsTopic,
                objectMapper.writeValueAsString(key),
                objectMapper.writeValueAsString(calculationValuePreProd),
            )

        // when
        producer.send(prodLiveInvCalcRecord).get()
        producer.send(preProdLiveInvCalcRecord).get()

        // then
        assertCalculationResult(key, calculationValue, isProdCalculation = true, isLive = true)
        assertCalculationResult(key, calculationValuePreProd, isProdCalculation = false, isLive = true)
    }

    @Test
    fun `should update unit of measure when changed`() {
        // given
        val key = CskuInventoryForecastKey(UUID.randomUUID(), "VE", LocalDate.now())

        val calculationValue = CskuInventoryForecastVal.random()
        val calculationValuePreProd = CskuInventoryForecastVal.random()

        val calculationValueLive = CskuInventoryForecastVal.random()
        val calculationValuePreProdLive = CskuInventoryForecastVal.random()

        send(calculationsTopic, key, calculationValue)
        send(preprodCalculationsTopic, key, calculationValuePreProd)
        send(liveInventoryCalculationsTopic, key, calculationValueLive)
        send(liveInventoryPreProdCalculationsTopic, key, calculationValuePreProdLive)

        // then
        assertCalculationResult(key, calculationValue, isProdCalculation = true, isLive = false)
        assertCalculationResult(key, calculationValuePreProd, isProdCalculation = false, isLive = false)
        assertCalculationResult(key, calculationValueLive, isProdCalculation = true, isLive = true)
        assertCalculationResult(key, calculationValuePreProdLive, isProdCalculation = false, isLive = true)

        // when
        val newCalculationValue = calculationValue.copy(uom = SkuUOM.entries.first { it != calculationValue.uom })
        val newCalculationValuePreProd = calculationValuePreProd.copy(
            uom = SkuUOM.entries.first {
                it != calculationValuePreProd.uom
            },
        )
        val newCalculationValueLive = calculationValueLive.copy(
            uom = SkuUOM.entries.first { it != calculationValueLive.uom },
        )
        val newCalculationValuePreProdLive = calculationValuePreProdLive.copy(
            uom = SkuUOM.entries.first {
                it != calculationValuePreProdLive.uom
            },
        )

        send(calculationsTopic, key, newCalculationValue)
        send(preprodCalculationsTopic, key, newCalculationValuePreProd)
        send(liveInventoryCalculationsTopic, key, newCalculationValueLive)
        send(liveInventoryPreProdCalculationsTopic, key, newCalculationValuePreProdLive)

        // then
        assertCalculationResult(key, newCalculationValue, isProdCalculation = true, isLive = false)
        assertCalculationResult(key, newCalculationValuePreProd, isProdCalculation = false, isLive = false)
        assertCalculationResult(key, newCalculationValueLive, isProdCalculation = true, isLive = true)
        assertCalculationResult(key, newCalculationValuePreProdLive, isProdCalculation = false, isLive = true)
    }

    @Test
    fun `should update inbound pos when changed`() {
        // given
        val key = CskuInventoryForecastKey(UUID.randomUUID(), "VE", LocalDate.now())

        val calculationValue = CskuInventoryForecastVal.random()
        val calculationValuePreProd = CskuInventoryForecastVal.random()

        val calculationValueLive = CskuInventoryForecastVal.random()
        val calculationValuePreProdLive = CskuInventoryForecastVal.random()

        send(calculationsTopic, key, calculationValue)
        send(preprodCalculationsTopic, key, calculationValuePreProd)
        send(liveInventoryCalculationsTopic, key, calculationValueLive)
        send(liveInventoryPreProdCalculationsTopic, key, calculationValuePreProdLive)

        // then
        assertCalculationResult(key, calculationValue, isProdCalculation = true, isLive = false)
        assertCalculationResult(key, calculationValuePreProd, isProdCalculation = false, isLive = false)
        assertCalculationResult(key, calculationValueLive, isProdCalculation = true, isLive = true)
        assertCalculationResult(key, calculationValuePreProdLive, isProdCalculation = false, isLive = true)

        // when
        val newCalculationValue = calculationValue.copy(
            actualInboundPurchaseOrders = setOf("PO"),
            expectedInboundPurchaseOrders = setOf("PO2"),
        )

        val newCalculationValuePreProd = calculationValuePreProd.copy(
            actualInboundPurchaseOrders = setOf("PO"),
            expectedInboundPurchaseOrders = setOf("PO2"),
        )
        val newCalculationValueLive = calculationValueLive.copy(
            actualInboundPurchaseOrders = setOf("PO"),
            expectedInboundPurchaseOrders = setOf("PO2"),
        )
        val newCalculationValuePreProdLive = calculationValuePreProdLive.copy(
            actualInboundPurchaseOrders = setOf("PO"),
            expectedInboundPurchaseOrders = setOf("PO2"),
        )

        send(calculationsTopic, key, newCalculationValue)
        send(preprodCalculationsTopic, key, newCalculationValuePreProd)
        send(liveInventoryCalculationsTopic, key, newCalculationValueLive)
        send(liveInventoryPreProdCalculationsTopic, key, newCalculationValuePreProdLive)

        // then
        assertCalculationResult(key, newCalculationValue, isProdCalculation = true, isLive = false)
        assertCalculationResult(key, newCalculationValuePreProd, isProdCalculation = false, isLive = false)
        assertCalculationResult(key, newCalculationValueLive, isProdCalculation = true, isLive = true)
        assertCalculationResult(key, newCalculationValuePreProdLive, isProdCalculation = false, isLive = true)
    }

    private fun send(
        topic: String,
        key: CskuInventoryForecastKey,
        newCalculationValue: CskuInventoryForecastVal
    ) {
        producer.send(
            ProducerRecord(
                topic,
                objectMapper.writeValueAsString(key),
                objectMapper.writeValueAsString(newCalculationValue),
            ),
        ).get()
    }

    private fun assertCalculationResult(
        key: CskuInventoryForecastKey,
        value: CskuInventoryForecastVal,
        isProdCalculation: Boolean,
        isLive: Boolean = false
    ) {
        val statement = postgres.createConnection("").createStatement()
        val calculationTable =
            if (isLive) {
                if (isProdCalculation) {
                    "live_inventory_calculation"
                } else {
                    "live_inventory_pre_production_calculation"
                }
            } else {
                if (isProdCalculation) {
                    "calculation"
                } else {
                    "pre_production_calculation"
                }
            }
        lateinit var resultSet: ResultSet
        Awaitility
            .await()
            .atMost(Duration.ofSeconds(20))
            .until {
                resultSet = statement.executeQuery("select * from $calculationTable where csku_id='${key.cskuId}'")
                resultSet.next()
            }.let {
                assertEquals(key.cskuId.toString(), resultSet.getString("csku_id"))
                assertEquals(key.dcCode, resultSet.getString("dc_code"))
                assertEquals(key.date, resultSet.getDate("date").toLocalDate())
                assertEquals(value.actualConsumption, resultSet.getBigDecimal("actual_consumption"))
                assertEquals(value.safetyStock, resultSet.getBigDecimal("safetystock"))
                assertEquals(value.safetyStockNeeds, resultSet.getBigDecimal("safetystock_needs"))
                assertEquals(value.netNeeds, resultSet.getBigDecimal("net_needs"))
                assertEquals(value.actualInbound, resultSet.getBigDecimal("actual_inbound"))
                assertEquals(
                    value.actualInboundPurchaseOrders,
                    resultSet.getString("actual_inbound_po").split(",").toSet()
                )
                assertEquals(value.expectedInbound, resultSet.getBigDecimal("expected_inbound"))
                assertEquals(
                    value.expectedInboundPurchaseOrders,
                    resultSet.getString("expected_inbound_po").split(",").toSet()
                )
                assertEquals(
                    value.expectedInboundTransferOrders,
                    resultSet.getString("expected_inbound_transfer_orders").split(",").toSet()
                )
                assertEquals(
                    value.expectedInboundTransferOrdersQuantity,
                    resultSet.getBigDecimal("expected_inbound_transfer_orders_quantity")
                )
                assertEquals(
                    value.actualInboundTransferOrders,
                    resultSet.getString("actual_inbound_transfer_orders").split(",").toSet()
                )
                assertEquals(
                    value.actualInboundTransferOrdersQuantity,
                    resultSet.getBigDecimal("actual_inbound_transfer_orders_quantity")
                )
                assertEquals(
                    value.expectedOutboundTransferOrders,
                    resultSet.getString("expected_outbound_transfer_orders").split(",").toSet()
                )
                assertEquals(
                    value.expectedOutboundTransferOrdersQuantity,
                    resultSet.getBigDecimal("expected_outbound_transfer_orders_quantity")
                )
            }
    }

    private fun assertNetNeedsIsNull(id: UUID) {
        val statement = postgres.createConnection("").createStatement()
        lateinit var resultSet: ResultSet
        Awaitility
            .await()
            .atMost(Duration.ofSeconds(20))
            .until {
                resultSet = statement.executeQuery("select * from calculation where csku_id='$id'")
                resultSet.next()
            }.let { assertNull(resultSet.getObject("net_needs")) }
    }

    private fun assertUnusableInventoryDetailsExist(id: UUID, expectedInventoryDetails: String) {
        val statement = postgres.createConnection("").createStatement()
        lateinit var resultSet: ResultSet
        Awaitility
            .await()
            .atMost(Duration.ofSeconds(20))
            .until {
                resultSet = statement.executeQuery("select * from calculation where csku_id='$id'")
                resultSet.next()
            }.let {
                val inventoryDetails = resultSet.getString("unusable_inventory")
                assertEquals(expectedInventoryDetails, inventoryDetails)
            }
    }

    companion object {
        private const val CALCULATION_TOPIC_VERSION = "4"
        private const val LIVE_INVENTORY_CALCULATION_TOPIC_VERSION = "1"
        private val objectMapper = ObjectMapper().findAndRegisterModules()
        private const val calculationsTopic = "csku-inventory-forecast.intermediate.calculations.v$CALCULATION_TOPIC_VERSION"
        private const val preprodCalculationsTopic =
            "private.csku-inventory-forecast.pre-production.calculations.v$CALCULATION_TOPIC_VERSION"
        private const val liveInventoryCalculationsTopic =
            "csku-inventory-forecast.intermediate.forecast.live-inventory-calculations.v$LIVE_INVENTORY_CALCULATION_TOPIC_VERSION"
        private const val liveInventoryPreProdCalculationsTopic =
            "csku-inventory-forecast.intermediate.forecast.live-inventory-pre-production-calculations.v$LIVE_INVENTORY_CALCULATION_TOPIC_VERSION"
        private lateinit var producer: KafkaProducer<String, String?>
        private lateinit var postgres: PostgreSQLContainer<*>
        private lateinit var kafka: KafkaContainer

        @AfterEach
        fun cleanTables() {
            val statement = postgres.createConnection("").createStatement()
            listOf("live_inventory_calculation", "live_inventory_pre_production_calculation").forEach {
                statement.execute("TRUNCATE TABLE $it")
            }
            statement.closeOnCompletion()
        }

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            postgres = startPostgresAndRunMigrations()
            kafka = startKafkaAndCreateTopics(
                listOf(
                    calculationsTopic,
                    preprodCalculationsTopic,
                    liveInventoryCalculationsTopic,
                    liveInventoryPreProdCalculationsTopic,
                ),
            )
            producer = createKafkaProducer(kafka)
            mapOf(
                "calculation" to calculationsTopic,
                "pre_production_calculation" to preprodCalculationsTopic,
                "pre_production_live_inventory_calculation" to liveInventoryPreProdCalculationsTopic,
                "live_inventory_calculation" to liveInventoryCalculationsTopic,
            ).map {
                startBenthos(
                    mapOf(
                        "DB_HOST" to postgres.networkAliases.first(),
                        "DB_SCHEMA" to "${postgres.databaseName}?sslmode=disable",
                        "APP_NAME" to it.key,
                        "CALCULATOR_MODE" to it.key,
                        "INPUT_TOPIC_NAME" to it.value,
                    ),
                )
            }
        }
    }
}
