---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
tag: '@dockerTag@'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

vaultNamespace: services/@projectName@

deployments:
  calculations:
    resources:
      requests:
        memory: '512Mi'
        cpu: '200m'
      limits:
        memory: '1Gi'
        cpu: '500m'
    nodeSelector: { }
    tolerations: [ ]
    affinity: { }
    hpa:
      enabled: false
    replicaCount: 1
    minAvailable: 0
    deploymentStrategy:
      type: Recreate
    containerPorts:
      http: 8080
    repository: '@dockerRepository@'
    pullPolicy: IfNotPresent
    env:
      LOG_LEVEL: INFO
      HF_TIER: '@tier@'
      BENTHOS_PORT: '8080'
      METRICS_PREFIX: '@projectKey@<EMAIL>@'
      APP_NAME: '@applicationName@'
      APP_VERSION: '2'
      KAFKA_TLS_ENABLED: 'true'
      KAFKA_ROOT_CA: '/tmp/ca.crt'
      KAFKA_USERNAME: '@projectName@'
      HF_KAFKA_SASL_MECHANISM: 'PLAIN'
      KAFKA_CLIENT_ID: '@projectKey@-@applicationName@'
      KAFKA_SASL_MECHANISM: PLAIN
      DB_HOST: 'inventory-db000.@<EMAIL>'
      DB_SCHEMA: 'inventory'
      CALCULATOR_MODE: 'calculation'
      INPUT_TOPIC_NAME: 'csku-inventory-forecast.intermediate.calculations.v1'
      HF_INVENTORY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#DB_PASSWORD'
      HF_INVENTORY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#DB_USERNAME'
      HF_KAFKA_SASL_PASSWORD: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#password'
      HF_KAFKA_BOOTSTRAP_SERVERS: 'vault:@tier@/key-value/data/kafka#KAFKA_BOOTSTRAP_SERVERS'
      HF_KAFKA_SSL_CA_PEM: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#ca'

    livenessProbe:
      httpGet:
        path: /ping
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /ready
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    command: [ "sh" ]
    args:
      - -c
      - echo "$HF_KAFKA_SSL_CA_PEM" > /tmp/ca.crt && exec ./benthos -r "/etc/benthos/resources*.yaml" -c "/etc/benthos/benthos.yaml"

  pre-production-calculations:
    resources:
      requests:
        memory: '512Mi'
        cpu: '200m'
      limits:
        memory: '1Gi'
        cpu: '500m'
    nodeSelector: { }
    tolerations: [ ]
    affinity: { }
    hpa:
      enabled: false
    replicaCount: 1
    minAvailable: 0
    deploymentStrategy:
      type: Recreate
    containerPorts:
      http: 8080
    repository: '@dockerRepository@'
    pullPolicy: IfNotPresent
    env:
      LOG_LEVEL: INFO
      HF_TIER: '@tier@'
      BENTHOS_PORT: '8080'
      METRICS_PREFIX: '@projectKey@<EMAIL>@'
      APP_NAME: '@applicationName@'
      APP_VERSION: '2'
      KAFKA_TLS_ENABLED: 'true'
      KAFKA_ROOT_CA: '/tmp/ca.crt'
      KAFKA_USERNAME: '@projectName@'
      HF_KAFKA_SASL_MECHANISM: 'PLAIN'
      KAFKA_CLIENT_ID: '@projectKey@-@applicationName@'
      KAFKA_SASL_MECHANISM: PLAIN
      DB_HOST: 'inventory-db000.@<EMAIL>'
      DB_SCHEMA: 'inventory'
      PRIVATE_CSKU_INVENTORY_FORECAST_CALCULATIONS_VERSION: '4'
      INTERMEDIATE_LIVE_INVENTORY_CALCULATIONS_VERSION: '1'
      CALCULATOR_MODE: 'pre_production_calculation'
      INPUT_TOPIC_NAME: 'csku-inventory-forecast.intermediate.pre-production.calculations.v1'
      HF_INVENTORY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#DB_PASSWORD'
      HF_INVENTORY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#DB_USERNAME'
      HF_KAFKA_SASL_PASSWORD: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#password'
      HF_KAFKA_BOOTSTRAP_SERVERS: 'vault:@tier@/key-value/data/kafka#KAFKA_BOOTSTRAP_SERVERS'
      HF_KAFKA_SSL_CA_PEM: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#ca'

    livenessProbe:
      httpGet:
        path: /ping
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /ready
        port: http
    initialDelaySeconds: 15
    periodSeconds: 10
    timeoutSeconds: 3
    successThreshold: 1
    failureThreshold: 3
    command: [ "sh" ]
    args:
      - -c
      - echo "$HF_KAFKA_SSL_CA_PEM" > /tmp/ca.crt && exec ./benthos -r "/etc/benthos/resources*.yaml" -c "/etc/benthos/benthos.yaml"
  live-inventory-calculations:
    resources:
      requests:
        memory: '512Mi'
        cpu: '200m'
      limits:
        memory: '1Gi'
        cpu: '500m'
    nodeSelector: { }
    tolerations: [ ]
    affinity: { }
    hpa:
      enabled: false
    replicaCount: 1
    minAvailable: 0
    deploymentStrategy:
      type: Recreate
    containerPorts:
      http: 8080
    repository: '@dockerRepository@'
    pullPolicy: IfNotPresent
    env:
      LOG_LEVEL: INFO
      HF_TIER: '@tier@'
      BENTHOS_PORT: '8080'
      METRICS_PREFIX: '@projectKey@<EMAIL>@'
      APP_NAME: '@applicationName@'
      APP_VERSION: '2'
      KAFKA_TLS_ENABLED: 'true'
      KAFKA_ROOT_CA: '/tmp/ca.crt'
      KAFKA_USERNAME: '@projectName@'
      HF_KAFKA_SASL_MECHANISM: 'PLAIN'
      KAFKA_CLIENT_ID: '@projectKey@-@applicationName@'
      KAFKA_SASL_MECHANISM: PLAIN
      DB_HOST: 'inventory-db000.@<EMAIL>'
      DB_SCHEMA: 'inventory'
      CALCULATOR_MODE: 'live_inventory_calculation'
      INPUT_TOPIC_NAME: 'csku-inventory-forecast.intermediate.forecast.live-inventory-calculations.v1'
      HF_INVENTORY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#DB_PASSWORD'
      HF_INVENTORY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#DB_USERNAME'
      HF_KAFKA_SASL_PASSWORD: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#password'
      HF_KAFKA_BOOTSTRAP_SERVERS: 'vault:@tier@/key-value/data/kafka#KAFKA_BOOTSTRAP_SERVERS'
      HF_KAFKA_SSL_CA_PEM: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#ca'

    livenessProbe:
      httpGet:
        path: /ping
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /ready
        port: http
    initialDelaySeconds: 15
    periodSeconds: 10
    timeoutSeconds: 3
    successThreshold: 1
    failureThreshold: 3
    command: [ "sh" ]
    args:
      - -c
      - echo "$HF_KAFKA_SSL_CA_PEM" > /tmp/ca.crt && exec ./benthos -r "/etc/benthos/resources*.yaml" -c "/etc/benthos/benthos.yaml"
  live-inventory-preprod-calculations:
    resources:
      requests:
        memory: '512Mi'
        cpu: '200m'
      limits:
        memory: '1Gi'
        cpu: '500m'
    nodeSelector: { }
    tolerations: [ ]
    affinity: { }
    hpa:
      enabled: false
    replicaCount: 1
    minAvailable: 0
    deploymentStrategy:
      type: Recreate
    containerPorts:
      http: 8080
    repository: '@dockerRepository@'
    pullPolicy: IfNotPresent
    env:
      LOG_LEVEL: INFO
      HF_TIER: '@tier@'
      BENTHOS_PORT: '8080'
      METRICS_PREFIX: '@projectKey@<EMAIL>@'
      APP_NAME: '@applicationName@'
      APP_VERSION: '2'
      KAFKA_TLS_ENABLED: 'true'
      KAFKA_ROOT_CA: '/tmp/ca.crt'
      KAFKA_USERNAME: '@projectName@'
      HF_KAFKA_SASL_MECHANISM: 'PLAIN'
      KAFKA_CLIENT_ID: '@projectKey@-@applicationName@'
      KAFKA_SASL_MECHANISM: PLAIN
      DB_HOST: 'inventory-db000.@<EMAIL>'
      DB_SCHEMA: 'inventory'
      CALCULATOR_MODE: 'pre_production_live_inventory_calculation'
      INPUT_TOPIC_NAME: 'csku-inventory-forecast.intermediate.forecast.live-inventory-pre-production-calculations.v1'
      HF_INVENTORY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#DB_PASSWORD'
      HF_INVENTORY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#DB_USERNAME'
      HF_KAFKA_SASL_PASSWORD: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#password'
      HF_KAFKA_BOOTSTRAP_SERVERS: 'vault:@tier@/key-value/data/kafka#KAFKA_BOOTSTRAP_SERVERS'
      HF_KAFKA_SSL_CA_PEM: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#ca'
    livenessProbe:
      httpGet:
        path: /ping
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /ready
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    command: [ "sh" ]
    args:
      - -c
      - echo "$HF_KAFKA_SSL_CA_PEM" > /tmp/ca.crt && exec ./benthos -r "/etc/benthos/resources*.yaml" -c "/etc/benthos/benthos.yaml"

services:
  calculations:
    enablePrometheus: true
    metricPortName: 'http'
    metricPath: '/metrics'
    enabled: true
    type: ClusterIP
    ports:
      http: 8080
  pre-production-calculations:
    enablePrometheus: true
    metricPortName: 'http'
    metricPath: '/metrics'
    enabled: true
    type: ClusterIP
    ports:
      http: 8080
  live-inventory-calculations:
    enablePrometheus: true
    metricPortName: 'http'
    metricPath: '/metrics'
    enabled: true
    type: ClusterIP
    ports:
      http: 8080
  live-inventory-preprod-calculations:
    enablePrometheus: true
    metricPortName: 'http'
    metricPath: '/metrics'
    enabled: true
    type: ClusterIP
    ports:
      http: 8080

