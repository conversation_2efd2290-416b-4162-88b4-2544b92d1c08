output_resources:
  - label: persist_to_calculation_table
    retry:
      max_retries: 2
      backoff:
        initial_interval: 500ms
        max_interval: 3s
      output:
        sql:
          max_in_flight: 1
          driver: postgres
          data_source_name: postgres://${HF_INVENTORY_DB_USERNAME}:${HF_INVENTORY_DB_PASSWORD}@${DB_HOST}:5432/${DB_SCHEMA}
          query: >-
            INSERT INTO calculation
            (dc_code,
            production_week,
            csku_id,
            date,
            expired,
            opening_stock,
            present,
            demanded,
            closing_stock,
            actual_inbound,
            expected_inbound,
            actual_inbound_po,
            expected_inbound_po,
            daily_needs,
            actual_consumption,
            safetystock,
            safetystock_needs,
            purchase_order_due_in_for_suppliers,
            max_purchase_order_due_in,
            net_needs,
            unusable_inventory,
            uom,
            stock_update,
            strategy,
            expected_inbound_transfer_orders,
            expected_inbound_transfer_orders_quantity,
            actual_inbound_transfer_orders,
            actual_inbound_transfer_orders_quantity,
            expected_outbound_transfer_orders,
            expected_outbound_transfer_orders_quantity
            )
            VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, CAST(NULLIF($16, 'null') as NUMERIC), CAST(NULLIF($17, 'null') as NUMERIC), $18, CAST(NULLIF($19, 'null') as INTEGER), CAST(NULLIF($20, 'null') as NUMERIC), $21,
              COALESCE(NULLIF($22, 'null'), 'UOM_UNIT')::uom, CAST(NULLIF($23, 'null') as NUMERIC), $24, $25, CAST(NULLIF($26, 'null') as NUMERIC), $27, CAST(NULLIF($28, 'null') as NUMERIC), $29, CAST(NULLIF($30, 'null') as NUMERIC)
            )
            ON CONFLICT (csku_id, dc_code, date)
            DO UPDATE SET
            (dc_code,
            production_week,
            csku_id,
            date,
            expired,
            opening_stock,
            present,
            demanded,
            closing_stock,
            actual_inbound,
            expected_inbound,
            actual_inbound_po,
            expected_inbound_po,
            daily_needs,
            actual_consumption,
            safetystock,
            safetystock_needs,
            purchase_order_due_in_for_suppliers,
            max_purchase_order_due_in,
            updated_at,
            net_needs,
            unusable_inventory,
            uom,
            stock_update,
            strategy,
            expected_inbound_transfer_orders,
            expected_inbound_transfer_orders_quantity,
            actual_inbound_transfer_orders,
            actual_inbound_transfer_orders_quantity,
            expected_outbound_transfer_orders,
            expected_outbound_transfer_orders_quantity
            )
            = ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, CAST(NULLIF($16, 'null') as NUMERIC), CAST(NULLIF($17, 'null') as NUMERIC), $18, CAST(NULLIF($19, 'null') as INTEGER),now(), CAST(NULLIF($20, 'null') as NUMERIC), $21,
              COALESCE(NULLIF($22, 'null'), 'UOM_UNIT')::uom, CAST(NULLIF($23, 'null') as NUMERIC), $24, $25, CAST(NULLIF($26, 'null') as NUMERIC), $27, CAST(NULLIF($28, 'null') as NUMERIC), $29, CAST(NULLIF($30, 'null') as NUMERIC)
              )
              WHERE calculation.production_week != $2 and calculation.expired != $5 or calculation.opening_stock != $6 or calculation.present != $7 or calculation.demanded != $8 or calculation.closing_stock != $9 or
              calculation.actual_inbound != $10 or calculation.expected_inbound != $11 or calculation.actual_inbound_po != $12 or calculation.expected_inbound_po != $13 or
              calculation.daily_needs != $14 or calculation.actual_consumption != $15 or
              calculation.safetystock IS DISTINCT FROM CAST(NULLIF($16, 'null') as NUMERIC) or calculation.safetystock_needs IS DISTINCT FROM CAST(NULLIF($17, 'null') as NUMERIC)
              or not(
                     calculation.purchase_order_due_in_for_suppliers @> $18
                     AND $18 @> calculation.purchase_order_due_in_for_suppliers
                     )
              or calculation.max_purchase_order_due_in IS DISTINCT FROM CAST(NULLIF($19, 'null') as INTEGER)
              or calculation.net_needs IS DISTINCT FROM CAST(NULLIF($20, 'null') as NUMERIC)
              or calculation.unusable_inventory != $21
              or calculation.uom::text != $22
              or calculation.stock_update  IS DISTINCT FROM CAST(NULLIF($23, 'null') as NUMERIC)
              or calculation.strategy != $24
              or calculation.expected_inbound_transfer_orders != $25 
              or calculation.expected_inbound_transfer_orders_quantity IS DISTINCT FROM CAST(NULLIF($26, 'null') as NUMERIC)
              or calculation.actual_inbound_transfer_orders != $27 
              or calculation.actual_inbound_transfer_orders_quantity IS DISTINCT FROM CAST(NULLIF($28, 'null') as NUMERIC)
              or calculation.expected_outbound_transfer_orders != $29 
              or calculation.expected_outbound_transfer_orders_quantity IS DISTINCT FROM CAST(NULLIF($30, 'null') as NUMERIC)
          args:
            - ${! json("key.dc_code") }
            - ${! json("doc.production_week") }
            - ${! json("key.csku_id") }
            - ${! json("key.date") }
            - ${! json("doc.expired")}
            - ${! json("doc.opening_stock") }
            - ${! json("doc.present") }
            - ${! json("doc.demanded") }
            - ${! json("doc.closing_stock")}
            - ${! json("doc.actual_inbound") }
            - ${! json("doc.expected_inbound") }
            - ${! json("doc.actual_inbound_purchase_orders").join(",") }
            - ${! json("doc.expected_inbound_purchase_orders").join(",") }
            - ${! json("doc.daily_needs") }
            - ${! json("doc.actual_consumption") }
            - ${! json("doc.safety_stock") }
            - ${! json("doc.safety_stock_needs") }
            - ${! json("doc.purchase_order_due_in_for_suppliers") }
            - ${! json("doc.max_purchase_order_due_in") }
            - ${! json("doc.net_needs") }
            - ${! json("doc.unusable_inventory") }
            - ${! json("doc.uom") }
            - ${! json("doc.stock_update") }
            - ${! json("doc.strategy") }
            - ${! json("doc.expected_inbound_transfer_orders").join(",") }
            - ${! json("doc.expected_inbound_transfer_orders_quantity") }
            - ${! json("doc.actual_inbound_transfer_orders").join(",") }
            - ${! json("doc.actual_inbound_transfer_orders_quantity") }
            - ${! json("doc.expected_outbound_transfer_orders").join(",") }
            - ${! json("doc.expected_outbound_transfer_orders_quantity") }

  - label: persist_to_pre_production_calculation_table
    retry:
      max_retries: 2
      backoff:
        initial_interval: 600ms
        max_interval: 3s
      output:
        sql:
          max_in_flight: 1
          driver: postgres
          data_source_name: postgres://${HF_INVENTORY_DB_USERNAME}:${HF_INVENTORY_DB_PASSWORD}@${DB_HOST}:5432/${DB_SCHEMA}
          query: >-
            INSERT INTO pre_production_calculation
            (dc_code,
            production_week,
            csku_id,
            date,
            expired,
            opening_stock,
            present,
            demanded,
            closing_stock,
            actual_inbound,
            expected_inbound,
            actual_inbound_po,
            expected_inbound_po,
            daily_needs,
            actual_consumption,
            safetystock,
            safetystock_needs,
            purchase_order_due_in_for_suppliers,
            max_purchase_order_due_in,
            net_needs,
            uom,
            stock_update,
            strategy,
            expected_inbound_transfer_orders,
            expected_inbound_transfer_orders_quantity,
            actual_inbound_transfer_orders,
            actual_inbound_transfer_orders_quantity,
            expected_outbound_transfer_orders,
            expected_outbound_transfer_orders_quantity
            )
            VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, CAST(NULLIF($16, 'null') as NUMERIC), CAST(NULLIF($17, 'null') as NUMERIC), $18, CAST(NULLIF($19, 'null') as INTEGER), CAST(NULLIF($20, 'null') as NUMERIC),
               COALESCE(NULLIF($21, 'null'), 'UOM_UNIT')::uom, CAST(NULLIF($22, 'null') as NUMERIC), $23, $24, CAST(NULLIF($25, 'null') as NUMERIC), $26, CAST(NULLIF($27, 'null') as NUMERIC), $28, CAST(NULLIF($29, 'null') as NUMERIC)
            )
            ON CONFLICT (csku_id, dc_code, date)
            DO UPDATE SET
            (dc_code,
            production_week,
            csku_id,
            date,
            expired,
            opening_stock,
            present,
            demanded,
            closing_stock,
            actual_inbound,
            expected_inbound,
            actual_inbound_po,
            expected_inbound_po,
            daily_needs,
            actual_consumption,
            safetystock,
            safetystock_needs,
            purchase_order_due_in_for_suppliers,
            max_purchase_order_due_in,
            updated_at,
            net_needs,
            uom,
            stock_update,
            strategy,
            expected_inbound_transfer_orders,
            expected_inbound_transfer_orders_quantity,
            actual_inbound_transfer_orders,
            actual_inbound_transfer_orders_quantity,
            expected_outbound_transfer_orders,
            expected_outbound_transfer_orders_quantity
            )
            = ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, CAST(NULLIF($16, 'null') as NUMERIC), CAST(NULLIF($17, 'null') as NUMERIC), $18, CAST(NULLIF($19, 'null') as INTEGER),now(), CAST(NULLIF($20, 'null') as NUMERIC),
               COALESCE(NULLIF($21, 'null'), 'UOM_UNIT')::uom, CAST(NULLIF($22, 'null') as NUMERIC), $23, $24, CAST(NULLIF($25, 'null') as NUMERIC), $26, CAST(NULLIF($27, 'null') as NUMERIC), $28, CAST(NULLIF($29, 'null') as NUMERIC)
            )
               WHERE pre_production_calculation.production_week != $2 and pre_production_calculation.expired != $5 or pre_production_calculation.opening_stock != $6 or pre_production_calculation.present != $7 or pre_production_calculation.demanded != $8 or pre_production_calculation.closing_stock != $9 or
               pre_production_calculation.actual_inbound != $10 or pre_production_calculation.expected_inbound != $11 or pre_production_calculation.actual_inbound_po != $12 or pre_production_calculation.expected_inbound_po != $13 or
               pre_production_calculation.daily_needs != $14 or pre_production_calculation.actual_consumption != $15 or
               pre_production_calculation.safetystock IS DISTINCT FROM CAST(NULLIF($16, 'null') as NUMERIC) or pre_production_calculation.safetystock_needs IS DISTINCT FROM CAST(NULLIF($17, 'null') as NUMERIC)
               or not(
                      pre_production_calculation.purchase_order_due_in_for_suppliers @> $18
                      AND $18 @> pre_production_calculation.purchase_order_due_in_for_suppliers
                      )
               or pre_production_calculation.max_purchase_order_due_in IS DISTINCT FROM CAST(NULLIF($19, 'null') as INTEGER)
               or pre_production_calculation.net_needs IS DISTINCT FROM CAST(NULLIF($20, 'null') as NUMERIC)
               or pre_production_calculation.uom::text != $21
               or pre_production_calculation.stock_update IS DISTINCT FROM CAST(NULLIF($22, 'null') as NUMERIC)
               or pre_production_calculation.strategy != $23
               or pre_production_calculation.expected_inbound_transfer_orders != $24 
               or pre_production_calculation.expected_inbound_transfer_orders_quantity IS DISTINCT FROM CAST(NULLIF($25, 'null') as NUMERIC)
               or pre_production_calculation.actual_inbound_transfer_orders != $26 
               or pre_production_calculation.actual_inbound_transfer_orders_quantity IS DISTINCT FROM CAST(NULLIF($27, 'null') as NUMERIC)
               or pre_production_calculation.expected_outbound_transfer_orders != $28
               or pre_production_calculation.expected_outbound_transfer_orders_quantity IS DISTINCT FROM CAST(NULLIF($29, 'null') as NUMERIC)
          args:
            - ${! json("key.dc_code") }
            - ${! json("doc.production_week") }
            - ${! json("key.csku_id") }
            - ${! json("key.date") }
            - ${! json("doc.expired")}
            - ${! json("doc.opening_stock") }
            - ${! json("doc.present") }
            - ${! json("doc.demanded") }
            - ${! json("doc.closing_stock")}
            - ${! json("doc.actual_inbound") }
            - ${! json("doc.expected_inbound") }
            - ${! json("doc.actual_inbound_purchase_orders").join(",") }
            - ${! json("doc.expected_inbound_purchase_orders").join(",") }
            - ${! json("doc.daily_needs") }
            - ${! json("doc.actual_consumption") }
            - ${! json("doc.safety_stock") }
            - ${! json("doc.safety_stock_needs") }
            - ${! json("doc.purchase_order_due_in_for_suppliers") }
            - ${! json("doc.max_purchase_order_due_in") }
            - ${! json("doc.net_needs") }
            - ${! json("doc.uom") }
            - ${! json("doc.stock_update") }
            - ${! json("doc.strategy") }
            - ${! json("doc.expected_inbound_transfer_orders").join(",") }
            - ${! json("doc.expected_inbound_transfer_orders_quantity") }
            - ${! json("doc.actual_inbound_transfer_orders").join(",") }
            - ${! json("doc.actual_inbound_transfer_orders_quantity") }
            - ${! json("doc.expected_outbound_transfer_orders").join(",") }
            - ${! json("doc.expected_outbound_transfer_orders_quantity") }

  - label: persist_to_live_inventory_calculation_table
    retry:
      max_retries: 2
      backoff:
        initial_interval: 500ms
        max_interval: 3s
      output:
        sql:
          max_in_flight: 1
          driver: postgres
          data_source_name: postgres://${HF_INVENTORY_DB_USERNAME}:${HF_INVENTORY_DB_PASSWORD}@${DB_HOST}:5432/${DB_SCHEMA}
          query: >-
            INSERT INTO live_inventory_calculation
            (dc_code,
            production_week,
            csku_id,
            date,
            expired,
            opening_stock,
            present,
            demanded,
            closing_stock,
            actual_inbound,
            expected_inbound,
            actual_inbound_po,
            expected_inbound_po,
            daily_needs,
            actual_consumption,
            safetystock,
            safetystock_needs,
            staging_stock,
            storage_stock,
            purchase_order_due_in_for_suppliers,
            max_purchase_order_due_in,
            net_needs,
            uom,
            strategy,
            expected_inbound_transfer_orders,
            expected_inbound_transfer_orders_quantity,
            actual_inbound_transfer_orders,
            actual_inbound_transfer_orders_quantity,
            expected_outbound_transfer_orders,
            expected_outbound_transfer_orders_quantity
            )
            VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, CAST(NULLIF($16, 'null') as NUMERIC), CAST(NULLIF($17, 'null') as NUMERIC), $18, $19, $20, CAST(NULLIF($21, 'null') as INTEGER), CAST(NULLIF($22, 'null') as NUMERIC),
                    COALESCE(NULLIF($23, 'null'), 'UOM_UNIT')::uom, $24, $25, CAST(NULLIF($26, 'null') as NUMERIC), $27, CAST(NULLIF($28, 'null') as NUMERIC), $29, CAST(NULLIF($30, 'null') as NUMERIC)
            )
            ON CONFLICT (csku_id, dc_code, date)
            DO UPDATE SET
            (dc_code,
            production_week,
            csku_id,
            date,
            expired,
            opening_stock,
            present,
            demanded,
            closing_stock,
            actual_inbound,
            expected_inbound,
            actual_inbound_po,
            expected_inbound_po,
            daily_needs,
            actual_consumption,
            safetystock,
            safetystock_needs,
            staging_stock,
            storage_stock,
            purchase_order_due_in_for_suppliers,
            max_purchase_order_due_in,
            updated_at,
            net_needs,
            uom,
            strategy,
            expected_inbound_transfer_orders,
            expected_inbound_transfer_orders_quantity,
            actual_inbound_transfer_orders,
            actual_inbound_transfer_orders_quantity,
            expected_outbound_transfer_orders,
            expected_outbound_transfer_orders_quantity
            )
            = ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, CAST(NULLIF($16, 'null') as NUMERIC), CAST(NULLIF($17, 'null') as NUMERIC), $18, $19, $20, CAST(NULLIF($21, 'null') as INTEGER), now(), CAST(NULLIF($22, 'null') as NUMERIC),
               COALESCE(NULLIF($23, 'null'), 'UOM_UNIT')::uom, $24, $25, CAST(NULLIF($26, 'null') as NUMERIC), $27, CAST(NULLIF($28, 'null') as NUMERIC), $29, CAST(NULLIF($30, 'null') as NUMERIC)
            )
            WHERE live_inventory_calculation.production_week != $2 and live_inventory_calculation.expired != $5 or live_inventory_calculation.opening_stock != $6 or live_inventory_calculation.present != $7 or live_inventory_calculation.demanded != $8 or live_inventory_calculation.closing_stock != $9 or
              live_inventory_calculation.actual_inbound != $10 or live_inventory_calculation.expected_inbound != $11 or live_inventory_calculation.actual_inbound_po != $12 or live_inventory_calculation.expected_inbound_po != $13 or
              live_inventory_calculation.daily_needs != $14 or live_inventory_calculation.actual_consumption != $15 or
              live_inventory_calculation.safetystock IS DISTINCT FROM CAST(NULLIF($16, 'null') as NUMERIC) or live_inventory_calculation.safetystock_needs IS DISTINCT FROM CAST(NULLIF($17, 'null') as NUMERIC) or
              live_inventory_calculation.staging_stock != $18 or live_inventory_calculation.storage_stock != $19
              or not(
                      live_inventory_calculation.purchase_order_due_in_for_suppliers @> $20
                      AND $20 @> live_inventory_calculation.purchase_order_due_in_for_suppliers
                      )
              or live_inventory_calculation.max_purchase_order_due_in IS DISTINCT FROM CAST(NULLIF($21, 'null') as INTEGER)
              or live_inventory_calculation.net_needs IS DISTINCT FROM CAST(NULLIF($22, 'null') as NUMERIC)
              or live_inventory_calculation.uom::text != $23
              or live_inventory_calculation.strategy != $24
              or live_inventory_calculation.expected_inbound_transfer_orders != $25
              or live_inventory_calculation.expected_inbound_transfer_orders_quantity IS DISTINCT FROM CAST(NULLIF($26, 'null') as NUMERIC)
              or live_inventory_calculation.actual_inbound_transfer_orders != $27 
              or live_inventory_calculation.actual_inbound_transfer_orders_quantity IS DISTINCT FROM CAST(NULLIF($28, 'null') as NUMERIC)
              or live_inventory_calculation.expected_outbound_transfer_orders != $29 
              or live_inventory_calculation.expected_outbound_transfer_orders_quantity IS DISTINCT FROM CAST(NULLIF($30, 'null') as NUMERIC)
          args:
            - ${! json("key.dc_code") }
            - ${! json("doc.production_week") }
            - ${! json("key.csku_id") }
            - ${! json("key.date") }
            - ${! json("doc.expired")}
            - ${! json("doc.opening_stock") }
            - ${! json("doc.present") }
            - ${! json("doc.demanded") }
            - ${! json("doc.closing_stock")}
            - ${! json("doc.actual_inbound") }
            - ${! json("doc.expected_inbound") }
            - ${! json("doc.actual_inbound_purchase_orders").join(",") }
            - ${! json("doc.expected_inbound_purchase_orders").join(",") }
            - ${! json("doc.daily_needs") }
            - ${! json("doc.actual_consumption") }
            - ${! json("doc.safety_stock") }
            - ${! json("doc.safety_stock_needs") }
            - ${! json("doc.staging_stock") }
            - ${! json("doc.storage_stock") }
            - ${! json("doc.purchase_order_due_in_for_suppliers") }
            - ${! json("doc.max_purchase_order_due_in") }
            - ${! json("doc.net_needs") }
            - ${! json("doc.uom") }
            - ${! json("doc.strategy") }
            - ${! json("doc.expected_inbound_transfer_orders").join(",") }
            - ${! json("doc.expected_inbound_transfer_orders_quantity") }
            - ${! json("doc.actual_inbound_transfer_orders").join(",") }
            - ${! json("doc.actual_inbound_transfer_orders_quantity") }
            - ${! json("doc.expected_outbound_transfer_orders").join(",") }
            - ${! json("doc.expected_outbound_transfer_orders_quantity") }

  - label: persist_to_pre_production_live_inventory_calculation_table
    retry:
      max_retries: 2
      backoff:
        initial_interval: 500ms
        max_interval: 3s
      output:
        sql:
          max_in_flight: 1
          driver: postgres
          data_source_name: postgres://${HF_INVENTORY_DB_USERNAME}:${HF_INVENTORY_DB_PASSWORD}@${DB_HOST}:5432/${DB_SCHEMA}
          query: >-
            INSERT INTO live_inventory_pre_production_calculation
            (dc_code,
            production_week,
            csku_id,
            date,
            expired,
            opening_stock,
            present,
            demanded,
            closing_stock,
            actual_inbound,
            expected_inbound,
            actual_inbound_po,
            expected_inbound_po,
            daily_needs,
            actual_consumption,
            safetystock,
            safetystock_needs,
            staging_stock,
            storage_stock,
            purchase_order_due_in_for_suppliers,
            max_purchase_order_due_in,
            net_needs,
            uom,
            strategy,
            expected_inbound_transfer_orders,
            expected_inbound_transfer_orders_quantity,
            actual_inbound_transfer_orders,
            actual_inbound_transfer_orders_quantity,
            expected_outbound_transfer_orders,
            expected_outbound_transfer_orders_quantity
            )
            VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, CAST(NULLIF($16, 'null') as NUMERIC), CAST(NULLIF($17, 'null') as NUMERIC), $18, $19, $20, CAST(NULLIF($21, 'null') as INTEGER), CAST(NULLIF($22, 'null') as NUMERIC),
                   COALESCE(NULLIF($23, 'null'), 'UOM_UNIT')::uom, $24, $25, CAST(NULLIF($26, 'null') as NUMERIC), $27, CAST(NULLIF($28, 'null') as NUMERIC), $29, CAST(NULLIF($30, 'null') as NUMERIC)
            )
            ON CONFLICT (csku_id, dc_code, date)
            DO UPDATE SET
            (dc_code,
            production_week,
            csku_id,
            date,
            expired,
            opening_stock,
            present,
            demanded,
            closing_stock,
            actual_inbound,
            expected_inbound,
            actual_inbound_po,
            expected_inbound_po,
            daily_needs,
            actual_consumption,
            safetystock,
            safetystock_needs,
            staging_stock,
            storage_stock,
            purchase_order_due_in_for_suppliers,
            max_purchase_order_due_in,
            updated_at,
            net_needs,
            uom,
            strategy,
            expected_inbound_transfer_orders,
            expected_inbound_transfer_orders_quantity,
            actual_inbound_transfer_orders,
            actual_inbound_transfer_orders_quantity,
            expected_outbound_transfer_orders,
            expected_outbound_transfer_orders_quantity
            )
            = ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, CAST(NULLIF($16, 'null') as NUMERIC), CAST(NULLIF($17, 'null') as NUMERIC), $18, $19, $20, CAST(NULLIF($21, 'null') as INTEGER), now(), CAST(NULLIF($22, 'null') as NUMERIC),
               COALESCE(NULLIF($23, 'null'), 'UOM_UNIT')::uom, $24, $25, CAST(NULLIF($26, 'null') as NUMERIC), $27, CAST(NULLIF($28, 'null') as NUMERIC), $29, CAST(NULLIF($30, 'null') as NUMERIC)
            )
              WHERE live_inventory_pre_production_calculation.production_week != $2 and live_inventory_pre_production_calculation.expired != $5 or live_inventory_pre_production_calculation.opening_stock != $6 or live_inventory_pre_production_calculation.present != $7 or live_inventory_pre_production_calculation.demanded != $8 or live_inventory_pre_production_calculation.closing_stock != $9 or
                live_inventory_pre_production_calculation.actual_inbound != $10 or live_inventory_pre_production_calculation.expected_inbound != $11 or live_inventory_pre_production_calculation.actual_inbound_po != $12 or live_inventory_pre_production_calculation.expected_inbound_po != $13 or
                live_inventory_pre_production_calculation.daily_needs != $14 or live_inventory_pre_production_calculation.actual_consumption != $15 or
                live_inventory_pre_production_calculation.safetystock IS DISTINCT FROM CAST(NULLIF($16, 'null') as NUMERIC) or live_inventory_pre_production_calculation.safetystock_needs IS DISTINCT FROM CAST(NULLIF($17, 'null') as NUMERIC) or
                live_inventory_pre_production_calculation.staging_stock != $18 or live_inventory_pre_production_calculation.storage_stock != $19
                 or not(
                        live_inventory_pre_production_calculation.purchase_order_due_in_for_suppliers @> $20
                        AND $20 @> live_inventory_pre_production_calculation.purchase_order_due_in_for_suppliers
                        )
            or live_inventory_pre_production_calculation.max_purchase_order_due_in IS DISTINCT FROM CAST(NULLIF($21, 'null') as INTEGER)
            or live_inventory_pre_production_calculation.net_needs IS DISTINCT FROM CAST(NULLIF($22, 'null') as NUMERIC)
            or live_inventory_pre_production_calculation.uom::text != $23
            or live_inventory_pre_production_calculation.strategy != $24
            or live_inventory_pre_production_calculation.expected_inbound_transfer_orders != $25
            or live_inventory_pre_production_calculation.expected_inbound_transfer_orders_quantity IS DISTINCT FROM CAST(NULLIF($26, 'null') as NUMERIC)
            or live_inventory_pre_production_calculation.actual_inbound_transfer_orders != $27 
            or live_inventory_pre_production_calculation.actual_inbound_transfer_orders_quantity IS DISTINCT FROM CAST(NULLIF($28, 'null') as NUMERIC)
            or live_inventory_pre_production_calculation.expected_outbound_transfer_orders != $29 
            or live_inventory_pre_production_calculation.expected_outbound_transfer_orders_quantity IS DISTINCT FROM CAST(NULLIF($30, 'null') as NUMERIC)
          args:
            - ${! json("key.dc_code") }
            - ${! json("doc.production_week") }
            - ${! json("key.csku_id") }
            - ${! json("key.date") }
            - ${! json("doc.expired")}
            - ${! json("doc.opening_stock") }
            - ${! json("doc.present") }
            - ${! json("doc.demanded") }
            - ${! json("doc.closing_stock")}
            - ${! json("doc.actual_inbound") }
            - ${! json("doc.expected_inbound") }
            - ${! json("doc.actual_inbound_purchase_orders").join(",") }
            - ${! json("doc.expected_inbound_purchase_orders").join(",") }
            - ${! json("doc.daily_needs") }
            - ${! json("doc.actual_consumption") }
            - ${! json("doc.safety_stock") }
            - ${! json("doc.safety_stock_needs") }
            - ${! json("doc.staging_stock") }
            - ${! json("doc.storage_stock") }
            - ${! json("doc.purchase_order_due_in_for_suppliers") }
            - ${! json("doc.max_purchase_order_due_in") }
            - ${! json("doc.net_needs") }
            - ${! json("doc.uom") }
            - ${! json("doc.strategy") }
            - ${! json("doc.expected_inbound_transfer_orders").join(",") }
            - ${! json("doc.expected_inbound_transfer_orders_quantity") }
            - ${! json("doc.actual_inbound_transfer_orders").join(",") }
            - ${! json("doc.actual_inbound_transfer_orders_quantity") }
            - ${! json("doc.expected_outbound_transfer_orders").join(",") }
            - ${! json("doc.expected_outbound_transfer_orders_quantity") }
