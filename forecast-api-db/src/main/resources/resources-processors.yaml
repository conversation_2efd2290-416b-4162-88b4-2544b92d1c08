processor_resources:
  - label: decode_topic_record
    bloblang: |
      root.doc =  this
      root.key = meta("kafka_key").parse_json()
      root.topic = meta("kafka_topic")

  - label: log_failed_message_decode
    log:
      message: "Error while processing a message"
      level: ERROR
      fields:
        payload: ${! json() }
        from_topic: ${! meta("kafka_topic")}
        error: ${! error() }

