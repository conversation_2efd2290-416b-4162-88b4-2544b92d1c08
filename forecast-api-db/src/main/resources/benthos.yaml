metrics:
  prometheus:
    prefix: ${METRICS_PREFIX:benthos}

http:
  address: "0.0.0.0:${BENTHOS_PORT}"

logger:
  level: '${LOG_LEVEL:INFO}'
  add_timestamp: true
  format: json
  static_fields:
    "@service": forecast-api-db-consumer

input:
  kafka:
    addresses:
      - ${HF_KAFKA_BOOTSTRAP_SERVERS}
    tls:
      enabled: ${TLS_ENABLED:true}
      root_cas_file: /tmp/ca.crt
    sasl:
      mechanism: ${HF_KAFKA_SASL_MECHANISM}
      user: ${KAFKA_USERNAME}
      password: ${HF_KAFKA_SASL_PASSWORD}
    topics: [ '${INPUT_TOPIC_NAME}' ]
    consumer_group: csku-inventory-forecast.${APP_NAME}.${CALCULATOR_MODE}.v${APP_VERSION}
    client_id: ${APP_NAME}
    target_version: 2.6.0
    start_from_oldest: true
    checkpoint_limit: 1
    batching:
      count: 1500
      period: 100ms

pipeline:
  processors:
    - resource: decode_topic_record
    - catch:
        - resource: log_failed_message_decode
        - bloblang: 'root = deleted()'

output:
  resource: persist_to_${CALCULATOR_MODE}_table
