@file:Suppress("TooManyFunctions")

package com.hellofresh.dateUtil.models

import com.google.protobuf.Timestamp
import com.google.protobuf.util.Timestamps
import com.google.type.DateTime
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.time.ZonedDateTime

fun Timestamp.toOffsetDateTime(): OffsetDateTime = Instant.ofEpochSecond(
    seconds,
    nanos.toLong(),
).atOffset(UTC)

fun Timestamp.toOffsetDateTime(zoneId: ZoneId): OffsetDateTime =
    this.toOffsetDateTime().atZoneSameInstant(zoneId).toOffsetDateTime()

fun Timestamp.toNullableOffSetDateTime() =
    if (this.isNotDefaultValues()) this.toOffsetDateTime() else null

fun Timestamp.toNullableOffSetDateTime(zoneId: ZoneId) =
    this.toNullableOffSetDateTime()?.atZoneSameInstant(zoneId)?.toOffsetDateTime()

/**
 * When Timestamp is sent in local dc time
 */
fun Timestamp.toNullableOffSetDateTimeFromLocalTime(zoneId: ZoneId) =
    if (this.isNotDefaultValues()) this.toOffsetDateTimeFromLocalTime(zoneId) else null

fun Timestamp.toOffsetDateTimeFromLocalTime(zoneId: ZoneId): OffsetDateTime =
    LocalDateTime.ofEpochSecond(seconds, nanos, UTC).atZone(zoneId).toOffsetDateTime()

fun Timestamp.isNotDefaultValues() =
    this != Timestamp.getDefaultInstance() &&
        this != Timestamps.MIN_VALUE &&
        this != Timestamps.MAX_VALUE &&
        this != Timestamps.EPOCH

fun DateTime.toNullableLocalDate() =
    if (this.isNotDefaultValues()) this.toLocalDate() else null

fun DateTime.toNullableLocalDate(zoneId: ZoneId) =
    if (this.isNotDefaultValues()) this.toLocalDate(zoneId) else null

fun DateTime.toLocalDate() = LocalDate.of(year, month, day)

// https://github.com/googleapis/googleapis/blob/master/google/type/datetime.proto
fun DateTime.toLocalDate(zoneId: ZoneId) = ZonedDateTime.of(
    year,
    month,
    day,
    hours,
    minutes,
    seconds,
    nanos,
    UTC,
).withZoneSameInstant(zoneId).toLocalDate()

fun DateTime.isNotDefaultValues() = this != DateTime.getDefaultInstance()

fun OffsetDateTime.toProtoTimestamp(): Timestamp = Timestamp.newBuilder().apply {
    val utcTime = this@toProtoTimestamp
    nanos = utcTime.nano
    seconds = utcTime.toEpochSecond()
}.build()

fun DateTime.toNullableOffsetDateTime(): OffsetDateTime? = if (this == DateTime.getDefaultInstance()) {
    null
} else {
    ZonedDateTime.of(
        this.year,
        this.month,
        this.day,
        this.hours,
        this.minutes,
        this.seconds,
        this.nanos,
        ZoneId.of(this.timeZone.id),
    ).toOffsetDateTime()
}
