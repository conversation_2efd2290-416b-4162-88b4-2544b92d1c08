# suppress inspection "UnusedProperty" for whole file
name=csku-inventory-forecast
group=com.hellofresh.cif

org.gradle.configureondemand=true
org.gradle.parallel=true
org.gradle.caching=true

# Digest from 2022-02-08 (`docker pull gcr.io/distroless/java21:latest`)
jib.from.image=gcr.io/distroless/java21@sha256:fe0560fbf87031402f6c725917eabbf9217b5e9fc95fdff9a004dba3587a0513
jib.from.credHelper=gcr
jib.to.repository=489198589229.dkr.ecr.eu-west-1.amazonaws.com/csku-inventory-forecast

jib.container.jvmFlags=\
  -XX:InitialRAMPercentage=50.0 \
  -XX:MaxRAMPercentage=75.0 \
  -Dcom.sun.management.jmxremote=false \
  -Xshare:off \
  -Dlogging.layout=HelloFresh \
  -Dlogging.slack.level=Error

gradle.javaExec.jvmFlags=\
  -XX:MaxRAMPercentage=75.0 \
  -Dcom.sun.management.jmxremote=false \
  -Xshare:off

kotlin.daemon.jvmargs=-Xmx2g

org.gradle.jvmargs=\
  -XX:MaxRAMPercentage=75.0 \
  -Xmx3072m \
  -XX:MaxMetaspaceSize=512m
