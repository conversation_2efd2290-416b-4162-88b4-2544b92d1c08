package com.hellofresh.inventory.models

import com.hellofresh.cif.featureflags.Context.CATEGORY
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.NoAclUnusableInventory
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_PRODUCTION
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import java.math.BigDecimal
import java.time.LocalDate
import java.util.stream.Stream
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

private const val DEFAULT_DC_CODE = "dc"
private const val DEFAULT_SKU_CATEGORY = "cat"

class UsableInventoryEvaluatorTest {

    private val usableInventoryEvaluator = UsableInventoryEvaluator(
        StatsigTestFeatureFlagClient(emptySet()),
    )

    @Test
    fun `inventory is usable when no expiry date exists`() {
        // when

        with(
            usableInventoryEvaluator.isUsable(
                DEFAULT_DC_CODE,
                LocalDate.now().minusDays(10),
                LOCATION_TYPE_STORAGE,
                null,
                0,
                DEFAULT_SKU_CATEGORY,
            ),
        ) {
            assertTrue(this.usable)
            assertNull(this.unusableReason)
        }

        with(
            usableInventoryEvaluator.isUsable(
                DEFAULT_DC_CODE,
                LocalDate.now().minusDays(10),
                LOCATION_TYPE_QUARANTINE,
                null,
                0,
                DEFAULT_SKU_CATEGORY,
            ),
        ) {
            assertFalse(this.usable)
            assertNotNull(this.unusableReason)
        }

        with(
            usableInventoryEvaluator.isUsable(
                DEFAULT_DC_CODE,
                LocalDate.now().minusDays(10),
                LOCATION_TYPE_PRODUCTION,
                null,
                0,
                DEFAULT_SKU_CATEGORY,
            ),
        ) {
            assertTrue(this.usable)
            assertNull(this.unusableReason)
        }
        with(
            usableInventoryEvaluator.isUsable(
                DEFAULT_DC_CODE,
                LocalDate.now().minusDays(10),
                LOCATION_TYPE_QUARANTINE,
                null,
                0,
                DEFAULT_SKU_CATEGORY,
            ),
        ) {
            assertFalse(this.usable)
            assertNotNull(this.unusableReason)
        }
    }

    @ParameterizedTest
    @MethodSource("provideUsableExpiry")
    fun `should return the correct behaviour when state is Usable and not Expired for live`(
        locationType: LocationType,
        targetDate: LocalDate,
        acceptableCodeLife: Int,
        usable: Boolean
    ) {
        // when
        val liveInventory = Inventory(
            qty = SkuQuantity.fromBigDecimal(BigDecimal(50)),
            expiryDate = LocalDate.now().plusDays(5),
            location = Location("", locationType, null),
        )

        val usabilityDetails = usableInventoryEvaluator.isUsable(
            DEFAULT_DC_CODE,
            targetDate,
            liveInventory,
            acceptableCodeLife,
            DEFAULT_SKU_CATEGORY,
        )
        assertEquals(usable, usabilityDetails.usable)

        if (usable) {
            assertNull(usabilityDetails.unusableReason)
        } else {
            assertNotNull(usabilityDetails.unusableReason)
        }
    }

    @ParameterizedTest
    @MethodSource("aclFlagUsableExpiry")
    fun `should no use acl rule when flag is enabled`(
        acceptableCodeLife: Int,
        dc: String,
        skuCategory: String,
        defaultUsable: Boolean,
        flagUsable: Boolean
    ) {
        val newUsableInventoryEvaluator = UsableInventoryEvaluator(
            StatsigTestFeatureFlagClient(
                setOf(
                    NoAclUnusableInventory(
                        setOf(ContextData(DC, DEFAULT_DC_CODE), ContextData(CATEGORY, DEFAULT_SKU_CATEGORY)),
                    ),
                ),
            ),
        )
        val today = LocalDate.now()
        val expiryDate = today.plusDays(5)
        assertEquals(
            defaultUsable,
            usableInventoryEvaluator.isUsable(
                dc,
                today,
                LOCATION_TYPE_STORAGE,
                expiryDate,
                acceptableCodeLife,
                skuCategory,
            ).usable,
        )
        assertEquals(
            flagUsable,
            newUsableInventoryEvaluator.isUsable(
                dc,
                today,
                LOCATION_TYPE_STORAGE,
                expiryDate,
                acceptableCodeLife,
                skuCategory,
            ).usable,
        )
    }

    companion object {

        @JvmStatic
        fun provideUsableExpiry(): Stream<Arguments> = Stream.of(
            Arguments.of(LocationType.LOCATION_TYPE_STAGING, LocalDate.now(), 0, true),
            Arguments.of(LocationType.LOCATION_TYPE_STORAGE, LocalDate.now().plusDays(-10), 4, true),
            Arguments.of(LocationType.LOCATION_TYPE_STAGING, LocalDate.now().plusDays(10), 5, false),
            Arguments.of(LocationType.LOCATION_TYPE_UNSPECIFIED, LocalDate.now().plusDays(4), 5, false),
            Arguments.of(LocationType.LOCATION_TYPE_UNSPECIFIED, LocalDate.now().plusDays(4), 5, false),
            Arguments.of(LocationType.LOCATION_TYPE_RECEIVING_ERROR, LocalDate.now().plusDays(-9), 10, false),
            Arguments.of(LocationType.LOCATION_TYPE_QUARANTINE, LocalDate.now(), 0, false),
            Arguments.of(LocationType.LOCATION_TYPE_DONATIONS, LocalDate.now(), 0, false),
        )

        @JvmStatic
        fun aclFlagUsableExpiry(): Stream<Arguments> = Stream.of(
            Arguments.of(0, DEFAULT_DC_CODE, DEFAULT_SKU_CATEGORY, true, true),
            Arguments.of(4, DEFAULT_DC_CODE, DEFAULT_SKU_CATEGORY, true, true),
            Arguments.of(5, DEFAULT_DC_CODE, DEFAULT_SKU_CATEGORY, true, true),
            Arguments.of(6, DEFAULT_DC_CODE, DEFAULT_SKU_CATEGORY, false, true),
            Arguments.of(10, "DC2", DEFAULT_SKU_CATEGORY, false, false),
            Arguments.of(10, DEFAULT_DC_CODE, "CAT2", false, false),
            Arguments.of(0, "DC3", "CAT4", true, true),
        )
    }
}
