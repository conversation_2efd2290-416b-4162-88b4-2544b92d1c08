package com.hellofresh.inventory.models.inbounds

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_FCMS
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_UNSPECIFIED
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_WMS_LITE
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.inventory.models.CleardownData
import com.hellofresh.inventory.models.CleardownMode.SCHEDULED
import com.hellofresh.inventory.models.InventoryAdjustment
import com.hellofresh.inventory.models.InventoryAdjustmentTypeId
import com.hellofresh.inventory.models.InventoryMovement
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.default
import com.hellofresh.inventory.models.inbounds.InventorySnapshotInbounds.Companion.InboundSnapshotState
import com.hellofresh.inventory.models.inbounds.InventorySnapshotInbounds.Companion.inventorySnapshotInbounds
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.assertFailsWith
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class InventorySnapshotInboundsTest {

    private val defaultInventorySnapshot = InventorySnapshot.default()
    private val defaultSkuId = defaultInventorySnapshot.skus.first().skuId
    private val defaultDc = DistributionCenterConfiguration.Companion.default(dcCode = defaultInventorySnapshot.dcCode)
    private val defaultPoNumber = UUID.randomUUID().toString()
    private val defaultActivityTime = defaultInventorySnapshot.snapshotTime.minusHours(1).atOffset(ZoneOffset.UTC)
    private val defaultFcmsInboundedInventoryActivity = InventoryAdjustment.default().copy(
        skuId = defaultSkuId,
        dcCode = defaultDc.dcCode,
        activityTime = defaultInventorySnapshot.snapshotTime.minusHours(1).atOffset(ZoneOffset.UTC),
        poNumber = defaultPoNumber,
        typeId = UUID.randomUUID().toString(),
    )

    @Test
    fun `inbound state is unknown when dc wms system is not supported`() {
        assertEquals(
            InventorySnapshotInbounds(
                defaultInventorySnapshot,
                emptyList(),
            ).isPurchaseOrderInbounded(
                defaultDc.copy(
                    wmsType = WMS_SYSTEM_UNSPECIFIED,
                ),
                defaultSkuId,
                "po",
            ),
            InboundSnapshotState.UNKNOWN,
        )
    }

    @ParameterizedTest
    @EnumSource(
        value = (WmsSystem::class),
        mode = EnumSource.Mode.INCLUDE,
        names = ["WMS_SYSTEM_WMS_LITE", "WMS_SYSTEM_FCMS"],
    )
    fun `po is not inbounded when there are no movements`(wmsSystem: WmsSystem) {
        assertEquals(
            InventorySnapshotInbounds(
                defaultInventorySnapshot,
                emptyList(),
            ).isPurchaseOrderInbounded(
                defaultDc.copy(
                    wmsType = wmsSystem,
                ),
                defaultSkuId,
                "po",
            ),
            InboundSnapshotState.NON_INBOUNDED,
        )
    }

    @ParameterizedTest
    @EnumSource(
        value = InventoryAdjustmentTypeId::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["USP", "REJ"],
    )
    fun `po is inbounded for fmcs system if valid event is found`(typeId: InventoryAdjustmentTypeId) {
        assertEquals(
            inventorySnapshotInbounds(
                CleardownData(
                    defaultDc.dcCode,
                    defaultInventorySnapshot.snapshotTime.plusHours(1),
                    SCHEDULED,
                    defaultInventorySnapshot,
                ),
                listOf(
                    defaultFcmsInboundedInventoryActivity.copy(typeId = typeId.name),
                ),
            ).isPurchaseOrderInbounded(
                defaultDc.copy(
                    wmsType = WMS_SYSTEM_FCMS,
                ),
                defaultSkuId,
                defaultPoNumber,
            ),
            InboundSnapshotState.INBOUNDED,
        )
    }

    @ParameterizedTest
    @EnumSource(
        value = InventoryAdjustmentTypeId::class,
        mode = EnumSource.Mode.INCLUDE,
        names = ["USP", "REJ"],
    )
    fun `po is inbounded when there is no snapshot for fmcs`(typeId: InventoryAdjustmentTypeId) {
        assertEquals(
            inventorySnapshotInbounds(
                CleardownData(
                    defaultDc.dcCode,
                    defaultActivityTime.plusHours(1).toLocalDateTime(),
                    SCHEDULED,
                    null,
                ),
                listOf(
                    defaultFcmsInboundedInventoryActivity.copy(typeId = typeId.name),
                ),
            ).isPurchaseOrderInbounded(
                defaultDc.copy(
                    wmsType = WMS_SYSTEM_FCMS,
                ),
                defaultSkuId,
                defaultPoNumber,
            ),
            InboundSnapshotState.INBOUNDED,
        )
    }

    @Test
    fun `snapshot time is used when snapshot exists`() {
        val cleardownData = CleardownData(
            defaultDc.dcCode,
            defaultActivityTime.plusHours(1).toLocalDateTime(),
            SCHEDULED,
            defaultInventorySnapshot,
        )
        with(inventorySnapshotInbounds(cleardownData, emptyList())) {
            assertEquals(
                InboundRange(cleardownData.snapshot!!.snapshotTime),
                inboundsRange,
            )
        }
    }

    @Test
    fun `cleardown time is used when snapshot doesn't exist`() {
        val cleardownData = CleardownData(
            defaultDc.dcCode,
            defaultActivityTime.plusHours(1).toLocalDateTime(),
            SCHEDULED,
            snapshot = null,
        )
        with(inventorySnapshotInbounds(cleardownData, emptyList())) {
            assertEquals(
                InboundRange(cleardownData.cleardownTime),
                inboundsRange,
            )
        }
    }

    @Test
    fun `po is not inbounded for fmcs system if no valid event type is found`() {
        assertEquals(
            InventorySnapshotInbounds(
                defaultInventorySnapshot,
                listOf(defaultFcmsInboundedInventoryActivity.copy(typeId = UUID.randomUUID().toString())),
            ).isPurchaseOrderInbounded(
                defaultDc.copy(
                    wmsType = WMS_SYSTEM_FCMS,
                ),
                defaultSkuId,
                defaultPoNumber,
            ),
            InboundSnapshotState.NON_INBOUNDED,
        )
    }

    @Test
    fun `po is not inbounded if no event for sku is found`() {
        assertEquals(
            InventorySnapshotInbounds(
                defaultInventorySnapshot,
                listOf(defaultFcmsInboundedInventoryActivity.copy(skuId = UUID.randomUUID())),
            ).isPurchaseOrderInbounded(
                defaultDc.copy(
                    wmsType = WMS_SYSTEM_FCMS,
                ),
                defaultSkuId,
                defaultPoNumber,
            ),
            InboundSnapshotState.NON_INBOUNDED,
        )
    }

    @Test
    fun `po is not inbounded if no event for po number is found`() {
        assertEquals(
            InventorySnapshotInbounds(
                defaultInventorySnapshot,
                listOf(defaultFcmsInboundedInventoryActivity.copy(poNumber = UUID.randomUUID().toString())),
            ).isPurchaseOrderInbounded(
                defaultDc.copy(
                    wmsType = WMS_SYSTEM_FCMS,
                ),
                defaultSkuId,
                defaultPoNumber,
            ),
            InboundSnapshotState.NON_INBOUNDED,
        )
    }

    @Test
    fun `given distribution center must match inventory snapshot dc`() {
        assertFailsWith(IllegalArgumentException::class) {
            InventorySnapshotInbounds(
                defaultInventorySnapshot,
                listOf(defaultFcmsInboundedInventoryActivity),
            ).isPurchaseOrderInbounded(
                defaultDc.copy(
                    dcCode = UUID.randomUUID().toString(),
                    wmsType = WMS_SYSTEM_FCMS,
                ),
                defaultSkuId,
                defaultPoNumber,
            )
        }
    }

    @Test
    fun `po is not inbounded if no events in time range`() {
        assertEquals(
            InventorySnapshotInbounds(
                defaultInventorySnapshot,
                listOf(
                    defaultFcmsInboundedInventoryActivity.copy(
                        activityTime = defaultInventorySnapshot.snapshotTime.plusHours(1).atOffset(ZoneOffset.UTC),
                    ),
                    defaultFcmsInboundedInventoryActivity.copy(
                        activityTime = defaultInventorySnapshot.snapshotTime.minusDays(2).atOffset(ZoneOffset.UTC),
                    ),
                ),
            ).isPurchaseOrderInbounded(
                defaultDc.copy(
                    wmsType = WMS_SYSTEM_FCMS,
                ),
                defaultSkuId,
                defaultPoNumber,
            ),
            InboundSnapshotState.NON_INBOUNDED,
        )
    }

    @Test
    fun `po is inbounded for wms system if valid event types are found`() {
        assertEquals(
            InventorySnapshotInbounds(
                defaultInventorySnapshot,
                listOf(
                    InventoryMovement.default().copy(
                        skuId = defaultSkuId,
                        dcCode = defaultDc.dcCode,
                        activityTime = defaultActivityTime.plusMinutes(10),
                        poNumber = defaultPoNumber,
                    ),
                    InventoryAdjustment.default().copy(
                        skuId = defaultSkuId,
                        dcCode = defaultDc.dcCode,
                        activityTime = defaultActivityTime,
                        poNumber = defaultPoNumber,
                        typeId = InventoryAdjustmentTypeId.RCV.name,
                    ),
                ),
            ).isPurchaseOrderInbounded(
                defaultDc.copy(
                    wmsType = WMS_SYSTEM_WMS_LITE,
                ),
                defaultSkuId,
                defaultPoNumber,
            ),
            InboundSnapshotState.INBOUNDED,
        )
    }

    @Test
    fun `po is not inbounded for wms system if no RCV event is found`() {
        assertEquals(
            InventorySnapshotInbounds(
                defaultInventorySnapshot,
                listOf(
                    InventoryAdjustment.default().copy(
                        skuId = defaultSkuId,
                        dcCode = defaultDc.dcCode,
                        activityTime = defaultActivityTime,
                        poNumber = defaultPoNumber,
                        typeId = UUID.randomUUID().toString(),
                    ),
                    InventoryMovement.default().copy(
                        skuId = defaultSkuId,
                        dcCode = defaultDc.dcCode,
                        activityTime = defaultActivityTime,
                        poNumber = defaultPoNumber,
                    ),
                ),
            ).isPurchaseOrderInbounded(
                defaultDc.copy(
                    wmsType = WMS_SYSTEM_WMS_LITE,
                ),
                defaultSkuId,
                defaultPoNumber,
            ),
            InboundSnapshotState.NON_INBOUNDED,
        )
    }

    @Test
    fun `po is not inbounded for wms system if no movement is found`() {
        assertEquals(
            InventorySnapshotInbounds(
                defaultInventorySnapshot,
                listOf(
                    InventoryAdjustment.default().copy(
                        skuId = defaultSkuId,
                        dcCode = defaultDc.dcCode,
                        activityTime = defaultActivityTime,
                        poNumber = defaultPoNumber,
                        typeId = InventoryAdjustmentTypeId.RCV.toString(),
                    ),
                ),
            ).isPurchaseOrderInbounded(
                defaultDc.copy(
                    wmsType = WMS_SYSTEM_WMS_LITE,
                ),
                defaultSkuId,
                defaultPoNumber,
            ),
            InboundSnapshotState.NON_INBOUNDED,
        )
    }
}
