# KEEP ALPHABETICALLY SORTED TO AID HUMANS IN SEARCHING!
[versions]
apache-httpcomponents = '4.5.14'
jetbrains-annotations = '26.0.1'
kotlin = "2.0.0"
aws-java-sdk = '2.31.28'
caffeine = '3.1.6'
flyway = "9.16.3"
hellofresh-libs = "0.6.2"
jackson = '2.19.0'
junit = '5.12.2'
kafka = '3.9.1'
detekt = "1.23.7"
java = "21"
kafka-avro-serializer = '7.7.2'
krontab = '0.8.5'
protobuf = '4.31.1'
grpc = '2.57.0'
ktor = '3.1.2'
kotlinx-version = '1.9.0'
prometheus = '0.16.0'
micrometer = '1.11.2'
resilience4j = '2.3.0'
restassured = '5.5.1'
testcontainers = "1.20.1"
slf4j = "2.0.7"
log4j = "2.25.0"
mockk-core = "1.13.9"
schema-registry = "0.1.2674"
statsig = '1.17.3'
jib = "3.4.0"
jooq = "3.19.8"

[libraries]
apache-commonscsv = { module = 'org.apache.commons:commons-csv', version = '1.10.0' }
apache-parquet-avro = { module = 'org.apache.parquet:parquet-avro', version = '1.15.2' }
apache-avro-avro = { module = 'org.apache.avro:avro', version = '1.12.0' }
apache-hadoop-common = { module = 'org.apache.hadoop:hadoop-common', version = '3.4.1' }
apache-hadoop-client = { module = 'org.apache.hadoop:hadoop-client', version = '3.4.1' }
apache-commons-math = { module = 'org.apache.commons:commons-math3', version = '3.6.1' }
apache-httpclient = { module = 'org.apache.httpcomponents.client5:httpclient5', version = '5.2.1' }
apache-httpcomponents-httpclient = { module = 'org.apache.httpcomponents:httpclient', version.ref = 'apache-httpcomponents' }
awaitility = { module = 'org.awaitility:awaitility', version = '4.2.0' }
aws-java-sdk-s3 = { module = 'software.amazon.awssdk:s3', version.ref = 'aws-java-sdk' }
aws-java-sdk-sts = { module = 'software.amazon.awssdk:sts', version.ref = 'aws-java-sdk' }
aws-java-sdk-sqs = { module = "software.amazon.awssdk:sqs", version.ref = "aws-java-sdk" }
caffeine-core = { module = 'com.github.ben-manes.caffeine:caffeine', version.ref = 'caffeine' }
coroutines-core = { module = 'org.jetbrains.kotlinx:kotlinx-coroutines-core', version.ref = 'kotlinx-version' }
coroutines-jdk8 = { module = 'org.jetbrains.kotlinx:kotlinx-coroutines-jdk8', version.ref = 'kotlinx-version' }
coroutines-test = { module = 'org.jetbrains.kotlinx:kotlinx-coroutines-test', version.ref = 'kotlinx-version' }
cronutils = { module = 'com.cronutils:cron-utils', version = '9.2.1' }
flyway-core = { module = "org.flywaydb:flyway-core", version.ref = "flyway" }
hellofresh-logging = { module = "com.hellofresh:logging", version.ref = "hellofresh-libs" }
hellofresh-schemaregistry = { module = "com.hellofresh:schema-registry", version.ref = "schema-registry" }
hellofresh-service = { module = "com.hellofresh:service", version.ref = "hellofresh-libs" }
hikaricp = { module = 'com.zaxxer:HikariCP', version = '5.0.1' }
jackson-cbor = { module = 'com.fasterxml.jackson.dataformat:jackson-dataformat-cbor', version.ref = 'jackson' }
jackson-databind = { module = 'com.fasterxml.jackson.core:jackson-databind', version.ref = 'jackson' }
jackson-jsr310 = { module = 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310', version.ref = 'jackson' }
jackson-kotlin = { module = 'com.fasterxml.jackson.module:jackson-module-kotlin', version.ref = 'jackson' }
jooq-core = { module = 'org.jooq:jooq', version.ref = 'jooq' }
jgit = { module = 'org.eclipse.jgit:org.eclipse.jgit', version = '6.7.0.202309050840-r' }
junit-params = { module = 'org.junit.jupiter:junit-jupiter-params', version.ref = 'junit' }
junit-jupiter = { module = 'org.junit.jupiter:junit-jupiter', version.ref = 'junit' }
junit-engine = { module = 'org.junit.jupiter:junit-jupiter-engine', version.ref = 'junit' }
junit-api = { module = 'org.junit.jupiter:junit-jupiter-api', version.ref = 'junit' }
junit-bom = { module = 'org.junit:junit-bom', version.ref = 'junit' }
kafka-avro-serializer = { module = 'io.confluent:kafka-avro-serializer', version.ref = 'kafka-avro-serializer' }
jetbrains-annotations = { module = 'org.jetbrains:annotations', version.ref = 'jetbrains-annotations' }
kafka-schema-registry-client = { module = 'io.confluent:kafka-schema-registry-client', version.ref = 'kafka-avro-serializer' }
kafka-clients = { module = 'org.apache.kafka:kafka-clients', version.ref = 'kafka' }
kafka-kafka213 = { module = 'org.apache.kafka:kafka_2.13', version.ref = 'kafka' }
krontab = { module = 'dev.inmo:krontab', version = '0.7.1' }
ktor-client-cio = { module = 'io.ktor:ktor-client-cio', version.ref = 'ktor' }
ktor-client-encoding = { module = 'io.ktor:ktor-client-encoding', version.ref = 'ktor' }
ktor-content-negotiation = { module = 'io.ktor:ktor-server-content-negotiation', version.ref = 'ktor' }
ktor-core = { module = 'io.ktor:ktor-server-core', version.ref = 'ktor' }
ktor-server-auth-jwt = { module = 'io.ktor:ktor-server-auth-jwt', version.ref = 'ktor' }
ktor-jackson = { module = 'io.ktor:ktor-serialization-jackson', version.ref = 'ktor' }
ktor-micrometer = { module = 'io.ktor:ktor-server-metrics-micrometer', version.ref = 'ktor' }
ktor-netty = { module = 'io.ktor:ktor-server-netty', version.ref = 'ktor' }
ktor-server-callid = { module = 'io.ktor:ktor-server-call-id', version.ref = 'ktor' }
ktor-server-compression = { module = 'io.ktor:ktor-server-compression-jvm', version.ref = 'ktor' }
ktor-test = { module = 'io.ktor:ktor-server-test-host', version.ref = 'ktor' }
kotlin-test = { module = 'org.jetbrains.kotlin:kotlin-test', version.ref = 'kotlinx-version' }
log4j-api2 = { module = "org.apache.logging.log4j:log4j-api", version.ref = "log4j" }
log4j-bom = { module = "org.apache.logging.log4j:log4j-bom", version.ref = "log4j" }
log4j-core = { module = "org.apache.logging.log4j:log4j-core", version.ref = "log4j" }
log4j-json = { module = "org.apache.logging.log4j:log4j-layout-template-json" }
log4j-kotlin = { module = "org.apache.logging.log4j:log4j-api-kotlin", version = "1.5.0" }
log4j-slf4j = { module = "org.apache.logging.log4j:log4j-slf4j-impl" }
micrometer-core = { module = 'io.micrometer:micrometer-core', version.ref = 'micrometer' }
micrometer-registry-prometheus = { module = "io.micrometer:micrometer-registry-prometheus", version.ref = 'micrometer' }
prometheus-pushgateway = { module = 'io.prometheus:simpleclient_pushgateway', version.ref = 'prometheus' }
mockk = { module = 'io.mockk:mockk-jvm', version.ref = 'mockk-core' }
okhttp = { module = "com.squareup.okhttp3:okhttp", version = "4.11.0" }
postgresql-driver = { module = 'org.postgresql:postgresql', version = '42.7.7' }
protobuf-java-util = { module = 'com.google.protobuf:protobuf-java-util', version.ref = 'protobuf' }
protobuf-java = { module = 'com.google.protobuf:protobuf-java', version.ref = 'protobuf' }
protobuf-grpc = { module = 'com.google.api.grpc:proto-google-common-protos', version.ref = 'grpc' }
gson = { module = 'com.google.code.gson:gson', version = '2.13.1' }
resilience4j-kotlin = { module = 'io.github.resilience4j:resilience4j-kotlin', version.ref = 'resilience4j' }
resilience4j-retry = { module = 'io.github.resilience4j:resilience4j-retry', version.ref = 'resilience4j' }
resilience4j-timelimiter = { module = 'io.github.resilience4j:resilience4j-timelimiter', version.ref = 'resilience4j' }
restassured-core = { module = 'io.rest-assured:rest-assured', version.ref = 'restassured' }
restassured-validator = { module = 'io.rest-assured:json-schema-validator', version.ref = 'restassured' }
slack-appender = "be.olsson:slack-appender:1.3.0"
statsig = { module = 'com.statsig:serversdk', version.ref = 'statsig' }
detekt-gradle = { module = "io.gitlab.arturbosch.detekt:detekt-gradle-plugin", version.ref = "detekt" }
detekt-formatting = { module = "io.gitlab.arturbosch.detekt:detekt-formatting", version.ref = "detekt" }
detekt-cli = { module = "io.gitlab.arturbosch.detekt:detekt-cli", version.ref = "detekt" }
slf4j-api = { module = "org.slf4j:slf4j-api", version.ref = "slf4j" }
slf4j-simple = { module = 'org.slf4j:slf4j-simple', version.ref = "slf4j" }
testcontainers-core = { module = "org.testcontainers:testcontainers", version.ref = "testcontainers" }
testcontainers-junit = { module = 'org.testcontainers:junit-jupiter', version.ref = "testcontainers" }
testcontainers-kafka = { module = "org.testcontainers:kafka", version.ref = "testcontainers" }
kotlin-compiler-embeddable = { module = "org.jetbrains.kotlin:kotlin-compiler-embeddable", version.ref = "kotlin" }
testcontainers-postgresql = { module = "org.testcontainers:postgresql", version.ref = "testcontainers" }
apacheconfig = { module = "commons-configuration:commons-configuration", version = "1.10" }
# buildSrc dependencies (plugins)
kotlin-allopen = { module = "org.jetbrains.kotlin:kotlin-allopen", version.ref = "kotlin" }
kotlin-noarg = { module = "org.jetbrains.kotlin:kotlin-noarg", version.ref = "kotlin" }
kotlin-gradle = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }
jackson-yaml = { module = "com.fasterxml.jackson.dataformat:jackson-dataformat-yaml", version.ref = "jackson" }
jib-gradle = { module = "com.google.cloud.tools:jib-gradle-plugin", version.ref = "jib" }

[plugins]
gradle-docker-compose = { id = "com.avast.gradle.docker-compose", version = "0.17.4" }
# gradle-docker = { id = "com.palantir.docker", version = "0.35.0" }
jacoco = { id = "org.barfuin.gradle.jacocolog", version = "3.1.0" }
jooq = { id = "nu.studer.jooq", version = "8.2.1" }
openapi = { id = 'org.openapi.generator', version = '7.9.0' }
shadow = { id = "com.github.johnrengelman.shadow", version = "8.1.1" }
sonar = { id = "org.sonarqube", version = "4.4.1.3373" }
detekt = { id = "io.gitlab.arturbosch.detekt", version.ref = "detekt" }
flyway = { id = "org.flywaydb.flyway", version.ref = "flyway" }

[bundles]
main-compileOnly = ['jetbrains-annotations']
test-implementation = ['junit-jupiter', 'junit-params', 'junit-api', 'mockk']
test-runtime = ['junit-engine']
