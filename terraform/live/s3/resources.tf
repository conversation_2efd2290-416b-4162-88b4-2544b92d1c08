import {
    to =  aws_s3_bucket.cif-stock-files-export-live
    id = "cif-stock-files-export-live"
}

resource "aws_s3_bucket" "cif-stock-files-export-live" {
  bucket = "cif-stock-files-export-live"
  tags = {
    Name        = "csv export files"
    Environment = "live"
    Squad       = "supply-automation"
    Tribe       = "planning-and-purchasing"
    CostGroup   = "Global Core"
  }
}

resource "aws_s3_bucket_policy" "cif-stock-files-export-live-policy" {
    bucket = aws_s3_bucket.cif-stock-files-export-live.id
    policy = jsonencode({
        Version = "2012-10-17"
        Statement = [
            {
                Sid = "AllowReadAccessToCIFRole"
                Effect = "Allow"
                Principal = {
                    AWS = "arn:aws:iam::489198589229:role/csku-inventory-forecast-live-role"
                }
                Action = [
                    "s3:Get*",
                    "s3:List*"
                ]
                Resource = [
                    "${aws_s3_bucket.cif-stock-files-export-live.arn}",
                    "${aws_s3_bucket.cif-stock-files-export-live.arn}/*",
                ]
            },

        ]
    })
}

resource "aws_s3_bucket_notification" "cif-stock-files-export-live" {
    bucket = aws_s3_bucket.cif-stock-files-export-live.id

    eventbridge = true

    queue {
        queue_arn     = "arn:aws:sqs:eu-west-1:489198589229:supply_automation_3pw_inventory_notification_live"
        events        = ["s3:ObjectCreated:*"]
        filter_prefix = "third_pw_stock_inventory_uploads/"
        filter_suffix = ".csv"
    }

    queue {
      queue_arn     = "arn:aws:sqs:eu-west-1:489198589229:supply_automation_stock_update_live"
      events        = ["s3:ObjectCreated:*"]
      filter_prefix = "stock_updates/"
      filter_suffix = ".csv"
    }
}


resource "aws_s3_bucket_cors_configuration" "cif-stock-files-export-cors-live" {
    bucket = aws_s3_bucket.cif-stock-files-export-live.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["PUT", "POST", "GET"]
    allowed_origins = ["*"]
  }
}

resource "aws_s3_bucket" "cif-packaging-import-files-live" {
  bucket = "cif-packaging-import-files-live"

  tags = {
    Name        = "Packaging import files"
    Environment = "Live"
    Squad       = "supply-automation"
    Tribe       = "planning-and-purchasing"
    CostGroup   = "Global Core"
  }
}

resource "aws_s3_bucket_policy" "cif-packaging-import-files-policy-live" {
  bucket = aws_s3_bucket.cif-packaging-import-files-live.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid = "AllowReadAccessToCIFRole"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::489198589229:role/csku-inventory-forecast-live-role"
        }
        Action = [
          "s3:Get*",
          "s3:List*"
        ]
        Resource = [
          aws_s3_bucket.cif-packaging-import-files-live.arn,
          "${aws_s3_bucket.cif-packaging-import-files-live.arn}/*",
        ]
      }
    ]

  })
}

resource "aws_sns_topic" "cif-packaging-import-files-topic-live" {
  name = "cif-packaging-import-files-topic-live"

  tags = {
    Name        = "Packaging import files topic"
    Environment = "Live"
    Squad       = "supply-automation"
    Tribe       = "planning-and-purchasing"
    CostGroup   = "Global Core"
  }
}

resource "aws_sns_topic_policy" "cif-packaging-import-files-topic-policy-live" {
  arn = aws_sns_topic.cif-packaging-import-files-topic-live.arn

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "s3.amazonaws.com"
        },
        Action = "SNS:Publish",
        Resource = aws_sns_topic.cif-packaging-import-files-topic-live.arn,
        Condition = {
          ArnLike = {
            "aws:SourceArn" = aws_s3_bucket.cif-packaging-import-files-live.arn
          }
        }
      }
    ]
  })
}

resource "aws_s3_bucket_notification" "cif-packaging-import-files-notification-live" {
  bucket = aws_s3_bucket.cif-packaging-import-files-live.id

  eventbridge = true

  topic {
    topic_arn     = aws_sns_topic.cif-packaging-import-files-topic-live.arn
    events        = ["s3:ObjectCreated:*"]
    filter_prefix = "sku_level_demands/"
    filter_suffix = ".csv"
  }

  depends_on = [aws_sns_topic_policy.cif-packaging-import-files-topic-policy-live]
}

resource "aws_sns_topic_subscription" "cif-packaging-import-files-sns-to-sqs-live" {
  topic_arn = aws_sns_topic.cif-packaging-import-files-topic-live.arn
  protocol  = "sqs"
  endpoint  = "arn:aws:sqs:eu-west-1:489198589229:cif_packaging_import_live"
}
