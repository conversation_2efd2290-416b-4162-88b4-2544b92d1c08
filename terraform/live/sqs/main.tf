
provider "aws" {
  region = "eu-west-1"
}

terraform {
  backend "s3" {
    bucket = "hf-terraform-live"
    key    = "infrastructure/ip_safety_stock_multiplier_file_upload_notifications_live.tfstate"
    region = "eu-west-1"
  }
}

resource "aws_sqs_queue" "ip_safety_stock_multiplier_file_upload_notifications_live" {
  name = "ip_safety_stock_multiplier_file_upload_notifications_live"

  visibility_timeout_seconds = 30
  message_retention_seconds  = 86400

   tags = {
      Name        = "ip safety stock multiplier file upload notifications SQS queue live"
      Environment = "live"
      Squad       = "supply-automation"
      Tribe       = "planning-and-purchasing"
      CostGroup   = "Global Core"
    }
}

// allow s3 in main-bi to send messages to this sqs queue
resource "aws_sqs_queue_policy" "ip_safety_stock_multiplier_file_upload_sqs_policy_live" {
  queue_url = aws_sqs_queue.ip_safety_stock_multiplier_file_upload_notifications_live.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid = "AllowS3BucketToSendMessage",
        Effect = "Allow",
        Principal = {
         Service = "s3.amazonaws.com"
        },
        Action = "SQS:SendMessage",
        Resource = aws_sqs_queue.ip_safety_stock_multiplier_file_upload_notifications_live.arn,
        Condition = {
          ArnEquals = {
            "aws:SourceArn": "arn:aws:s3:::hf-bi-dwh-uploader"
          }
        }
      },
      {
        Sid: "AllowCIFIAMRoleAccess",
        Effect: "Allow",
        Principal : {
            AWS : [
                  "arn:aws:iam::489198589229:role/csku-inventory-forecast-live-role",
                  "arn:aws:iam::985437859871:role/procurement-inventory-live-staging",
            ]
        },
        Action: "sqs:*",
        Resource: aws_sqs_queue.ip_safety_stock_multiplier_file_upload_notifications_live.arn
      }
    ]
  })
}

resource "aws_sqs_queue" "supply_automation_3pw_inventory_notification_live" {
    name = "supply_automation_3pw_inventory_notification_live"

    visibility_timeout_seconds = 30
    message_retention_seconds  = 86400

    tags = {
        Name        = "3pw stock inventory files notifications SQS queue live"
        Environment = "live"
        Squad       = "supply-automation"
        Tribe       = "planning-and-purchasing"
        CostGroup   = "Global Core"
    }
}

resource "aws_sqs_queue_policy" "supply_automation_3pw_inventory_notification_live" {
    queue_url = aws_sqs_queue.supply_automation_3pw_inventory_notification_live.id

    policy = jsonencode({
        Version = "2012-10-17",
        Statement = [
            {
                Sid = "AllowS3BucketToSendMessage",
                Effect = "Allow",
                Principal = {
                    Service = "s3.amazonaws.com"
                },
                Action = "SQS:SendMessage",
                Resource = aws_sqs_queue.supply_automation_3pw_inventory_notification_live.arn,
                Condition = {
                    ArnEquals = {
                        "aws:SourceArn": "arn:aws:s3:::cif-stock-files-export-live"
                    }
                }
            }, {
                Sid: "AllowCIFIAMRoleAccess",
                Effect: "Allow",
                Principal : {
                    AWS : "arn:aws:iam::489198589229:role/csku-inventory-forecast-live-role",
                },
                Action: "sqs:*",
                Resource: aws_sqs_queue.supply_automation_3pw_inventory_notification_live.arn
            }
        ]
    })
}

resource "aws_sqs_queue" "supply_automation_stock_update_live" {
  name = "supply_automation_stock_update_live"

  visibility_timeout_seconds = 30
  message_retention_seconds  = 86400

  tags = {
    Name        = "stock update files notifications SQS queue live"
    Environment = "live"
    Squad       = "supply-automation"
    Tribe       = "planning-and-purchasing"
    CostGroup   = "Global Core"
  }
}

resource "aws_sqs_queue_policy" "supply_automation_stock_update_policy_live" {
  queue_url = aws_sqs_queue.supply_automation_stock_update_live.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "AllowS3BucketToSendMessage",
        Effect = "Allow",
        Principal = {
          Service = "s3.amazonaws.com"
        },
        Action   = "SQS:SendMessage",
        Resource = aws_sqs_queue.supply_automation_stock_update_live.arn,
        Condition = {
          ArnEquals = {
            "aws:SourceArn" : "arn:aws:s3:::cif-stock-files-export-live"
          }
        }
        }, {
        Sid : "AllowCIFIAMRoleAccess",
        Effect : "Allow",
        Principal : {
          AWS : "arn:aws:iam::489198589229:role/csku-inventory-forecast-live-role",
        },
        Action : "sqs:*",
        Resource : aws_sqs_queue.supply_automation_stock_update_live.arn
      }
    ]
  })
}

resource "aws_sqs_queue" "cif_packaging_import_live" {
  name = "cif_packaging_import_live"

  visibility_timeout_seconds = 30
  message_retention_seconds  = 86400

  tags = {
    Name        = "Inventory packaging import queue live"
    Environment = "Live"
    Squad       = "supply-automation"
    Tribe       = "planning-and-purchasing"
    CostGroup   = "Global Core"
  }
}

resource "aws_sqs_queue_policy" "cif_packaging_import_policy_live" {
  queue_url = aws_sqs_queue.cif_packaging_import_live.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "AllowSNSPublish",
        Effect = "Allow",
        Principal: "*",
        Action   = "SQS:SendMessage",
        Resource = aws_sqs_queue.cif_packaging_import_live.arn,
        Condition = {
          ArnEquals = {
            "aws:SourceArn" : "arn:aws:sns:eu-west-1:489198589229:cif-packaging-import-files-topic-live"
          }
        }
      }, {
        Sid : "AllowCIFIAMRoleAccess",
        Effect : "Allow",
        Principal : {
          AWS : "arn:aws:iam::489198589229:role/csku-inventory-forecast-live-role",
        },
        Action : "sqs:*",
        Resource : aws_sqs_queue.cif_packaging_import_live.arn
      }
    ]
  })
}

