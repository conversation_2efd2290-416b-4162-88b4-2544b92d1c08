provider "postgresql" {
  host      = module.inventory-db.db_instance_address
  port      = module.inventory-db.db_instance_port
  username  = module.inventory-db.db_instance_username
  password  = module.inventory-db.db_instance_password
  superuser = false
}

resource "aws_db_parameter_group" "inventory_live_postgres_parameter_group" {
  name        = "inventory-postgres16-live"
  description = "Parameter group for rds instance"
  family      = local.family

  parameter {
    name         = "shared_preload_libraries"
    value        = "pg_cron,pg_stat_statements"
    apply_method = "pending-reboot"
  }

  parameter {
    name         = "cron.database_name"
    value        = "inventory"
    apply_method = "pending-reboot"
  }

  parameter {
    name  = "rds.log_retention_period"
    value = "1440"
  }

  parameter {
    name  = "log_min_error_statement"
    value = "error"
  }

  parameter {
    name  = "log_min_duration_statement"
    value = "2000" // 2s
  }
}
