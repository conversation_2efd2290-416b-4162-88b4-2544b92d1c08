locals {
  project_name                = "csku-inventory-forecast"
  db_identifier               = "inventory-db000-live"
  allocated_storage           = 300
  engine                      = "postgres"
  engine_version              = "16"
  major_engine_version        = "16"
  storage_encrypted           = false
  storage_type                = "gp3"
  port                        = "5432"
  db_subnet_group_name        = "default"
  create_db_parameter_group   = false
  create_db_option_group      = false
  parameter_group_name        = aws_db_parameter_group.inventory_live_postgres_parameter_group.name
  publicly_accessible         = false
  allow_major_version_upgrade = true
  auto_minor_version_upgrade  = true
  apply_immediately           = true
  copy_tags_to_snapshot       = true
  skip_final_snapshot         = true
  final_snapshot_identifier   = "inventory-db-live"
  maintenance_window          = "thu:00:20-thu:00:50"
  backup_window               = "03:40-04:10"
  subnet_ids                  = flatten([data.aws_subnet_ids.all.ids])
  vpc_security_group_ids      = [data.aws_security_group.db.id]
  deletion_protection         = true
  monitoring_interval         = 10
  monitoring_role_arn         = data.aws_iam_role.rds-monitoring.arn
  family                      = "postgres16"
  ca_cert_identifier          = "rds-ca-rsa2048-g1"
  iops                        = 3000

   tags                       = {
      Environment = "Live"
      Group       = "inventory-db"
      Squad       = "supply-automation"
      Tribe       = "planning-and-purchasing"
      CostGroup   = "Global Core"
  }
}

module "inventory-db" {
  source  = "terraform-aws-modules/rds/aws"
  version = "~> 3.0"

  identifier                      = local.db_identifier
  instance_class                  = "db.m6g.2xlarge"
  name                            = "inventory"
  username                        = data.vault_generic_secret.vault_rds_secrets.data["DB_USERNAME"]
  password                        = data.vault_generic_secret.vault_rds_secrets.data["DB_PASSWORD"]
  engine                          = local.engine
  engine_version                  = local.engine_version
  major_engine_version            = local.major_engine_version
  allow_major_version_upgrade     = local.allow_major_version_upgrade
  auto_minor_version_upgrade      = local.auto_minor_version_upgrade
  allocated_storage               = local.allocated_storage
  storage_encrypted               = local.storage_encrypted
  storage_type                    = local.storage_type
  create_db_parameter_group       = local.create_db_parameter_group
  create_db_option_group          = local.create_db_option_group
  parameter_group_name            = local.parameter_group_name
  port                            = local.port
  subnet_ids                      = local.subnet_ids
  iops                            = local.iops
  db_subnet_group_name            = local.db_subnet_group_name
  vpc_security_group_ids          = local.vpc_security_group_ids
  publicly_accessible             = local.publicly_accessible
  maintenance_window              = local.maintenance_window
  backup_window                   = local.backup_window
  backup_retention_period         = 7
  apply_immediately               = local.apply_immediately
  copy_tags_to_snapshot           = local.copy_tags_to_snapshot
  skip_final_snapshot             = local.skip_final_snapshot
  final_snapshot_identifier       = local.final_snapshot_identifier
  deletion_protection             = local.deletion_protection
  monitoring_interval             = local.monitoring_interval
  monitoring_role_arn             = local.monitoring_role_arn
  family                          = local.family
  tags                            = local.tags
  performance_insights_enabled    = true
  multi_az                        = true
  ca_cert_identifier              = local.ca_cert_identifier
}

module "inventory-db-replica" {
  source  = "terraform-aws-modules/rds/aws"
  version = "~> 3.0"

  identifier              = "inventory-replica-db001-live"
  replicate_source_db     = module.inventory-db.db_instance_id
  instance_class          = "db.m6g.2xlarge"
  create_db_subnet_group  = false
  backup_retention_period = 0

  # Username and password must not be set for replicas
  password = ""
  username = ""

  allocated_storage            = local.allocated_storage
  engine                       = local.engine
  engine_version               = local.engine_version
  major_engine_version         = local.major_engine_version
  port                         = local.port
  db_subnet_group_name         = local.db_subnet_group_name
  create_db_parameter_group    = local.create_db_parameter_group
  create_db_option_group       = local.create_db_option_group
  parameter_group_name         = aws_db_parameter_group.inventory-live-db-replica.name
  storage_type                 = local.storage_type
  allow_major_version_upgrade  = local.allow_major_version_upgrade
  apply_immediately            = local.apply_immediately
  copy_tags_to_snapshot        = local.copy_tags_to_snapshot
  maintenance_window           = local.maintenance_window
  backup_window                = local.backup_window
  subnet_ids                   = local.subnet_ids
  iops                         = local.iops
  vpc_security_group_ids       = local.vpc_security_group_ids
  publicly_accessible          = local.publicly_accessible
  deletion_protection          = local.deletion_protection
  monitoring_interval          = local.monitoring_interval
  monitoring_role_arn          = local.monitoring_role_arn
  family                       = local.family
  tags                         = local.tags
  performance_insights_enabled = true
  multi_az                     = true
  ca_cert_identifier           = local.ca_cert_identifier
}

resource "aws_db_parameter_group" "inventory-live-db-replica" {
  name        = "inventory-replica-db001-postgres16-live"
  family      = local.family
  description = "Postgres 16 parameter group with changes for increased replication delays"


  parameter {
    name         = "shared_preload_libraries"
    value        = "pg_stat_statements"
    apply_method = "pending-reboot"
  }

  parameter {
    name  = "max_standby_archive_delay"
    value = "100000"
  }

  parameter {
    name  = "max_standby_streaming_delay"
    value = "100000"
  }

  parameter {
    name  = "statement_timeout"
    value = "25000" # Set the timeout in milliseconds
  }

  tags = {
    Environment = "live"
    Group       = "inventory-db"
    Squad       = "supply-automation"
    Tribe       = "planning-and-purchasing"
    CostGroup   = "Global Core"
  }
}

module "inventory-db-replica-2" {
  source  = "terraform-aws-modules/rds/aws"
  version = "~> 3.0"
  identifier              = "inventory-replica-db002-live"
  replicate_source_db     = module.inventory-db.db_instance_id
  instance_class          = "db.m6g.2xlarge"
  create_db_subnet_group  = false
  backup_retention_period = 0

  # Username and password must not be set for replicas
  password = ""
  username = ""

  allocated_storage            = local.allocated_storage
  engine                       = local.engine
  engine_version               = local.engine_version
  major_engine_version         = local.major_engine_version
  port                         = local.port
  db_subnet_group_name         = local.db_subnet_group_name
  create_db_parameter_group    = local.create_db_parameter_group
  create_db_option_group       = local.create_db_option_group
  parameter_group_name         = aws_db_parameter_group.inventory-live-db-replica.name
  storage_type                 = local.storage_type
  allow_major_version_upgrade  = local.allow_major_version_upgrade
  apply_immediately            = local.apply_immediately
  copy_tags_to_snapshot        = local.copy_tags_to_snapshot
  maintenance_window           = local.maintenance_window
  backup_window                = local.backup_window
  subnet_ids                   = local.subnet_ids
  iops                         = local.iops
  vpc_security_group_ids       = local.vpc_security_group_ids
  publicly_accessible          = local.publicly_accessible
  deletion_protection          = local.deletion_protection
  monitoring_interval          = local.monitoring_interval
  monitoring_role_arn          = local.monitoring_role_arn
  family                       = local.family
  tags                         = local.tags
  performance_insights_enabled = true
  multi_az                     = true
  ca_cert_identifier           = local.ca_cert_identifier
}
