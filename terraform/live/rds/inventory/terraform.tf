terraform {
  backend "s3" {
    bucket = "hf-terraform-live"
    key    = "cif/inventory-rds.tfstate"
    region = "eu-west-1"
  }

  required_providers {
    aws = {
      version = "~> 3.0"
    }

    vault = {
      version = "~> 2.24"
    }

    postgresql = {
      version = "~> 1.19"
      source  = "cyrilgdn/postgresql"
    }
  }
}

provider "aws" {
  region = "eu-west-1"
}

data "aws_vpc" "default" {
  default = true
}

data "aws_subnet_ids" "all" {
  vpc_id = data.aws_vpc.default.id
}

data "aws_iam_role" "rds-monitoring" {
    name = "rds-monitoring-role"
}
