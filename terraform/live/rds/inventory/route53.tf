data "aws_route53_zone" "hf-io" {
  name = "hellofresh.io."
}

resource "aws_route53_record" "inventory-db000-live" {
  zone_id = data.aws_route53_zone.hf-io.zone_id
  name    = "inventory-db000.live.hellofresh.io"
  type    = "CNAME"
  ttl     = "60"
  records = [module.inventory-db.db_instance_address]
}

resource "aws_route53_record" "inventory-replica-db001-live" {
  name    = "inventory-replica-db001.live.hellofresh.io"
  zone_id = data.aws_route53_zone.hf-io.zone_id
  type    = "CNAME"
  ttl     = "60"
  records = [module.inventory-db-replica.db_instance_address]
}

resource "aws_route53_record" "inventory-replica-db002-live" {
  name    = "inventory-replica-db002.live.hellofresh.io"
  zone_id = data.aws_route53_zone.hf-io.zone_id
  type    = "CNAME"
  ttl     = "60"
  records = [module.inventory-db-replica-2.db_instance_address]
}
