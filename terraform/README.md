# Basic flow

```bash
# go to the directory with terraform.tf file - e.g. terraform/staging/rds
terraform init # to install terraform modules
terraform plan # to see what changes can be applied
terraform apply
```

## Troubleshooting

### Invalid credentials for AWS Provider

You've got `Error: No valid credential sources found for AWS Provider.` while running `terraform init`.


* Make sure you're logged in
```bash
> $ saml2aws login -a aws-it --session-duration=900
```
* Make sure that a proper env variable is set
```bash
> $ env | grep -i aws_profile AWS_PROFILE=saml
```
* make sure your `VAULT_TOKEN` is valid(can be retrieved from [vault](https://vault.secrets.hellofresh.io/)

Another way to log in AWS is through Azure, the instruction can be found [here](http://docs.hellofresh.io/guides/aws-access-with-azure/).
