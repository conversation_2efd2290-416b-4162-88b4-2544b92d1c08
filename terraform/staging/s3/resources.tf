import {
    to = aws_s3_bucket.cif-stock-files-export-staging
    id = "cif-stock-files-export-staging"
}

resource "aws_s3_bucket" "cif-stock-files-export-staging" {
  bucket = "cif-stock-files-export-staging"
  tags = {
    Name        = "csv export files"
    Environment = "Staging"
    Squad       = "supply-automation"
    Tribe       = "planning-and-purchasing"
    CostGroup   = "Global Core"
  }
}

resource "aws_s3_bucket_policy" "cif-stock-files-export-staging-policy" {
    bucket = aws_s3_bucket.cif-stock-files-export-staging.id
    policy = jsonencode({
        Version = "2012-10-17"
        Statement = [
            {
                Sid = "AllowReadAccessToCIFRole"
                Effect = "Allow"
                Principal = {
                    AWS = "arn:aws:iam::489198589229:role/csku-inventory-forecast-staging-role"
                }
                Action = [
                    "s3:Get*",
                    "s3:List*"
                ]
                Resource = [
                    "${aws_s3_bucket.cif-stock-files-export-staging.arn}",
                    "${aws_s3_bucket.cif-stock-files-export-staging.arn}/*",
                ]
            }
        ]

    })
}

resource "aws_s3_bucket_notification" "cif-stock-files-export-staging" {
    bucket = aws_s3_bucket.cif-stock-files-export-staging.id

    eventbridge = true

    queue {
        queue_arn     = "arn:aws:sqs:eu-west-1:489198589229:supply_automation_3pw_inventory_notification_staging"
        events        = ["s3:ObjectCreated:*"]
        filter_prefix = "third_pw_stock_inventory_uploads/"
        filter_suffix = ".csv"
    }

    queue {
      queue_arn     = "arn:aws:sqs:eu-west-1:489198589229:supply_automation_stock_update_staging"
      events        = ["s3:ObjectCreated:*"]
      filter_prefix = "stock_updates/"
      filter_suffix = ".csv"
    }
}

resource "aws_s3_bucket_cors_configuration" "cif-stock-files-export-cors-staging" {
    bucket = aws_s3_bucket.cif-stock-files-export-staging.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["PUT", "POST", "GET"]
    allowed_origins = ["*"]
  }
}

resource "aws_s3_bucket" "cif-packaging-import-files-staging" {
  bucket = "cif-packaging-import-files-staging"

  tags = {
    Name        = "Packaging import files"
    Environment = "Staging"
    Squad       = "supply-automation"
    Tribe       = "planning-and-purchasing"
    CostGroup   = "Global Core"
  }
}

resource "aws_s3_bucket_policy" "cif-packaging-import-files-policy-staging" {
  bucket = aws_s3_bucket.cif-packaging-import-files-staging.id
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "AllowReadAccessToCIFRole",
        Effect = "Allow",
        Principal = {
          AWS = "arn:aws:iam::489198589229:role/csku-inventory-forecast-staging-role"
        },
        Action = [
          "s3:Get*",
          "s3:List*"
        ],
        Resource = [
          aws_s3_bucket.cif-packaging-import-files-staging.arn,
          "${aws_s3_bucket.cif-packaging-import-files-staging.arn}/*"
        ]
      }
    ]
  })
}

resource "aws_sns_topic" "cif-packaging-import-files-topic-staging" {
  name = "cif-packaging-import-files-topic-staging"

  tags = {
    Name        = "Packaging import files topic"
    Environment = "Staging"
    Squad       = "supply-automation"
    Tribe       = "planning-and-purchasing"
    CostGroup   = "Global Core"
  }
}

resource "aws_sns_topic_policy" "cif-packaging-import-files-topic-policy-staging" {
  arn = aws_sns_topic.cif-packaging-import-files-topic-staging.arn

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "s3.amazonaws.com"
        },
        Action = "SNS:Publish",
        Resource = aws_sns_topic.cif-packaging-import-files-topic-staging.arn,
        Condition = {
          ArnLike = {
            "aws:SourceArn" = aws_s3_bucket.cif-packaging-import-files-staging.arn
          }
        }
      }
    ]
  })
}

resource "aws_s3_bucket_notification" "cif-packaging-import-files-notification-staging" {
  bucket = aws_s3_bucket.cif-packaging-import-files-staging.id

  eventbridge = true

  topic {
    topic_arn     = aws_sns_topic.cif-packaging-import-files-topic-staging.arn
    events        = ["s3:ObjectCreated:*"]
    filter_prefix = "sku_level_demands/"
    filter_suffix = ".csv"
  }

  depends_on = [aws_sns_topic_policy.cif-packaging-import-files-topic-policy-staging]
}

resource "aws_sns_topic_subscription" "cif-packaging-import-files-sns-to-sqs-staging" {
  topic_arn = aws_sns_topic.cif-packaging-import-files-topic-staging.arn
  protocol  = "sqs"
  endpoint  = "arn:aws:sqs:eu-west-1:489198589229:cif_packaging_import_staging"
}
