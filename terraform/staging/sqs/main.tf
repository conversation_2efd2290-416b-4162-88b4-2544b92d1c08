
provider "aws" {
  region = "eu-west-1"
}

terraform {
  backend "s3" {
    bucket = "hf-terraform-staging"
    key    = "infrastructure/ip_safety_stock_multiplier_file_upload_notifications_staging.tfstate"
    region = "eu-west-1"
  }
}

resource "aws_sqs_queue" "ip_safety_stock_multiplier_file_upload_notifications_staging" {
  name = "ip_safety_stock_multiplier_file_upload_notifications_staging"

  visibility_timeout_seconds = 30
  message_retention_seconds  = 86400

  tags = {
        Name        = "ip safety stock multiplier file upload notifications SQS queue staging"
        Environment = "staging"
        Squad       = "supply-automation"
        Tribe       = "planning-and-purchasing"
        CostGroup   = "Global Core"
      }
}

// allow s3 in main-bi to send messages to this sqs queue
resource "aws_sqs_queue_policy" "ip_safety_stock_multiplier_file_upload_sqs_policy_staging" {
  queue_url = aws_sqs_queue.ip_safety_stock_multiplier_file_upload_notifications_staging.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid = "AllowS3BucketToSendMessage",
        Effect = "Allow",
        Principal = {
         Service = "s3.amazonaws.com"
        },
        Action = "SQS:SendMessage",
        Resource = aws_sqs_queue.ip_safety_stock_multiplier_file_upload_notifications_staging.arn,
        Condition = {
          ArnEquals = {
            "aws:SourceArn": "arn:aws:s3:::hf-bi-dwh-uploader-staging"
          }
        }
      }, {
        Sid: "AllowCIFIAMRoleAccess",
        Effect: "Allow",
        Principal : {
            AWS : [
                "arn:aws:iam::489198589229:role/csku-inventory-forecast-staging-role",
                "arn:aws:iam::985437859871:role/procurement-inventory-live-staging",
            ]
        },
        Action: "sqs:*",
        Resource: aws_sqs_queue.ip_safety_stock_multiplier_file_upload_notifications_staging.arn
      }
    ]
  })
}

resource "aws_sqs_queue" "supply_automation_3pw_inventory_notification_staging" {
    name = "supply_automation_3pw_inventory_notification_staging"

    visibility_timeout_seconds = 30
    message_retention_seconds  = 86400

    tags = {
        Name        = "3pw stock inventory files notifications SQS queue staging"
        Environment = "staging"
        Squad       = "supply-automation"
        Tribe       = "planning-and-purchasing"
        CostGroup   = "Global Core"
    }
}

resource "aws_sqs_queue_policy" "supply_automation_3pw_inventory_notification_staging" {
    queue_url = aws_sqs_queue.supply_automation_3pw_inventory_notification_staging.id

    policy = jsonencode({
        Version = "2012-10-17",
        Statement = [
            {
                Sid = "AllowS3BucketToSendMessage",
                Effect = "Allow",
                Principal = {
                    Service = "s3.amazonaws.com"
                },
                Action = "SQS:SendMessage",
                Resource = aws_sqs_queue.supply_automation_3pw_inventory_notification_staging.arn,
                Condition = {
                    ArnEquals = {
                        "aws:SourceArn": "arn:aws:s3:::cif-stock-files-export-staging"
                    }
                }
            }, {
                Sid: "AllowCIFIAMRoleAccess",
                Effect: "Allow",
                Principal : {
                    AWS : "arn:aws:iam::489198589229:role/csku-inventory-forecast-staging-role",
                },
                Action: "sqs:*",
                Resource: aws_sqs_queue.supply_automation_3pw_inventory_notification_staging.arn
            }
        ]
    })
}

resource "aws_sqs_queue" "supply_automation_stock_update_staging" {
  name = "supply_automation_stock_update_staging"

  visibility_timeout_seconds = 30
  message_retention_seconds  = 86400

  tags = {
    Name        = "stock update files notifications SQS queue staging"
    Environment = "staging"
    Squad       = "supply-automation"
    Tribe       = "planning-and-purchasing"
    CostGroup   = "Global Core"
  }
}

resource "aws_sqs_queue_policy" "supply_automation_stock_update_policy_staging" {
  queue_url = aws_sqs_queue.supply_automation_stock_update_staging.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "AllowS3BucketToSendMessage",
        Effect = "Allow",
        Principal = {
          Service = "s3.amazonaws.com"
        },
        Action   = "SQS:SendMessage",
        Resource = aws_sqs_queue.supply_automation_stock_update_staging.arn,
        Condition = {
          ArnEquals = {
            "aws:SourceArn" : "arn:aws:s3:::cif-stock-files-export-staging"
          }
        }
        }, {
        Sid : "AllowCIFIAMRoleAccess",
        Effect : "Allow",
        Principal : {
          AWS : "arn:aws:iam::489198589229:role/csku-inventory-forecast-staging-role",
        },
        Action : "sqs:*",
        Resource : aws_sqs_queue.supply_automation_stock_update_staging.arn
      }
    ]
  })
}

resource "aws_sqs_queue" "cif_packaging_import_staging" {
  name = "cif_packaging_import_staging"

  visibility_timeout_seconds = 30
  message_retention_seconds  = 86400

  tags = {
    Name        = "Inventory packaging import queue staging"
    Environment = "Staging"
    Squad       = "supply-automation"
    Tribe       = "planning-and-purchasing"
    CostGroup   = "Global Core"
  }
}

resource "aws_sqs_queue_policy" "cif_packaging_import_policy_staging" {
  queue_url = aws_sqs_queue.cif_packaging_import_staging.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "AllowSNSPublish",
        Effect = "Allow",
        Principal: "*",
        Action   = "SQS:SendMessage",
        Resource = aws_sqs_queue.cif_packaging_import_staging.arn,
        Condition = {
          ArnEquals = {
            "aws:SourceArn" : "arn:aws:sns:eu-west-1:489198589229:cif-packaging-import-files-topic-staging"
          }
        }
      }, {
        Sid : "AllowCIFIAMRoleAccess",
        Effect : "Allow",
        Principal : {
          AWS : "arn:aws:iam::489198589229:role/csku-inventory-forecast-staging-role",
        },
        Action : "sqs:*",
        Resource : aws_sqs_queue.cif_packaging_import_staging.arn
      }
    ]
  })
}
