terraform {
  backend "s3" {
    bucket = "hf-terraform-staging"
    key    = "cif/inventory-iam.tfstate"
    region = "eu-west-1"
  }
}

import {
    to = module.oidc_csku_inventory_forecast.aws_iam_role.this
    id = "csku-inventory-forecast-staging-role"
}

import {
    to = aws_iam_policy.csku-inventory-forecast-staging-k8s-policy
    id = "arn:aws:iam::489198589229:policy/csku-inventory-forecast-staging-k8s-policy"
}

resource "aws_iam_policy" "csku-inventory-forecast-staging-k8s-policy" {
    name = "csku-inventory-forecast-staging-k8s-policy"

    policy = jsonencode({
        "Statement" : [
            {
                "Action" : [
                    "s3:*"
                ],
                "Effect" : "Allow",
                "Resource" : [
                    "arn:aws:s3:::cif-stock-files-export-staging",
                    "arn:aws:s3:::cif-stock-files-export-staging/*",
                ],
                "Sid" : "AllowS3Object"
            },
            {
                "Action" : [
                    "sqs:*"
                ],
                "Effect" : "Allow",
                "Resource" : [
                    "arn:aws:sqs:eu-west-1:489198589229:ip_safety_stock_multiplier_file_upload_notifications_staging"
                ],
                "Sid" : "AllowSQSReading"
            },
            {
                "Action" : [
                    "tag:GetResources",
                    "cloudwatch:ListTagsForResource",
                    "cloudwatch:GetMetricData",
                    "cloudwatch:ListMetrics"
                ],
                "Effect" : "Allow",
                "Resource" : [
                    "*"
                ],
                "Sid" : "AllowCloudwatchExport"
            },
             {
               "Effect": "Allow",
               "Action": [
                 "sts:AssumeRole"
               ],
               "Resource": "*"
             }
        ],
        "Version" : "2012-10-17"
    }
    )
}

module "oidc_csku_inventory_forecast" {
    source = "git::https://github.com/hellofresh/terraform-aws-iam-irsa-role.git"

    iam_role_name          = "csku-inventory-forecast-staging-role"
    environments           = ["staging"]

    kubernetes_service_account = "*"
    kubernetes_namespace       = "scm"

    tags = {
        tribe       = "planning-and-purchasing"
        squad       = "supply-automation"
        Environment = "Staging"
    }
}

resource "aws_iam_role_policy_attachment" "csku_inventory_forecast_staging_role_policy_attachment" {
    role       = module.oidc_csku_inventory_forecast.iam_role_name
    policy_arn = aws_iam_policy.csku-inventory-forecast-staging-k8s-policy.arn
}

