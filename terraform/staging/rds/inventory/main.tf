locals {
  db_identifier          = "inventory-db000-staging"
  engine                 = "postgres"
  engine_version         = "16"
  major_engine_version   = "16"
  ca_cert_identifier     = "rds-ca-rsa2048-g1"
}

module "inventory-db" {
  source  = "terraform-aws-modules/rds/aws"
  version = "~> 3.0"

  identifier                      = local.db_identifier
  instance_class                  = "db.m6g.xlarge"
  name                            = "inventory"
  username                        = data.vault_generic_secret.vault_rds_secrets.data["DB_USERNAME"]
  password                        = data.vault_generic_secret.vault_rds_secrets.data["DB_PASSWORD"]
  engine                          = local.engine
  engine_version                  = local.engine_version
  major_engine_version            = local.major_engine_version
  allow_major_version_upgrade     = true
  auto_minor_version_upgrade      = true
  allocated_storage               = 100
  storage_encrypted               = false
  storage_type                    = "gp3"
  create_db_parameter_group       = false
  create_db_option_group          = false
  parameter_group_name            = aws_db_parameter_group.inventory_staging_postgres_parameter_group.name
  port                            = "5432"
  subnet_ids                      = flatten([data.aws_subnet_ids.all.ids])
  iops                            = 3000
  db_subnet_group_name            = "default"
  vpc_security_group_ids          = [data.aws_security_group.db.id]
  publicly_accessible             = false
  maintenance_window              = "thu:00:20-thu:00:50"
  backup_window                   = "03:40-04:10"
  backup_retention_period         = 1
  skip_final_snapshot             = true
  monitoring_interval             = 60
  monitoring_role_arn             = data.aws_iam_role.rds-monitoring.arn
  apply_immediately               = true
  copy_tags_to_snapshot           = true
  final_snapshot_identifier       = "inventory-db-staging"
  deletion_protection             = true
  ca_cert_identifier              = local.ca_cert_identifier
  tags = {
    Environment = "Staging"
    Group       = "inventory-db"
    Squad       = "supply-automation"
    Tribe       = "planning-and-purchasing"
    CostGroup   = "Global Core"
  }
}
