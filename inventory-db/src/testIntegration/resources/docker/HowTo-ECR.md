# How to pull, build and push an ECR image

## Login into AWS in terminal to be able to pull and push images
1. Export AWS accesskeys enviroment vars in terminal <br>
   Go to AWS management console and tale Access Keys from ```main-it``` profile and ```eu-west-1``` region
   ```bash
   export AWS_ACCESS_KEY_ID=XXX
   export AWS_SECRET_ACCESS_KEY=YYYY
   export AWS_SESSION_TOKEN=ZZZZ
   ```

2. Log in
   ```bash
   aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin 489198589229.dkr.ecr.eu-west-1.amazonaws.com
   ```

## Build and push postgres image

1. Build
   On folder inventory-db/src/testIntegration/resources/docker
   ```bash
   docker build --platform linux/amd64 -t supply-automation-postgres:X.Y .
   ```
   * (Last point after image name should be included, it references Dockerfile in folder)

2. Tag Image
   ```bash
   docker tag supply-automation-postgres:X.Y 489198589229.dkr.ecr.eu-west-1.amazonaws.com/supply-automation-postgres:X.Y
   ```

3. Push Image to ECR
   ```bash
   docker push 489198589229.dkr.ecr.eu-west-1.amazonaws.com/supply-automation-postgres:X.Y
   ```

4. Verify the Pushed postgres image <br>
Navigate to the AWS Management Console, navigate to the ECR service, and check your repository to verify that your Docker image has been pushed successfully.
