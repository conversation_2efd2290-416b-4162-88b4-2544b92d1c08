package com.hellofresh.cif.db.migration

import com.hellofresh.cif.db.migration.FlywayMigrationTest.Companion.Config
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import java.io.FileInputStream
import java.sql.DriverManager
import java.sql.Statement
import java.util.Properties
import javax.sql.DataSource
import org.flywaydb.core.Flyway
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import org.testcontainers.containers.GenericContainer
import org.testcontainers.containers.output.Slf4jLogConsumer
import org.testcontainers.containers.wait.strategy.LogMessageWaitStrategy
import org.testcontainers.junit.jupiter.Testcontainers
import org.testcontainers.utility.DockerImageName

private val POSTGRESQL_PORT = Config.postgresqlPort
private val DB_NAME = Config.dbName
private val CIF_READONLY_USER = Config.cifReadonlyUser
private val CIF_READONLY_PASSWORD = Config.cifReadonlyPassword
private val CIF_USER = Config.cifUser
private val CIF_PASSWORD = Config.cifPassword

@Testcontainers
class FlywayMigrationTest {

    private val expectedCifObjects = setOf(
        DbObject("actual_consumption_view", "MATERIALIZED VIEW"),
        DbObject("actual_consumption_inventory_activity", "MATERIALIZED VIEW"),
        DbObject("calculation", "TABLE"),
        DbObject("actual_consumption", "TABLE"),
        DbObject("calculation_current", "TABLE"),
        DbObject("calculation_042024", "TABLE"),
        DbObject("calculation_012025", "TABLE"),
        DbObject("calculation_052023", "TABLE"),
        DbObject("dc_config", "TABLE"),
        DbObject("demand", "TABLE"),
        DbObject("pick_2_light", "TABLE"),
        DbObject("pre_production_calculation", "TABLE"),
        DbObject("pre_production_calculation_current", "TABLE"),
        DbObject("pre_production_calculation_042024", "TABLE"),
        DbObject("pre_production_calculation_012025", "TABLE"),
        DbObject("pre_production_calculation_052023", "TABLE"),
        DbObject("live_inventory_calculation", "TABLE"),
        DbObject("live_inventory_calculation_current", "TABLE"),
        DbObject("live_inventory_calculation_042024", "TABLE"),
        DbObject("live_inventory_calculation_012025", "TABLE"),
        DbObject("live_inventory_pre_production_calculation", "TABLE"),
        DbObject("live_inventory_pre_production_calculation_current", "TABLE"),
        DbObject("live_inventory_pre_production_calculation_042024", "TABLE"),
        DbObject("live_inventory_pre_production_calculation_012025", "TABLE"),
        DbObject("sku_specification", "TABLE"),
        DbObject("sku_specification_yf", "TABLE"),
        DbObject("sku_specification_view", "VIEW"),
        DbObject("supplier", "TABLE"),
        DbObject("note", "TABLE"),
        DbObject("goods_received_note", "TABLE"),
        DbObject("purchase_orders_view", "VIEW"),
        DbObject("supplier_sku", "TABLE"),
        DbObject("purchase_order", "TABLE"),
        DbObject("purchase_order_sku", "TABLE"),
        DbObject("advanced_shipping_notice", "TABLE"),
        DbObject("advanced_shipping_notice_sku", "TABLE"),
        DbObject("dc_config_weight_view", "VIEW"),
        DbObject("supplier_culinary_sku", "TABLE"),
        DbObject("po_calculations_view", "VIEW"),
        DbObject("inventory_variance", "TABLE"),
        DbObject("inventory_snapshot_raw", "TABLE"),
        DbObject("inventory_snapshot_raw_sku", "TABLE"),
        DbObject("inventory_all_snapshots", "TABLE"),
        DbObject("inventory_snapshot", "TABLE"),
        DbObject("inventory_snapshot_current", "TABLE"),
        DbObject("inventory_snapshot_30062025", "TABLE"),
        DbObject("inventory_snapshot_22082023", "TABLE"),
        DbObject("inventory_live_snapshot", "TABLE"),
        DbObject("calculation_substitution_view", "VIEW"),
        DbObject("pre_prod_calculation_substitution_view", "VIEW"),
        DbObject("supplier_sku_pricing", "TABLE"),
        DbObject("supplier_details_view", "VIEW"),
        DbObject("inventory_cleardown_trigger", "TABLE"),
        DbObject("inventory_activity", "TABLE"),
        DbObject("stock_update", "TABLE"),
        DbObject("supply_quantity_recommendation_conf", "TABLE"),
        DbObject("supply_quantity_recommendation", "TABLE"),
        DbObject("sqr_short_shelf_life_conf", "TABLE"),
        DbObject("sqr_short_shelf_life", "TABLE"),
        DbObject("safety_stock_conf", "TABLE"),
        DbObject("safety_stocks", "TABLE"),
        DbObject("us_demand", "TABLE"),
        DbObject("file_export_request", "TABLE"),
        DbObject("safety_stock_multiplier", "TABLE"),
        DbObject("safety_stock_import", "TABLE"),
        DbObject("inventory_processed_snapshots", "TABLE"),
        DbObject("safety_stock_buffer", "TABLE"),
        DbObject("file_uploads", "TABLE"),
        DbObject("transfer_order", "TABLE"),
        DbObject("transfer_order_skus", "TABLE"),
        DbObject("transfer_orders_grn_view", "VIEW"),
    )

    private data class DbObject(val name: String, val type: String)

    @AfterEach
    fun cleanup() {
        dataSource.connection.use {
            it.createStatement().use { statement ->
                expectedCifObjects.forEach { cifObject ->
                    statement.execute("drop ${cifObject.type} if exists ${cifObject.name} cascade")
                }
            }
        }
    }

    @Test
    fun `should migrate the DB`() {
        // when
        migrateDb()

        // then
        dataSource.connection.use { conn ->
            conn.createStatement().use { statement ->
                expectedCifObjects.forEach {
                    assertTableOrViewExists(statement, it.name)
                }
            }
        }
        val actual = getSchemaObjects("public")
        assertTrue(
            actual.sorted() == expectedCifObjects.map { it.name }.sorted(),
            "keep these collections synced to ensure that flyway will create all the expected tables/views.",
        )
    }

    @Test
    fun `should not fail if cif-readonly user already exists`() {
        val newPostgres = postgresContainer()
        newPostgres.start()
        val newDataSource = createDataSource(newPostgres)
        // given
        givenUserAlreadyExists(newDataSource)
        // when
        migrateDb(newDataSource)

        // then
        DriverManager.getConnection(getJdbcUrl(newPostgres), CIF_READONLY_USER, CIF_READONLY_PASSWORD).use { conn ->
            assertTrue(conn.isValid(0))
        }
        newPostgres.stop()
    }

    @Test
    fun `the cif-readonly user can select tables and views`() {
        // when
        migrateDb()

        // then
        val actual = getSchemaObjects("public")
        val conn = DriverManager.getConnection(getJdbcUrl(postgres), CIF_READONLY_USER, CIF_READONLY_PASSWORD)
        conn.use {
            it.createStatement().use { statement ->
                actual.forEach { name ->
                    assertTableOrViewExists(statement, name)
                }
            }
        }
    }

    @Test
    fun `the cif-readonly user can select on cron job, job_run_details tables`() {
        // when
        migrateDb()
        // inserting a mock data inorder to avoid waiting for cron.run_job_details to be filled.
        dataSource.connection.createStatement().executeUpdate(
            "insert into cron.job_run_details values ( 1,100, 1000, 'inventory', 'cif', 'flyway migration test', 'succeeded', '', now(), now() )",
        )

        // then
        val actual = getSchemaObjects("cron")
        DriverManager.getConnection(getJdbcUrl(postgres), CIF_READONLY_USER, CIF_READONLY_PASSWORD).use {
            it.createStatement().use { statement ->
                actual.forEach { name ->
                    assertTableOrViewExists(statement, "cron.$name")
                    val resultSet = statement.executeQuery("SELECT * FROM cron.$name")
                    assertTrue(resultSet.next(), "table is empty $name")
                }
            }
        }
    }

    @Test
    fun `should install pg_cron extension`() {
        // when
        migrateDb()

        // then
        val cronSchemaObjects = getSchemaObjects("cron")
        cronSchemaObjects.containsAll(setOf("job", "job_run_details"))
    }

    @Test
    fun `should create the config schema and tables`() {
        // when
        migrateDb()

        // then
        val configSchemaObjects = getSchemaObjects("config")
        assertEquals(setOf("distribution_center"), configSchemaObjects)
    }

    @Test
    fun `should schedule and unschedule pg_cron jobs`() {
        val jobName = "test123"
        // when
        migrateDb()
        dataSource.connection.createStatement()
            .execute("select schedule_cron_job('$jobName', '*/1 * * * *', 'select 1;');")

        DriverManager.getConnection(getJdbcUrl(postgres), CIF_READONLY_USER, CIF_READONLY_PASSWORD).use {
            it.createStatement().use { statement ->
                val resultSet = statement.executeQuery("SELECT * FROM cron.job where jobname = '$jobName'")
                assertTrue(resultSet.next())
            }
        }
        dataSource.connection.createStatement()
            .execute("select unschedule_cron_job('$jobName');")

        DriverManager.getConnection(getJdbcUrl(postgres), CIF_READONLY_USER, CIF_READONLY_PASSWORD).use {
            it.createStatement().use { statement ->
                val resultSet = statement.executeQuery("SELECT * FROM cron.job where jobname = '$jobName'")
                assertFalse(resultSet.next())
            }
        }
    }

    companion object {
        private lateinit var dataSource: DataSource
        private val LOGGER = LoggerFactory.getLogger(FlywayMigrationTest::class.java)
        private val logConsumer = Slf4jLogConsumer(LOGGER)

        private val skippedObjects =
            setOf(
                "flyway_schema_history",
                "pg_stat_statements",
                "pg_stat_statements_info",
                "inventory_snapshot_bkp",
            )

        private val postgresqlImage = DockerImageName
            .parse("489198589229.dkr.ecr.eu-west-1.amazonaws.com/supply-automation-postgres:15.5")

        private var postgres = postgresContainer()

        @BeforeAll
        @JvmStatic
        fun setUp() {
            postgres.start()
            dataSource = createDataSource(postgres)
            assertDbIsNotMigrated()
        }

        private fun postgresContainer() =
            GenericContainer(postgresqlImage)
                .withEnv(
                    mapOf(
                        "POSTGRES_DB" to DB_NAME,
                        "POSTGRES_USER" to CIF_USER,
                        "POSTGRES_PASSWORD" to CIF_PASSWORD,
                        "LC_ALL" to "C.UTF-8",
                    ),
                )
                .withExposedPorts(POSTGRESQL_PORT)
                .withLogConsumer(logConsumer)
                .waitingFor(
                    LogMessageWaitStrategy()
                        .withRegEx(".*pg_cron scheduler started.*\\s")
                        .withTimes(2),
                )
                .withReuse(true)

        private fun createDataSource(container: GenericContainer<*>) =
            HikariDataSource(
                HikariConfig()
                    .also {
                        it.jdbcUrl = getJdbcUrl(container)
                        it.username = CIF_USER
                        it.password = CIF_PASSWORD
                        it.driverClassName = "org.postgresql.Driver"
                    },
            )

        private fun migrateDb() = migrateDb(dataSource)

        private fun migrateDb(dataSource: DataSource) {
            val escapedCIfReadOnlyPassword = "\'$CIF_READONLY_PASSWORD\'"
            val flyway = Flyway.configure()
                .locations("classpath:migrations")
                .dataSource(dataSource)
                .placeholders(mapOf("cifReadonlyPassword" to escapedCIfReadOnlyPassword))
                .load()

            val result = flyway.migrate()
            assertTrue(result.success, "fail to migrate!")
        }

        private fun assertDbIsNotMigrated() {
            dataSource.connection.use { conn ->
                val resultSet = conn.metaData.getTables(null, null, "flyway_schema_history", null)
                assertFalse(resultSet.next(), "DB has been already migrated!")
            }
        }

        private fun givenUserAlreadyExists(dataSource: DataSource) {
            dataSource.connection.use { conn ->
                conn.createStatement().use { statement ->
                    statement.execute(
                        """
                        CREATE USER "cif-readonly" WITH PASSWORD '$CIF_READONLY_PASSWORD';
                        """.trimMargin(),
                    )
                }
            }
        }

        private fun getSchemaObjects(schema: String) =
            dataSource.connection
                .use {
                    val cifObjects: MutableSet<String> = mutableSetOf()
                    val rs = it.metaData.getTables(
                        null,
                        schema,
                        null,
                        arrayOf("VIEW", "TABLE", "MATERIALIZED VIEW", "PARTITIONED TABLE"),
                    )
                    while (rs.next()) {
                        val name = rs.getString("TABLE_NAME")
                        if (skippedObjects.contains(name)) continue
                        cifObjects.add(name)
                    }
                    cifObjects.toSet()
                }

        private fun assertTableOrViewExists(statement: Statement, tableOrViewName: String) {
            val selectResult = statement.execute("SELECT 1 FROM $tableOrViewName")
            assertTrue(selectResult, "The $tableOrViewName table/view does not exists.")
        }

        private fun getJdbcUrl(container: GenericContainer<*>) =
            "jdbc:postgresql://${container.host}:${container.getMappedPort(POSTGRESQL_PORT)}/$DB_NAME?TC_REUSABLE=true"

        object Config {
            private val properties = Properties()

            init {
                FileInputStream("src/testIntegration/resources/application.properties").use { properties.load(it) }
            }

            val postgresqlPort: Int
                get() = properties.getProperty("postgresql.port").toInt()

            val dbName: String
                get() = properties.getProperty("db.name")

            val cifReadonlyUser: String
                get() = properties.getProperty("cif.readonly.user")

            val cifReadonlyPassword: String
                get() = properties.getProperty("cif.readonly.password")

            val cifUser: String
                get() = properties.getProperty("cif.user")

            val cifPassword: String
                get() = properties.getProperty("cif.password")
        }
    }
}
