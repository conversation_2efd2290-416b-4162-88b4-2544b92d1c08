-- Increasing local timeout for transaction
set local statement_timeout = 900000;

INSERT INTO inventory_processed_snapshots (dc_code, snapshot_id, snapshot_time)
SELECT dc_code,
       snapshot_id,
       max(snapshot_time)
FROM inventory_all_snapshots AS ias
WHERE NOT EXISTS (SELECT 1
    FROM inventory_processed_snapshots AS ips
    WHERE ips.snapshot_id = ias.snapshot_id
    AND ips.dc_code = ias.dc_code)
GROUP BY dc_code, snapshot_id;

