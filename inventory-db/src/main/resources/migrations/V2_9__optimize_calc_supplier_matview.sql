DROP MATERIALIZED VIEW IF EXISTS calculation_view;

CREATE MATERIALIZED VIEW IF NOT EXISTS calculation_view AS
SELECT calc.*, po_suppliers.supplier_ids
from calculation calc
         left join (select pov.po_dc_code,
                           pov.po_sku_id,
                           pov.po_expected_arrival_start_time::date as date,
                           array_agg(pov.po_supplier_id)            as supplier_ids
                    from purchase_orders_view pov
                    group by pov.po_dc_code, pov.po_sku_id, pov.po_expected_arrival_start_time::date) as po_suppliers
                   on (
                               calc.dc_code = po_suppliers.po_dc_code and
                               calc.csku_id = po_suppliers.po_sku_id and
                               calc.date = po_suppliers.date)
where calc.date > now() - interval '70 days';

create unique index if not exists c_view_week_dc_sku__date_uniq_index
    on public.calculation_view (csku_id, dc_code, date);

create index if not exists c_view_week_dc_sku_index
    on public.calculation_view (production_week, dc_code, csku_id);

create index if not exists c_view_dccode_week_index
    on public.calculation_view (dc_code, production_week, date);

grant select on public.calculation_view to readonly;
select schedule_cron_job('calculation_view_schedule', '*/1 * * * *', 'refresh materialized view concurrently calculation_view;');


DROP MATERIALIZED VIEW IF EXISTS pre_production_calculation_view;

CREATE MATERIALIZED VIEW IF NOT EXISTS pre_production_calculation_view AS
SELECT calc.*, po_suppliers.supplier_ids
from pre_production_calculation calc
         left join (select pov.po_dc_code,
                           pov.po_sku_id,
                           pov.po_expected_arrival_start_time::date as date,
                           array_agg(pov.po_supplier_id)            as supplier_ids
                    from purchase_orders_view pov
                    group by pov.po_dc_code, pov.po_sku_id, pov.po_expected_arrival_start_time::date) as po_suppliers
                   on (
                               calc.dc_code = po_suppliers.po_dc_code and
                               calc.csku_id = po_suppliers.po_sku_id and
                               calc.date = po_suppliers.date)
where calc.date > now() - interval '70 days';

create unique index if not exists ppc_view_week_dc_sku__date_uniq_index
    on public.pre_production_calculation_view (csku_id, dc_code, date);

create index if not exists ppc_view_week_dc_sku_index
    on public.pre_production_calculation_view (production_week, dc_code, csku_id);

create index if not exists ppc_view_dccode_week_index
    on public.pre_production_calculation_view (dc_code, production_week, date);

grant select on public.pre_production_calculation_view to readonly;
select schedule_cron_job('pre_production_calculation_view_schedule', '*/1 * * * *', 'refresh materialized view concurrently pre_production_calculation_view;');


DROP MATERIALIZED VIEW IF EXISTS live_inventory_calculation_view;
CREATE MATERIALIZED VIEW IF NOT EXISTS live_inventory_calculation_view AS
SELECT calc.*, po_suppliers.supplier_ids
from live_inventory_calculation calc
         left join (select pov.po_dc_code,
                           pov.po_sku_id,
                           pov.po_expected_arrival_start_time::date as date,
                           array_agg(pov.po_supplier_id)            as supplier_ids
                    from purchase_orders_view pov
                    group by pov.po_dc_code, pov.po_sku_id, pov.po_expected_arrival_start_time::date) as po_suppliers
                   on (
                               calc.dc_code = po_suppliers.po_dc_code and
                               calc.csku_id = po_suppliers.po_sku_id and
                               calc.date = po_suppliers.date)
where calc.date > now() - interval '70 days';

create unique index if not exists lc_view_week_dc_sku__date_uniq_index
    on public.live_inventory_calculation_view (csku_id, dc_code, date);

create index if not exists lc_view_week_dc_sku_index
    on public.live_inventory_calculation_view (production_week, dc_code, csku_id);

create index if not exists lc_view_dccode_week_index
    on public.live_inventory_calculation_view (dc_code, production_week, date);

grant select on public.live_inventory_calculation_view to readonly;
select schedule_cron_job('live_inventory_calculation_view_schedule', '*/1 * * * *', 'refresh materialized view concurrently live_inventory_calculation_view;');


DROP MATERIALIZED VIEW IF EXISTS live_inventory_pre_production_calculation_view;
CREATE MATERIALIZED VIEW IF NOT EXISTS live_inventory_pre_production_calculation_view AS
SELECT calc.*, po_suppliers.supplier_ids
from live_inventory_pre_production_calculation calc
         left join (select pov.po_dc_code,
                           pov.po_sku_id,
                           pov.po_expected_arrival_start_time::date as date,
                           array_agg(pov.po_supplier_id)            as supplier_ids
                    from purchase_orders_view pov
                    group by pov.po_dc_code, pov.po_sku_id, pov.po_expected_arrival_start_time::date) as po_suppliers
                   on (
                               calc.dc_code = po_suppliers.po_dc_code and
                               calc.csku_id = po_suppliers.po_sku_id and
                               calc.date = po_suppliers.date)
where calc.date > now() - interval '70 days';

create unique index if not exists lpc_view_week_dc_sku__date_uniq_index
    on public.live_inventory_pre_production_calculation_view (csku_id, dc_code, date);

create index if not exists lpc_view_week_dc_sku_index
    on public.live_inventory_pre_production_calculation_view (production_week, dc_code, csku_id);

create index if not exists lpc_view_dccode_week_index
    on public.live_inventory_pre_production_calculation_view (dc_code, production_week, date);

grant select on public.live_inventory_pre_production_calculation_view to readonly;
select schedule_cron_job('live_inventory_pre_production_calculation_view_schedule', '*/1 * * * *',
                         'refresh materialized view concurrently live_inventory_pre_production_calculation_view;');
