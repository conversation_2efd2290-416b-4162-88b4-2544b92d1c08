
UPDATE public.goods_received_note
SET delivery_id = po_ref
WHERE delivery_id is null;

ALTER TABLE goods_received_note ADD CONSTRAINT goods_received_note_pkey
    PRIMARY KEY (sku_id, dc_code, po_number, delivery_id, delivery_time);


DROP MATERIALIZED VIEW IF EXISTS PURCHASE_ORDERS_VIEW;

CREATE
MATERIALIZED VIEW IF NOT EXISTS PURCHASE_ORDERS_VIEW AS
WITH
    sku_mlor as (
        select supplier_sku.sku_id, min(supplier_sku.mlor_days) as mlor
        from supplier_sku
        where supplier_sku.status ILIKE 'active'
        group by supplier_sku.sku_id
    ),
    purchase_order AS (
        SELECT po.*, posku.sku_id, posku.quantity, supplier.name as supplier_name, sku_mlor.mlor
        from purchase_order po
            left join purchase_order_sku posku on po.po_number = posku.po_number
            left join supplier on supplier.id = po.supplier_id
            left join sku_mlor on (sku_mlor.sku_id = posku.sku_id)
        where po.expected_arrival_start_time > now() - interval '70 days'
    ),
    grn AS (
        SELECT goods_received_note.*, sku_mlor.mlor
        from goods_received_note
            left join sku_mlor on (sku_mlor.sku_id = goods_received_note.sku_id)
        where goods_received_note.delivery_time > now() - interval '70 days'
)

select purchase_order.po_ref                    as po_ref,
       purchase_order.po_id                     as po_id,
       purchase_order.po_number                 as po_number,
       purchase_order.dc_code                   as po_dc_code,
       purchase_order.sku_id                    as po_sku_id,
       purchase_order.expected_arrival_start_time as po_expected_arrival_start_time,
       purchase_order.expected_arrival_end_time as po_expected_arrival_end_time,
       purchase_order.quantity                  as po_quantity,
       purchase_order.supplier_id               as po_supplier_id,
       purchase_order.supplier_name             as po_supplier_name,
       purchase_order.mlor                      as po_mlor,
       grn.po_ref                               as grn_po_ref,
       grn.dc_code                              as grn_dc_code,
       grn.sku_id                               as grn_sku_id,
       grn.delivery_id                          as grn_delivery_id,
       grn.delivery_time                        as grn_delivery_time,
       grn.quantity                             as grn_quantity,
       grn.delivery_status                      as grn_delivery_status,
       grn.mlor                                 as grn_mlor
from purchase_order
         full join grn
                   on (
                               purchase_order.po_ref = grn.po_ref
                           and purchase_order.sku_id = grn.sku_id
                           and purchase_order.dc_Code = grn.dc_code
                       );


create index  if not exists purchase_orders_view_po_ref
    on public.purchase_orders_view (po_ref, grn_po_ref);

create index  if not exists purchase_orders_view_dc_code
    on public.purchase_orders_view (po_dc_code, grn_dc_code);

create index  if not exists purchase_orders_view_expected_arrival_start_time
    on public.purchase_orders_view (po_expected_arrival_start_time, grn_delivery_time);

create unique index  if not exists purchase_orders_view_index
    on public.purchase_orders_view (po_ref, grn_po_ref, po_sku_id, grn_sku_id, po_dc_code, grn_dc_code,
    po_expected_arrival_start_time, grn_delivery_id, grn_delivery_time);

grant select on public.purchase_orders_view to readonly;
