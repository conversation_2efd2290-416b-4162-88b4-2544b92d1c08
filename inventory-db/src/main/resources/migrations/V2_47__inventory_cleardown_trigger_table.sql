create table if not exists public.inventory_cleardown_trigger
(
    id               uuid                                not null,
    dc_code          varchar                             not null,
    timestamp        timestamp with time zone            not null,
    author_email     text                                not null,
    author_name      text,
    created_at       timestamp default CURRENT_TIMESTAMP not null,
    primary key (id)
);

create index if not exists inventory_cleardown_trigger_time_index
    on public.inventory_cleardown_trigger (dc_code, timestamp);

grant select on public.inventory_cleardown_trigger to readonly;
