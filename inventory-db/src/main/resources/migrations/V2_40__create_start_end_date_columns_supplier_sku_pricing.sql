TRUNCATE TABLE PUBLIC.supplier_sku_pricing;

ALTER TABLE PUBLIC.supplier_sku_pricing
    ADD COLUMN IF NOT EXISTS start_date DATE NOT NULL;

ALTER TABLE PUBLIC.supplier_sku_pricing
    ADD COLUMN IF NOT EXISTS end_date DATE NOT NULL;

DROP VIEW IF EXISTS supplier_details_view;

CREATE VIEW supplier_details_view AS
SELECT ss.sku_id           AS sku_id,
       scs.market          AS market,
       s.id                AS supplier_id,
       s.name              AS supplier_name,
       ssp.lead_time       AS lead_time,
       ssp.start_date      AS start_date,
       ssp.end_date        AS end_date,
       ss.mlor_days        AS mlor,
       scs.culinary_sku_id AS culinary_sku_id
FROM supplier_sku ss
         JOIN supplier_culinary_sku scs ON scs.id = ss.supplier_sku_id
         LEFT JOIN (SELECT supplier_sku_id, lead_time, market, start_date, end_date FROM supplier_sku_pricing WHERE enabled = true) AS ssp
                   ON ssp.supplier_sku_id = ss.supplier_sku_id
         JOIN supplier s ON scs.supplier_id = s.parent_id
WHERE ss.status ILIKE 'active';

grant select on public.supplier_details_view to readonly;
