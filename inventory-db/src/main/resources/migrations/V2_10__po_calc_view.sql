DROP MATERIALIZED VIEW IF EXISTS calculation_view;

CREATE MATERIALIZED VIEW IF NOT EXISTS po_by_calculation_view AS
select pov.po_dc_code, pov.po_sku_id , pov.po_expected_arrival_start_time::date as date,
       array_agg(pov.po_supplier_id) as supplier_ids
from purchase_orders_view pov where po_number is not null
group by pov.po_dc_code, pov.po_sku_id , pov.po_expected_arrival_start_time::date;

create unique index if not exists c_view_week_dc_sku__date_uniq_index
    on public.po_by_calculation_view (po_sku_id, po_dc_code, date);

grant select on public.po_by_calculation_view to readonly;
select schedule_cron_job('po_by_calculation_view_schedule', '*/1 * * * *', 'refresh materialized view concurrently calculation_view;');


DROP MATERIALIZED VIEW IF EXISTS pre_production_calculation_view;

CREATE MATERIALIZED VIEW IF NOT EXISTS po_by_pre_production_calculation_view AS
select pov.po_dc_code, pov.po_sku_id , pov.po_expected_arrival_start_time::date as date,
       array_agg(pov.po_supplier_id) as supplier_ids
from purchase_orders_view pov where po_number is not null
group by pov.po_dc_code, pov.po_sku_id , pov.po_expected_arrival_start_time::date;

create unique index if not exists ppc_view_week_dc_sku__date_uniq_index
    on public.po_by_pre_production_calculation_view (po_sku_id, po_dc_code, date);

grant select on public.po_by_pre_production_calculation_view to readonly;
select schedule_cron_job('po_by_pre_production_calculation_view_schedule', '*/1 * * * *', 'refresh materialized view concurrently pre_production_calculation_view;');


DROP MATERIALIZED VIEW IF EXISTS live_inventory_calculation_view;

CREATE MATERIALIZED VIEW IF NOT EXISTS po_by_live_inventory_calculation_view AS
select pov.po_dc_code, pov.po_sku_id , pov.po_expected_arrival_start_time::date as date,
       array_agg(pov.po_supplier_id) as supplier_ids
from purchase_orders_view pov where po_number is not null
group by pov.po_dc_code, pov.po_sku_id , pov.po_expected_arrival_start_time::date;

create unique index if not exists lc_view_week_dc_sku__date_uniq_index
    on public.po_by_live_inventory_calculation_view (po_sku_id, po_dc_code, date);

grant select on public.po_by_live_inventory_calculation_view to readonly;
select schedule_cron_job('po_by_live_inventory_calculation_view_schedule', '*/1 * * * *', 'refresh materialized view concurrently live_inventory_calculation_view;');


DROP MATERIALIZED VIEW IF EXISTS live_inventory_pre_production_calculation_view;

CREATE MATERIALIZED VIEW IF NOT EXISTS po_by_live_inventory_pre_production_calculation_view AS
select pov.po_dc_code, pov.po_sku_id , pov.po_expected_arrival_start_time::date as date,
       array_agg(pov.po_supplier_id) as supplier_ids
from purchase_orders_view pov where po_number is not null
group by pov.po_dc_code, pov.po_sku_id , pov.po_expected_arrival_start_time::date;

create unique index if not exists lpc_view_week_dc_sku__date_uniq_index
    on public.po_by_live_inventory_pre_production_calculation_view (po_sku_id, po_dc_code, date);

grant select on public.po_by_live_inventory_pre_production_calculation_view to readonly;
select schedule_cron_job('po_by_live_inventory_pre_production_calculation_view_schedule', '*/1 * * * *',
                         'refresh materialized view concurrently live_inventory_pre_production_calculation_view;');
