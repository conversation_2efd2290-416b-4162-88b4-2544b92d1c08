
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'wms_type') THEN
        CREATE TYPE wms_type AS ENUM ('FCMS', 'WMS_LITE', 'E2POPEN', 'HIGH<PERSON>UM<PERSON>', 'UNKNOWN');
    END IF;
END $$;

ALTER TABLE public.dc_config
    ADD COLUMN IF NOT EXISTS wms_type wms_type DEFAULT 'UNKNOWN';

ALTER TABLE config.distribution_center
     ADD COLUMN IF NOT EXISTS wms_type wms_type DEFAULT 'UNKNOWN';
