create table if not exists public.safety_stock_buffer
(
    sku_id          uuid                                    not null,
    dc_code         varchar                                 not null,
    week            varchar                                 not null,
    buffer          decimal                                 not null,
    created_at      timestamp default CURRENT_TIMESTAMP     not null ,
    updated_at      timestamp default CURRENT_TIMESTAMP     not null ,
    primary key (sku_id, dc_code, week)
);

create index if not exists safety_stock_buffer_index
    on public.safety_stock_buffer (sku_id, dc_code, week);

create or replace trigger set_timestamp
    before update on public.safety_stock_buffer
    for each row
    execute procedure public.trigger_set_timestamp();
