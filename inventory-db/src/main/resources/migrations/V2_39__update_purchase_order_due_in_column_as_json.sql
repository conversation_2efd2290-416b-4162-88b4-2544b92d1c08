ALTER TABLE IF EXISTS public.calculation
ALTER COLUMN purchase_order_due_in_for_suppliers type jsonb using purchase_order_due_in_for_suppliers::jsonb;

ALTER TABLE IF EXISTS public.pre_production_calculation
ALTER COLUMN purchase_order_due_in_for_suppliers type jsonb using purchase_order_due_in_for_suppliers::jsonb;

ALTER TABLE IF EXISTS public.live_inventory_calculation
ALTER COLUMN purchase_order_due_in_for_suppliers type jsonb using purchase_order_due_in_for_suppliers::jsonb;

ALTER TABLE IF EXISTS public.live_inventory_pre_production_calculation
ALTER COLUMN purchase_order_due_in_for_suppliers type jsonb using purchase_order_due_in_for_suppliers::jsonb;
