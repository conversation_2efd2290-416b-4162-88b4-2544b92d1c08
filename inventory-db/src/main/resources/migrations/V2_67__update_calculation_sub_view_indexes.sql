CREATE UNIQUE INDEX IF NOT EXISTS
    calculation_substitution_view_index
    ON calculation_substitution_view (csku_id, dc_code, production_week);

CREATE UNIQUE INDEX IF NOT EXISTS
    pre_prod_calculation_substitution_view_index
    ON pre_prod_calculation_substitution_view (csku_id, dc_code, production_week);

CREATE UNIQUE INDEX IF NOT EXISTS
    live_calculation_substitution_view_index
    ON live_calculation_substitution_view (csku_id, dc_code, production_week);

CREATE UNIQUE INDEX IF NOT EXISTS
    live_inventory_pre_production_calculation_substitution_view_index
    ON live_inventory_pre_production_calculation_substitution_view (csku_id, dc_code, production_week);
