
-- CALCULATION
-----
ALTER TABLE public.calculation DETACH PARTITION public.calculation_current;

ALTER TABLE IF EXISTS public.calculation_current RENAME TO calculation_042024;

ALTER TABLE IF EXISTS public.calculation_042024 DROP CONSTRAINT IF EXISTS calculation_current_date_partition;

ALTER INDEX IF EXISTS calculation_current_pkey RENAME TO calculation_042024_pkey;
ALTER INDEX IF EXISTS calculation_current_dc_code_production_week_date_idx RENAME TO calculation_042024_dc_week_date_idx;
ALTER INDEX IF EXISTS calculation_current_production_week_dc_code_csku_id_idx RENAME TO calculation_042024_week_dc_sku_idx;

-----

create table if not exists public.calculation_current partition of public.calculation for values from ('2024-05-01') to ('2099-01-01');

insert into public.calculation_current select * from public.calculation_042024 where date >= '2024-05-01';
alter table public.calculation_current add constraint calculation_current_date_partition check (date >='2024-05-01' and date <'2099-01-01');

grant select on public.calculation_current to readonly;

-----

delete from public.calculation_042024 where date >= '2024-05-01';
alter table public.calculation_042024 add constraint calculation_042024_date_partition check (date >= '2023-06-01' and date <'2024-05-01');
alter table public.calculation attach partition public.calculation_042024 for values from ('2023-06-01') to ('2024-05-01');
grant select on public.calculation_042024 to readonly;

-----

-- PRE PROD CALCULATION
-----

ALTER TABLE public.pre_production_calculation DETACH PARTITION public.pre_production_calculation_current;

ALTER TABLE IF EXISTS public.pre_production_calculation_current RENAME TO pre_production_calculation_042024;

ALTER TABLE IF EXISTS public.pre_production_calculation_042024 DROP CONSTRAINT IF EXISTS pre_production_calculation_current_date_partition;

ALTER INDEX IF EXISTS pre_production_calculation_current_pkey RENAME TO pre_production_calculation_042024_pkey;
ALTER INDEX IF EXISTS pre_production_calculation_cur_dc_code_production_week_date_idx RENAME TO pre_production_calculation_042024_dc_week_date_idx;
ALTER INDEX IF EXISTS pre_production_calculation_cu_production_week_dc_code_csku__idx RENAME TO pre_production_calculation_042024_week_dc_sku_idx;

-----

create table if not exists public.pre_production_calculation_current partition of public.pre_production_calculation for values from ('2024-05-01') to ('2099-01-01');

insert into public.pre_production_calculation_current select * from public.pre_production_calculation_042024 where date >= '2024-05-01';
alter table public.pre_production_calculation_current add constraint pre_production_calculation_current_date_partition check (date >='2024-05-01' and date <'2099-01-01');

grant select on public.pre_production_calculation_current to readonly;

-----

delete from public.pre_production_calculation_042024 where date >= '2024-05-01';
alter table public.pre_production_calculation_042024 add constraint pre_production_calculation_042024_date_partition check (date >= '2023-06-01' and date <'2024-05-01');
alter table public.pre_production_calculation attach partition public.pre_production_calculation_042024 for values from ('2023-06-01') to ('2024-05-01');
grant select on public.pre_production_calculation_042024 to readonly;

