
create table if not exists public.inventory_snapshot_bkp as table public.inventory_snapshot;

-- Rename current table
ALTER TABLE public.inventory_snapshot RENAME TO inventory_snapshot_current;
alter index if exists inventory_snapshot_pkey rename to inventory_snapshot_current_pkey;
alter index if exists inventory_snapshot_dc_code_sku_id_snapshot_id_key rename to inventory_snapshot_current_dc_code_sku_id_snapshot_id_key;
drop trigger if exists set_timestamp on inventory_snapshot_current;

-- New partitioned table

create table if not exists public.inventory_snapshot
(
    dc_code          varchar                             not null,
    date             date                                not null,
    sku_id           uuid                                not null,
    snapshot_id      uuid                                not null,
    value            jsonb                               not null,
    created_at       timestamp default CURRENT_TIMESTAMP not null,
    updated_at       timestamp default CURRENT_TIMESTAMP not null,
    primary key (dc_code, date, sku_id)
    ) PARTITION BY RANGE(date);

create or replace trigger set_timestamp
    before update on public.inventory_snapshot
                      for each row
                      execute procedure public.trigger_set_timestamp();

grant select on public.inventory_snapshot to readonly;

-- Clean and attach current table

delete from public.inventory_snapshot_current where date < '2023-08-23';
alter table public.inventory_snapshot_current add constraint inventory_snapshot_current_date_partition check (date >='2023-08-23' and date <'2099-01-01');

alter table public.inventory_snapshot attach partition public.inventory_snapshot_current for values from ('2023-08-23') to ('2099-01-01');

grant select on public.inventory_snapshot_current to readonly;

-- Create and populate historical partition table

create table if not exists public.inventory_snapshot_22082023 partition of public.inventory_snapshot for values from ('2000-01-01') to ('2023-08-23');
create unique index if not exists inventory_snapshot_22082023_dc_code_sku_id_snapshot_id_key on inventory_snapshot_22082023 (dc_code, sku_id, snapshot_id);

insert into inventory_snapshot_22082023(dc_Code, date, sku_id, snapshot_id, value)
select i.dc_code, i.date, i.sku_id, inventory_with_snapshot.snapshot_id, i.value
from inventory i
         join (select dc_code, date, gen_random_uuid() as snapshot_id
               from inventory inner_inv
               where date < '2023-08-23' and dc_Code in (select dc_Code from inventory_snapshot where date = '2023-08-23')
               group by dc_code, date
              ) as inventory_with_snapshot
         on inventory_with_snapshot.dc_Code = i.dc_Code and inventory_with_snapshot.date = i.date;

alter table public.inventory_snapshot_22082023 add constraint inventory_snapshot_22082023_date_partition check (date >= '2000-01-01' and date <'2023-08-23');

grant select on public.inventory_snapshot_22082023 to readonly;

