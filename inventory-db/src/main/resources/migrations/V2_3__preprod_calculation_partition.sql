
create table if not exists public.pre_production_calculation_bkp as table public.pre_production_calculation;

ALTER TABLE public.pre_production_calculation RENAME TO pre_production_calculation_052023;

drop index if exists preprod_calculation_unenriched_week_dc_sku_index;
alter index if exists pre_production_calculation_unenriched_pkey rename to pre_production_calculation_052023_pkey;

create table if not exists public.pre_production_calculation
(
    dc_code             text not null,
    production_week     text not null,
    csku_id             uuid not null,
    date                date not null,
    expired             bigint,
    opening_stock       bigint,
    demanded            bigint,
    present             bigint,
    closing_stock       bigint,
    actual_inbound      bigint,
    expected_inbound    bigint,
    actual_inbound_po   text,
    expected_inbound_po text,
    daily_needs         bigint,
    created_at          timestamp default now(),
    actual_consumption  bigint,
    safetystock         bigint,
    safetystock_needs   bigint,
    updated_at          timestamp,
    constraint pre_production_calculation_pkey
    primary key (csku_id, dc_code, date)
    ) PARTITION BY RANGE(date);


create index  if not exists pre_production_calculation_week_dc_sku_index on pre_production_calculation (production_week, dc_code, csku_id);
create index  if not exists pre_production_calculation_dccode_week_index on pre_production_calculation (dc_code, production_week, date);

grant select on public.pre_production_calculation to readonly;

------------
create table if not exists public.pre_production_calculation_current partition of public.pre_production_calculation for values from ('2023-06-01') to ('2099-01-01');

insert into public.pre_production_calculation_current select * from public.pre_production_calculation_052023 where date >= '2023-06-01';
alter table public.pre_production_calculation_current add constraint pre_production_calculation_current_date_partition check (date >='2023-06-01' and date <'2099-01-01');

grant select on public.pre_production_calculation_current to readonly;
------------

delete from public.pre_production_calculation_052023 where date >= '2023-06-01';
alter table public.pre_production_calculation_052023 add constraint pre_production_calculation_052023_date_partition check (date >= '2000-01-01' and date <'2023-06-01');
alter table public.pre_production_calculation attach partition public.pre_production_calculation_052023 for values from ('2000-01-01') to ('2023-06-01');

grant select on public.pre_production_calculation_052023 to readonly;
