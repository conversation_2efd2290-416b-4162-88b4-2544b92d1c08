DO
$$
BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'export_status') THEN
CREATE TYPE export_status AS ENUM ('Pending', 'Failed', 'Completed');
END IF;
END
$$;

create table if not exists public.file_export_request
(
    request_id UUID PRIMARY KEY,
    parameters JSONB,
    status export_status NOT NULL default 'Pending',
    file_url TEXT,
    created_at TIMESTAMP default CURRENT_TIMESTAMP not null,
    updated_at TIMESTAMP default CURRENT_TIMESTAMP not null
);

create or replace trigger set_timestamp
    before update on public.file_export_request
        for each row
        execute procedure public.trigger_set_timestamp();
