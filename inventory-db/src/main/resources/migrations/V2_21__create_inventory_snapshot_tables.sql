create table if not exists public.inventory_raw_snapshot
(
    hash_message        text    primary key,
    snapshot_id         uuid    not null,
    snapshot_time       timestamp with time zone    not null,
    dc_code             varchar    not null,
    sku_code            text    not null,
    expiration_date     date,
    quantity            bigint  not null,
    location_type       text    not null,
    created_at          timestamp with time zone default now()
);

create index if not exists inventory_raw_snapshot_index
    on public.inventory_raw_snapshot (snapshot_id, snapshot_time, dc_code);

grant select on public.inventory_raw_snapshot to readonly;

select schedule_cron_job('clean inventory_raw_snapshot schedule', '30 1 * * *', 'delete from inventory_raw_snapshot where snapshot_time < now()::date - interval ''2 days''');


create table if not exists public.inventory_snapshot
(
    dc_code          varchar                             not null,
    date             date                                not null,
    sku_id           uuid                                not null,
    snapshot_id      uuid                                not null,
    value            jsonb                               not null,
    created_at       timestamp default CURRENT_TIMESTAMP not null,
    updated_at       timestamp default CURRENT_TIMESTAMP not null,
    primary key (dc_code, date, sku_id),
    unique (dc_code, sku_id, snapshot_id)
    );

create or replace trigger set_timestamp
    before update on public.inventory_snapshot
    for each row
    execute procedure public.trigger_set_timestamp();

grant select on public.inventory_snapshot to readonly;
