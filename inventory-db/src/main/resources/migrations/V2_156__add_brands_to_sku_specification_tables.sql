ALTER TABLE IF EXISTS public.sku_specification
    ADD COLUMN IF NOT EXISTS brands text[] DEFAULT ARRAY[]::TEXT[];

DROP MATERIALIZED VIEW IF EXISTS sku_specification_view;

CREATE MATERIALIZED VIEW IF NOT EXISTS sku_specification_view AS
SELECT DISTINCT ON (code, market)
    id, parent_id, category, code, name, acceptable_code_life, cooling_type, packaging, market, uom, brands
FROM (
    SELECT DISTINCT ON(id) *
    FROM (
    SELECT *, '1' AS priority
    FROM sku_specification
    UNION ALL
    SELECT id, parent_id, category, code, name, acceptable_code_life, cooling_type, packaging, market, uom,
    ARRAY['youfoodz']::text[] AS brands, '2' AS priority
    FROM sku_specification_yf
    ) AS distinct_id_skus
    ORDER BY id, priority ASC
    ) AS all_distinct_skus
ORDER BY code, market, priority ASC;


GRANT SELECT ON sku_specification_view TO readonly;

CREATE UNIQUE INDEX IF NOT EXISTS
    sku_specification_view_id_idx
    ON sku_specification_view (id);

CREATE UNIQUE INDEX IF NOT EXISTS
    sku_specification_view_market_code_idx
    ON sku_specification_view (market, code);

SELECT schedule_cron_job('sku_specification_view_schedule', '*/2 * * * *',
                         'REFRESH MATERIALIZED VIEW CONCURRENTLY sku_specification_view;');
