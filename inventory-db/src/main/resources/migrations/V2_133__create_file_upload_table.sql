DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'file_upload_status') THEN
        CREATE TYPE file_upload_status AS ENUM ('IMPORTED', 'ERROR');
    END IF;
END $$;

CREATE TABLE IF NOT EXISTS public.file_uploads
(
    id                      UUID 	    NOT NULL,
    file_name     		    TEXT 	    NOT NULL,
    market                  TEXT        NOT NULL,
    dcs                     TEXT[]      NOT NULL,
    author_name     	    TEXT 	    NOT NULL,
    author_email     	    TEXT 	    NOT NULL,
    status                  file_upload_status NOT NULL,
    message                 TEXT,
    created_at              TIMESTAMP DEFAULT CURRENT_TIMESTAMP     NOT NULL
);

create or replace trigger set_timestamp
    before update on public.file_uploads
    for each row
    execute procedure public.trigger_set_timestamp();

grant select on public.file_uploads to readonly;
