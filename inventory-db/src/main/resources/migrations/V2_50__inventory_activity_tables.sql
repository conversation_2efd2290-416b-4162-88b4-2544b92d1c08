
DO $$ BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'inventory_activity_type') THEN
       CREATE TYPE inventory_activity_type AS ENUM ('ADJ', 'MOV');
    END IF;
END $$;


create table if not exists public.inventory_activity
(
    hash_message     text                                   primary key,
    activity_id      uuid                                   not null,
    activity_time    timestamp with time zone               not null,
    dc_code          text                                   not null,
    sku_id           uuid                                   not null,
    type             inventory_activity_type                not null,
    type_id          text                                   not null,
    value            jsonb                                  not null,
    published_time   timestamp with time zone               not null,
    created_at       timestamp with time zone default now() not null
);

create index if not exists inventory_activity_time_dc_index
    on public.inventory_activity (activity_time, dc_Code, type_id, type);

grant select on public.inventory_activity to readonly;


select schedule_cron_job('clean inventory_activity schedule', '0 2 * * *', 'delete from inventory_activity where inventory_time < now()::date - interval ''15 days''');

