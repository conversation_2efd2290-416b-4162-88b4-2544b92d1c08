CREATE TABLE IF NOT EXISTS sqr_short_shelf_life_conf
(
    dc_code                         varchar      NOT NULL,
    date                            date         NOT NULL,
    sku_id                          uuid         NOT NULL,
    buffer_percentage               decimal      NOT NULL,
    buffer_additional               decimal      NOT NULL,
    created_at                      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at                      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (dc_code, date, sku_id)
);

create or replace trigger set_timestamp
    before update on public.sqr_short_shelf_life_conf
                      for each row
                      execute procedure public.trigger_set_timestamp();

GRANT SELECT ON public.sqr_short_shelf_life_conf TO readonly;

CREATE TABLE IF NOT EXISTS sqr_short_shelf_life
(
    dc_code                         varchar      NOT NULL,
    date                            date         NOT NULL,
    sku_id                          uuid         NOT NULL,
    opening_sock                    decimal       NOT NULL,
    unusable_stock                  decimal       NOT NULL,
    consumption                     decimal       NOT NULL,
    stock_update                    decimal,
    buffer_percentage               decimal       NOT NULL,
    buffer_additional               decimal       NOT NULL,
    sqr                             decimal       NOT NULL,
    uom                             uom          NOT NULL,
    created_at                      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at                      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (dc_code, date, sku_id)
    );

create or replace trigger set_timestamp
    before update on public.sqr_short_shelf_life
                      for each row
                      execute procedure public.trigger_set_timestamp();

GRANT SELECT ON public.sqr_short_shelf_life TO readonly;
