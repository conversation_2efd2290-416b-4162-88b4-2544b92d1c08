ALTER TABLE IF EXISTS public.purchase_order
    ADD COLUMN supplier_name varchar;

ALTER TABLE IF EXISTS public.purchase_order
    ALTER COLUMN po_id DROP NOT NULL;

DROP MATERIALIZED VIEW IF EXISTS PURCHASE_ORDERS_VIEW;

CREATE
MATERIALIZED VIEW IF NOT EXISTS PURCHASE_ORDERS_VIEW AS
WITH
    sku_mlor AS (
        SELECT ss.mlor_days AS mlor, s.id AS supplierId, scs.culinary_sku_id AS culinary_sku_id
        FROM supplier_sku ss
                 JOIN supplier_culinary_sku scs ON scs.id = ss.supplier_sku_id
                 JOIN supplier s ON scs.supplier_id = s.parent_id
        WHERE ss.status ILIKE 'active'
    ),
    purchase_order AS (
        SELECT po.*, posku.sku_id, posku.quantity, posku.uom,
               COALESCE(po.supplier_name, supplier.name) AS po_supplier_name,
               sku_mlor.mlor,
               (po.expected_arrival_start_time AT TIME ZONE dcConfig.zone_id) AS po_dc_local_expected_arrival_start_time
        FROM purchase_order po
                 LEFT JOIN purchase_order_sku posku ON po.po_number = posku.po_number
                 LEFT JOIN supplier ON supplier.id = po.supplier_id
                 LEFT JOIN sku_mlor ON (sku_mlor.culinary_sku_id = posku.sku_id AND sku_mlor.supplierId = po.supplier_id)
                 JOIN (SELECT dc_code, zone_id FROM dc_config) AS dcConfig ON (dcConfig.dc_code = po.dc_code)
        WHERE (po.expected_arrival_start_time AT TIME ZONE dcConfig.zone_id) > (NOW() AT TIME ZONE dcConfig.zone_id) - INTERVAL '70 days'
    ),
    grn AS (
        SELECT (goods_received_note.delivery_time AT TIME ZONE dcConfig.zone_id) AS dc_local_delivery_time,
               goods_received_note.*, grnMlor.mlor
        FROM goods_received_note
                 LEFT JOIN (
            SELECT supplier_sku.sku_id, MIN(supplier_sku.mlor_days) AS mlor
            FROM supplier_sku
            WHERE supplier_sku.status ILIKE 'active'
            GROUP BY supplier_sku.sku_id
        ) AS grnMlor ON (grnMlor.sku_id = goods_received_note.sku_id)
                 JOIN (
            SELECT dc_code, zone_id FROM dc_config
        ) AS dcConfig ON (dcConfig.dc_code = goods_received_note.dc_code)
        WHERE (goods_received_note.delivery_time AT TIME ZONE dcConfig.zone_id) > (NOW() AT TIME ZONE dcConfig.zone_id) - INTERVAL '70 days'
    )
SELECT
    purchase_order.po_id                                         AS po_id,
    purchase_order.po_number                                     AS po_number,
    purchase_order.po_ref                                        AS po_ref,
    purchase_order.dc_code                                       AS po_dc_code,
    purchase_order.sku_id                                        AS po_sku_id,
    purchase_order.expected_arrival_start_time                   AS po_expected_arrival_start_time,
    purchase_order.expected_arrival_end_time                     AS po_expected_arrival_end_time,
    purchase_order.quantity                                      AS po_quantity,
    purchase_order.uom                                           AS po_uom,
    purchase_order.supplier_id                                   AS po_supplier_id,
    purchase_order.po_supplier_name                              AS po_supplier_name,
    purchase_order.mlor                                          AS po_mlor,
    purchase_order.status                                        AS po_status,
    purchase_order.po_dc_local_expected_arrival_start_time       AS po_dc_local_expected_arrival_start_time,
    grn.po_number                                                AS grn_po_number,
    grn.po_ref                                                   AS grn_po_ref,
    grn.dc_code                                                  AS grn_dc_code,
    grn.sku_id                                                   AS grn_sku_id,
    grn.delivery_id                                              AS grn_delivery_id,
    grn.delivery_time                                            AS grn_delivery_time,
    grn.quantity                                                 AS grn_quantity,
    grn.uom                                                      AS grn_uom,
    grn.expiry_date                                              AS grn_expiry_date,
    grn.delivery_status                                          AS grn_delivery_status,
    grn.dc_local_delivery_time                                   AS grn_dc_local_delivery_time,
    CASE
        WHEN purchase_order.mlor IS NULL THEN grn.mlor ELSE purchase_order.mlor
        END                                                         AS grn_mlor
FROM purchase_order
         FULL JOIN grn
                   ON (
                       purchase_order.po_number = grn.po_number
                           AND purchase_order.sku_id = grn.sku_id
                           AND purchase_order.dc_code = grn.dc_code
                       );

CREATE INDEX IF NOT EXISTS purchase_orders_view_po_ref
    ON public.purchase_orders_view (po_ref, grn_po_ref);

CREATE INDEX IF NOT EXISTS purchase_orders_view_dc_code
    ON public.purchase_orders_view (po_dc_code, grn_dc_code);

CREATE INDEX IF NOT EXISTS purchase_orders_view_expected_arrival_start_time
    ON public.purchase_orders_view (po_dc_local_expected_arrival_start_time, grn_dc_local_delivery_time);

CREATE UNIQUE INDEX IF NOT EXISTS purchase_orders_view_index
    ON public.purchase_orders_view (po_number, grn_po_number, po_sku_id, grn_sku_id, po_dc_code, grn_dc_code,
    po_dc_local_expected_arrival_start_time, grn_delivery_id, grn_dc_local_delivery_time);

GRANT SELECT ON public.purchase_orders_view TO readonly;


