DROP MATERIALIZED VIEW IF EXISTS public.actual_consumption;

CREATE TABLE IF NOT EXISTS public.actual_consumption (
    dc_code          VARCHAR NOT NULL,
    sku_id           UUID NOT NULL,
    date             DATE NOT NULL,
    quantity         DECIMAL NOT NULL,
    uom              uom NOT NULL
);

CREATE MATERIALIZED VIEW IF NOT EXISTS public.actual_consumption_view AS
SELECT p2l.dc_code,
       sku.id               AS sku_id,
       sku.uom              AS uom,
       (p2l.created_at at time zone dc.zone_id)::date as date,
       sum(p2l.quantity)::numeric    AS quantity
FROM pick_2_light p2l
    JOIN sku_specification sku ON sku.code = p2l.csku_code AND sku.market = lower(p2l.market)
    JOIN dc_config dc on (dc.dc_code = p2l.dc_code)
WHERE p2l.created_at >=
    date_trunc('day', now() at time zone dc.zone_id) at time zone dc.zone_id - INTERVAL '10 DAY'
GROUP BY p2l.dc_code, sku_id, date
UNION
SELECT
    ac.dc_code,
    ac.sku_id,
    ac.uom,
    ac.date,
    ac.quantity
FROM actual_consumption ac
JOIN dc_config dc on dc.dc_code = ac.dc_code
WHERE ac.date >= (now() at time zone dc.zone_id)::date - 10;

drop index if exists actual_consumption_dc_code_sku_id_date;
create unique index  if not exists actual_consumption_view_dc_code_sku_id_date
    on public.actual_consumption_view (dc_code, sku_id, date);

grant select on public.actual_consumption_view to readonly;

select schedule_cron_job('actual_consumption_view_schedule', '0 */2 * * *', 'refresh materialized view concurrently actual_consumption_view;');
