
create table if not exists public.calculation_bkp as table public.calculation;

ALTER TABLE public.calculation RENAME TO calculation_052023;

drop index if exists calculation_dccode_week_index;
drop index if exists calculation_unenriched_week_dc_sku_index;
alter index if exists calculation_unenriched_pkey rename to calculation_052023_pkey;

create table if not exists public.calculation
(
    dc_code             text not null,
    production_week     text not null,
    csku_id             uuid not null,
    date                date not null,
    expired             bigint,
    opening_stock       bigint,
    demanded            bigint,
    present             bigint,
    closing_stock       bigint,
    actual_inbound      bigint,
    expected_inbound    bigint,
    actual_inbound_po   text,
    expected_inbound_po text,
    daily_needs         bigint,
    created_at          timestamp default now(),
    actual_consumption  bigint,
    safetystock         bigint,
    safetystock_needs   bigint,
    updated_at          timestamp,
    constraint calculation_pkey
    primary key (csku_id, dc_code, date)
    ) PARTITION BY RANGE(date);


create index  if not exists calculation_week_dc_sku_index on calculation (production_week, dc_code, csku_id);
create index  if not exists calculation_dccode_week_index on calculation (dc_code, production_week, date);

grant select on public.calculation to readonly;

------------
create table if not exists public.calculation_current partition of public.calculation for values from ('2023-06-01') to ('2099-01-01');

insert into public.calculation_current select * from public.calculation_052023 where date >= '2023-06-01';
alter table public.calculation_current add constraint calculation_current_date_partition check (date >='2023-06-01' and date <'2099-01-01');

grant select on public.calculation_current to readonly;
------------

delete from public.calculation_052023 where date >= '2023-06-01';
alter table public.calculation_052023 add constraint calculation_052023_date_partition check (date >= '2000-01-01' and date <'2023-06-01');
alter table public.calculation attach partition public.calculation_052023 for values from ('2000-01-01') to ('2023-06-01');

grant select on public.calculation_052023 to readonly;

-----------

drop MATERIALIZED view if exists public.dc_config_weight_view;

create materialized view if not exists public.dc_config_weight_view as
SELECT dc_config.dc_code,
       dc_config.market,
       dc_config.production_start,
       dc_config.cleardown,
       dc_config.zone_id,
       dc_config.enabled,
       dc_config.record_timestamp,
       dc_config.created_at,
       dc_config.updated_at,
       dc_config.has_cleardown,
       CASE
           WHEN dc_calc.sku_count IS NULL THEN 0
           ELSE dc_calc.sku_count
           END AS weight
FROM dc_config
         LEFT JOIN (SELECT c.dc_code,
                           count(DISTINCT c.csku_id) AS sku_count
                    FROM calculation c
                    WHERE c.date > now()
                    GROUP BY c.dc_code) dc_calc ON dc_calc.dc_code = dc_config.dc_code
WHERE dc_config.enabled = true;

create unique index  if not exists dc_config_weight_view_index
    on public.dc_config_weight_view (dc_code);

grant select on public.dc_config_weight_view to readonly;
