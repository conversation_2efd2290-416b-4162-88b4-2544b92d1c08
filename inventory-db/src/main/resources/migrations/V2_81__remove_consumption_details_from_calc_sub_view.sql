DROP MATERIALIZED VIEW IF EXISTS calculation_substitution_view;
DROP MATERIALIZED VIEW IF EXISTS pre_prod_calculation_substitution_view;
DROP MATERIALIZED VIEW IF EXISTS live_calculation_substitution_view;
DROP MATERIALIZED VIEW IF EXISTS live_inventory_pre_production_calculation_substitution_view;

CREATE MATERIALIZED VIEW IF NOT EXISTS calculation_substitution_view AS
SELECT
    calculation.csku_id,
    calculation.dc_code,
    calculation.production_week,
    jsonb_agg(
        json_build_object('date', demand.date,
                          'inQty', COALESCE(demand.substituted_in_qty,0),
                          'outQty', COALESCE(demand.substituted_out_qty,0)
        )
    ) FILTER (WHERE demand.substituted_in_qty > 0 or demand.substituted_out_qty > 0)
    as substitutions,
    substitution_type(array_agg(DISTINCT demand.subbed)) as demand_subbed
FROM
    calculation
        INNER JOIN
    demand ON calculation.csku_id = demand.sku_id
        AND calculation.dc_code = demand.dc_code
        AND calculation.date = demand.date
WHERE
    calculation.date > CURRENT_DATE - interval '70 days'
  AND demand.date > CURRENT_DATE - interval '70 days'
GROUP BY
    calculation.csku_id, calculation.dc_code, calculation.production_week;

CREATE MATERIALIZED VIEW IF NOT EXISTS pre_prod_calculation_substitution_view AS
SELECT
    pre_production_calculation.csku_id,
    pre_production_calculation.dc_code,
    pre_production_calculation.production_week,
    jsonb_agg(
        json_build_object('date', demand.date,
                          'inQty', COALESCE(demand.substituted_in_qty,0),
                          'outQty', COALESCE(demand.substituted_out_qty,0)
        )
    ) FILTER (WHERE demand.substituted_in_qty > 0 or demand.substituted_out_qty > 0)
    as substitutions,
    substitution_type(array_agg(DISTINCT demand.subbed)) as demand_subbed
FROM
    pre_production_calculation
        INNER JOIN
    demand ON pre_production_calculation.csku_id = demand.sku_id
        AND pre_production_calculation.dc_code = demand.dc_code
        AND pre_production_calculation.date = demand.date
WHERE
    pre_production_calculation.date > CURRENT_DATE - interval '70 days'
  AND demand.date > CURRENT_DATE - interval '70 days'
GROUP BY
    pre_production_calculation.csku_id, pre_production_calculation.dc_code, pre_production_calculation.production_week;

CREATE MATERIALIZED VIEW IF NOT EXISTS live_calculation_substitution_view AS
SELECT
    live_inventory_calculation.csku_id,
    live_inventory_calculation.dc_code,
    live_inventory_calculation.production_week,
    jsonb_agg(
        json_build_object('date', demand.date,
                          'inQty', COALESCE(demand.substituted_in_qty,0),
                          'outQty', COALESCE(demand.substituted_out_qty,0)
        )
    ) FILTER (WHERE demand.substituted_in_qty > 0 or demand.substituted_out_qty > 0)
    as substitutions,
    substitution_type(array_agg(DISTINCT demand.subbed)) as demand_subbed
FROM
    live_inventory_calculation
        INNER JOIN
    demand ON live_inventory_calculation.csku_id = demand.sku_id
        AND live_inventory_calculation.dc_code = demand.dc_code
        AND live_inventory_calculation.date = demand.date
WHERE
    live_inventory_calculation.date > CURRENT_DATE - interval '70 days'
  AND demand.date > CURRENT_DATE - interval '70 days'
GROUP BY
    live_inventory_calculation.csku_id, live_inventory_calculation.dc_code, live_inventory_calculation.production_week;

CREATE MATERIALIZED VIEW IF NOT EXISTS live_inventory_pre_production_calculation_substitution_view AS
SELECT
    live_inventory_pre_production_calculation.csku_id,
    live_inventory_pre_production_calculation.dc_code,
    live_inventory_pre_production_calculation.production_week,
    jsonb_agg(
        json_build_object('date', demand.date,
                          'inQty', COALESCE(demand.substituted_in_qty,0),
                          'outQty', COALESCE(demand.substituted_out_qty,0)
        )
    ) FILTER (WHERE demand.substituted_in_qty > 0 or demand.substituted_out_qty > 0)
    as substitutions,
    substitution_type(array_agg(DISTINCT demand.subbed)) as demand_subbed
FROM
    live_inventory_pre_production_calculation
        INNER JOIN
    demand ON live_inventory_pre_production_calculation.csku_id = demand.sku_id
        AND live_inventory_pre_production_calculation.dc_code = demand.dc_code
        AND live_inventory_pre_production_calculation.date = demand.date
WHERE
    live_inventory_pre_production_calculation.date > CURRENT_DATE - interval '70 days'
  AND demand.date > CURRENT_DATE - interval '70 days'
GROUP BY
    live_inventory_pre_production_calculation.csku_id, live_inventory_pre_production_calculation.dc_code,
    live_inventory_pre_production_calculation.production_week;

GRANT SELECT ON live_calculation_substitution_view TO readonly;
GRANT SELECT ON live_inventory_pre_production_calculation_substitution_view TO readonly;
GRANT SELECT ON calculation_substitution_view TO readonly;
GRANT SELECT ON pre_prod_calculation_substitution_view TO readonly;

CREATE UNIQUE INDEX IF NOT EXISTS
    calculation_substitution_view_index
    ON calculation_substitution_view (csku_id, dc_code, production_week);

CREATE UNIQUE INDEX IF NOT EXISTS
    pre_prod_calculation_substitution_view_index
    ON pre_prod_calculation_substitution_view (csku_id, dc_code, production_week);

CREATE UNIQUE INDEX IF NOT EXISTS
    live_calculation_substitution_view_index
    ON live_calculation_substitution_view (csku_id, dc_code, production_week);

CREATE UNIQUE INDEX IF NOT EXISTS
    live_inventory_pre_production_calculation_substitution_view_index
    ON live_inventory_pre_production_calculation_substitution_view (csku_id, dc_code, production_week);

SELECT schedule_cron_job('pre_prod_calculation_substitution_view_schedule', '*/5 * * * *',
                         'REFRESH MATERIALIZED VIEW CONCURRENTLY pre_prod_calculation_substitution_view;');

SELECT schedule_cron_job('calculation_substitution_view_schedule', '*/5 * * * *',
                         'REFRESH MATERIALIZED VIEW CONCURRENTLY calculation_substitution_view;');

SELECT schedule_cron_job('live_calculation_substitution_view_schedule', '*/5 * * * *',
                         'REFRESH MATERIALIZED VIEW CONCURRENTLY live_calculation_substitution_view;');

SELECT schedule_cron_job('live_inventory_pre_production_calculation_substitution_view_schedule', '*/5 * * * *',
                         'REFRESH MATERIALIZED VIEW CONCURRENTLY live_inventory_pre_production_calculation_substitution_view;');
