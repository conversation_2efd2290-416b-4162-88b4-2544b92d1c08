select unschedule_cron_job('calculation_substitution_view_schedule');
SELECT schedule_cron_job('calculation_substitution_view_schedule', '*/30 * * * *',
                         'REFRESH MATERIALIZED VIEW CONCURRENTLY calculation_substitution_view;');

select unschedule_cron_job('pre_prod_calculation_substitution_view_schedule');
SELECT schedule_cron_job('pre_prod_calculation_substitution_view_schedule', '0 */1 * * *',
                         'REFRESH MATERIALIZED VIEW CONCURRENTLY pre_prod_calculation_substitution_view;');
