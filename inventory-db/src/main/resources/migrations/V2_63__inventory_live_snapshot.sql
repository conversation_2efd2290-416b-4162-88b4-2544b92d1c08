


create table if not exists public.inventory_live_snapshot
(
    dc_code          varchar                             not null,
    date             date                                not null,
    sku_id           uuid                                not null,
    snapshot_id      uuid                                not null,
    snapshot_time    timestamp with time zone            not null,
    value            jsonb                               not null,
    created_at       timestamp default CURRENT_TIMESTAMP not null,
    updated_at       timestamp default CURRENT_TIMESTAMP not null,
    primary key (dc_code, date, sku_id),
    unique(dc_code, sku_id, snapshot_id)
    );

create or replace trigger set_timestamp
    before update on public.inventory_live_snapshot
                      for each row
                      execute procedure public.trigger_set_timestamp();

grant select on public.inventory_live_snapshot to readonly;

create unique index if not exists inventory_live_snapshot_dc_code_sku_id_snapshot_id on inventory_live_snapshot (dc_code, sku_id, snapshot_id);
