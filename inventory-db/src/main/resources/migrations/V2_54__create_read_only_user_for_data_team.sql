DO
$$
BEGIN
        IF EXISTS(
            SELECT
            FROM pg_catalog.pg_roles
            WHERE rolname = 'cif-readonly-analyze') THEN
            RAISE NOTICE 'Role "cif-readonly-analyze" already exists. Skipping.';
ELSE
CREATE ROLE "cif-readonly-analyze" LOGIN PASSWORD ${cifReadonlyPassword}
    IN ROLE readonly;
END IF;
END
$$;

ALTER ROLE "cif-readonly-analyze" SET max_parallel_workers_per_gather TO 3;
ALTER ROLE "cif-readonly-analyze" SET work_mem TO '256MB';
