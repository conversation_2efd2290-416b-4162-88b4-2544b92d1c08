
ALTER TABLE IF EXISTS public.pre_production_calculation
ALTER COLUMN expired TYPE decimal,
      ALTER COLUMN opening_stock TYPE decimal,
      ALTER COLUMN demanded TYPE decimal,
      ALTER COLUMN present TYPE decimal,
      ALTER COLUMN closing_stock TYPE decimal,
      ALTER COLUMN actual_inbound TYPE decimal,
      ALTER COLUMN expected_inbound TYPE decimal,
      ALTER COLUMN daily_needs TYPE decimal,
      ALTER COLUMN actual_consumption TYPE decimal,
      ALTER COLUMN safetystock TYPE decimal,
      ALTER COLUMN safetystock_needs TYPE decimal,
      ALTER COLUMN net_needs TYPE decimal,
      ADD COLUMN IF NOT EXISTS uom uom DEFAULT 'UOM_UNIT'
      ;

ALTER TABLE IF EXISTS public.calculation
      ALTER COLUMN expired TYPE decimal,
      ALTER COLUMN opening_stock TYPE decimal,
      ALTER COLUMN demanded TYPE decimal,
      ALTER COLUMN present TYPE decimal,
      ALTER COLUMN closing_stock TYPE decimal,
      ALTER COLUMN actual_inbound TYPE decimal,
      ALTER COLUMN expected_inbound TYPE decimal,
      ALTER COLUMN daily_needs TYPE decimal,
      ALTER COLUMN actual_consumption TYPE decimal,
      ALTER COLUMN safetystock TYPE decimal,
      ALTER COLUMN safetystock_needs TYPE decimal,
      ALTER COLUMN net_needs TYPE decimal,
      ADD COLUMN IF NOT EXISTS uom uom DEFAULT 'UOM_UNIT'
      ;

