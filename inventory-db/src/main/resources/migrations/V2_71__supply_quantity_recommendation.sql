
CREATE TABLE IF NOT EXISTS public.supply_quantity_recommendation (
    dc_code                      varchar    NOT NULL,
    week                         varchar    NOT NULL,
    sku_id                       UUID       NOT NULL,
    sqr                          bigint     NOT NULL,
    inventory_rollover           bigint     NOT NULL,
    demand                       bigint     NOT NULL,
    recommendation_enabled       boolean    NOT NULL,
    safety_stock                 bigint,
    created_at                   timestamp with time zone default now() NOT NULL,
    updated_at                   timestamp with time zone default now() NOT NULL,
    PRIMARY KEY (dc_code, week, sku_id)
);

GRANT SELECT ON public.supply_quantity_recommendation TO readonly;


create or replace trigger set_timestamp
    before update on public.supply_quantity_recommendation
                      for each row
                      execute procedure public.trigger_set_timestamp();

GRANT SELECT ON public.supply_quantity_recommendation_conf TO readonly;
