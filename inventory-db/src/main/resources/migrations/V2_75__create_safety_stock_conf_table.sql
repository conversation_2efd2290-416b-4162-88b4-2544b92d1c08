CREATE TABLE IF NOT EXISTS public.safety_stock_conf (
    dc_code                      varchar    NOT NULL,
    week                         varchar    NOT NULL,
    sku_id                       UUID       NOT NULL,
    risk_multiplier              DECIMAL    NOT NULL,
    created_at                   timestamp with time zone default now() NOT NULL,
    updated_at                   timestamp with time zone default now() NOT NULL,
    PRIMARY KEY (dc_code, week, sku_id)
);

GRANT SELECT ON public.safety_stock_conf TO readonly;

create or replace trigger set_timestamp
    before update on public.safety_stock_conf
                      for each row
                      execute procedure public.trigger_set_timestamp();
