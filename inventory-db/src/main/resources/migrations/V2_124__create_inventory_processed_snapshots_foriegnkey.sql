select unschedule_cron_job('clean inventory_snapshots schedule');

ALTER TABLE inventory_all_snapshots
    ADD CONSTRAINT fk_inventory_all_snapshots
        FOREIGN KEY (snapshot_id, dc_code) REFERENCES inventory_processed_snapshots (snapshot_id, dc_code) ON DELETE CASCADE;

select schedule_cron_job('clean inventory_processed_snapshots schedule', '30 2 * * *', 'delete from inventory_processed_snapshots where snapshot_time < now()::date - interval ''15 days''');
