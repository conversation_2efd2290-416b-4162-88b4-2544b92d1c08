ALTER TABLE IF EXISTS public.calculation
    ADD COLUMN IF NOT EXISTS purchase_order_due_in_for_suppliers text default null;

ALTER TABLE IF EXISTS public.calculation
    ADD COLUMN IF NOT EXISTS max_purchase_order_due_in integer default null;

ALTER TABLE IF EXISTS public.pre_production_calculation
    ADD COLUMN IF NOT EXISTS purchase_order_due_in_for_suppliers text default null;

ALTER TABLE IF EXISTS public.pre_production_calculation
    ADD COLUMN IF NOT EXISTS max_purchase_order_due_in integer default null;

ALTER TABLE IF EXISTS public.live_inventory_calculation
    ADD COLUMN IF NOT EXISTS purchase_order_due_in_for_suppliers text default null;

ALTER TABLE IF EXISTS public.live_inventory_calculation
    ADD COLUMN IF NOT EXISTS max_purchase_order_due_in integer default null;

ALTER TABLE IF EXISTS public.live_inventory_pre_production_calculation
    ADD COLUMN IF NOT EXISTS purchase_order_due_in_for_suppliers text default null;

ALTER TABLE IF EXISTS public.live_inventory_pre_production_calculation
    ADD COLUMN IF NOT EXISTS max_purchase_order_due_in integer default null;
