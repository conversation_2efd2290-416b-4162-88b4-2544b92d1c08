DO
$$
    BEGIN
        IF
            NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'transfer_order_status') THEN
            CREATE TYPE transfer_order_status AS ENUM (
                'STATE_RESERVED',
                'STATE_DELIVERED'
                );
        END IF;
    END
$$;

CREATE TABLE IF NOT EXISTS public.transfer_order
(
    id                    UUID PRIMARY KEY,
    transfer_order_number TEXT                     NOT NULL,
    source_dc             TEXT                     NOT NULL,
    destination_dc        TEXT                     NOT NULL,
    status                transfer_order_status    NOT NULL,
    week                  TEXT                     NOT NULL,
    delivery_start_time   TIMESTAMP WITH TIME ZONE NOT NULL,
    delivery_end_time     TIMESTAMP WITH TIME ZONE NOT NULL,
    market_code           TEXT                     NOT NULL,
    created_at            TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at            TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.transfer_order_skus
(
    transfer_order_id   UUID                     NOT NULL REFERENCES transfer_order (id),
    sku_id              UUID                     NOT NULL,
    supplier_id         UUID                     NOT NULL,
    uom                 uom                      NOT NULL DEFAULT 'UOM_UNIT',
    quantity            DECIMAL                  NOT NULL,
    lot_expiration_time TIMESTAMP WITH TIME ZONE,
    created_at          TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at          TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    PRIMARY KEY (transfer_order_id, sku_id, supplier_id)
);

grant select on public.transfer_order to readonly;
grant select on public.transfer_order_skus to readonly;

create or replace trigger set_timestamp
    before update
    on public.transfer_order
    for each row
execute procedure public.trigger_set_timestamp();

create or replace trigger set_timestamp
    before update
    on public.transfer_order_skus
    for each row
execute procedure public.trigger_set_timestamp();
