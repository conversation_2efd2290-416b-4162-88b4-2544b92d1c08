ALTER TABLE IF EXISTS public.calculation
    ADD COLUMN IF NOT EXISTS expected_inbound_transfer_orders text DEFAULT null,
    ADD COLUMN IF NOT EXISTS expected_inbound_transfer_orders_quantity bigint DEFAULT null,
    ADD COLUMN IF NOT EXISTS actual_inbound_transfer_orders text DEFAULT null,
    ADD COLUMN IF NOT EXISTS actual_inbound_transfer_orders_quantity bigint DEFAULT null,

    ADD COLUMN IF NOT EXISTS expected_outbound_transfer_orders text DEFAULT null,
    ADD COLUMN IF NOT EXISTS expected_outbound_transfer_orders_quantity bigint DEFAULT null;

ALTER TABLE IF EXISTS public.pre_production_calculation
    ADD COLUMN IF NOT EXISTS expected_inbound_transfer_orders text DEFAULT null,
    ADD COLUMN IF NOT EXISTS expected_inbound_transfer_orders_quantity bigint DEFAULT null,
    ADD COLUMN IF NOT EXISTS actual_inbound_transfer_orders text DEFAULT null,
    ADD COLUMN IF NOT EXISTS actual_inbound_transfer_orders_quantity bigint DEFAULT null,

    ADD COLUMN IF NOT EXISTS expected_outbound_transfer_orders text DEFAULT null,
    ADD COLUMN IF NOT EXISTS expected_outbound_transfer_orders_quantity bigint DEFAULT null;

ALTER TABLE IF EXISTS public.live_inventory_calculation
    ADD COLUMN IF NOT EXISTS expected_inbound_transfer_orders text DEFAULT null,
    ADD COLUMN IF NOT EXISTS expected_inbound_transfer_orders_quantity bigint DEFAULT null,
    ADD COLUMN IF NOT EXISTS actual_inbound_transfer_orders text DEFAULT null,
    ADD COLUMN IF NOT EXISTS actual_inbound_transfer_orders_quantity bigint DEFAULT null,

    ADD COLUMN IF NOT EXISTS expected_outbound_transfer_orders text DEFAULT null,
    ADD COLUMN IF NOT EXISTS expected_outbound_transfer_orders_quantity bigint DEFAULT null;

ALTER TABLE IF EXISTS public.live_inventory_pre_production_calculation
    ADD COLUMN IF NOT EXISTS expected_inbound_transfer_orders text DEFAULT null,
    ADD COLUMN IF NOT EXISTS expected_inbound_transfer_orders_quantity bigint DEFAULT null,
    ADD COLUMN IF NOT EXISTS actual_inbound_transfer_orders text DEFAULT null,
    ADD COLUMN IF NOT EXISTS actual_inbound_transfer_orders_quantity bigint DEFAULT null,

    ADD COLUMN IF NOT EXISTS expected_outbound_transfer_orders text DEFAULT null,
    ADD COLUMN IF NOT EXISTS expected_outbound_transfer_orders_quantity bigint DEFAULT null;
