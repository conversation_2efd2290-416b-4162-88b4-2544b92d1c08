DROP MATERIALIZED VIEW IF EXISTS public.transfer_orders_grn_view;

CREATE MATERIALIZED VIEW public.transfer_orders_grn_view AS
WITH dc_config AS (SELECT dc_code, zone_id
                   FROM dc_config),
     transfer_order AS (SELECT to_.*,
                               tosku.sku_id,
                               tosku.quantity,
                               tosku.uom,
                               supplier.name                                     AS supplier_name,
                               dc.zone_id,
                               (to_.delivery_start_time AT TIME ZONE dc.zone_id) AS local_delivery_start_time
                        FROM transfer_order to_
                                 LEFT JOIN transfer_order_skus tosku on tosku.transfer_order_id = to_.id
                                 LEFT JOIN supplier on supplier.id = tosku.supplier_id
                                 JOIN dc_config dc ON dc.dc_code = to_.source_dc
                        WHERE to_.delivery_start_time > (now() AT TIME ZONE dc.zone_id) - interval '70 days'),
     grn AS (SELECT grn.*,
                    dc.zone_id,
                    (grn.delivery_time AT TIME ZONE dc.zone_id) AS local_delivery_time
             FROM goods_received_note grn
                      JOIN dc_config dc ON dc.dc_code = grn.dc_code
             WHERE grn.delivery_time > (now() AT TIME ZONE dc.zone_id) - interval '70 days')
SELECT to_.id                        AS to_id,
       to_.transfer_order_number     AS to_number,
       to_.source_dc                 AS to_source_dc,
       to_.destination_dc            AS to_destination_dc,
       to_.status                    AS to_status,
       to_.week                      AS to_week,
       to_.delivery_start_time       AS to_delivery_start_time,
       to_.delivery_end_time         AS to_delivery_end_time,
       to_.market_code               AS to_market_code,
       to_.supplier_name             AS to_supplier_name,
       to_.sku_id                    AS to_sku_id,
       to_.quantity                  AS to_quantity,
       to_.uom                       AS to_uom,
       to_.local_delivery_start_time AS to_local_expected_arrival_start_time,
       grn.sku_id                    AS grn_sku_id,
       grn.quantity                  AS grn_quantity,
       grn.uom                       AS grn_uom,
       grn.delivery_id               AS grn_delivery_id,
       grn.delivery_time             AS grn_delivery_time,
       grn.dc_code                   AS grn_dc_code,
       grn.expiry_date               AS grn_expire_date,
       grn.delivery_status           AS grn_delivery_status,
       grn.local_delivery_time       AS grn_local_delivery_time
FROM transfer_order to_
         LEFT JOIN grn ON (grn.po_number = to_.transfer_order_number AND
                           grn.dc_code = to_.destination_dc AND
                           grn.sku_id = to_.sku_id);

CREATE INDEX IF NOT EXISTS idx_transfer_order_skus_order_id_sku
    ON transfer_order_skus (transfer_order_id, sku_id);

CREATE INDEX IF NOT EXISTS idx_transfer_order_number_dest_dc
    ON transfer_order (transfer_order_number, destination_dc);

CREATE INDEX IF NOT EXISTS idx_transfer_order_delivery_start_time
    ON transfer_order (delivery_start_time);

CREATE INDEX IF NOT EXISTS idx_grn_po_number_dc_code_sku
    ON goods_received_note (po_number, dc_code, sku_id);

CREATE INDEX IF NOT EXISTS idx_grn_delivery_time
    ON goods_received_note (delivery_time);

grant select on public.transfer_orders_grn_view to readonly;
