CREATE MATERIALIZED VIEW IF NOT EXISTS live_calculation_substitution_view AS
SELECT live_inventory_calculation.csku_id,
       live_inventory_calculation.dc_code,
       live_inventory_calculation.production_week,
       substitution_type(array_agg(DISTINCT demand.subbed)) as demand_subbed
FROM live_inventory_calculation
         INNER JOIN demand ON
            live_inventory_calculation.csku_id = demand.sku_id
        AND live_inventory_calculation.dc_code = demand.dc_code
        AND live_inventory_calculation.date = demand.date
GROUP BY live_inventory_calculation.csku_id, live_inventory_calculation.dc_code, live_inventory_calculation.production_week;

CREATE MATERIALIZED VIEW IF NOT EXISTS live_inventory_pre_production_calculation_substitution_view AS
SELECT live_inventory_pre_production_calculation.csku_id,
       live_inventory_pre_production_calculation.dc_code,
       live_inventory_pre_production_calculation.production_week,
       substitution_type(array_agg(DISTINCT demand.subbed)) as demand_subbed
FROM live_inventory_pre_production_calculation
         INNER JOIN demand ON
            live_inventory_pre_production_calculation.csku_id = demand.sku_id
        AND live_inventory_pre_production_calculation.dc_code = demand.dc_code
        AND live_inventory_pre_production_calculation.date = demand.date
GROUP BY live_inventory_pre_production_calculation.csku_id, live_inventory_pre_production_calculation.dc_code,
         live_inventory_pre_production_calculation.production_week;

grant select on live_calculation_substitution_view to readonly;
grant select on live_inventory_pre_production_calculation_substitution_view to readonly;

select schedule_cron_job('live_calculation_substitution_view_schedule', '*/5 * * * *',
                         'refresh materialized view concurrently live_calculation_substitution_view;');

select schedule_cron_job('live_inventory_pre_production_calculation_substitution_view_schedule', '*/5 * * * *',
                         'refresh materialized view concurrently live_inventory_pre_production_calculation_substitution_view;');
