create table if not exists public.supplier_culinary_sku
(
    id                uuid    not null,
    supplier_id       uuid    not null,
    culinary_sku_id   uuid    not null,
    market            text    not null,
    status            text    not null,
    created_at        timestamp with time zone default now(),
    updated_at        timestamp with time zone default now(),
    primary key (id)
);

grant select on public.supplier_culinary_sku to readonly;
