
create table if not exists public.inventory_all_snapshots
(
    dc_code          varchar                             not null,
    snapshot_id      uuid                                not null,
    snapshot_time    timestamp with time zone            not null,
    sku_id           uuid                                not null,
    value            jsonb                               not null,
    created_at       timestamp default CURRENT_TIMESTAMP not null,
    primary key (dc_code, snapshot_id, sku_id)
);

create index if not exists inventory_all_snapshots_time_index
    on public.inventory_all_snapshots (dc_code, snapshot_time, snapshot_id);

grant select on public.inventory_all_snapshots to readonly;

select schedule_cron_job('clean inventory_snapshots schedule', '30 2 * * *', 'delete from inventory_all_snapshots where snapshot_time < now()::date - interval ''15 days''');

