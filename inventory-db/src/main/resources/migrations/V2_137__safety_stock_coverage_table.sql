create table if not exists public.safety_stock_import
(
    dc_code         varchar                                 not null,
    sku_id          uuid                                    not null,
    week            varchar                                 not null,
    safety_stock    bigint                                  not null,
    created_at      timestamp default CURRENT_TIMESTAMP     not null ,
    updated_at      timestamp default CURRENT_TIMESTAMP     not null ,
    primary key (dc_code, sku_id, week)
);

create or replace trigger set_timestamp
    before update on public.safety_stock_import
    for each row
    execute procedure public.trigger_set_timestamp();


grant select on public.safety_stock_import to readonly;
