DO
$$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'sku_risk_rating') THEN
            CREATE TYPE sku_risk_rating AS ENUM (
                'CRITICAL',
                'HIGH',
                'MEDIUM',
                'MEDIUM-LOW',
                'LOW'
                );
        END IF;
    END
$$;


ALTER TABLE IF EXISTS public.safety_stock_conf
    ADD COLUMN IF NOT EXISTS sku_risk_rating sku_risk_rating not null DEFAULT 'MEDIUM';
