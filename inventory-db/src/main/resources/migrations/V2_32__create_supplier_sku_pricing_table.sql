CREATE TABLE IF NOT EXISTS PUBLIC.supplier_sku_pricing
(
    id              UUID    NOT NULL,
    supplier_sku_id UUID    NOT NULL,
    lead_time       INTEGER NOT NULL DEFAULT 0,
    enabled         BOOLEAN NOT NULL DEFAULT FALSE ,
    market          TEXT    ,
    created_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at      TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (id)
);

grant select on public.supplier_sku_pricing to readonly;

create or replace trigger set_timestamp
    before update on public.supplier_sku_pricing
                      for each row
                      execute procedure public.trigger_set_timestamp();
