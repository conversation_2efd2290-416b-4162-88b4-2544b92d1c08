CREATE TABLE IF NOT EXISTS inventory_processed_snapshots
(
    dc_code               varchar      NOT NULL,
    snapshot_id           uuid NOT NULL,
    snapshot_time         timestamp with time zone NOT NULL,
    created_at            timestamp with time zone default now(),
    PRIMARY KEY (snapshot_id,dc_code)
);

create index if not exists inventory_processed_snapshots_dccode_id_snapshottime
    on public.inventory_processed_snapshots (dc_code, snapshot_id, snapshot_time);

grant select on public.inventory_processed_snapshots to readonly;
