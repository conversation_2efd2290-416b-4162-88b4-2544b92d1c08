
create table if not exists public.inventory_snapshot_raw
(
    snapshot_id         uuid    not null,
    snapshot_time       timestamp with time zone    not null,
    dc_code             varchar    not null,
    message_count       integer,
    created_at          timestamp with time zone default now(),
    primary key(snapshot_id,dc_code)
    );

create index if not exists inventory_snapshot_raw_id_time_dc
    on public.inventory_snapshot_raw (snapshot_id, snapshot_time, dc_code);

grant select on public.inventory_snapshot_raw to readonly;

create table if not exists public.inventory_snapshot_raw_sku
(
    hash_message            text        primary key,
    snapshot_id             uuid        not null,
    dc_code                 varchar     not null,
    sku_code                text        not null,
    expiration_timestamp    TIMESTAMP WITH TIME ZONE,
    quantity                bigint      not null,
    location_type           text        not null,
    state                   varchar     default null,
    created_at              timestamp with time zone default now(),
    FOREIGN KEY (snapshot_id, dc_code) REFERENCES inventory_snapshot_raw (snapshot_id, dc_code) ON DELETE CASCADE
    );

create index if not exists inventory_snapshot_raw_sku_snapshot_dc
    on public.inventory_snapshot_raw_sku (snapshot_id, dc_code);

grant select on public.inventory_snapshot_raw_sku to readonly;

select schedule_cron_job('clean inventory_snapshot_raw schedule', '30 1 * * *', 'delete from inventory_snapshot_raw where snapshot_time < now()::date - interval ''2 days'';');
