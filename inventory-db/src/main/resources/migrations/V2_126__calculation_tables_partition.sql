
-- CALCULATION
-----
ALTER TABLE public.calculation DETACH PARTITION public.calculation_current;

ALTER TABLE IF EXISTS public.calculation_current RENAME TO calculation_012025;

ALTER TABLE IF EXISTS public.calculation_012025 DROP CONSTRAINT IF EXISTS calculation_current_date_partition;

ALTER INDEX IF EXISTS calculation_current_pkey RENAME TO calculation_012025_pkey;
ALTER INDEX IF EXISTS calculation_current_dc_code_production_week_date_idx RENAME TO calculation_012025_dc_week_date_idx;
ALTER INDEX IF EXISTS calculation_current_production_week_dc_code_csku_id_idx RENAME TO calculation_012025_week_dc_sku_idx;

-----

create table if not exists public.calculation_current partition of public.calculation for values from ('2025-02-01') to ('2099-01-01');

insert into public.calculation_current select * from public.calculation_012025 where date >= '2025-02-01';
alter table public.calculation_current add constraint calculation_current_date_partition check (date >='2025-02-01' and date <'2099-01-01');

grant select on public.calculation_current to readonly;

-----

delete from public.calculation_012025 where date >= '2025-02-01';
alter table public.calculation_012025 add constraint calculation_012025_date_partition check (date >= '2024-05-01' and date <'2025-02-01');
alter table public.calculation attach partition public.calculation_012025 for values from ('2024-05-01') to ('2025-02-01');
grant select on public.calculation_012025 to readonly;

-----

-- PRE PROD CALCULATION
-----

ALTER TABLE public.pre_production_calculation DETACH PARTITION public.pre_production_calculation_current;

ALTER TABLE IF EXISTS public.pre_production_calculation_current RENAME TO pre_production_calculation_012025;

ALTER TABLE IF EXISTS public.pre_production_calculation_012025 DROP CONSTRAINT IF EXISTS pre_production_calculation_current_date_partition;

ALTER INDEX IF EXISTS pre_production_calculation_current_pkey RENAME TO pre_production_calculation_012025_pkey;
ALTER INDEX IF EXISTS pre_production_calculation_cur_dc_code_production_week_date_idx RENAME TO pre_production_calculation_012025_dc_week_date_idx;
ALTER INDEX IF EXISTS pre_production_calculation_cu_production_week_dc_code_csku__idx RENAME TO pre_production_calculation_012025_week_dc_sku_idx;

-----

create table if not exists public.pre_production_calculation_current partition of public.pre_production_calculation for values from ('2025-02-01') to ('2099-01-01');

insert into public.pre_production_calculation_current select * from public.pre_production_calculation_012025 where date >= '2025-02-01';
alter table public.pre_production_calculation_current add constraint pre_production_calculation_current_date_partition check (date >='2025-02-01' and date <'2099-01-01');

grant select on public.pre_production_calculation_current to readonly;

-----

delete from public.pre_production_calculation_012025 where date >= '2025-02-01';
alter table public.pre_production_calculation_012025 add constraint pre_production_calculation_012025_date_partition check (date >= '2024-05-01' and date <'2025-02-01');
alter table public.pre_production_calculation attach partition public.pre_production_calculation_012025 for values from ('2024-05-01') to ('2025-02-01');
grant select on public.pre_production_calculation_012025 to readonly;




-- LIVE CALCULATION
-----
ALTER TABLE public.live_inventory_calculation DETACH PARTITION public.live_inventory_calculation_current;

ALTER TABLE IF EXISTS public.live_inventory_calculation_current RENAME TO live_inventory_calculation_012025;

ALTER TABLE IF EXISTS public.live_inventory_calculation_012025 DROP CONSTRAINT IF EXISTS live_inventory_calculation_current_date_partition;

ALTER INDEX IF EXISTS live_inventory_calculation_current_pkey RENAME TO live_inventory_calculation_012025_pkey;
ALTER INDEX IF EXISTS live_inventory_calculation_cur_dc_code_production_week_date_idx RENAME TO live_inventory_calculation_012025_dc_week_date_idx;
ALTER INDEX IF EXISTS live_inventory_calculation_cu_production_week_dc_code_csku__idx RENAME TO live_inventory_calculation_012025_week_dc_sku_idx;

-----

create table if not exists public.live_inventory_calculation_current partition of public.live_inventory_calculation for values from ('2025-02-01') to ('2099-01-01');

insert into public.live_inventory_calculation_current select * from public.live_inventory_calculation_012025 where date >= '2025-02-01';
alter table public.live_inventory_calculation_current add constraint live_inventory_calculation_current_date_partition check (date >='2025-02-01' and date <'2099-01-01');

grant select on public.live_inventory_calculation_current to readonly;

-----

delete from public.live_inventory_calculation_012025 where date >= '2025-02-01';
alter table public.live_inventory_calculation_012025 add constraint live_inventory_calculation_012025_date_partition check (date >= '2024-05-01' and date <'2025-02-01');
alter table public.live_inventory_calculation attach partition public.live_inventory_calculation_012025 for values from ('2024-05-01') to ('2025-02-01');
grant select on public.live_inventory_calculation_012025 to readonly;

-----

-- LIVE PRE PROD CALCULATION
-----

ALTER TABLE public.live_inventory_pre_production_calculation DETACH PARTITION public.live_inventory_pre_production_calculation_current;

ALTER TABLE IF EXISTS public.live_inventory_pre_production_calculation_current RENAME TO live_inventory_pre_production_calculation_012025;

ALTER TABLE IF EXISTS public.live_inventory_pre_production_calculation_012025 DROP CONSTRAINT IF EXISTS live_inventory_pre_production_calculation_current_date_partition;

ALTER INDEX IF EXISTS live_inventory_pre_production_calculation_current_pkey RENAME TO live_inventory_pre_production_calculation_012025_pkey;
ALTER INDEX IF EXISTS live_inventory_pre_production__dc_code_production_week_date_idx RENAME TO live_inventory_pre_production_calculation_012025_dc_week_date_idx;
ALTER INDEX IF EXISTS live_inventory_pre_production_production_week_dc_code_csku__idx RENAME TO live_inventory_pre_production_calculation_012025_week_dc_sku_idx;

-----

create table if not exists public.live_inventory_pre_production_calculation_current partition of public.live_inventory_pre_production_calculation for values from ('2025-02-01') to ('2099-01-01');

insert into public.live_inventory_pre_production_calculation_current select * from public.live_inventory_pre_production_calculation_012025 where date >= '2025-02-01';
alter table public.live_inventory_pre_production_calculation_current add constraint live_inventory_pre_production_calculation_current_date_partition check (date >='2025-02-01' and date <'2099-01-01');

grant select on public.live_inventory_pre_production_calculation_current to readonly;

-----

delete from public.live_inventory_pre_production_calculation_012025 where date >= '2025-02-01';
alter table public.live_inventory_pre_production_calculation_012025 add constraint live_inventory_pre_production_calculation_012025_date_partition check (date >= '2024-05-01' and date <'2025-02-01');
alter table public.live_inventory_pre_production_calculation attach partition public.live_inventory_pre_production_calculation_012025 for values from ('2024-05-01') to ('2025-02-01');
grant select on public.live_inventory_pre_production_calculation_012025 to readonly;
