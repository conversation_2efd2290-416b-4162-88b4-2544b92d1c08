CREATE TABLE IF NOT EXISTS stock_update (
    sku_id          uuid         NOT NULL,
    dc_code         varchar      NOT NULL,
    date            date         NOT NULL,
    week            varchar      NOT NULL,
    quantity        bigint       NOT NULL,
    reason          varchar      NOT NULL,
    reason_detail   text,
    author_name     varchar,
    author_email    varchar      NOT NULL,
    created_at      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted         boolean      NOT NULL DEFAULT FALSE,
    version         int          NOT NULL,
    PRIMARY KEY (dc_code, sku_id, date, version)
    );

GRANT SELECT ON stock_update TO readonly;

CREATE INDEX idx_stock_update_dc_week ON stock_update (dc_code, week);
