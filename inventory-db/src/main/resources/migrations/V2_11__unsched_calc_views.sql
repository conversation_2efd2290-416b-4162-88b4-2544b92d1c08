DROP MATERIALIZED VIEW IF EXISTS po_by_calculation_view;
DROP MATERIALIZED VIEW IF EXISTS po_by_pre_production_calculation_view;
DROP MATERIALIZED VIEW IF EXISTS po_by_live_inventory_calculation_view;
DROP MATERIALIZED VIEW IF EXISTS po_by_live_inventory_pre_production_calculation_view;

create or replace function public.unschedule_cron_job(job_name text) returns boolean
    language plpgsql
as
$$
declare
    unscheduled boolean := false;
    jobid bigint := -1;

begin
    if exists(select * from pg_available_extensions where name = 'pg_cron') then
        if exists (select 1 from cron.job where jobname = job_name) then
          select job.jobid into jobid from cron.job where jobname = job_name;
          select cron.unschedule(jobid) into unscheduled;
        else
          raise notice 'job does not exist, skip';
        end if;
    else
        -- it skips the executing when running inside the zonky embedded DB
        -- I couldn't find a way how to load the pg_cron shared library on it.
        raise notice 'the pg_cron extension is not installed. skipping.';
    end if;
    return unscheduled;
end;
$$;

select unschedule_cron_job('po_by_calculation_view_schedule');
select unschedule_cron_job('po_by_pre_production_calculation_view_schedule');
select unschedule_cron_job('po_by_live_inventory_calculation_view_schedule');
select unschedule_cron_job('po_by_live_inventory_pre_production_calculation_view_schedule');
