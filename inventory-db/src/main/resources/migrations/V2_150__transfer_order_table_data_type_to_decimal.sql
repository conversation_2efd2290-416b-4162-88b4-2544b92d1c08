ALTER TABLE IF EXISTS public.calculation
    ALTER COLUMN expected_inbound_transfer_orders_quantity TYPE decimal,
    ALTER COLUMN actual_inbound_transfer_orders_quantity TYPE decimal,
    ALTER COLUMN expected_outbound_transfer_orders_quantity TYPE decimal;

ALTER TABLE IF EXISTS public.pre_production_calculation
    ALTER COLUMN expected_inbound_transfer_orders_quantity TYPE decimal,
    ALTER COLUMN actual_inbound_transfer_orders_quantity TYPE decimal,
    ALTER COLUMN expected_outbound_transfer_orders_quantity TYPE decimal;

ALTER TABLE IF EXISTS public.live_inventory_calculation
    ALTER COLUMN expected_inbound_transfer_orders_quantity TYPE decimal,
    ALTER COLUMN actual_inbound_transfer_orders_quantity TYPE decimal,
    ALTER COLUMN expected_outbound_transfer_orders_quantity TYPE decimal;

ALTER TABLE IF EXISTS public.live_inventory_pre_production_calculation
    ALTER COLUMN expected_inbound_transfer_orders_quantity TYPE decimal,
    ALTER COLUMN actual_inbound_transfer_orders_quantity TYPE decimal,
    ALTER COLUMN expected_outbound_transfer_orders_quantity TYPE decimal;
