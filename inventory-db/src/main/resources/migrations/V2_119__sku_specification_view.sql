
DROP MATERIALIZED VIEW IF EXISTS sku_specification_view;

CREATE MATERIALIZED VIEW IF NOT EXISTS sku_specification_view AS
select distinct on (code, market)
    id, parent_id, category, code, name, acceptable_code_life, cooling_type, packaging, market, uom
from(
    select distinct on(id) *
    from (
        select *, '1' as priority
        from sku_specification
        union all
        select *, '2' as priority
        from sku_specification_yf
    ) as distinct_id_skus
    order by id, priority asc
    ) as all_distinct_skus
order by code, market, priority asc;


GRANT SELECT ON sku_specification_view TO readonly;

CREATE UNIQUE INDEX IF NOT EXISTS
    sku_specification_view_id_idx
    ON sku_specification_view (id);

CREATE UNIQUE INDEX IF NOT EXISTS
    sku_specification_view_market_code_idx
    ON sku_specification_view (market, code);

SELECT schedule_cron_job('sku_specification_view_schedule', '*/2 * * * *',
                         'REFRESH MATERIALIZED VIEW CONCURRENTLY sku_specification_view;');
