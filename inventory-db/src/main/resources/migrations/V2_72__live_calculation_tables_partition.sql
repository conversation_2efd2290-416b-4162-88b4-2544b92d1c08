

--- LIVE CALCULATION
---

ALTER TABLE public.live_inventory_calculation RENAME TO live_inventory_calculation_042024;

ALTER INDEX IF EXISTS live_inventory_calculation_pkey RENAME TO live_inventory_calculation_042024_pkey;
ALTER INDEX IF EXISTS live_calculation_dccode_week_date_index RENAME TO live_inventory_calculation_042024_dc_week_date_idx;
ALTER INDEX IF EXISTS live_calculation_week_dc_sku_index RENAME TO live_inventory_calculation_042024_week_dc_sku_idx;


create table if not exists public.live_inventory_calculation
(
    dc_code             text not null,
    csku_id             uuid not null,
    date                date not null,
    production_week     text not null,
    expired             bigint not null,
    present             bigint not null,
    opening_stock       bigint not null,
    storage_stock       bigint not null,
    staging_stock       bigint not null,
    demanded            bigint not null,
    actual_consumption  bigint not null,
    closing_stock       bigint not null,
    actual_inbound      bigint not null,
    expected_inbound    bigint not null,
    actual_inbound_po   text,
    expected_inbound_po text,
    daily_needs         bigint not null,
    safetystock         bigint,
    safetystock_needs   bigint,
    created_at          timestamp default now(),
    updated_at          timestamp,
    purchase_order_due_in_for_suppliers jsonb,
    max_purchase_order_due_in integer default null,
    net_needs bigint default null,

    constraint live_inventory_calculation_pkey primary key (csku_id, dc_code, date)
    ) PARTITION BY RANGE(date);


create index  if not exists live_inventory_calculation_week_dc_sku_idx on live_inventory_calculation (production_week, dc_code, csku_id);
create index  if not exists live_inventory_calculation_dc_week_date_idx on live_inventory_calculation (dc_code, production_week, date);

grant select on public.live_inventory_calculation to readonly;

------------
create table if not exists public.live_inventory_calculation_current partition of public.live_inventory_calculation for values from ('2024-05-01') to ('2099-01-01');

insert into public.live_inventory_calculation_current select * from public.live_inventory_calculation_042024 where date >= '2024-05-01';
alter table public.live_inventory_calculation_current add constraint live_inventory_calculation_current_date_partition check (date >='2024-05-01' and date <'2099-01-01');

grant select on public.live_inventory_calculation_current to readonly;
------------

delete from public.live_inventory_calculation_042024 where date >= '2024-05-01';
alter table public.live_inventory_calculation_042024 add constraint live_inventory_calculation_042024_date_partition check (date >= '2000-01-01' and date <'2024-05-01');
alter table public.live_inventory_calculation attach partition public.live_inventory_calculation_042024 for values from ('2000-01-01') to ('2024-05-01');

grant select on public.live_inventory_calculation_042024 to readonly;

-----------


--- PREPROD LIVE CALCULATION
---

ALTER TABLE public.live_inventory_pre_production_calculation RENAME TO live_inventory_pre_production_calculation_042024;

ALTER INDEX IF EXISTS live_inventory_pre_production_calculation_pkey RENAME TO live_inventory_pre_prod_calculation_042024_pkey;
ALTER INDEX IF EXISTS live_inventory_pre_production__dc_code_production_week_date_idx RENAME TO live_inventory_pre_prod_calculation_042024_dc_week_date_idx;
ALTER INDEX IF EXISTS live_inventory_pre_production_production_week_dc_code_csku__idx RENAME TO live_inventory_pre_prod_calculation_042024_week_dc_sku_idx;


create table if not exists public.live_inventory_pre_production_calculation
(
    dc_code             text not null,
    csku_id             uuid not null,
    date                date not null,
    production_week     text not null,
    expired             bigint not null,
    present             bigint not null,
    opening_stock       bigint not null,
    storage_stock       bigint not null,
    staging_stock       bigint not null,
    demanded            bigint not null,
    actual_consumption  bigint not null,
    closing_stock       bigint not null,
    actual_inbound      bigint not null,
    expected_inbound    bigint not null,
    actual_inbound_po   text,
    expected_inbound_po text,
    daily_needs         bigint not null,
    safetystock         bigint,
    safetystock_needs   bigint,
    created_at          timestamp default now(),
    updated_at          timestamp,
    purchase_order_due_in_for_suppliers jsonb,
    max_purchase_order_due_in integer default null,
    net_needs bigint default null,

    constraint live_inventory_pre_production_calculation_pkey primary key (csku_id, dc_code, date)
    ) PARTITION BY RANGE(date);


create index  if not exists live_inventory_pre_production_calculation_week_dc_sku_index on live_inventory_pre_production_calculation (production_week, dc_code, csku_id);
create index  if not exists live_inventory_pre_production_calculation_dc_week_date_index on live_inventory_pre_production_calculation (dc_code, production_week, date);

grant select on public.live_inventory_pre_production_calculation to readonly;

------------
create table if not exists public.live_inventory_pre_production_calculation_current partition of public.live_inventory_pre_production_calculation for values from ('2024-05-01') to ('2099-01-01');

insert into public.live_inventory_pre_production_calculation_current select * from public.live_inventory_pre_production_calculation_042024 where date >= '2024-05-01';
alter table public.live_inventory_pre_production_calculation_current add constraint live_inventory_pre_prod_calculation_current_date_partition check (date >='2024-05-01' and date <'2099-01-01');

grant select on public.live_inventory_pre_production_calculation_current to readonly;
------------

delete from public.live_inventory_pre_production_calculation_042024 where date >= '2024-05-01';
alter table public.live_inventory_pre_production_calculation_042024 add constraint live_inventory_pre_prod_calculation_042024_date_partition check (date >= '2000-01-01' and date <'2024-05-01');
alter table public.live_inventory_pre_production_calculation attach partition public.live_inventory_pre_production_calculation_042024 for values from ('2000-01-01') to ('2024-05-01');

grant select on public.live_inventory_pre_production_calculation_042024 to readonly;
