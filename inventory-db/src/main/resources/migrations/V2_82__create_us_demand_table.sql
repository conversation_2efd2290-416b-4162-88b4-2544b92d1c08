DO
$$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'uom') THEN
            CREATE TYPE uom AS ENUM (
                'UOM_UNSPECIFIED',
                'UOM_UNIT',
                'UOM_KG',
                'UOM_LBS',
                'UOM_GAL',
                'UOM_LITRE',
                'UOM_OZ'
                );
        END IF;
    END
$$;

create table if not exists public.us_demand
(
    sku_id           uuid                                                             not null,
    dc_code          varchar                                                          not null,
    date             date                                                             not null,
    week             varchar                                                          not null,
    quantity         decimal                                                          not null,
    uom              uom                                                              not null,
    record_timestamp timestamp                                                        not null,
    created_at       timestamp with time zone default CURRENT_TIMESTAMP not null,
    updated_at       timestamp with time zone default CURRENT_TIMESTAMP not null,
    primary key (sku_id, dc_code, date, week)
);

create or replace trigger set_timestamp
    before update
    on public.us_demand
    for each row
execute procedure public.trigger_set_timestamp();

grant select on public.us_demand to "cif-readonly";
