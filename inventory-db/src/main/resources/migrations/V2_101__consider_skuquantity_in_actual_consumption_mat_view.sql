DROP MATERIALIZED VIEW IF EXISTS public.actual_consumption;

CREATE MATERIALIZED VIEW IF NOT EXISTS public.actual_consumption as
SELECT p2l.dc_code,
       sku.id               AS sku_id,
       sku.uom              AS uom,
       (p2l.created_at at time zone dc.zone_id)::date as date,
       sum(p2l.quantity)    AS quantity
FROM pick_2_light p2l
    JOIN sku_specification sku ON sku.code = p2l.csku_code AND sku.market = lower(p2l.market)
    JOIN dc_config dc on (dc.dc_code = p2l.dc_code)
WHERE p2l.created_at >=
    date_trunc('day', now() at time zone dc.zone_id) at time zone dc.zone_id - INTERVAL '7 DAY'
  and dc.dc_code != 'BX'
GROUP BY p2l.dc_code, sku_id, date
UNION
SELECT ia.dc_code, ia.sku_id,'UOM_UNIT' as uom,
       (ia.activity_time at time zone dc.zone_id)::date as date,
       SUM(
            COALESCE(
                ((ia.value->'quantity'->'value')::numeric),
                (ia.value->>'quantity')::int
            )
    ) * -1 AS quantity
FROM inventory_activity ia
    JOIN dc_config dc on (dc.dc_code = ia.dc_code)
WHERE ia.dc_Code = 'BX' AND ia.type = 'ADJ' and ia.type_id = 'PCK'
  AND ia.activity_time >= date_trunc('day', now() at time zone dc.zone_id) at time zone dc.zone_id - INTERVAL '7 DAY'
  AND COALESCE((ia.value->'quantity'->>'uom') ,'UOM_UNIT') = 'UOM_UNIT'
group by ia.dc_Code, ia.sku_id, date;

create unique index  if not exists actual_consumption_dc_code_sku_id_date
    on public.actual_consumption (dc_code, sku_id, date);

grant select on public.actual_consumption to readonly;
