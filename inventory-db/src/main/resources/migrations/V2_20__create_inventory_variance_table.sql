create table if not exists public.inventory_variance
(
    dc_code             text    not null,
    production_week     text    not null,
    sku_id              uuid    not null,
    cleardown_variance  bigint  not null,
    live_variance       bigint  not null,
    value               jsonb   not null,
    created_at          timestamp with time zone default now(),
    updated_at          timestamp with time zone default now(),
    primary key (dc_code, production_week, sku_id)
);

create or replace trigger set_timestamp
    before update on public.inventory_variance
    for each row
    execute procedure public.trigger_set_timestamp();

grant select on public.inventory_variance to readonly;
