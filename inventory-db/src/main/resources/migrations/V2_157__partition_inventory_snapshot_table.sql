SET LOCAL statement_timeout = 1800000;

create table if not exists public.inventory_snapshot_bkp as table public.inventory_snapshot;

-- Detach the current partition
ALTER TABLE public.inventory_snapshot DETACH PARTITION public.inventory_snapshot_current;

-- Rename current table to keep historical data
ALTER TABLE IF EXISTS public.inventory_snapshot_current RENAME TO inventory_snapshot_30062025;

-- Drop old constraint
ALTER TABLE IF EXISTS public.inventory_snapshot_30062025
DROP CONSTRAINT IF EXISTS inventory_snapshot_current_date_partition;

-- Rename indexes to match new table name
ALTER INDEX IF EXISTS inventory_snapshot_current_pkey
    RENAME TO inventory_snapshot_30062025_pkey;
ALTER INDEX IF EXISTS inventory_snapshot_current_dc_code_sku_id_snapshot_id_key
    RENAME TO inventory_snapshot_30062025_dc_code_sku_id_snapshot_id_key;

-- Create new "current" table for dates from 2025-06-30 onwards
CREATE TABLE IF NOT EXISTS public.inventory_snapshot_current
    PARTITION OF public.inventory_snapshot
    FOR VALUES FROM ('2025-06-30') TO ('2099-01-01');

-- Move data for 2025-06-30 and later into new current partition
INSERT INTO public.inventory_snapshot_current
SELECT *
FROM public.inventory_snapshot_30062025
WHERE date >= '2025-06-30';

-- Add constraint to new current table
ALTER TABLE public.inventory_snapshot_current
    ADD CONSTRAINT inventory_snapshot_current_date_partition
        CHECK (date >= '2025-06-30' AND date < '2099-01-01');

create index if not exists inventory_snapshot_current_dc_code_sku_id_snapshot_id_key
    on public.inventory_snapshot_current (dc_code, sku_id, snapshot_id);

-- Grant access
GRANT SELECT ON public.inventory_snapshot_current TO readonly;

-- Clean up old renamed table to only contain < 2025-06-30 data
DELETE FROM public.inventory_snapshot_30062025 WHERE date >= '2025-06-30';

-- Add correct constraint for historical table
ALTER TABLE public.inventory_snapshot_30062025
    ADD CONSTRAINT inventory_snapshot_30062025_date_partition
        CHECK (date >= '2023-08-23' AND date < '2025-06-30');

-- Attach historical partition
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_inherits
        JOIN pg_class parent ON pg_inherits.inhparent = parent.oid
        JOIN pg_class child ON pg_inherits.inhrelid = child.oid
        WHERE parent.relname = 'inventory_snapshot'
          AND child.relname = 'inventory_snapshot_30062025'
    ) THEN
ALTER TABLE public.inventory_snapshot
    ATTACH PARTITION public.inventory_snapshot_30062025
    FOR VALUES FROM ('2023-08-23') TO ('2025-06-30');
END IF;
END
$$;

-- Grant access to historical partition
GRANT SELECT ON public.inventory_snapshot_30062025 TO readonly;


