/**
* If we have multiple subbed type in a week, that means same sku is subbed in
  and out in that week.

  If filter is applied on the API, we join this table with the calculation and
  return the sub information to filter
**/

CREATE OR REPLACE FUNCTION substitution_type(all_substitutes subbed_type[]) R<PERSON><PERSON>NS subbed_type
    LANGUAGE SQL
    IMMUTABLE
    RETURNS NULL ON NULL INPUT
RETURN CASE
           WHEN cardinality(all_substitutes) > 2 THEN 'SUB_IN_AND_OUT'
           WHEN cardinality(all_substitutes) = 1 THEN all_substitutes[1]
           WHEN cardinality(all_substitutes) = 0 THEN 'NONE'
    /* 2 cardinality only*/
           WHEN 'SUB_IN_AND_OUT' = ANY (all_substitutes) THEN 'SUB_IN_AND_OUT'
           WHEN 'SUB_IN' = ANY (all_substitutes) AND 'SUB_OUT' = ANY (all_substitutes) THEN 'SUB_IN_AND_OUT'
           WHEN 'SUB_IN' = ANY (all_substitutes) AND 'NONE' = ANY (all_substitutes) THEN 'SUB_IN'
           WHEN 'SUB_OUT' = ANY (all_substitutes) AND 'NONE' = ANY (all_substitutes) THEN 'SUB_OUT'
           ELSE all_substitutes[1]
    END :: subbed_type;

CREATE MATERIALIZED VIEW IF NOT EXISTS calculation_substitution_view AS
SELECT calculation.csku_id,
       calculation.dc_code,
       calculation.production_week,
       substitution_type(array_agg(DISTINCT demand.subbed)) as demand_subbed
FROM calculation
         INNER JOIN demand ON
            calculation.csku_id = demand.sku_id
        AND calculation.dc_code = demand.dc_code
        AND calculation.date = demand.date
GROUP BY calculation.csku_id, calculation.dc_code, calculation.production_week;

CREATE MATERIALIZED VIEW IF NOT EXISTS pre_prod_calculation_substitution_view AS
SELECT pre_production_calculation.csku_id,
       pre_production_calculation.dc_code,
       pre_production_calculation.production_week,
       substitution_type(array_agg(DISTINCT demand.subbed)) as demand_subbed
FROM pre_production_calculation
         INNER JOIN demand ON
            pre_production_calculation.csku_id = demand.sku_id
        AND pre_production_calculation.dc_code = demand.dc_code
        AND pre_production_calculation.date = demand.date
GROUP BY pre_production_calculation.csku_id, pre_production_calculation.dc_code, pre_production_calculation.production_week;

grant select on calculation_substitution_view to readonly;
grant select on pre_prod_calculation_substitution_view to readonly;

select schedule_cron_job('pre_prod_calculation_substitution_view_schedule', '*/5 * * * *',
                         'refresh materialized view concurrently pre_prod_calculation_substitution_view;');

select schedule_cron_job('calculation_substitution_view_schedule', '*/5 * * * *',
                         'refresh materialized view concurrently calculation_substitution_view;');
