DROP MATERIALIZED VIEW IF EXISTS po_calculations_view;

CREATE MATERIALIZED VIEW IF NOT EXISTS po_calculations_view AS
select po.dc_code, po_sku.sku_id ,
       (po.expected_arrival_start_time at time zone dc.zone_id)::date as date,
       array_agg(po.supplier_id) as supplier_ids
from purchase_order po
join purchase_order_sku po_sku on po.po_number = po_sku.po_number
join dc_config dc on (dc.dc_code = po.dc_code)
where (po.expected_arrival_start_time at time zone dc.zone_id)::date > (now() at time zone dc.zone_id)::date - interval '70 days'
group by po.dc_code, po_sku.sku_id, date;

create unique index if not exists po_calc_view_dc_sku_date_uniq_index
    on public.po_calculations_view (sku_id, dc_code, date);

grant select on public.po_calculations_view to readonly;
select schedule_cron_job('po_calculations_view_schedule', '*/2 * * * *', 'refresh materialized view concurrently po_calculations_view;');
