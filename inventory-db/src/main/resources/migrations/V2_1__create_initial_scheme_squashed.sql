DO
$$
    BEGIN
        IF EXISTS(
            SELECT
            FROM pg_catalog.pg_roles
            WHERE rolname = 'readonly') THEN
            RAISE NOTICE 'Role "readonly" already exists. Skipping.';
        ELSE
            CREATE ROLE readonly;
        END IF;
    END
$$;

DO
$$
    BEGIN
        IF EXISTS(
            SELECT
            FROM pg_catalog.pg_roles
            WHERE rolname = 'cif-readonly') THEN
            RAISE NOTICE 'Role "cif-readonly" already exists. Skipping.';
        ELSE
            CREATE ROLE "cif-readonly" LOGIN PASSWORD ${cifReadonlyPassword}
                IN ROLE readonly;
        END IF;
    END
$$;

DO
$$
    BEGIN
        IF EXISTS(
            SELECT
            FROM pg_catalog.pg_roles
            WHERE rolname = 'cif') THEN
                ALTER ROLE "cif" SET max_parallel_workers_per_gather TO 6;
                ALTER ROLE "cif" SET work_mem TO '256MB';
        END IF;
    END
$$;

ALTER ROLE "cif-readonly" SET max_parallel_workers_per_gather TO 6;
ALTER ROLE "cif-readonly" SET work_mem TO '256MB';

create table if not exists public.demand
(
    sku_id           uuid                                not null,
    dc_code          varchar                             not null,
    date             date                                not null,
    value            jsonb                               not null,
    record_timestamp timestamp                           not null,
    created_at       timestamp default CURRENT_TIMESTAMP not null,
    updated_at       timestamp default CURRENT_TIMESTAMP not null,
    primary key (sku_id, dc_code, date)
);

create index  if not exists demand_dc_code_and_date_index
    on public.demand (dc_code, date);

grant select on public.demand to readonly;

create table if not exists public.inventory
(
    sku_id           uuid                                not null,
    dc_code          varchar                             not null,
    date             date                                not null,
    value            jsonb                               not null,
    record_timestamp timestamp                           not null,
    created_at       timestamp default CURRENT_TIMESTAMP not null,
    updated_at       timestamp default CURRENT_TIMESTAMP not null,
    primary key (sku_id, dc_code, date)
);

create index  if not exists inventory_dc_code_and_date_index
    on public.inventory (dc_code, date);

grant select on public.inventory to readonly;

create table if not exists public.dc_config
(
    dc_code          varchar                             not null
        primary key,
    market           varchar                             not null,
    production_start varchar                             not null,
    cleardown        varchar,
    zone_id          varchar                             not null,
    enabled          boolean                             not null,
    record_timestamp timestamp                           not null,
    created_at       timestamp default CURRENT_TIMESTAMP not null,
    updated_at       timestamp default CURRENT_TIMESTAMP not null,
    has_cleardown    boolean                             not null
);

grant select on public.dc_config to readonly;

create table if not exists public.sku_specification
(
    id                        uuid       not null
primary key,
    parent_id                 uuid,
    category                  varchar(3) not null,
    code                      text       not null,
    name                      text       not null,
    acceptable_code_life      integer    not null,
    cooling_type              varchar    not null,
    packaging                 varchar    not null,
    market                    varchar    not null,
    safetystock_leadtime      smallint,
    safetystock_leadtime_sd   smallint,
    safetystock_timeincrement numeric(5, 2) default NULL,
    safetystock_servicelevel  numeric(4, 2) default NULL
);

create index  if not exists sku_specification_parent_idx
    on public.sku_specification (parent_id);

grant select on public.sku_specification to readonly;

grant select on public.sku_specification to "cif-readonly";

create table if not exists public.supplier
(
    id        uuid not null
        primary key,
    parent_id uuid,
    name      text not null
);

grant select on public.supplier to readonly;

grant select on public.supplier to "cif-readonly";

create table if not exists public.calculation
(
    dc_code             text not null,
    production_week     text not null,
    csku_id             uuid not null,
    date                date not null,
    expired             bigint,
    opening_stock       bigint,
    demanded            bigint,
    present             bigint,
    closing_stock       bigint,
    actual_inbound      bigint,
    expected_inbound    bigint,
    actual_inbound_po   text,
    expected_inbound_po text,
    daily_needs         bigint,
    created_at          timestamp default now(),
    actual_consumption  bigint,
    safetystock         bigint,
    safetystock_needs   bigint,
    updated_at          timestamp,
    constraint calculation_unenriched_pkey
        primary key (csku_id, dc_code, date)
);

create index  if not exists calculation_unenriched_week_dc_sku_index
    on public.calculation (production_week, dc_code, csku_id);

create index  if not exists calculation_dccode_week_index
    on public.calculation (dc_code, production_week, date);

grant select on public.calculation to readonly;

create table if not exists public.pre_production_calculation
(
    dc_code             text not null,
    production_week     text not null,
    csku_id             uuid not null,
    date                date not null,
    expired             bigint,
    opening_stock       bigint,
    demanded            bigint,
    present             bigint,
    closing_stock       bigint,
    actual_inbound      bigint,
    expected_inbound    bigint,
    actual_inbound_po   text,
    expected_inbound_po text,
    daily_needs         bigint,
    created_at          timestamp default now(),
    actual_consumption  bigint,
    safetystock         bigint,
    safetystock_needs   bigint,
    updated_at          timestamp,
    constraint pre_production_calculation_unenriched_pkey
        primary key (csku_id, dc_code, date)
);

create index  if not exists preprod_calculation_unenriched_week_dc_sku_index
    on public.pre_production_calculation (production_week, dc_code, csku_id);

grant select on public.pre_production_calculation to readonly;

create table if not exists public.pick_2_light
(
    kafka_message_key varchar(32) not null
        primary key,
    created_at        timestamp   not null,
    quantity          smallint    not null,
    market            varchar     not null,
    dc_code           varchar(2)  not null,
    csku_code         varchar     not null
);

create index  if not exists pick_2_light_created_at_idx
    on public.pick_2_light (created_at);

grant select on public.pick_2_light to readonly;

create table if not exists public.live_inventory_calculation
(
    dc_code             text   not null,
    csku_id             uuid   not null,
    date                date   not null,
    production_week     text   not null,
    expired             bigint not null,
    present             bigint not null,
    opening_stock       bigint not null,
    storage_stock       bigint not null,
    staging_stock       bigint not null,
    demanded            bigint not null,
    actual_consumption  bigint not null,
    closing_stock       bigint not null,
    actual_inbound      bigint not null,
    expected_inbound    bigint not null,
    actual_inbound_po   text,
    expected_inbound_po text,
    daily_needs         bigint not null,
    safetystock         bigint,
    safetystock_needs   bigint,
    created_at          timestamp default now(),
    updated_at          timestamp,
    primary key (csku_id, dc_code, date)
);

create index  if not exists live_calculation_week_dc_sku_index
    on public.live_inventory_calculation (production_week, dc_code, csku_id);

create index  if not exists live_calculation_dccode_week_date_index
    on public.live_inventory_calculation (dc_code, production_week, date);

grant select on public.live_inventory_calculation to readonly;

create table if not exists public.live_inventory_pre_production_calculation
(
    dc_code             text   not null,
    csku_id             uuid   not null,
    date                date   not null,
    production_week     text   not null,
    expired             bigint not null,
    present             bigint not null,
    opening_stock       bigint not null,
    storage_stock       bigint not null,
    staging_stock       bigint not null,
    demanded            bigint not null,
    actual_consumption  bigint not null,
    closing_stock       bigint not null,
    actual_inbound      bigint not null,
    expected_inbound    bigint not null,
    actual_inbound_po   text,
    expected_inbound_po text,
    daily_needs         bigint not null,
    safetystock         bigint,
    safetystock_needs   bigint,
    created_at          timestamp default now(),
    updated_at          timestamp,
    primary key (csku_id, dc_code, date)
);

create index  if not exists live_inventory_pre_production_production_week_dc_code_csku__idx
    on public.live_inventory_pre_production_calculation (production_week, dc_code, csku_id);

create index  if not exists live_inventory_pre_production__dc_code_production_week_date_idx
    on public.live_inventory_pre_production_calculation (dc_code, production_week, date);

grant select on public.live_inventory_pre_production_calculation to readonly;

create table if not exists public.goods_received_note
(
    sku_id          uuid                     not null,
    dc_code         text                     not null,
    delivery_time   timestamp with time zone not null,
    quantity        bigint                   not null,
    delivery_status text                     not null,
    created_at      timestamp with time zone default now(),
    updated_at      timestamp with time zone default now(),
    po_ref          text                     not null,
    po_number       text                     not null,
    primary key (sku_id, dc_code, po_number, delivery_time)
);

create index  if not exists goods_received_note_sku_id_po_ref_index
    on public.goods_received_note (sku_id, po_ref);

create index  if not exists goods_received_note_sku_id_po_ref_dc_code_date_index
    on public.goods_received_note (sku_id, po_ref, dc_code, delivery_time);

grant select on public.goods_received_note to readonly;

CREATE SCHEMA IF NOT EXISTS config;
GRANT USAGE ON SCHEMA config TO "readonly";

create table if not exists config.distribution_center
(
    dc_code               text                                not null
        primary key,
    market                text                                not null,
    production_start      text                                not null,
    cleardown             text,
    zone_id               text                                not null,
    enabled               boolean                             not null,
    published             boolean                             not null,
    created_at            timestamp default CURRENT_TIMESTAMP not null,
    updated_at            timestamp default CURRENT_TIMESTAMP not null,
    last_updated_by_email text,
    has_cleardown         boolean                             not null
);

create table if not exists public.note
(
    id               uuid                                   not null,
    version          integer                  default 0     not null,
    sku_id           uuid                                   not null,
    dc_codes         text[]                                 not null,
    production_weeks text[]                                 not null,
    note_text        text                                   not null,
    author_email     text                                   not null,
    author_name      text,
    created_at       timestamp with time zone default now(),
    is_deleted       boolean                  default false not null,
    constraint note_new_pkey1
        primary key (id, version)
);

create index  if not exists note_sku_dcs_weeks_index
    on public.note (sku_id, dc_codes, production_weeks);

grant select on public.note to readonly;

create table if not exists public.supplier_sku
(
    supplier_sku_id uuid    not null,
    sku_id          uuid    not null,
    mlor_days       integer not null,
    created_at      timestamp with time zone default now(),
    updated_at      timestamp with time zone default now(),
    status          text,
    primary key (supplier_sku_id, sku_id)
);

grant select on public.supplier_sku to readonly;

create table if not exists public.purchase_order
(
    po_ref                      varchar                                not null,
    po_id                       uuid                                   not null
        unique,
    po_number                   varchar                                not null
        primary key,
    dc_code                     varchar                                not null,
    expected_arrival_start_time timestamp with time zone               not null,
    expected_arrival_end_time   timestamp with time zone               not null,
    supplier_id                 uuid                                   not null,
    created_at                  timestamp with time zone default now() not null,
    updated_at                  timestamp with time zone
);

create index  if not exists purchase_order_dc_date
    on public.purchase_order (dc_code, expected_arrival_start_time);

grant select on public.purchase_order to readonly;

create table if not exists public.purchase_order_sku
(
    sku_id    uuid    not null,
    quantity  integer not null,
    po_number text    not null
        references public.purchase_order,
    primary key (po_number, sku_id)
);

grant select on public.purchase_order_sku to readonly;

create table if not exists public.advanced_shipping_notice
(
    asn_id                varchar                                not null
        primary key,
    po_number             varchar                                not null,
    po_id                 uuid                                   not null,
    dc_code               varchar                                not null,
    planned_delivery_time timestamp with time zone               not null,
    supplier_id           uuid                                   not null,
    created_at            timestamp with time zone default now() not null,
    updated_at            timestamp with time zone
);

create index  if not exists asn_po_id_idx
    on public.advanced_shipping_notice (po_id);

grant select on public.advanced_shipping_notice to readonly;

create table if not exists public.advanced_shipping_notice_sku
(
    asn_id           varchar                                not null
        references public.advanced_shipping_notice,
    sku_id           uuid                                   not null,
    shipped_quantity bigint                                 not null,
    created_at       timestamp with time zone default now() not null,
    updated_at       timestamp with time zone,
    primary key (asn_id, sku_id)
);

grant select on public.advanced_shipping_notice_sku to readonly;

create materialized view if not exists public.actual_consumption as
SELECT p2l.dc_code,
       sku.id               AS sku_id,
    date(p2l.created_at) AS date,
    sum(p2l.quantity)    AS quantity
FROM pick_2_light p2l
    JOIN sku_specification sku ON sku.code = p2l.csku_code AND sku.market = lower(p2l.market)
WHERE p2l.created_at >=
    date_trunc('day', now() at time zone 'utc') at time zone 'utc' - INTERVAL '7 DAY'
GROUP BY p2l.dc_code, sku_id, date;

create unique index  if not exists actual_consumption_dc_code_sku_id_date
    on public.actual_consumption (dc_code, sku_id, date);

grant select on public.actual_consumption to readonly;

create materialized view if not exists public.dc_config_weight_view as
SELECT dc_config.dc_code,
       dc_config.market,
       dc_config.production_start,
       dc_config.cleardown,
       dc_config.zone_id,
       dc_config.enabled,
       dc_config.record_timestamp,
       dc_config.created_at,
       dc_config.updated_at,
       dc_config.has_cleardown,
       CASE
           WHEN dc_calc.sku_count IS NULL THEN 0
           ELSE dc_calc.sku_count
           END AS weight
FROM dc_config
         LEFT JOIN (SELECT c.dc_code,
                           count(DISTINCT c.csku_id) AS sku_count
                    FROM calculation c
                    WHERE c.date > now()
                    GROUP BY c.dc_code) dc_calc ON dc_calc.dc_code = dc_config.dc_code
WHERE dc_config.enabled = true;

create unique index  if not exists dc_config_weight_view_index
    on public.dc_config_weight_view (dc_code);

grant select on public.dc_config_weight_view to readonly;

CREATE
MATERIALIZED VIEW IF NOT EXISTS PURCHASE_ORDERS_VIEW AS
WITH
    sku_mlor as (
        select supplier_sku.sku_id, min(supplier_sku.mlor_days) as mlor
        from supplier_sku
        where supplier_sku.status ILIKE 'active'
        group by supplier_sku.sku_id
    ),
    purchase_order AS (
        SELECT po.*, posku.sku_id, posku.quantity, supplier.name as supplier_name, sku_mlor.mlor
        from purchase_order po
            left join purchase_order_sku posku on po.po_number = posku.po_number
            left join supplier on supplier.id = po.supplier_id
            left join sku_mlor on (sku_mlor.sku_id = posku.sku_id)
        where po.expected_arrival_start_time > now() - interval '70 days'
    ),
    grn AS (
        SELECT goods_received_note.*, sku_mlor.mlor
        from goods_received_note
            left join sku_mlor on (sku_mlor.sku_id = goods_received_note.sku_id)
        where goods_received_note.delivery_time > now() - interval '70 days'
)

select purchase_order.po_ref                    as po_ref,
       purchase_order.po_id                     as po_id,
       purchase_order.po_number                 as po_number,
       purchase_order.dc_code                   as po_dc_code,
       purchase_order.sku_id                    as po_sku_id,
       purchase_order.expected_arrival_start_time as po_expected_arrival_start_time,
       purchase_order.expected_arrival_end_time as po_expected_arrival_end_time,
       purchase_order.quantity                  as po_quantity,
       purchase_order.supplier_id               as po_supplier_id,
       purchase_order.supplier_name             as po_supplier_name,
       purchase_order.mlor                      as po_mlor,
       grn.po_ref                               as grn_po_ref,
       grn.dc_code                              as grn_dc_code,
       grn.sku_id                               as grn_sku_id,
       grn.delivery_time                        as grn_delivery_time,
       grn.quantity                             as grn_quantity,
       grn.delivery_status                      as grn_delivery_status,
       grn.mlor                                 as grn_mlor
from purchase_order
         full join grn
                   on (
                               purchase_order.po_ref = grn.po_ref
                           and purchase_order.sku_id = grn.sku_id
                           and purchase_order.dc_Code = grn.dc_code
                       );


create index  if not exists purchase_orders_view_po_ref
    on public.purchase_orders_view (po_ref, grn_po_ref);

create index  if not exists purchase_orders_view_dc_code
    on public.purchase_orders_view (po_dc_code, grn_dc_code);

create index  if not exists purchase_orders_view_expected_arrival_start_time
    on public.purchase_orders_view (po_expected_arrival_start_time, grn_delivery_time);

create unique index  if not exists purchase_orders_view_index
    on public.purchase_orders_view (po_ref, grn_po_ref, po_sku_id, grn_sku_id, po_dc_code, grn_dc_code,
                                    po_expected_arrival_start_time, grn_delivery_time);

grant select on public.purchase_orders_view to readonly;

DO
$$
begin
    if exists(select * from pg_available_extensions where name = 'pg_stat_statements') then
        CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
        GRANT pg_read_all_stats TO readonly;
    else
        raise notice 'the pg_stat_statements extension is not installed. skipping.';
    end if;
end;
$$ language plpgsql;

DO
$$
begin
    if exists(select * from pg_available_extensions where name = 'hypopg') then
        CREATE EXTENSION IF NOT EXISTS hypopg;
    else
        raise notice 'the hypopg extension is not installed. skipping.';
    end if;
end;
$$ language plpgsql;

DO
$$
begin
    if exists(select * from pg_available_extensions where name = 'pg_cron') then
        CREATE EXTENSION IF NOT EXISTS pg_cron;
        if exists(SELECT * FROM pg_catalog.pg_roles WHERE rolname = 'readonly') then
            ALTER TABLE cron.job disable  ROW LEVEL SECURITY;
            ALTER TABLE cron.job_run_details disable  ROW LEVEL SECURITY;
            GRANT USAGE ON SCHEMA cron TO "readonly";
            GRANT SELECT ON cron.JOB TO "readonly";
            GRANT SELECT ON cron.JOB_RUN_DETAILS TO "readonly";
        else
            raise notice 'the pg_cron extension is not installed. skipping.';
        end if;
    end if;
end;
$$ language plpgsql;

create or replace function public.schedule_cron_job(job_name text, cron_expression text, command text) returns bigint
    language plpgsql
as
$$
declare
scheduled bigint := 0;

begin
    if exists(select * from pg_available_extensions where name = 'pg_cron') then
select cron.schedule(job_name, cron_expression, command) into scheduled;
else
        -- it skips the executing when running inside the zonky embedded DB
        -- I couldn't find a way how to load the pg_cron shared library on it.
        raise notice 'the pg_cron extension is not installed. skipping.';
end if;
return scheduled;
end;
$$;

create or replace function public.trigger_set_timestamp() returns trigger
    language plpgsql
as
$$
BEGIN
    NEW.updated_at = NOW();
RETURN NEW;
END;
$$;

create or replace trigger set_timestamp
    before update
    on public.demand
    for each row
    execute procedure public.trigger_set_timestamp();

create or replace trigger set_timestamp
    before update
    on public.inventory
    for each row
    execute procedure public.trigger_set_timestamp();

create or replace trigger set_timestamp
    before update
    on public.dc_config
    for each row
    execute procedure public.trigger_set_timestamp();

create or replace trigger set_timestamp
    before update
    on public.goods_received_note
    for each row
    execute procedure public.trigger_set_timestamp();

create or replace trigger set_timestamp
    before update
    on public.supplier_sku
    for each row
    execute procedure public.trigger_set_timestamp();

create or replace trigger set_timestamp
    before update
    on public.purchase_order
    for each row
    execute procedure public.trigger_set_timestamp();

create or replace trigger set_timestamp
    before update
    on public.advanced_shipping_notice
    for each row
    execute procedure public.trigger_set_timestamp();

create or replace trigger set_timestamp
    before update
    on public.advanced_shipping_notice_sku
    for each row
    execute procedure public.trigger_set_timestamp();

select schedule_cron_job('dc_config_weight_view_schedule', '*/5 * * * *', 'refresh materialized view concurrently DC_CONFIG_WEIGHT_VIEW;');
select schedule_cron_job('purchase_orders_view_schedule', '*/2 * * * *', 'refresh materialized view concurrently PURCHASE_ORDERS_VIEW;');
select schedule_cron_job('actual_consumption_schedule', '0 */2 * * *', 'refresh materialized view concurrently actual_consumption;');
select schedule_cron_job('delete_log_schedule', '0 0 * * *', 'delete from cron.job_run_details where end_time < now() - interval ''14 days''');
select schedule_cron_job('clean_pick2light_schedule', '30 */4 * * *', 'delete from pick_2_light where created_at < now() - interval ''30 days''');
