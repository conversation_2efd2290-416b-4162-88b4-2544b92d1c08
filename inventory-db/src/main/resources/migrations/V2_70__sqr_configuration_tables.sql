CREATE TABLE IF NOT EXISTS supply_quantity_recommendation_conf
(
    dc_code                         varchar      NOT NULL,
    week                            varchar      NOT NULL,
    sku_id                          uuid         NOT NULL,
    recommendation_enabled          boolean      NOT NULL DEFAULT FALSE,
    created_at                      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at                      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (dc_code, week, sku_id)
);

create or replace trigger set_timestamp
    before update on public.supply_quantity_recommendation_conf
                      for each row
                      execute procedure public.trigger_set_timestamp();

