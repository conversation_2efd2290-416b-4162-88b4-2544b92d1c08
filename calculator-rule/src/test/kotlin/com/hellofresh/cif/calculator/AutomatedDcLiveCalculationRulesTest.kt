package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.models.CalculatorMode.LIVE_INVENTORY_PRODUCTION
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.Context.MARKET
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.AutomatedDcLiveRules
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.demand.models.Demand
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.sku.models.DEFAULT_MAX_DAYS_BEFORE_EXPIRY
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test

class AutomatedDcLiveCalculationRulesTest : SharedCalculationRules(LIVE_INVENTORY_PRODUCTION) {

    private val calculatorClient = CalculatorClient(
        StatsigTestFeatureFlagClient(
            setOf(AutomatedDcLiveRules(setOf(ContextData(DC, DEFAULT_DC_CODE), ContextData(MARKET, DEFAULT_MARKET))))
        ),
    )

    @Test
    fun `past days use only USABLE inventory data for opening and closing stock`() {
        val inventoryExpired = SkuQuantity.fromBigDecimal(BigDecimal(100L))
        val inventoryUnusableState = SkuQuantity.fromBigDecimal(BigDecimal(150L))
        val inventoryDayBeforeYesterday = SkuQuantity.fromBigDecimal(BigDecimal(200L))
        val inventoryYesterday = SkuQuantity.fromBigDecimal(BigDecimal(500L))
        val today = LocalDate.now(UTC)
        val yesterday = today.minusDays(1)
        val dayBeforeYesterday = yesterday.minusDays(1)
        val inputData = defaultInputData.copy(
            inventory = inventorySnapshots(
                inventorySnapshots = listOf(
                    inventorySnapshot(
                        dayBeforeYesterday,
                        listOf(
                            createInventory(inventoryExpired, dayBeforeYesterday.minusDays(1)),
                            createInventory(
                                inventoryUnusableState,
                                null,
                                Location("", LOCATION_TYPE_QUARANTINE, null),
                            ),
                            createInventory(inventoryDayBeforeYesterday, null),
                        ),
                    ),
                    inventorySnapshot(
                        yesterday,
                        listOf(
                            createInventory(inventoryExpired, dayBeforeYesterday.minusDays(1)),
                            createInventory(
                                inventoryUnusableState,
                                null,
                                Location("", LOCATION_TYPE_QUARANTINE, null),
                            ),
                            createInventory(inventoryYesterday, null),
                        ),
                    ),
                ),
                emptyList(),
            ),
        )

        val runDailyCalculations = calculatorClient.runDailyCalculations(inputData)

        runDailyCalculations.first { it.date == yesterday }.also {
            assertEquals(inventoryDayBeforeYesterday, it.openingStock)
            assertEquals((inventoryExpired + inventoryUnusableState), it.unusable)
            assertEquals(inventoryYesterday, it.closingStock)
        }
    }

    @Test
    fun `today opening stock uses only USABLE from yesterday inventory data`() {
        val inventoryExpired = SkuQuantity.fromBigDecimal(BigDecimal(100L))
        val inventoryUnusableState = SkuQuantity.fromBigDecimal(BigDecimal(150L))
        val inventory = SkuQuantity.fromBigDecimal(BigDecimal(500L))
        val today = LocalDate.now(UTC)
        val yesterday = today.minusDays(1)
        val inputData = defaultInputData.copy(
            inventory = inventorySnapshots(
                inventorySnapshot = inventorySnapshot(
                    yesterday,
                    listOf(
                        createInventory(inventoryExpired, yesterday),
                        createInventory(
                            inventoryUnusableState,
                            null,
                            Location("", LOCATION_TYPE_QUARANTINE, null),
                        ),
                        createInventory(inventory, null),
                    ),
                ),
                liveInventorySnapshot = liveInventorySnapshot(
                    yesterday,
                    listOf(),
                ),
            ),
        )

        val runDailyCalculations = calculatorClient.runDailyCalculations(inputData)

        runDailyCalculations.first { it.date == today }.also {
            assertEquals(inventory, it.openingStock)
            assertEquals((inventoryExpired + inventoryUnusableState), it.unusable)
            assertEquals(inventory, it.closingStock)
        }
    }

    @Test
    fun `future days opening stock uses only USABLE closing stock from previous day`() {
        val inventory = SkuQuantity.fromBigDecimal(BigDecimal(400L))
        val inventoryExpiredTomorrow = SkuQuantity.fromBigDecimal(BigDecimal(100L))
        val today = LocalDate.now(UTC)
        val yesterday = today.minusDays(1)
        val tomorrow = today.plusDays(1)
        val inputData = defaultInputData.copy(
            inventory = inventorySnapshots(
                defaultInputData.dcConfig,
                inventorySnapshot(
                    yesterday,
                    listOf(
                        createInventory(inventoryExpiredTomorrow, today.plusDays(DEFAULT_MAX_DAYS_BEFORE_EXPIRY)),
                        createInventory(
                            SkuQuantity.fromBigDecimal(BigDecimal(150L)),
                            null,
                            Location("", LOCATION_TYPE_QUARANTINE, null),
                        ),
                        createInventory(inventory, null),
                    ),
                ),
                liveInventorySnapshot = liveInventorySnapshot(
                    yesterday,
                    listOf(),
                ),
            ),
        )

        val runDailyCalculations = calculatorClient.runDailyCalculations(inputData)

        assertEquals(
            (inventory + inventoryExpiredTomorrow),
            runDailyCalculations.first { it.date == today }.closingStock
        )
        runDailyCalculations.first { it.date == tomorrow }.also {
            assertEquals(inventory, it.openingStock)
            assertEquals(inventoryExpiredTomorrow, it.unusable)
            assertEquals(inventory, it.closingStock)
        }
    }

    @Test
    fun `inbounds becomes inventory just for today and future days`() {
        val todayInboundQty = SkuQuantity.fromLong(100L)
        val tomorrowInboundQty = SkuQuantity.fromLong(200L)
        val today = LocalDate.now(UTC)
        val yesterday = today.minusDays(1)
        val tomorrow = today.plusDays(1)

        val inputData = defaultInputData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(
                createPurchaseOrders(today, todayInboundQty) +
                    createPurchaseOrders(tomorrow, tomorrowInboundQty),
            ),
        )

        val runDailyCalculations = calculatorClient.runDailyCalculations(inputData)

        assertEquals(SkuQuantity.fromLong(0), runDailyCalculations.first { it.date == yesterday }.closingStock)

        assertEquals(todayInboundQty, runDailyCalculations.first { it.date == today }.closingStock)
        assertEquals(
            todayInboundQty + tomorrowInboundQty,
            runDailyCalculations.first { it.date == tomorrow }.closingStock,
        )
    }

    @Test
    fun `demand is removed from closing stock for today and future days`() {
        val inventoryDayBeforeYesterday = SkuQuantity.fromBigDecimal(BigDecimal(200L))
        val inventoryYesterday = SkuQuantity.fromBigDecimal(BigDecimal(500L))
        val demandToday = SkuQuantity.fromLong(100)
        val demandTomorrow = SkuQuantity.fromLong(200)
        val today = LocalDate.now(UTC)
        val tomorrow = today.plusDays(1)
        val yesterday = today.minusDays(1)
        val dayBeforeYesterday = yesterday.minusDays(1)
        val inputData = defaultInputData.copy(
            inventory = inventorySnapshots(
                inventorySnapshots = listOf(
                    inventorySnapshot(
                        dayBeforeYesterday,
                        listOf(
                            createInventory(inventoryDayBeforeYesterday, null),
                        ),
                    ),
                    inventorySnapshot(
                        yesterday,
                        listOf(
                            createInventory(inventoryYesterday, null),
                        ),
                    ),
                ),
                listOf(),
            ),
            demands = Demands(
                listOf(
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, yesterday, SkuQuantity.fromLong(500)),
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, today, demandToday),
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, tomorrow, demandTomorrow),
                ),
            ),
        )
        val runDailyCalculations = calculatorClient.runDailyCalculations(inputData)

        assertEquals(
            inventoryDayBeforeYesterday,
            runDailyCalculations.first { it.date == yesterday }.openingStock
        )
        assertEquals(inventoryYesterday, runDailyCalculations.first { it.date == yesterday }.closingStock)
        assertEquals(inventoryYesterday, runDailyCalculations.first { it.date == today }.openingStock)
        assertEquals(
            inventoryYesterday - demandToday,
            runDailyCalculations.first { it.date == today }.closingStock
        )
        assertEquals(
            inventoryYesterday - demandToday,
            runDailyCalculations.first { it.date == tomorrow }.openingStock
        )
        assertEquals(
            inventoryYesterday - demandToday - demandTomorrow,
            runDailyCalculations.first { it.date == tomorrow }.closingStock,
        )
    }

    @Test
    fun `domain demand is removed from closing stock for today and future days`() {
        val inventoryDayBeforeYesterday = SkuQuantity.fromBigDecimal(BigDecimal(200L))
        val inventoryYesterday = SkuQuantity.fromBigDecimal(BigDecimal(500L))
        val demandToday = SkuQuantity.fromLong(100L)
        val demandTomorrow = SkuQuantity.fromLong(200L)
        val today = LocalDate.now(UTC)
        val tomorrow = today.plusDays(1)
        val yesterday = today.minusDays(1)
        val dayBeforeYesterday = yesterday.minusDays(1)

        val inputData = defaultInputData.copy(
            inventory = inventorySnapshots(
                inventorySnapshots = listOf(
                    inventorySnapshot(
                        dayBeforeYesterday,
                        listOf(
                            createInventory(inventoryDayBeforeYesterday, null),
                        ),
                    ),
                    inventorySnapshot(
                        yesterday,
                        listOf(
                            createInventory(inventoryYesterday, null),
                        ),
                    ),
                ),
                listOf(),
            ),
            demands = Demands(
                listOf(
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, yesterday, SkuQuantity.fromLong(500)),
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, today, demandToday),
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, tomorrow, demandTomorrow),
                ),
            ),
        )

        val runDailyCalculations = calculatorClient.runDailyCalculations(inputData)

        assertEquals(
            inventoryDayBeforeYesterday,
            runDailyCalculations.first { it.date == yesterday }.openingStock
        )
        assertEquals(inventoryYesterday, runDailyCalculations.first { it.date == yesterday }.closingStock)
        assertEquals(inventoryYesterday, runDailyCalculations.first { it.date == today }.openingStock)
        assertEquals(
            inventoryYesterday - demandToday,
            runDailyCalculations.first { it.date == today }.closingStock
        )
        assertEquals(
            inventoryYesterday - demandToday,
            runDailyCalculations.first { it.date == tomorrow }.openingStock
        )
        assertEquals(
            inventoryYesterday - demandToday - demandTomorrow,
            runDailyCalculations.first { it.date == tomorrow }.closingStock,
        )
    }

    @Test
    fun `today and future day calculations use inbounds and demand to calculate closing stock`() {
        val todayInboundQty = SkuQuantity.fromLong(100L)
        val tomorrowInboundQty = SkuQuantity.fromLong(150L)
        val inventoryYesterday = SkuQuantity.fromBigDecimal(BigDecimal(1000L))
        val demandToday = SkuQuantity.fromLong(200L)
        val demandTomorrow = SkuQuantity.fromLong(300L)
        val today = LocalDate.now(UTC)
        val tomorrow = today.plusDays(1)
        val dayAfterTomorrow = tomorrow.plusDays(1)
        val yesterday = today.minusDays(1)

        val inputData = defaultInputData.copy(
            inventory = inventorySnapshots(
                defaultInputData.dcConfig,
                inventorySnapshot(
                    yesterday,
                    listOf(
                        createInventory(inventoryYesterday, null),
                    ),
                ),
                liveInventorySnapshot(yesterday, listOf()),
            ),
            purchaseOrderInbounds = PurchaseOrderInbounds(
                createPurchaseOrders(today, todayInboundQty) +
                    createPurchaseOrders(tomorrow, tomorrowInboundQty),
            ),
            demands = Demands(
                listOf(
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, today, demandToday),
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, tomorrow, demandTomorrow),
                ),
            ),
        )

        val runDailyCalculations = calculatorClient.runDailyCalculations(inputData)

        assertEquals(inventoryYesterday, runDailyCalculations.first { it.date == today }.openingStock)
        assertEquals(
            inventoryYesterday -
                demandToday + todayInboundQty,
            runDailyCalculations.first { it.date == today }.closingStock,
        )
        assertEquals(
            inventoryYesterday -
                demandToday + todayInboundQty,
            runDailyCalculations.first { it.date == tomorrow }.openingStock
        )
        assertEquals(
            inventoryYesterday -
                demandToday + todayInboundQty -
                demandTomorrow + tomorrowInboundQty,
            runDailyCalculations.first { it.date == tomorrow }.closingStock
        )
        assertEquals(
            inventoryYesterday -
                demandToday + todayInboundQty -
                demandTomorrow + tomorrowInboundQty,
            runDailyCalculations.first { it.date == dayAfterTomorrow }.openingStock,
        )
        assertEquals(
            inventoryYesterday -
                demandToday + todayInboundQty -
                demandTomorrow + tomorrowInboundQty,
            runDailyCalculations.first { it.date == dayAfterTomorrow }.closingStock,
        )
    }

    @Test
    fun `today and future day calculations use inbounds and domain demand to calculate closing stock`() {
        val todayInboundQty = SkuQuantity.fromLong(100L)
        val tomorrowInboundQty = SkuQuantity.fromLong(150L)
        val inventoryYesterday = SkuQuantity.fromBigDecimal(BigDecimal(1000L))
        val demandToday = SkuQuantity.fromLong(200L)
        val demandTomorrow = SkuQuantity.fromLong(300L)
        val today = LocalDate.now(UTC)
        val tomorrow = today.plusDays(1)
        val dayAfterTomorrow = tomorrow.plusDays(1)
        val yesterday = today.minusDays(1)

        val inputData = defaultInputData.copy(
            inventory = inventorySnapshots(
                defaultInputData.dcConfig,
                inventorySnapshot(
                    yesterday,
                    listOf(
                        createInventory(inventoryYesterday, null),
                    ),
                ),
                liveInventorySnapshot(yesterday, listOf()),
            ),
            demands = Demands(
                listOf(
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, today, demandToday),
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, tomorrow, demandTomorrow),
                ),
            ),
            purchaseOrderInbounds = PurchaseOrderInbounds(
                createPurchaseOrders(today, todayInboundQty) +
                    createPurchaseOrders(tomorrow, tomorrowInboundQty),
            ),
        )

        val runDailyCalculations = calculatorClient.runDailyCalculations(inputData)

        assertEquals(inventoryYesterday, runDailyCalculations.first { it.date == today }.openingStock)
        assertEquals(
            inventoryYesterday - demandToday + todayInboundQty,
            runDailyCalculations.first { it.date == today }.closingStock,
        )
        assertEquals(
            inventoryYesterday - demandToday + todayInboundQty,
            runDailyCalculations.first { it.date == tomorrow }.openingStock,
        )
        assertEquals(
            inventoryYesterday -
                demandToday + todayInboundQty -
                demandTomorrow + tomorrowInboundQty,
            runDailyCalculations.first { it.date == tomorrow }.closingStock,
        )
        assertEquals(
            inventoryYesterday -
                demandToday + todayInboundQty -
                demandTomorrow + tomorrowInboundQty,
            runDailyCalculations.first { it.date == dayAfterTomorrow }.openingStock,
        )
        assertEquals(
            inventoryYesterday -
                demandToday + todayInboundQty -
                demandTomorrow + tomorrowInboundQty,
            runDailyCalculations.first { it.date == dayAfterTomorrow }.closingStock,
        )
    }
}
