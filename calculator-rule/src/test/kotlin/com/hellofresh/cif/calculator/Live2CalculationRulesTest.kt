package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.calculations.CalculationStep
import com.hellofresh.cif.calculator.calculations.DayCalculation
import com.hellofresh.cif.calculator.calculations.rules.selectCalculationsRules
import com.hellofresh.cif.calculator.models.CalculationInventory
import com.hellofresh.cif.calculator.models.CalculatorMode.LIVE_INVENTORY_PRODUCTION
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.Context.MARKET
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.Live2Rule
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.cif.safetystock.model.SafetyStockValue
import com.hellofresh.demand.models.Demand
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.CleardownData
import com.hellofresh.inventory.models.CleardownMode.TRIGGERED
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import com.hellofresh.inventory.models.inventory.live.SkuLiveInventory
import com.hellofresh.sku.models.DEFAULT_MAX_DAYS_BEFORE_EXPIRY
import java.math.BigDecimal
import java.time.DayOfWeek.FRIDAY
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import org.junit.jupiter.api.Test

class Live2CalculationRulesTest : SharedCalculationRules(LIVE_INVENTORY_PRODUCTION) {

    private val stagingLocation = Location("", LOCATION_TYPE_STAGING, null)
    private val storageLocation = Location("", LOCATION_TYPE_STORAGE, null)
    private val quarantineLocation = Location("", LOCATION_TYPE_QUARANTINE, null)
    private val calculatorClient = CalculatorClient(
        StatsigTestFeatureFlagClient(
            setOf(
                Live2Rule(
                    setOf(
                        ContextData(DC, DEFAULT_DC_CODE),
                        ContextData(MARKET, DEFAULT_MARKET),
                    ),
                ),
            ),
        ),
    )

    @Test
    fun `past days use only USABLE inventory data for opening and closing stock`() {
        val inventoryExpired = BigDecimal(100L)
        val inventoryUnusableState = BigDecimal(150L)
        val inventoryDayBeforeYesterday = SkuQuantity.fromBigDecimal(BigDecimal(200L))
        val inventoryYesterday = SkuQuantity.fromBigDecimal(BigDecimal(500L))
        val today = LocalDate.now(UTC)
        val yesterday = today.minusDays(1)
        val dayBeforeYesterday = yesterday.minusDays(1)
        val inputData = defaultInputData.copy(
            inventory = liveInventorySnapshots(
                listOf(
                    liveInventorySnapshot(
                        dayBeforeYesterday,
                        listOf(
                            Inventory(
                                SkuQuantity.fromBigDecimal(inventoryExpired),
                                dayBeforeYesterday.minusDays(1),
                                stagingLocation,
                                null,
                            ),
                            Inventory(
                                SkuQuantity.fromBigDecimal(inventoryUnusableState),
                                null,
                                quarantineLocation,
                                null,
                            ),
                            Inventory(
                                inventoryDayBeforeYesterday,
                                null,
                                storageLocation,
                                null,
                            ),
                        ),
                    ),
                    liveInventorySnapshot(
                        yesterday,
                        listOf(
                            Inventory(
                                SkuQuantity.fromBigDecimal(inventoryExpired),
                                dayBeforeYesterday.minusDays(1),
                                stagingLocation,
                                null,
                            ),
                            Inventory(
                                SkuQuantity.fromBigDecimal(inventoryUnusableState),
                                null,
                                quarantineLocation,
                                null,
                            ),
                            Inventory(
                                inventoryYesterday,
                                null,
                                storageLocation,
                                null,
                            ),
                        ),
                    ),
                ),
            ),
        )

        val runDailyCalculations = calculatorClient.runDailyCalculations(inputData)

        runDailyCalculations.first { it.date == yesterday }.also {
            assertEquals(inventoryDayBeforeYesterday, it.openingStock)
            assertEquals((inventoryExpired + inventoryUnusableState), it.unusable.getValue())
            assertEquals(inventoryYesterday, it.closingStock)
        }
    }

    @Test
    fun `today opening stock uses only USABLE from today inventory data`() {
        val inventoryExpired = BigDecimal(100L)
        val inventoryUnusableState = BigDecimal(150L)
        val inventory = SkuQuantity.fromBigDecimal(BigDecimal(500L))
        val today = LocalDate.now(UTC)
        val yesterday = today.minusDays(1)
        val inputData = defaultInputData.copy(
            inventory = liveInventorySnapshots(
                liveInventorySnapshot(
                    today,
                    listOf(
                        Inventory(SkuQuantity.fromBigDecimal(inventoryExpired), yesterday, stagingLocation, null),
                        Inventory(
                            SkuQuantity.fromBigDecimal(inventoryUnusableState),
                            null,
                            quarantineLocation,
                            null,
                        ),
                        Inventory(inventory, null, storageLocation, null),
                    ),
                ),
            ),
        )

        val runDailyCalculations = calculatorClient.runDailyCalculations(inputData)

        runDailyCalculations.first { it.date == today }.also {
            assertEquals(inventory, it.openingStock)
            assertEquals((inventoryExpired + inventoryUnusableState), it.unusable.getValue())
            assertEquals(inventory, it.closingStock)
        }
    }

    @Test
    fun `future days opening stock uses only USABLE closing stock from previous day`() {
        val inventory = SkuQuantity.fromBigDecimal(BigDecimal(400L))
        val inventoryExpiredTomorrow = SkuQuantity.fromBigDecimal(BigDecimal(100L))
        val today = LocalDate.now(UTC)
        val tomorrow = today.plusDays(1)
        val inputData = defaultInputData.copy(
            inventory = liveInventorySnapshots(
                liveInventorySnapshot(
                    today,
                    listOf(
                        Inventory(
                            inventoryExpiredTomorrow,
                            today.plusDays(DEFAULT_MAX_DAYS_BEFORE_EXPIRY),
                            stagingLocation,
                            null,
                        ),
                        Inventory(SkuQuantity.fromBigDecimal(BigDecimal(150L)), null, quarantineLocation, null),
                        Inventory(inventory, null, storageLocation, null),
                    ),
                ),
            ),
        )

        val runDailyCalculations = calculatorClient.runDailyCalculations(inputData)

        assertEquals(
            (inventory + inventoryExpiredTomorrow),
            runDailyCalculations.first { it.date == today }.closingStock,
        )
        runDailyCalculations.first { it.date == tomorrow }.also {
            assertEquals(inventory, it.openingStock)
            assertEquals(inventoryExpiredTomorrow.getValue(), it.unusable.getValue())
            assertEquals(inventory, it.closingStock)
        }
    }

    @Test
    fun `inbounds becomes inventory just for today and future days`() {
        val todayInboundQty = SkuQuantity.fromLong(100L)
        val tomorrowInboundQty = SkuQuantity.fromLong(200L)
        val today = LocalDate.now(UTC)
        val yesterday = today.minusDays(1)
        val tomorrow = today.plusDays(1)

        val inputData = defaultInputData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(
                createPurchaseOrders(today, todayInboundQty) +
                    createPurchaseOrders(tomorrow, tomorrowInboundQty),
            ),
        )

        val runDailyCalculations = calculatorClient.runDailyCalculations(inputData)

        assertEquals(0, runDailyCalculations.first { it.date == yesterday }.closingStock.getValue().toLong())

        assertEquals(
            todayInboundQty,
            runDailyCalculations.first { it.date == today }.closingStock,
        )
        assertEquals(
            todayInboundQty.plus(tomorrowInboundQty),
            runDailyCalculations.first { it.date == tomorrow }.closingStock,
        )
    }

    @Test
    fun `demand is removed from closing stock for today and future days`() {
        val inventoryDayBeforeYesterday = SkuQuantity.fromBigDecimal(BigDecimal(200L))
        val inventoryYesterday = SkuQuantity.fromBigDecimal(BigDecimal(500L))
        val inventoryToday = SkuQuantity.fromBigDecimal(BigDecimal(400L))
        val demandToday = SkuQuantity.fromLong(100)
        val demandTomorrow = SkuQuantity.fromLong(200)
        val today = LocalDate.now(UTC)
        val tomorrow = today.plusDays(1)
        val yesterday = today.minusDays(1)
        val dayBeforeYesterday = yesterday.minusDays(1)
        val inputData = defaultInputData.copy(
            inventory = liveInventorySnapshots(
                listOf(
                    liveInventorySnapshot(
                        dayBeforeYesterday,
                        listOf(
                            Inventory(
                                inventoryDayBeforeYesterday,
                                null,
                                stagingLocation,
                                null,
                            ),
                        ),
                    ),
                    liveInventorySnapshot(
                        yesterday,
                        listOf(
                            Inventory(
                                inventoryYesterday,
                                null,
                                stagingLocation,
                                null,
                            ),
                        ),
                    ),
                    liveInventorySnapshot(
                        today,
                        listOf(
                            Inventory(inventoryToday, null, stagingLocation, null),
                        ),
                    ),
                ),
            ),
            demands = Demands(
                listOf(
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, yesterday, SkuQuantity.fromLong(500)),
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, today, demandToday),
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, tomorrow, demandTomorrow),
                ),
            ),
        )
        val runDailyCalculations = calculatorClient.runDailyCalculations(inputData)

        assertEquals(
            inventoryDayBeforeYesterday,
            runDailyCalculations.first { it.date == yesterday }.openingStock,
        )
        assertEquals(inventoryYesterday, runDailyCalculations.first { it.date == yesterday }.closingStock)
        assertEquals(inventoryToday, runDailyCalculations.first { it.date == today }.openingStock)
        assertEquals(
            inventoryToday - demandToday,
            runDailyCalculations.first { it.date == today }.closingStock,
        )
        assertEquals(
            inventoryToday - demandToday,
            runDailyCalculations.first { it.date == tomorrow }.openingStock,
        )
        assertEquals(
            inventoryToday - demandToday - demandTomorrow,
            runDailyCalculations.first { it.date == tomorrow }.closingStock,
        )
    }

    @Test
    fun `today and future day calculations use inbounds and demand to calculate closing stock`() {
        val todayInboundQty = SkuQuantity.fromLong(100L)
        val tomorrowInboundQty = SkuQuantity.fromLong(150L)
        val inventoryYesterday = SkuQuantity.fromBigDecimal(BigDecimal(1000L))
        val demandToday = SkuQuantity.fromLong(200)
        val demandTomorrow = SkuQuantity.fromLong(300)
        val today = LocalDate.now(UTC)
        val tomorrow = today.plusDays(1)
        val dayAfterTomorrow = tomorrow.plusDays(1)

        val inputData = defaultInputData.copy(
            inventory = liveInventorySnapshots(
                liveInventorySnapshot(
                    today,
                    listOf(
                        Inventory(inventoryYesterday, null, stagingLocation, null),
                    ),
                ),
            ),
            purchaseOrderInbounds = PurchaseOrderInbounds(
                createPurchaseOrders(today, todayInboundQty) +
                    createPurchaseOrders(tomorrow, tomorrowInboundQty),
            ),
            demands = Demands(
                listOf(
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, today, demandToday),
                    Demand(DEFAULT_UUID, DEFAULT_DC_CODE, tomorrow, demandTomorrow),
                ),
            ),
        )

        val runDailyCalculations = calculatorClient.runDailyCalculations(inputData)

        assertEquals(inventoryYesterday, runDailyCalculations.first { it.date == today }.openingStock)
        assertEquals(
            inventoryYesterday - demandToday + todayInboundQty,
            runDailyCalculations.first { it.date == today }.closingStock,
        )
        assertEquals(
            inventoryYesterday - demandToday + todayInboundQty,
            runDailyCalculations.first { it.date == today }.closingStock,
        )
        assertEquals(
            inventoryYesterday -
                demandToday +
                todayInboundQty -
                demandTomorrow +
                tomorrowInboundQty,
            runDailyCalculations.first { it.date == tomorrow }.closingStock,
        )
        assertEquals(
            inventoryYesterday -
                demandToday +
                todayInboundQty -
                demandTomorrow +
                tomorrowInboundQty,
            runDailyCalculations.first { it.date == dayAfterTomorrow }.openingStock,
        )
        assertEquals(
            inventoryYesterday -
                demandToday +
                todayInboundQty -
                demandTomorrow +
                tomorrowInboundQty,
            runDailyCalculations.first { it.date == dayAfterTomorrow }.closingStock,
        )
    }

    @Test
    fun `dailyNeeds should be zero if the cleardown date lies within last 3 days of production week for live version 2`() {
        val demand = SkuQuantity.fromLong(100)
        val safetyStock: Long = 5
        val calculationDate = LocalDate.now()
        val distributionCenterConfiguration = DistributionCenterConfiguration.default(
            DEFAULT_DC_CODE,
        ).copy(
            hasCleardown = true,
            productionStart = FRIDAY,
            cleardown = calculationDate.dayOfWeek,
        )

        val dayCalculation = DayCalculation(
            key = calculationKey(calculationDate),
            sku = defaultSku,
        )
        dayCalculation.inventory = listOf(
            CalculationInventory(
                SkuQuantity.fromLong(0),
                null,
                locationType = LOCATION_TYPE_STAGING,
            ),
        )

        val inputData = calculatorData(true, mapOf(DEFAULT_UUID to defaultSku), LIVE_INVENTORY_PRODUCTION)
            .let {
                it.copy(
                    dcConfig = mapOf(distributionCenterConfiguration.dcCode to distributionCenterConfiguration),
                    safetyStocks = mapOf(
                        SafetyStockKey(
                            DEFAULT_DC_CODE,
                            DcWeek(dayCalculation.key.date, distributionCenterConfiguration.productionStart),
                            DEFAULT_UUID,
                        ) to SafetyStockValue(safetyStock = safetyStock, strategy = DEFAULT_TEST_STRATEGY,),
                    ),
                    demands = Demands(
                        listOf(
                            Demand(
                                DEFAULT_UUID,
                                DEFAULT_DC_CODE,
                                calculationDate,
                                demand,
                            ),
                        ),
                    ),
                    inventory = InventorySnapshots(
                        emptyList(),
                        listOf(
                            LiveInventorySnapshot(
                                DEFAULT_DC_CODE,
                                UUID.randomUUID(),
                                calculationDate.atStartOfDay(),
                                listOf(
                                    SkuLiveInventory(
                                        skuId = DEFAULT_UUID,
                                        inventory = listOf(
                                            Inventory(
                                                SkuQuantity.fromBigDecimal(BigDecimal(100)),
                                                null,
                                                Location(
                                                    "location-id",
                                                    LOCATION_TYPE_STAGING,
                                                    "transport-module-id",
                                                ),
                                                null,
                                            ),
                                        ),
                                    ),
                                ),
                            ),
                        ),
                        listOf(
                            CleardownData(
                                DEFAULT_DC_CODE,
                                calculationDate.atStartOfDay(),
                                TRIGGERED,
                                InventorySnapshot(
                                    DEFAULT_DC_CODE,
                                    UUID.randomUUID(),
                                    calculationDate.atStartOfDay(),
                                    emptyList(),
                                ),
                            ),
                        ),
                    ),
                )
            }

        val rules = selectCalculationsRules(inputData, live2Rules = true)

        val calculations = mapOf(dayCalculation.key to dayCalculation)

        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        val today = results.first { it.date == dayCalculation.key.date }
        assertNotNull(today)
        assertEquals(0, today.dailyNeeds.getValue().toLong())
    }

    fun liveInventorySnapshots(liveInventorySnapshot: LiveInventorySnapshot) = liveInventorySnapshots(
        listOf(liveInventorySnapshot),
    )

    fun liveInventorySnapshots(liveInventorySnapshots: List<LiveInventorySnapshot>) =
        inventorySnapshots(defaultInputData.dcConfig, emptyList(), liveInventorySnapshots)
}
