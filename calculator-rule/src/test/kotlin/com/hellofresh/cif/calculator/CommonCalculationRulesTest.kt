package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.DEFAULT_DC_CODE
import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.DEFAULT_UUID
import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.calculationKey
import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.calculatorData
import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.defaultSku
import com.hellofresh.cif.calculator.calculations.CalculationStep
import com.hellofresh.cif.calculator.calculations.DayCalculation
import com.hellofresh.cif.calculator.calculations.daysInFuture
import com.hellofresh.cif.calculator.calculations.rules.selectCalculationsRules
import com.hellofresh.cif.calculator.models.CalculationInventory
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.CalculatorMode.PRODUCTION
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.DeliveryInfo
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.TimeRange
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.cif.safetystock.model.SafetyStockValue
import com.hellofresh.demand.models.ActualConsumption
import com.hellofresh.demand.models.Demand
import com.hellofresh.demand.models.DemandValue
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.CleardownData
import com.hellofresh.inventory.models.CleardownMode.TRIGGERED
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import io.mockk.every
import io.mockk.mockk
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource

open class CommonCalculationRulesTest {

    @ParameterizedTest
    @MethodSource("calculationModesCombination")
    fun `po inbounds info are always included in calculation output`(
        calculatorMode: CalculatorMode,
        automaticDcLiveRulesFlag: Boolean,
        live2RulesFlag: Boolean,
    ) {
        val today = LocalDate.now(UTC)
        val poDeliveryToday = TimeRange(today.atStartOfDay(UTC), today.atStartOfDay(UTC).plusMinutes(1))
        val yesterday = today.minusDays(1)
        val poDeliveryYesterday = TimeRange(yesterday.atStartOfDay(UTC), yesterday.atStartOfDay(UTC).plusMinutes(1))
        val tomorrow = today.plusDays(1)
        val poDeliveryTomorrow = TimeRange(tomorrow.atStartOfDay(UTC), tomorrow.atStartOfDay(UTC).plusMinutes(1))

        val yesterdayPo = PurchaseOrder(
            "PO1",
            "POREF-01",
            null,
            DEFAULT_DC_CODE,
            poDeliveryYesterday,
            null,
            listOf(
                PurchaseOrderSku(
                    DEFAULT_UUID,
                    SkuQuantity.fromLong(1000),
                    listOf(
                        DeliveryInfo(
                            UUID.randomUUID().toString(),
                            yesterday.atStartOfDay(UTC),
                            CLOSED,
                            SkuQuantity.fromLong(100),
                        ),
                    ),
                ),
            ),
            poStatus = APPROVED
        )
        val todayPo = PurchaseOrder(
            "PO2",
            "POREF-02",
            null,
            DEFAULT_DC_CODE,
            poDeliveryToday,
            null,
            listOf(
                PurchaseOrderSku(
                    DEFAULT_UUID,
                    SkuQuantity.fromLong(2000),
                    listOf(
                        DeliveryInfo(
                            UUID.randomUUID().toString(),
                            today.atStartOfDay(UTC),
                            CLOSED,
                            SkuQuantity.fromLong(200),
                        ),
                    ),
                ),
            ),
            poStatus = APPROVED
        )
        val tomorrowPo = PurchaseOrder(
            "PO3",
            "POREF-03",
            null,
            DEFAULT_DC_CODE,
            poDeliveryTomorrow,
            null,
            listOf(
                PurchaseOrderSku(
                    DEFAULT_UUID,
                    SkuQuantity.fromLong(3000),
                    listOf(
                        DeliveryInfo(
                            UUID.randomUUID().toString(),
                            tomorrow.atStartOfDay(UTC),
                            CLOSED,
                            SkuQuantity.fromLong(300),
                        ),
                    ),
                ),
            ),
            poStatus = APPROVED
        )
        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku), calculatorMode)
                .copy(purchaseOrderInbounds = PurchaseOrderInbounds(listOf(yesterdayPo, todayPo, tomorrowPo))),
            automatedDcLiveRules = automaticDcLiveRulesFlag,
            live2Rules = live2RulesFlag,
        )

        val dayCalculation = DayCalculation(
            key = calculationKey(yesterday),
            sku = defaultSku,
        )

        val calculations = (0L..2L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }

        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        listOf(yesterdayPo, todayPo, tomorrowPo)
            .forEach { po ->
                with(results.first { it.date == po.expectedDeliveryTimeslot?.expectedDeliveryDate }) {
                    assertEquals(setOf(po.number), this.actualInboundPurchaseOrders)
                    assertEquals(setOf(po.number), this.expectedInboundPurchaseOrders)
                    assertEquals(
                        po.purchaseOrderSkus.first().deliveries.first().quantity,
                        this.actualInbound,
                    )
                    assertEquals(
                        po.purchaseOrderSkus.first().expectedQuantity,
                        this.expectedInbound,
                    )
                }
            }
    }

    @ParameterizedTest
    @MethodSource("calculationModesCombination")
    fun `demand info is always included in calculation output using demand domain`(
        calculatorMode: CalculatorMode,
        automaticDcLiveRulesFlag: Boolean,
        live2RulesFlag: Boolean,
    ) {
        val dachZoneId = ZoneId.of("Europe/Berlin")
        val today = LocalDate.now(dachZoneId)
        val yesterday = today.minusDays(1)
        val tomorrow = today.plusDays(1)
        val dayAfterTomorrow = tomorrow.plusDays(1)
        val demands = mockk<Demands>()

        every {
            demands.getDemand(DEFAULT_UUID, DEFAULT_DC_CODE, yesterday, dachZoneId)
        } returns DemandValue(SkuQuantity.fromLong(500), SkuQuantity.fromLong(750), SkuQuantity.fromLong(0))
        every {
            demands.getDemand(DEFAULT_UUID, DEFAULT_DC_CODE, today, dachZoneId)
        } returns DemandValue(SkuQuantity.fromLong(1000), SkuQuantity.fromLong(1500), SkuQuantity.fromLong(0))
        every {
            demands.getDemand(DEFAULT_UUID, DEFAULT_DC_CODE, tomorrow, dachZoneId)
        } returns DemandValue(SkuQuantity.fromLong(3000), SkuQuantity.fromLong(4500), SkuQuantity.fromLong(0))
        every {
            demands.getDemand(DEFAULT_UUID, DEFAULT_DC_CODE, dayAfterTomorrow, dachZoneId)
        } returns DemandValue(SkuQuantity.fromLong(4000), SkuQuantity.fromLong(6000), SkuQuantity.fromLong(0))

        val inputData = calculatorData(true, mapOf(DEFAULT_UUID to defaultSku), calculatorMode)
            .copy(demands = demands)

        val rules = selectCalculationsRules(inputData, automaticDcLiveRulesFlag, live2RulesFlag)

        val dayCalculation = DayCalculation(
            key = calculationKey(yesterday),
            sku = defaultSku,
        )

        val calculations = (0L..2L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }

        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        results.forEach { calculation ->
            demands.getDemand(
                calculation.cskuId,
                calculation.dcCode,
                if (calculatorMode.isPreProd()) calculation.date.plusDays(1) else calculation.date,
                dachZoneId,
            )!!.also {
                assertEquals(it.forecastedQty, calculation.demanded)
                assertEquals(it.actualConsumptionQty, calculation.actualConsumption)
            }
        }
    }

    @ParameterizedTest
    @MethodSource("calculationModesCombination")
    fun `safetyStock info is always included in calculation output`(
        calculatorMode: CalculatorMode,
        automaticDcLiveRulesFlag: Boolean,
        live2RulesFlag: Boolean,
    ) {
        val safetyStockQty = 1111L
        val today = LocalDate.now(UTC)
        val yesterday = today.minusDays(1)

        val inputData = calculatorData(true, mapOf(DEFAULT_UUID to defaultSku), calculatorMode)
            .let {
                val dcConfig = it.dcConfig[DEFAULT_DC_CODE]!!
                it.copy(
                    safetyStocks = setOf(
                        DcWeek(dcConfig.getLatestProductionStart(), dcConfig.productionStart),
                        DcWeek(dcConfig.getLatestProductionStart().plusWeeks(2), dcConfig.productionStart),
                    ).associate {
                        SafetyStockKey(DEFAULT_DC_CODE, it, DEFAULT_UUID) to
                            SafetyStockValue(
                                safetyStock = safetyStockQty,
                                strategy = DEFAULT_TEST_STRATEGY,
                            )
                    },
                )
            }

        val rules = selectCalculationsRules(inputData, automaticDcLiveRulesFlag, live2RulesFlag)

        val dayCalculation = DayCalculation(
            key = calculationKey(yesterday),
            sku = defaultSku,
        )

        val calculations = (0L..2L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }

        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        val expectedSafetyStockWeeks = inputData.safetyStocks.map { it.key.dcWeek }.toSet()
        results.forEach { calculation ->
            if (expectedSafetyStockWeeks.contains(
                    DcWeek(calculation.date, inputData.dcConfig[DEFAULT_DC_CODE]!!.productionStart),
                )
            ) {
                assertEquals(safetyStockQty, calculation.safetyStock?.getValue()?.toLong())
            } else {
                assertNull(calculation.safetyStock)
            }
        }
    }

    @ParameterizedTest
    @CsvSource(
        "Net needs is demand + safety_stock - opening_stock, 10,50,30,70",
        "Safety stock is taken as 0 if missing, 10,50, ,40",
        "Opening stock can be 0,0,50,30,80",
        "Demand is taken as 0 if missing, 10,,30,20",
        "NetNeeds is 0 when opening_stock is bigger, 1000,50,30,0",
    )
    fun `NetNeeds = Usable Opening Stock - (Consumption + Safety Stock)`(
        name: String,
        openingStock: Long,
        demand: Long?,
        safetyStock: Long?,
        expectedNetNeeds: Long,
    ) {
        val defaultDate = LocalDate.now()
        val inputData = calculatorData(true, mapOf(DEFAULT_UUID to defaultSku), PRODUCTION)
            .let {
                val dcConfig = it.dcConfig[DEFAULT_DC_CODE]!!
                it.copy(
                    safetyStocks = safetyStock?.let {
                        mapOf(
                            SafetyStockKey(DEFAULT_DC_CODE, DcWeek(defaultDate, dcConfig.productionStart), DEFAULT_UUID)
                                to
                                SafetyStockValue(safetyStock = safetyStock, strategy = DEFAULT_TEST_STRATEGY)
                        )
                    } ?: emptyMap(),
                    demands = demand?.let {
                        Demands(
                            listOf(
                                Demand(
                                    DEFAULT_UUID,
                                    DEFAULT_DC_CODE,
                                    defaultDate,
                                    SkuQuantity.fromLong(
                                        demand,
                                    ),
                                ),
                            ),
                        )
                    } ?: Demands(
                        listOf(),
                    ),
                )
            }

        val rules = selectCalculationsRules(inputData, false)

        val dayCalculation = DayCalculation(
            key = calculationKey(defaultDate),
            sku = defaultSku,
        )
        dayCalculation.inventory = if (openingStock == 0L) {
            emptyList()
        } else {
            listOf(
                CalculationInventory(
                    SkuQuantity.fromLong(openingStock),
                    null,
                    locationType = LOCATION_TYPE_STAGING,
                ),
            )
        }

        val calculations = mapOf(dayCalculation.key to dayCalculation)

        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        val today = results.first { it.date == defaultDate }
        assertNotNull(today, name)
        assertEquals(expectedNetNeeds, today.netNeeds.getValue().toLong(), name)
    }

    @Test fun `NetNeeds calculations uses actualConsumtion for date older than today`() {
        val openingStock: Long = 10
        val demand = SkuQuantity.fromLong(100)
        val safetyStock: Long = 5
        val actualConsumption: Long = 50
        val expectedNetNeeds: Long = 45

        val defaultDate = LocalDate.now().minusDays(3)
        val inputData = calculatorData(true, mapOf(DEFAULT_UUID to defaultSku), PRODUCTION)
            .let {
                val dcConfig = it.dcConfig[DEFAULT_DC_CODE]!!
                it.copy(
                    safetyStocks = mapOf(
                        SafetyStockKey(DEFAULT_DC_CODE, DcWeek(defaultDate, dcConfig.productionStart), DEFAULT_UUID)
                            to SafetyStockValue(safetyStock = safetyStock, DEFAULT_TEST_STRATEGY)
                    ),
                    demands = Demands(
                        listOf(
                            Demand(
                                DEFAULT_UUID,
                                DEFAULT_DC_CODE,
                                defaultDate,
                                demand,
                                ActualConsumption(SkuQuantity.fromLong(actualConsumption), true),
                            ),
                        ),
                    ),
                )
            }

        val rules = selectCalculationsRules(inputData, false)

        val dayCalculation = DayCalculation(
            key = calculationKey(defaultDate),
            sku = defaultSku,
        )
        dayCalculation.inventory = listOf(
            CalculationInventory(
                SkuQuantity.fromLong(openingStock),
                null,
                locationType = LOCATION_TYPE_STAGING,
            ),
        )

        val calculations = mapOf(dayCalculation.key to dayCalculation)

        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        val today = results.first { it.date == defaultDate }
        assertNotNull(today)
        assertEquals(expectedNetNeeds, today.netNeeds.getValue().toLong())
    }

    @ParameterizedTest
    @CsvSource(
        "3,PRODUCTION,false",
        "3,PRE_PRODUCTION,true",
        "4,PRODUCTION,true",
        "4,PRE_PRODUCTION,true",
        "6,PRODUCTION,true",
        "6,PRE_PRODUCTION,false",
    )
    fun `dailyNeeds should be zero if the cleardown mode is triggered and cleardown date lies within last 3 days of production week`(
        daysFromCleardown: Long,
        calculatorMode: CalculatorMode,
        zeroDailyNeeds: Boolean
    ) {
        val demand = SkuQuantity.fromLong(100)
        val safetyStock: Long = 5
        val distributionCenterConfiguration = DistributionCenterConfiguration.default(
            DEFAULT_DC_CODE,
        ).copy(
            hasCleardown = true,
            productionStart = MONDAY,
        )
        val calculationDate = LocalDateTime.now(UTC)
            .with(TemporalAdjusters.previousOrSame(distributionCenterConfiguration.productionStart))
            .plusDays(daysFromCleardown)

        val dayCalculation = DayCalculation(
            key = calculationKey(calculationDate.toLocalDate()),
            sku = defaultSku,
        )
        dayCalculation.inventory = listOf(
            CalculationInventory(SkuQuantity.fromLong(0), null, locationType = LOCATION_TYPE_STAGING),
        )

        val inputData = calculatorData(true, mapOf(DEFAULT_UUID to defaultSku), calculatorMode)
            .let {
                it.copy(
                    dcConfig = mapOf(distributionCenterConfiguration.dcCode to distributionCenterConfiguration),
                    safetyStocks = mapOf(
                        SafetyStockKey(
                            DEFAULT_DC_CODE,
                            DcWeek(dayCalculation.key.date, distributionCenterConfiguration.productionStart),
                            DEFAULT_UUID,
                        ) to SafetyStockValue(safetyStock, DEFAULT_TEST_STRATEGY),
                    ),
                    demands = Demands(
                        listOf(
                            Demand(
                                DEFAULT_UUID,
                                DEFAULT_DC_CODE,
                                if (calculatorMode.isPreProd()) {
                                    calculationDate.toLocalDate().plusDays(
                                        1,
                                    )
                                } else {
                                    calculationDate.toLocalDate()
                                },
                                demand,
                            ),
                        ),
                    ),
                    inventory = InventorySnapshots(
                        emptyList(),
                        emptyList(),
                        listOf(
                            CleardownData(
                                DEFAULT_DC_CODE,
                                calculationDate,
                                TRIGGERED,
                                InventorySnapshot(DEFAULT_DC_CODE, UUID.randomUUID(), calculationDate, emptyList()),
                            ),
                        ),
                    ),
                )
            }

        val rules = selectCalculationsRules(inputData, false)

        val calculations = mapOf(dayCalculation.key to dayCalculation)

        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        val today = results.first { it.date == dayCalculation.key.date }
        assertNotNull(today)
        if (zeroDailyNeeds) {
            assertEquals(SkuQuantity.fromLong(0), today.dailyNeeds)
        } else {
            assertEquals(demand, today.dailyNeeds)
        }
    }

    companion object {
        @Suppress("unused")
        @JvmStatic
        fun calculationModesCombination() =
            CalculatorMode.values().flatMap {
                // Automated Dc
                listOf(Arguments.of(it, true, false)) +
                    // Live 2.0
                    listOf(
                        Arguments.of(it, false, true),
                        Arguments.of(it, false, false),
                    )
            }
    }
}
