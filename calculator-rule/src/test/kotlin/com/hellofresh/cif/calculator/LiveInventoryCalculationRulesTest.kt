package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.calculations.CalculationStep
import com.hellofresh.cif.calculator.calculations.DayCalculation
import com.hellofresh.cif.calculator.calculations.nextDay
import com.hellofresh.cif.calculator.calculations.rules.selectCalculationsRules
import com.hellofresh.cif.calculator.models.CalculationData
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode.LIVE_INVENTORY_PRE_PRODUCTION
import com.hellofresh.cif.calculator.models.CalculatorMode.LIVE_INVENTORY_PRODUCTION
import com.hellofresh.cif.calculator.models.InputData
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import java.math.BigDecimal
import java.time.LocalDate
import java.time.Period
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test

class LiveInventoryCalculationRulesTest : SharedCalculationRules(LIVE_INVENTORY_PRODUCTION) {

    @AfterEach
    fun afterEachTest() {
        unmockkAll()
    }

    @Test fun `InputData isLive() Should return false for non live in calculation modes LIVE_INVENTORY_PRODUCTION and LIVE_INVENTORY_PRE_PRODUCTION`() {
        val inDataProduction = defaultInputData
        assertTrue(inDataProduction.mode.isLive())

        val inDataPreProd = inDataProduction.copy(mode = LIVE_INVENTORY_PRE_PRODUCTION)
        assertTrue(inDataPreProd.mode.isLive())
    }

    @Test fun `InputData isPreProd() Should match the selected mode`() {
        val inDataProduction = defaultInputData
        assertFalse(inDataProduction.mode.isPreProd())

        val inDataPreProd = inDataProduction.copy(mode = LIVE_INVENTORY_PRE_PRODUCTION)
        assertTrue(inDataPreProd.mode.isPreProd())
    }

    @Test
    fun `inbound becomes storage inventory when current date is after today`() {
        val tomorrowKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, today.plusDays(1))
        val expectedPOQuantity = SkuQuantity.fromLong(50L)
        val purchaseOrders = createPurchaseOrders(today, expectedPOQuantity)
        val input = defaultInputData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
        )
        val calculations = performTestCalculations(input, -1L..2L)

        assertEquals(
            expectedPOQuantity.getValue().toLong(),
            calculations[tomorrowKey]?.storageInventory?.sumOf { it.qty.getValue() }?.toLong()
        )
    }

    @Test fun `Actual inbound needs to be present on live calculations output`() {
        val actualInbound = SkuQuantity.fromLong(234L)
        val expectedActualInboundQty = SkuQuantity.fromLong(0L)
        val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, today)
        val purchaseOrders = createPurchaseOrders(today, null, actualInbound)
        val input = defaultInputData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
        )

        val calculations = performTestCalculations(input, -1L..2L)
        assertEquals(actualInbound, calculations[todayKey]?.actualInboundQty)
        assertEquals(purchaseOrders.first().number, calculations[todayKey]?.actualInboundPos?.first())
        assertEquals(expectedActualInboundQty, calculations[todayKey.nextDay()]?.actualInboundQty)
    }

    @Test
    fun `Expected inbound is used from the purchase order inbounds whenever the inbounds exist and calculated for today`() {
        val expectedInboundQty = SkuQuantity.fromLong(100L)
        val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, today)

        val purchaseOrders = createPurchaseOrders(today, expectedInboundQty)
        val inputData = defaultInputData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
        )

        val calculations = performTestCalculations(inputData, 0L..1L)
        assertEquals(expectedInboundQty, calculations[todayKey]?.storageStock)
    }

    @Test
    fun `Expected inbound is used from the purchase order inbounds whenever the inbounds exist and calculated for tomorrow`() {
        val expectedInboundQty = SkuQuantity.fromLong(100L)
        val tomorrow = today.plusDays(1)
        val tomorrowKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, tomorrow)

        val purchaseOrders = createPurchaseOrders(tomorrow, expectedInboundQty)
        val inputData = defaultInputData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
        )

        val calculations = performTestCalculations(inputData, 0L..1L)
        assertEquals(expectedInboundQty, calculations[tomorrowKey]?.storageStock)
    }

    @Test
    fun `inventory is not considered if not in USABLE state`() {
        val storageQuarantineQty = BigDecimal(19L)
        val cleardownDate = latestCleardownDate()
        val dayBeforeCleardown = cleardownDate.minusDays(1)
        mockkStatic(LocalDate::class)

        val calcKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, dayBeforeCleardown)
        val inputData = defaultInputData.copy(
            inventory = inventorySnapshots(
                defaultInputData.dcConfig,
                inventorySnapshot(
                    calcKey,
                    Inventory(
                        qty = SkuQuantity.fromBigDecimal(storageQuarantineQty),
                        expiryDate = null,
                        location = Location("", LOCATION_TYPE_QUARANTINE, null),
                    ),
                ),
                liveInventorySnapshot(
                    calcKey,
                    Inventory(
                        qty = SkuQuantity.fromBigDecimal(BigDecimal.ZERO),
                        expiryDate = null,
                        Location(
                            "location-id",
                            LOCATION_TYPE_STAGING,
                            "transport-module-id",
                        ),
                    ),
                ),
            ),
        )

        // when
        val calculationStartDayIndex = Period.between(LocalDate.now(defaultZoneId), cleardownDate).days.toLong()
        val calculations = performTestCalculations(inputData, calculationStartDayIndex..calculationStartDayIndex)

        // then
        assertEquals(storageQuarantineQty, calculations[calcKey.copy(date = cleardownDate)]?.unusable?.getValue())
    }

    private fun latestCleardownDate() =
        DistributionCenterConfiguration.default().let {
            if (it.isLatestCleardown(LocalDate.now(defaultZoneId))) {
                LocalDate.now(defaultZoneId)
            } else {
                it.getLatestCleardown()
            }
        }

    private fun performTestCalculations(data: InputData, range: LongRange): MutableMap<CalculationKey, DayCalculation> {
        val rules =
            selectCalculationsRules(
                CalculationData(
                    data,
                    StatsigTestFeatureFlagClient(emptySet()),
                ),
            )
        val keys = (range).map { calculationKey(LocalDate.now(defaultZoneId).plusDays(it)) }
        val calculations = keys.associateWith { DayCalculation(it, defaultSku) }.toMutableMap()
        keys.forEach {
            val calculated = CalculationStep(calculations, calculations[it]!!).apply(rules)
            calculations[it] = calculated.dayCalculation
        }
        return calculations
    }
}
