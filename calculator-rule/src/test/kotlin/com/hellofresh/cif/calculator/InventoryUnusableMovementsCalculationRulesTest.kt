package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.calculations.CalculationStep
import com.hellofresh.cif.calculator.calculations.DayCalculation
import com.hellofresh.cif.calculator.calculations.daysInFuture
import com.hellofresh.cif.calculator.calculations.rules.selectCalculationsRules
import com.hellofresh.cif.calculator.models.CalculationInventory
import com.hellofresh.cif.calculator.models.CalculatorMode.PRODUCTION
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.ApplyUnusableMovementsStock
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.DeliveryInfo
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.TimeRange
import com.hellofresh.demand.models.Demand
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.CleardownData
import com.hellofresh.inventory.models.CleardownMode.SCHEDULED
import com.hellofresh.inventory.models.InventoryMovement
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_OUTBOUND_INTERNAL_TRANSFER
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_WASTE
import com.hellofresh.inventory.models.default
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

internal class InventoryUnusableMovementsCalculationRulesTest : SharedCalculationRules(PRODUCTION) {

    private val atTenAM = LocalTime.of(10, 0, 0)
    private val cleardownTime = today.atTime(atTenAM)

    /**
     * OPENING STOCK is UNCHANGED when the inventory stock is moved from USABLE to UNUSABLE,
     * because we don't consider the inventory movement that happened before the cleardown.
     */
    @Test
    fun `consider the inventory movement activity ONLY after the cleardown time`() {
        statsigFeatureFlagClient.fixtures = setOf(ApplyUnusableMovementsStock(setOf(ContextData(DC, DEFAULT_DC_CODE))))
        val acceptableCodeLife = 2
        val expectedOpeningStock = SkuQuantity.fromLong(500L)
        val originLocationId = UUID.randomUUID().toString()
        val transportModuleId = UUID.randomUUID().toString()
        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                inventory = InventorySnapshots(
                    emptyList(),
                    emptyList(),
                    listOf(
                        CleardownData(
                            dcCode = DEFAULT_DC_CODE,
                            cleardownTime = cleardownTime,
                            cleardownMode = SCHEDULED,
                            snapshot = null,
                        ),
                    ),
                    listOf(
                        InventoryMovement.Companion.default()
                            .copy(
                                activityTime = cleardownTime.minusHours(2).atOffset(UTC),
                                originLocationId = originLocationId,
                                quantity = SkuQuantity.fromBigDecimal(BigDecimal(100L)),
                                typeId = UUID.randomUUID().toString(),
                                originLocationType = LOCATION_TYPE_STAGING,
                                destinationLocationType = LOCATION_TYPE_WASTE,
                                transportModuleId = transportModuleId,
                            ),
                    ),
                ),
            ),
        )
        val inventory = CalculationInventory(
            SkuQuantity.fromLong(500),
            null,
            locationType = LOCATION_TYPE_STAGING,
            location = Location(
                id = originLocationId,
                type = LOCATION_TYPE_STAGING,
                transportModuleId = transportModuleId,
            ),
        )
        val dayCalculation = DayCalculation(
            key = calculationKey(today),
            sku = defaultSku.copy(acceptableCodeLife = acceptableCodeLife),
        )
        dayCalculation.inventory = listOf(inventory)

        val calculations = (0L..acceptableCodeLife).associate {
            dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it)
        }
        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }
        assertEquals(expectedOpeningStock, results[0].openingStock)
        assertEquals(0, results[0].unusable.getValue().toLong())
    }

    @Test fun `opening stock is increased when the inventory stock is moved from unusable to usable`() {
        statsigFeatureFlagClient.fixtures = setOf(ApplyUnusableMovementsStock(setOf(ContextData(DC, DEFAULT_DC_CODE))))
        val acceptableCodeLife = 2
        val expectedOpeningStock = SkuQuantity.fromLong(100L)
        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                inventory = InventorySnapshots(
                    emptyList(),
                    emptyList(),
                    listOf(
                        CleardownData(
                            dcCode = DEFAULT_DC_CODE,
                            cleardownTime = cleardownTime,
                            cleardownMode = SCHEDULED,
                            snapshot = null,
                        ),
                    ),
                    listOf(
                        InventoryMovement.Companion.default()
                            .copy(
                                activityTime = cleardownTime.plusHours(3).atOffset(UTC),
                                quantity = SkuQuantity.fromBigDecimal(BigDecimal(100L)),
                                typeId = UUID.randomUUID().toString(),
                                originLocationType = LOCATION_TYPE_WASTE,
                                destinationLocationType = LOCATION_TYPE_STAGING,
                                skuId = DEFAULT_UUID,
                                dcCode = DEFAULT_DC_CODE,
                            ),
                    ),
                ),
            ),
        )
        val inventory = CalculationInventory(SkuQuantity.fromLong(500), null, locationType = LOCATION_TYPE_WASTE)
        val dayCalculation = DayCalculation(
            key = calculationKey(today),
            sku = defaultSku.copy(acceptableCodeLife = acceptableCodeLife),
        )
        dayCalculation.inventory = listOf(inventory)

        val calculations = (0L..acceptableCodeLife).associate {
            dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it)
        }
        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }
        assertEquals(expectedOpeningStock, results[acceptableCodeLife].openingStock)
        assertEquals(400, results[0].unusable.getValue().toLong())
    }

    @Test fun `unusable is increased when the inventory stock is moved from usable to unusable and just exist in cleardown inventory`() {
        statsigFeatureFlagClient.fixtures = setOf(ApplyUnusableMovementsStock(setOf(ContextData(DC, DEFAULT_DC_CODE))))
        val acceptableCodeLife = 2
        val expectedOpeningStock = SkuQuantity.fromLong(0L)
        val expectedDemandQty = SkuQuantity.fromLong(300)
        val inventoryMovement = InventoryMovement.Companion.default()
            .copy(
                activityTime = cleardownTime.plusHours(3).atOffset(UTC),
                expirationDate = LocalDate.now(UTC),
                quantity = SkuQuantity.fromBigDecimal(BigDecimal(100L)),
                typeId = UUID.randomUUID().toString(),
                originLocationType = LOCATION_TYPE_STAGING,
                destinationLocationType = LOCATION_TYPE_WASTE,
                skuId = DEFAULT_UUID,
                dcCode = DEFAULT_DC_CODE,
            )
        val inventory1 = InventorySnapshot.Companion.default(
            DEFAULT_UUID,
            LocalDate.now(UTC),
            inventoryMovement.originLocationId,
        ).copy(dcCode = DEFAULT_DC_CODE)
        val cleardownInventory =
            CleardownData(
                DEFAULT_DC_CODE,
                cleardownTime,
                SCHEDULED,
                inventory1,
            )

        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                demands = Demands(
                    listOf(
                        Demand(
                            skuId = DEFAULT_UUID,
                            dcCode = DEFAULT_DC_CODE,
                            date = today,
                            forecastedQty = expectedDemandQty,
                        ),
                    ),
                ),
                inventory = InventorySnapshots(
                    emptyList(),
                    emptyList(),
                    listOf(cleardownInventory),
                    listOf(inventoryMovement),
                ),
            ),
        )
        val inventory = CalculationInventory(SkuQuantity.fromLong(500), null, locationType = LOCATION_TYPE_WASTE)
        val dayCalculation = DayCalculation(
            key = calculationKey(today),
            sku = defaultSku.copy(acceptableCodeLife = acceptableCodeLife),
        )
        dayCalculation.inventory = listOf(inventory)

        val calculations = (0L..acceptableCodeLife).associate {
            dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it)
        }
        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }
        assertEquals(expectedOpeningStock, results[acceptableCodeLife].openingStock)
        assertEquals(600, results[0].unusable.getValue().toLong())
    }

    @Test fun `unusable is increased when the inventory stock is moved from usable to unusable and it is linked to an inbound`() {
        statsigFeatureFlagClient.fixtures = setOf(ApplyUnusableMovementsStock(setOf(ContextData(DC, DEFAULT_DC_CODE))))
        val unusableMovQty = SkuQuantity.fromLong(40)
        val inboundQty = SkuQuantity.fromLong(50)
        val inventoryMovement = InventoryMovement.Companion.default()
            .copy(
                activityTime = cleardownTime.plusHours(3).atOffset(UTC),
                expirationDate = null,
                quantity = unusableMovQty,
                typeId = UUID.randomUUID().toString(),
                originLocationType = LOCATION_TYPE_STAGING,
                destinationLocationType = LOCATION_TYPE_WASTE,
                skuId = DEFAULT_UUID,
                dcCode = DEFAULT_DC_CODE,
                poNumber = UUID.randomUUID().toString(),
            )

        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                inventory = InventorySnapshots(
                    emptyList(),
                    emptyList(),
                    listOf(
                        CleardownData(
                            DEFAULT_DC_CODE,
                            cleardownTime,
                            SCHEDULED,
                            InventorySnapshot.Companion.default(
                                DEFAULT_UUID,
                            ),
                        ),
                    ),
                    listOf(inventoryMovement),
                ),
                purchaseOrderInbounds = PurchaseOrderInbounds(
                    listOf(
                        PurchaseOrder(
                            inventoryMovement.poNumber!!,
                            inventoryMovement.poNumber + "_1",
                            UUID.randomUUID(),
                            inventoryMovement.dcCode,
                            TimeRange(
                                cleardownTime.plusHours(2).atZone(defaultZoneId),
                                cleardownTime.plusHours(3).atZone(defaultZoneId)
                            ),
                            purchaseOrderSkus = listOf(
                                PurchaseOrderSku(
                                    inventoryMovement.skuId,
                                    SkuQuantity.fromLong(100),
                                    listOf(
                                        DeliveryInfo(
                                            UUID.randomUUID().toString(),
                                            cleardownTime.plusHours(1).atZone(defaultZoneId),
                                            CLOSED,
                                            inboundQty,
                                        ),
                                    ),
                                ),
                            ),
                            poStatus = APPROVED,
                            supplier = null,
                        ),
                    ),
                ),
            ),
        )
        val inventory = CalculationInventory(SkuQuantity.fromLong(500), null, locationType = LOCATION_TYPE_STAGING)
        val dayCalculation = DayCalculation(
            key = calculationKey(today),
            sku = defaultSku,
        ).apply {
            this.inventory = listOf(inventory)
        }

        val result = CalculationStep(
            mapOf(dayCalculation.key to dayCalculation),
            dayCalculation
        ).apply(rules).calculate()
        assertEquals(inventory.qty, result.openingStock)
        assertEquals(inventory.qty + inboundQty - unusableMovQty, result.closingStock)
        assertEquals(unusableMovQty, result.unusable)
    }

    @Test fun `unusable is increased when the inventory stock is moved from usable to unusable (LOCATION_TYPE_OUTBOUND_INTERNAL_TRANSFER)`() {
        statsigFeatureFlagClient.fixtures = setOf(ApplyUnusableMovementsStock(setOf(ContextData(DC, DEFAULT_DC_CODE))))
        val unusableMovQty = SkuQuantity.fromLong(40)
        val inboundQty = SkuQuantity.fromLong(50)
        val inventoryMovement = InventoryMovement.Companion.default()
            .copy(
                activityTime = cleardownTime.plusHours(3).atOffset(UTC),
                expirationDate = null,
                quantity = unusableMovQty,
                typeId = UUID.randomUUID().toString(),
                originLocationType = LOCATION_TYPE_OUTBOUND_INTERNAL_TRANSFER,
                destinationLocationType = LOCATION_TYPE_WASTE,
                skuId = DEFAULT_UUID,
                dcCode = DEFAULT_DC_CODE,
                poNumber = UUID.randomUUID().toString(),
            )

        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                inventory = InventorySnapshots(
                    emptyList(),
                    emptyList(),
                    listOf(
                        CleardownData(
                            DEFAULT_DC_CODE,
                            cleardownTime,
                            SCHEDULED,
                            InventorySnapshot.Companion.default(
                                DEFAULT_UUID,
                            ),
                        ),
                    ),
                    listOf(inventoryMovement),
                ),
                purchaseOrderInbounds = PurchaseOrderInbounds(
                    listOf(
                        PurchaseOrder(
                            inventoryMovement.poNumber!!,
                            inventoryMovement.poNumber + "_1",
                            UUID.randomUUID(),
                            inventoryMovement.dcCode,
                            TimeRange(
                                cleardownTime.plusHours(2).atZone(defaultZoneId),
                                cleardownTime.plusHours(3).atZone(defaultZoneId)
                            ),
                            purchaseOrderSkus = listOf(
                                PurchaseOrderSku(
                                    inventoryMovement.skuId,
                                    SkuQuantity.fromLong(100),
                                    listOf(
                                        DeliveryInfo(
                                            UUID.randomUUID().toString(),
                                            cleardownTime.plusHours(1).atZone(defaultZoneId),
                                            CLOSED,
                                            inboundQty,
                                        ),
                                    ),
                                ),
                            ),
                            poStatus = APPROVED,
                            supplier = null,
                        ),
                    ),
                ),
            ),
        )
        val inventory = CalculationInventory(SkuQuantity.fromLong(500), null, locationType = LOCATION_TYPE_STAGING)
        val dayCalculation = DayCalculation(
            key = calculationKey(today),
            sku = defaultSku,
        ).apply {
            this.inventory = listOf(inventory)
        }

        val result = CalculationStep(
            mapOf(dayCalculation.key to dayCalculation),
            dayCalculation
        ).apply(rules).calculate()
        assertEquals(inventory.qty, result.openingStock)
        assertEquals(inventory.qty + inboundQty - unusableMovQty, result.closingStock)
        assertEquals(unusableMovQty, result.unusable)
    }

    @Test fun `usable is increased when the unusable inbound inventory stock is moved from unusable to usable`() {
        statsigFeatureFlagClient.fixtures = setOf(ApplyUnusableMovementsStock(setOf(ContextData(DC, DEFAULT_DC_CODE))))
        val movQty = SkuQuantity.fromLong(50)
        val inboundInventoryQty = SkuQuantity.fromLong(50)
        val inventoryMovementUnusable = InventoryMovement.Companion.default()
            .copy(
                activityTime = cleardownTime.plusHours(3).atOffset(UTC),
                expirationDate = null,
                quantity = movQty,
                typeId = UUID.randomUUID().toString(),
                originLocationType = LOCATION_TYPE_STORAGE,
                destinationLocationType = LOCATION_TYPE_QUARANTINE,
                skuId = DEFAULT_UUID,
                dcCode = DEFAULT_DC_CODE,
                poNumber = UUID.randomUUID().toString(),
            )
        val inventoryMovementUsable = inventoryMovementUnusable
            .copy(
                activityTime = inventoryMovementUnusable.activityTime.plusMinutes(15),
                originLocationType = LOCATION_TYPE_QUARANTINE,
                destinationLocationType = LOCATION_TYPE_STORAGE,
            )

        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                inventory = InventorySnapshots(
                    emptyList(),
                    emptyList(),
                    listOf(
                        CleardownData(
                            DEFAULT_DC_CODE,
                            cleardownTime,
                            SCHEDULED,
                            InventorySnapshot.Companion.default(DEFAULT_UUID),
                        ),
                    ),
                    listOf(inventoryMovementUsable, inventoryMovementUnusable),
                ),
                purchaseOrderInbounds = PurchaseOrderInbounds(
                    listOf(
                        PurchaseOrder(
                            inventoryMovementUnusable.poNumber!!,
                            inventoryMovementUnusable.poNumber + "_1",
                            UUID.randomUUID(),
                            inventoryMovementUnusable.dcCode,
                            TimeRange(
                                cleardownTime.plusHours(2).atZone(defaultZoneId),
                                cleardownTime.plusHours(3).atZone(defaultZoneId)
                            ),
                            purchaseOrderSkus = listOf(
                                PurchaseOrderSku(
                                    inventoryMovementUnusable.skuId,
                                    SkuQuantity.fromLong(100),
                                    listOf(
                                        DeliveryInfo(
                                            UUID.randomUUID().toString(),
                                            cleardownTime.plusHours(1).atZone(defaultZoneId),
                                            CLOSED,
                                            inboundInventoryQty,
                                        ),
                                    ),
                                ),
                            ),
                            poStatus = APPROVED,
                            supplier = null,
                        ),
                    ),
                ),
            ),
        )
        val inventory = CalculationInventory(SkuQuantity.fromLong(500), null, locationType = LOCATION_TYPE_STAGING)
        val dayCalculation = DayCalculation(
            key = calculationKey(today),
            sku = defaultSku,
        ).apply {
            this.inventory = listOf(inventory)
        }

        val result = CalculationStep(
            mapOf(dayCalculation.key to dayCalculation),
            dayCalculation
        ).apply(rules).calculate()
        assertEquals(inventory.qty, result.openingStock)
        assertEquals(inventory.qty + inboundInventoryQty, result.closingStock)
        assertEquals(SkuQuantity.ZERO, result.unusable)
    }
}
