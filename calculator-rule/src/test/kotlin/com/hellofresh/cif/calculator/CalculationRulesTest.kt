package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.calculations.CalculationStep
import com.hellofresh.cif.calculator.calculations.DayCalculation
import com.hellofresh.cif.calculator.calculations.daysInFuture
import com.hellofresh.cif.calculator.calculations.rules.selectCalculationsRules
import com.hellofresh.cif.calculator.models.CalculationInventory
import com.hellofresh.cif.calculator.models.CalculatorMode.PRE_PRODUCTION
import com.hellofresh.cif.calculator.models.CalculatorMode.PRODUCTION
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_FCMS
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_HIGH_JUMP
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_UNSPECIFIED
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.ApplyUnusableMovementsStock
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.OPEN
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.models.sumOf
import com.hellofresh.demand.models.Demand
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.CleardownData
import com.hellofresh.inventory.models.CleardownMode.SCHEDULED
import com.hellofresh.inventory.models.InventoryAdjustmentTypeId
import com.hellofresh.inventory.models.InventoryMovement
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_WASTE
import com.hellofresh.inventory.models.default
import com.hellofresh.sku.models.DEFAULT_MAX_DAYS_BEFORE_EXPIRY
import java.math.BigDecimal
import java.math.RoundingMode.HALF_UP
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

internal class CalculationRulesTest : SharedCalculationRules(PRODUCTION) {

    private val atTenAM = LocalTime.of(10, 0, 0)
    private val cleardownTime = today.atTime(atTenAM)

    @Test fun `InputData isLive() should return false for non live in calculation modes PRODUCTION and PRE_PRODUCTION`() {
        val inDataProduction = calculatorData(true, mapOf(DEFAULT_UUID to defaultSku))
        assertFalse(inDataProduction.mode.isLive())

        val inDataPreProd = inDataProduction.copy(mode = PRE_PRODUCTION)
        assertFalse(inDataPreProd.mode.isLive())
    }

    @Test fun `InputData isPreProd() should match the selected mode`() {
        val inDataProduction = calculatorData(true, mapOf(DEFAULT_UUID to defaultSku))
        assertFalse(inDataProduction.mode.isPreProd())

        val inDataPreProd = inDataProduction.copy(mode = PRE_PRODUCTION)
        assertTrue(inDataPreProd.mode.isPreProd())
    }

    @Test fun `closing stock is carried over in subsequent calculations`() {
        val actualInboundQty1 = SkuQuantity.fromLong(1L)
        val demandQty = SkuQuantity.fromLong(2)
        val inventory = calculationInventory(BigDecimal(5), null)
        val key = calculationKey(targetDate)
        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku))
                .copy(
                    demands = Demands(
                        listOf(
                            Demand(
                                skuId = DEFAULT_UUID,
                                dcCode = DEFAULT_DC_CODE,
                                date = targetDate,
                                forecastedQty = demandQty,
                            ),
                        ),
                    ),
                )
                .withPo(key.date, SkuQuantity.fromLong(0), actualInboundQty1),
        )

        val dayCalculation = DayCalculation(key, defaultSku).apply {
            this.inventory = listOf(calculationInventory(BigDecimal(5), null))
            demand = demandQty
        }
        val calculations = (0L..10L).associate { key.daysInFuture(it) to dayCalculation.daysInFuture(it) }

        var calculationStep = CalculationStep(calculations, dayCalculation).apply(rules)
        val result1 = calculationStep.calculate()
        assertEquals(
            inventory.qty + actualInboundQty1.minus(SkuQuantity.fromLong(2)),
            result1.closingStock,
        )
        assertEquals(
            actualInboundQty1.getValue().toLong() + inventory.qty.getValue().toLong() - 2,
            calculationStep.dayCalculation.inventory.sumOf { it.qty }.getValue().toLong(),
        )

        calculationStep = CalculationStep(calculations, calculations[key.daysInFuture(1)]!!).apply(rules)
        val result2 = calculationStep.calculate()
        assertEquals(result1.closingStock, result2.closingStock)

        val demandedOn3rdDay = SkuQuantity.fromLong(1000)
        val newRules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku))
                .copy(
                    demands = Demands(
                        listOf(
                            Demand(
                                skuId = DEFAULT_UUID,
                                dcCode = DEFAULT_DC_CODE,
                                date = targetDate.plusDays(2),
                                forecastedQty = demandedOn3rdDay,
                            ),
                        ),
                    ),
                )
                .withPo(key.date, SkuQuantity.fromLong(0), actualInboundQty1),
        )
        calculationStep = CalculationStep(
            calculations,
            calculations[key.daysInFuture(2)]!!.apply {
                demand = demandedOn3rdDay
            },
        ).apply(newRules)
        val result3 = calculationStep.calculate()
        assertEquals(result1.closingStock, result2.closingStock)

        assertEquals(result2.closingStock - demandedOn3rdDay, result3.closingStock)
        assertTrue(result3.closingStock.getValue().toLong() < 0)
    }

    @Test fun `the carried over closing stock is used in opening stock for future days`() {
        val actualInboundQty1 = SkuQuantity.fromLong(1L)
        val key = calculationKey(targetDate)
        val rules = selectCalculationsRules(calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)))

        val dayCalculation = DayCalculation(key, defaultSku).apply {
            this.inventory = listOf(calculationInventory(BigDecimal(5), null, locationType = LOCATION_TYPE_STAGING))
            demand = SkuQuantity.fromLong(2)
            actualInboundQty = actualInboundQty1
        }
        val calculations = (0L..10L).associate { key.daysInFuture(it) to dayCalculation.daysInFuture(it) }

        var calculationStep = CalculationStep(calculations, dayCalculation).apply(rules)
        val result1 = calculationStep.calculate()

        calculationStep = CalculationStep(
            calculations,
            calculations[key.daysInFuture(1)]!!.apply {
                demand = SkuQuantity.fromLong(1000)
            },
        ).apply(rules)
        val result2 = calculationStep.calculate()

        assertEquals(
            result1.closingStock,
            result2.openingStock,
        )
    }

    @Test fun `opening stock is zeroed if expired inventory greater than previous day closing stock`() {
        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate.plusDays(1)),
            sku = defaultSku,
        )
        dayCalculation.inventory = listOf(
            calculationInventory(BigDecimal(5), targetDate.plusDays(5), locationType = LOCATION_TYPE_STAGING),
        )

        val calculations = (0L..10L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }

        val calculationStep = CalculationStep(
            calculations,
            dayCalculation,
        ).apply(selectCalculationsRules(calculatorData(true, mapOf(DEFAULT_UUID to defaultSku))))
        val result = calculationStep.calculate()
        assertEquals(0, result.openingStock.getValue().toLong())
    }

    @Test fun `opening stock is calculated with closing stock minus expired inventory`() {
        val expiryDate = targetDate.plusDays(DEFAULT_MAX_DAYS_BEFORE_EXPIRY + 1)
        val inventoryDayOne = calculationInventory(BigDecimal(5), expiryDate)
        val rules = selectCalculationsRules(calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)))

        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        dayCalculation.inventory = listOf(inventoryDayOne)
        dayCalculation.demand = SkuQuantity.fromLong(2)
        dayCalculation.actualInboundQty = SkuQuantity.fromLong(1)

        val calculations = (0L..10L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }

        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        assertEquals(
            results[0].closingStock - results[2].unusable,
            results[2].openingStock,
        )
    }

    @Test fun `opening stock is equal to unexpired inventory`() {
        val unexpiredInventory = calculationInventory(BigDecimal(100), null)
        val rules = selectCalculationsRules(calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)))

        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        dayCalculation.inventory = listOf(unexpiredInventory)
        val calculations = (0L..10L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }

        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        assertEquals(unexpiredInventory.qty, results[0].openingStock)
    }

    @Test fun `should correctly carry only usable inventory items if no demand, actual and-or expected inbound provided`() {
        val unexpiredInventory = calculationInventory(BigDecimal(11), null)
        val expiredInventory = calculationInventory(BigDecimal(100), targetDate)
        val rules = selectCalculationsRules(calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)))

        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        dayCalculation.inventory = listOf(unexpiredInventory, expiredInventory)

        val calculations = (0L..10L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }

        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        assertEquals(unexpiredInventory.qty, results[0].closingStock)
        assertEquals(unexpiredInventory.qty, results[0].openingStock)
        assertEquals(expiredInventory.qty, results[0].unusable)
    }

    @Test fun `demand lesser than (unexpired inventory + inbound) should result in positive closing stock quantity`() {
        val actualInboundQty = SkuQuantity.fromLong(10L)
        val demandQty = SkuQuantity.fromLong(10)
        val inventory = calculationInventory(BigDecimal(10), null)

        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        dayCalculation.inventory = listOf(inventory)
        dayCalculation.demand = demandQty
        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku))
                .copy(
                    demands = Demands(
                        listOf(
                            Demand(
                                skuId = DEFAULT_UUID,
                                dcCode = DEFAULT_DC_CODE,
                                date = targetDate,
                                forecastedQty = demandQty,
                            ),
                        ),
                    ),
                )
                .withPo(dayCalculation.key.date, SkuQuantity.fromLong(0), actualInboundQty),
        )

        val calculations = (0L..10L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }

        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        assertTrue(results[0].closingStock.getValue().toLong() > 0)
        assertEquals(
            SkuQuantity.fromLong(inventory.qty.getValue().toLong())
                .plus(actualInboundQty)
                .minus(SkuQuantity.fromLong(10)),
            results[0].closingStock,
        )
    }

    @Test fun `demand larger than (unexpired inventory + inbound) should result in negative closing stock quantity`() {
        val actualInboundQty = SkuQuantity.fromLong(10L)
        val demandQty = SkuQuantity.fromLong(100)
        val inventory = calculationInventory(BigDecimal(10), null)
        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku))
                .copy(
                    demands = Demands(
                        listOf(
                            Demand(
                                skuId = DEFAULT_UUID,
                                dcCode = DEFAULT_DC_CODE,
                                date = targetDate,
                                forecastedQty = demandQty,
                            ),
                        ),
                    ),
                )
                .withPo(
                    targetDate,
                    SkuQuantity.fromLong(0),
                    actualInboundQty,
                ),
        )

        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )

        dayCalculation.inventory = listOf(inventory)
        dayCalculation.demand = demandQty

        val calculations = (0L..10L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }

        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        assertTrue(results[0].closingStock.getValue().toLong() < 0)
        assertEquals(
            SkuQuantity.fromLong(inventory.qty.getValue().toLong())
                .plus(actualInboundQty) - SkuQuantity.fromLong(100),
            results[0].closingStock,
        )
    }

    @Test fun `expired and inventory stocks are correctly identified in inventory volume for particular day`() {
        val inboundQty = SkuQuantity.fromLong(10L)
        val demandQty = SkuQuantity.fromLong(100)
        val unexpiredInventory = calculationInventory(BigDecimal(1000), null)
        val expiredInventory = calculationInventory(BigDecimal(100), targetDate)
        val unusableInventory = calculationInventory(BigDecimal(50), null, LOCATION_TYPE_WASTE)
        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                demands = Demands(
                    listOf(
                        Demand(
                            skuId = DEFAULT_UUID,
                            dcCode = DEFAULT_DC_CODE,
                            date = targetDate,
                            forecastedQty = demandQty,
                        ),
                    ),
                ),
            )
                .withPo(
                    targetDate,
                    SkuQuantity.fromLong(0),
                    inboundQty,
                ),
        )

        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        dayCalculation.inventory = listOf(unexpiredInventory, expiredInventory, unusableInventory)
        dayCalculation.demand = demandQty

        val calculations = (0L..10L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }

        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        assertEquals(
            unexpiredInventory.qty.getValue().toLong() + inboundQty.getValue().toLong() - demandQty.getValue().toLong(),
            dayCalculation.inventory.sumOf {
                it.qty
            }.getValue().toLong(),
        )
        assertEquals(
            expiredInventory.qty + unusableInventory.qty,
            results[0].unusable,
        )
    }

    @Test fun `present quantity is the sum of expired and unexpired inventory`() {
        val unexpiredInventory = calculationInventory(BigDecimal(1000), null)
        val expiredInventory = calculationInventory(BigDecimal(100), targetDate)
        val rules = selectCalculationsRules(calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)))

        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        dayCalculation.inventory = listOf(unexpiredInventory, expiredInventory)

        val calculations = (0L..10L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }

        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        assertEquals(
            unexpiredInventory.qty + expiredInventory.qty,
            results[0].present,
        )
        assertEquals(expiredInventory.qty, results[0].unusable)
    }

    @Test fun `daily needs value is zero when closing stock is equal or greater than zero`() {
        val unexpiredInventory = listOf(calculationInventory(BigDecimal(1), null))
        val rules = selectCalculationsRules(calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)))
        val expectedDailyNeeds = 0L

        val positiveInventory = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        positiveInventory.inventory = unexpiredInventory

        var calculations = (0L..10L).associate { positiveInventory.key.daysInFuture(it) to positiveInventory.daysInFuture(it) }

        var results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }
        assertEquals(expectedDailyNeeds, results[0].dailyNeeds.getValue().toLong())

        val zeroInventory = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        calculations = (0L..10L).associate { zeroInventory.key.daysInFuture(it) to zeroInventory.daysInFuture(it) }

        results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }
        assertEquals(expectedDailyNeeds, results[0].dailyNeeds.getValue().toLong())
    }

    @Test fun `daily needs value is equal the closing stock value when absolute closing stock is less or equal demand`() {
        val demandQty = SkuQuantity.fromLong(100)
        val expectedClosingStock = demandQty
        val expectedDailyNeeds = 0L

        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku))
                .copy(
                    demands = Demands(
                        listOf(
                            Demand(
                                skuId = DEFAULT_UUID,
                                dcCode = DEFAULT_DC_CODE,
                                date = targetDate,
                                forecastedQty = demandQty,
                            ),
                        ),
                    ),
                ),
        )

        val dayCalculationOnlyWithDemand = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        dayCalculationOnlyWithDemand.demand = demandQty

        var calculations = (0L..10L).associate {
            dayCalculationOnlyWithDemand.key.daysInFuture(it) to dayCalculationOnlyWithDemand.daysInFuture(it)
        }
        var results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }
        assertEquals(expectedClosingStock.getValue().toLong(), results[0].dailyNeeds.getValue().toLong())

        val unexpiredInventory = BigDecimal(10L)
        val demand = SkuQuantity.fromLong(10)
        val dayCalculationWithDemandSameAsInventory = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        dayCalculationWithDemandSameAsInventory.demand = demand
        dayCalculationWithDemandSameAsInventory.inventory = listOf(calculationInventory(unexpiredInventory, null))

        val newRules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku))
                .copy(
                    demands = Demands(
                        listOf(
                            Demand(
                                skuId = DEFAULT_UUID,
                                dcCode = DEFAULT_DC_CODE,
                                date = targetDate,
                                forecastedQty = demand,
                            ),
                        ),
                    ),
                ),
        )

        calculations = (0L..10L).associate {
            dayCalculationWithDemandSameAsInventory.key.daysInFuture(it) to
                dayCalculationWithDemandSameAsInventory.daysInFuture(it)
        }
        results = calculations.map { CalculationStep(calculations, it.value).apply(newRules).calculate() }
        assertEquals(expectedDailyNeeds, results[0].dailyNeeds.getValue().toLong())
    }

    @Test fun `daily needs value is equal to absolute closing stock value`() {
        val demandQty = SkuQuantity.fromLong(10)
        val actualInbound = SkuQuantity.fromLong(1L)
        val inventory = BigDecimal(200L)
        val expectedDailyNeeds = 9L

        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku))
                .copy(
                    demands = Demands(
                        listOf(
                            Demand(
                                skuId = DEFAULT_UUID,
                                dcCode = DEFAULT_DC_CODE,
                                date = targetDate,
                                forecastedQty = demandQty,
                            ),
                        ),
                    ),
                )
                .withPo(
                    targetDate,
                    SkuQuantity.fromLong(0),
                    actualInbound,
                ),
        )

        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        dayCalculation.demand = demandQty
        dayCalculation.inventory = listOf(calculationInventory(inventory, targetDate.minusDays(1)))

        val calculations = (0L..10L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }
        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        assertEquals(expectedDailyNeeds, results[0].dailyNeeds.getValue().toLong())
    }

    @Test fun `if cleardown does not exists, don't consider the actual and expected inbound of the current date`() {
        val inboundQty = SkuQuantity.fromLong(100L)
        val expectedInboundQty = SkuQuantity.fromLong(100L)
        val expectedClosingStock = SkuQuantity.fromLong(0L)
        val rules = selectCalculationsRules(
            calculatorData(false, mapOf(DEFAULT_UUID to defaultSku))
                .withPo(
                    targetDate,
                    expectedInboundQty,
                    inboundQty,
                ),
        )

        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        dayCalculation.actualInboundQty = inboundQty
        dayCalculation.expectedInboundQty = expectedInboundQty

        val calculations = (0L..10L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }
        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        assertEquals(inboundQty, results[0].actualInbound)
        assertEquals(expectedInboundQty, results[0].expectedInbound)
        assertEquals(expectedClosingStock, results[0].closingStock)
    }

    @Test fun `if cleardown exists, then consider the actual inbound of the current date`() {
        val inboundQty = SkuQuantity.fromLong(100L)
        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku))
                .withPo(
                    targetDate,
                    SkuQuantity.fromLong(0),
                    inboundQty,
                ),
        )

        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )

        val calculations = (0L..10L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }
        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        assertEquals(100, results[0].actualInbound.getValue().toLong())
        assertEquals(100, results[0].closingStock.getValue().toLong())
    }

    @Test fun `demand is served from expected inbound for the days in future`() {
        val demandQty = SkuQuantity.fromLong(100)

        val dayCalculation = DayCalculation(
            key = calculationKey(today.plusDays(3)),
            sku = defaultSku,
        )
        dayCalculation.demand = demandQty
        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku))
                .copy(
                    demands = Demands(
                        listOf(
                            Demand(
                                skuId = DEFAULT_UUID,
                                dcCode = DEFAULT_DC_CODE,
                                date = today.plusDays(3),
                                forecastedQty = demandQty,
                            ),
                        ),
                    ),
                )
                .withPo(
                    dayCalculation.key.date,
                    SkuQuantity.fromLong(200),
                ),
        )

        val calculations = (0L..10L).associate { dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it) }
        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }

        assertEquals(200, results[0].expectedInbound.getValue().toLong())
        assertEquals(100, results[0].closingStock.getValue().toLong())
    }

    @Test fun `the inventory stock should become unusable on the sku acceptableCodeLife date`() {
        val acceptableCodeLife = 2
        val expectedOpeningStock = SkuQuantity.fromLong(0L)
        val rules = selectCalculationsRules(calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)))
        val inventory = calculationInventory(BigDecimal(5), today.plusDays(acceptableCodeLife + 1L))
        val dayCalculation = DayCalculation(
            key = calculationKey(today),
            sku = defaultSku.copy(acceptableCodeLife = acceptableCodeLife),
        )
        dayCalculation.inventory = listOf(inventory)

        val calculations = (0L..acceptableCodeLife).associate {
            dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it)
        }
        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }
        (0 until acceptableCodeLife).forEach {
            assertEquals(inventory.qty, results[it].openingStock)
        }
        assertEquals(inventory.qty, results[acceptableCodeLife].unusable)
        assertEquals(expectedOpeningStock, results[acceptableCodeLife].openingStock)
    }

    @Test fun `unusable movements should not be applied when the feature gate is switched off`() {
        statsigFeatureFlagClient.fixtures = setOf(ApplyUnusableMovementsStock(setOf()))
        val acceptableCodeLife = 2
        val expectedOpeningStock = SkuQuantity.fromLong(500L)
        val originLocationId = UUID.randomUUID().toString()
        val transportModuleId = UUID.randomUUID().toString()
        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku))
                .let {
                    it.copy(
                        inventory = it.inventory.copy(
                            inventoryActivities = listOf(
                                InventoryMovement.Companion.default()
                                    .copy(
                                        originLocationId = originLocationId,
                                        quantity = SkuQuantity.fromBigDecimal(BigDecimal(100L)),
                                        typeId = UUID.randomUUID().toString(),
                                        originLocationType = LOCATION_TYPE_STAGING,
                                        destinationLocationType = LOCATION_TYPE_WASTE,
                                        transportModuleId = transportModuleId,
                                    ),
                            ),
                        ),
                    )
                },
        )
        val inventory = CalculationInventory(
            SkuQuantity.fromLong(500),
            null,
            locationType = LOCATION_TYPE_STAGING,
            location = Location(
                id = originLocationId,
                type = LOCATION_TYPE_STAGING,
                transportModuleId = transportModuleId,
            ),
        )
        val dayCalculation = DayCalculation(
            key = calculationKey(today),
            sku = defaultSku.copy(acceptableCodeLife = acceptableCodeLife),
        )
        dayCalculation.inventory = listOf(inventory)

        val calculations = (0L..acceptableCodeLife).associate {
            dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it)
        }
        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }
        assertEquals(expectedOpeningStock, results[0].openingStock)
        assertEquals(0, results[0].unusable.getValue().toLong())
    }

    @Test fun `unusable movements should not be applied when inventory moved from unusable to another unusable location`() {
        statsigFeatureFlagClient.fixtures = setOf(ApplyUnusableMovementsStock(setOf(ContextData(DC, DEFAULT_DC_CODE))))
        val acceptableCodeLife = 2
        val expectedOpeningStock = SkuQuantity.fromLong(500L)
        val originLocationId = UUID.randomUUID().toString()
        val transportModuleId = UUID.randomUUID().toString()
        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                inventory = InventorySnapshots(
                    emptyList(),
                    emptyList(),
                    listOf(
                        CleardownData(
                            dcCode = DEFAULT_DC_CODE,
                            cleardownTime = cleardownTime,
                            cleardownMode = SCHEDULED,
                            snapshot = null,
                        ),
                    ),
                    listOf(
                        InventoryMovement.Companion.default()
                            .copy(
                                activityTime = cleardownTime.plusHours(3).atOffset(UTC),
                                originLocationId = originLocationId,
                                quantity = SkuQuantity.fromBigDecimal(BigDecimal(100L)),
                                typeId = UUID.randomUUID().toString(),
                                originLocationType = LOCATION_TYPE_QUARANTINE,
                                destinationLocationType = LOCATION_TYPE_WASTE,
                                transportModuleId = transportModuleId,
                            ),
                    ),
                ),
            ),
        )
        val inventory = CalculationInventory(
            SkuQuantity.fromLong(500),
            null,
            locationType = LOCATION_TYPE_STAGING,
            location = Location(
                id = originLocationId,
                type = LOCATION_TYPE_STAGING,
                transportModuleId = transportModuleId,
            ),
        )
        val dayCalculation = DayCalculation(
            key = calculationKey(today),
            sku = defaultSku.copy(acceptableCodeLife = acceptableCodeLife),
        )
        dayCalculation.inventory = listOf(inventory)

        val calculations = (0L..acceptableCodeLife).associate {
            dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it)
        }
        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }
        assertEquals(expectedOpeningStock, results[0].openingStock)
        assertEquals(0, results[0].unusable.getValue().toLong())
    }

    @Test fun `opening stock is decreased when the inventory stock is moved from usable to unusable`() {
        statsigFeatureFlagClient.fixtures = setOf(ApplyUnusableMovementsStock(setOf(ContextData(DC, DEFAULT_DC_CODE))))
        val acceptableCodeLife = 2
        val expectedOpeningStock = SkuQuantity.fromLong(400L)
        val originLocationId = UUID.randomUUID().toString()
        val transportModuleId = UUID.randomUUID().toString()
        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                inventory = InventorySnapshots(
                    emptyList(),
                    emptyList(),
                    listOf(
                        CleardownData(
                            dcCode = DEFAULT_DC_CODE,
                            cleardownTime = cleardownTime,
                            cleardownMode = SCHEDULED,
                            snapshot = null,
                        ),
                    ),
                    listOf(
                        InventoryMovement.Companion.default()
                            .copy(
                                activityTime = cleardownTime.plusHours(3).atOffset(UTC),
                                originLocationId = originLocationId,
                                quantity = SkuQuantity.fromBigDecimal(BigDecimal(100L)),
                                typeId = UUID.randomUUID().toString(),
                                originLocationType = LOCATION_TYPE_STAGING,
                                destinationLocationType = LOCATION_TYPE_WASTE,
                                transportModuleId = transportModuleId,
                                skuId = DEFAULT_UUID,
                                dcCode = DEFAULT_DC_CODE,
                            ),
                    ),
                ),
            ),
        )
        val inventory = CalculationInventory(
            SkuQuantity.fromLong(500),
            null,
            locationType = LOCATION_TYPE_STAGING,
            location = Location(
                id = originLocationId,
                type = LOCATION_TYPE_STAGING,
                transportModuleId = transportModuleId,
            ),
        )
        val dayCalculation = DayCalculation(
            key = calculationKey(today),
            sku = defaultSku.copy(acceptableCodeLife = acceptableCodeLife),
        )
        dayCalculation.inventory = listOf(inventory)

        val calculations = (0L..acceptableCodeLife).associate {
            dayCalculation.key.daysInFuture(it) to dayCalculation.daysInFuture(it)
        }
        val results = calculations.map { CalculationStep(calculations, it.value).apply(rules).calculate() }
        assertEquals(expectedOpeningStock, results[0].openingStock)
        assertEquals(100, results[0].unusable.getValue().toLong())
    }

    @Test fun `unconsumed Inbounds become inventory for the next day even if inventory exists for non FCMS and WMSLITE systems`() {
        val targetDate = LocalDate.now()
        val expiryDate = LocalDate.now().plusDays(100)
        var pos = createPurchaseOrders(
            date = targetDate,
            expectedQuantity = SkuQuantity.fromLong(1000L),
            actualQuantity = SkuQuantity.fromLong(10L),
            expiryDate = expiryDate,
        )

        val delivery = pos.first().purchaseOrderSkus.first().deliveries.first().copy(expiryDate = expiryDate)
        val delivery1 = delivery.copy(
            id = UUID.randomUUID().toString(),
            quantity = SkuQuantity.fromLong(10L),
            expiryDate = expiryDate,
        )
        val delivery2 = delivery.copy(
            id = UUID.randomUUID().toString(),
            quantity = SkuQuantity.fromLong(30L),
            expiryDate = null,
        )

        pos = listOf(
            pos.first().copy(
                purchaseOrderSkus = pos.first().purchaseOrderSkus.map {
                    it.copy(
                        // 2 deliveries have same expiry and 1 has null
                        deliveries = listOf(delivery, delivery1, delivery2),
                    )
                },
            ),
        )
        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                purchaseOrderInbounds = PurchaseOrderInbounds(pos),
                dcConfig = mapOf(
                    DEFAULT_DC_CODE to DistributionCenterConfiguration.default(DEFAULT_DC_CODE).copy(wmsType = WMS_SYSTEM_HIGH_JUMP),
                ),
            ),
        )

        val qty = BigDecimal(10_000L).setScale(5, HALF_UP)
        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        dayCalculation.inventory = listOf(
            CalculationInventory(
                qty = SkuQuantity.fromBigDecimal(qty),
                expiryDate = null,
                locationType = LOCATION_TYPE_STORAGE,
                sourcePoNumber = pos.first().number,
            ),
        )

        val calculations = mapOf(dayCalculation.key to dayCalculation)

        CalculationStep(calculations, dayCalculation).apply(rules).calculate()
        // 1 for each expiry
        assertEquals(3, dayCalculation.inventory.size)
        // We get both inventory and po qty
        assertEquals(
            setOf(
                qty.stripTrailingZeros().toPlainString().toBigDecimal(),
                BigDecimal(30).stripTrailingZeros().toPlainString().toBigDecimal(),
            ),
            dayCalculation.inventory.filter { it.expiryDate == null }.map { it.qty.getValue() }.toSet(),
        )
        // 2 deliveries are aggregated
        assertEquals(20, dayCalculation.inventory.first { it.expiryDate != null }.qty.getValue().toLong())
    }

    @ParameterizedTest
    @CsvSource("WMS_SYSTEM_FCMS", "WMS_SYSTEM_WMS_LITE")
    fun `unconsumed Inbounds in the future dates becomes inventory for FCMS and WMSLITE DCs`(
        wmsSystem: WmsSystem
    ) {
        val dc = DistributionCenterConfiguration.default(DEFAULT_DC_CODE).copy(wmsType = wmsSystem)
        val targetDate = LocalDate.now()
        val expiryDate = LocalDate.now().plusDays(100)
        var pos = createPurchaseOrders(
            date = targetDate,
            expectedQuantity = SkuQuantity.fromLong(1000L),
            actualQuantity = SkuQuantity.fromLong(10L),
            expiryDate = expiryDate,
        )

        val delivery1 = pos.first().purchaseOrderSkus.first().deliveries.first()
            .copy(
                expiryDate = expiryDate,
                deliveryTime = cleardownTime.plusHours(5).atZone(dc.zoneId),
            )
        val delivery2 = delivery1.copy(
            id = UUID.randomUUID().toString(),
            quantity = SkuQuantity.fromLong(30L),
            expiryDate = null,
        )

        pos = listOf(
            pos.first().copy(
                purchaseOrderSkus = pos.first().purchaseOrderSkus.map {
                    it.copy(
                        deliveries = listOf(delivery1, delivery2),
                    )
                },
            ),
        )

        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                purchaseOrderInbounds = PurchaseOrderInbounds(pos),
                dcConfig = mapOf(
                    DEFAULT_DC_CODE to dc,
                ),
                inventory = InventorySnapshots(
                    inventoryList = listOf(),
                    liveInventoryList = listOf(),
                    cleardownData = listOf(
                        CleardownData(
                            dcCode = DEFAULT_DC_CODE,
                            cleardownTime = cleardownTime,
                            cleardownMode = SCHEDULED,
                            snapshot = InventorySnapshot.default().copy(
                                dcCode = DEFAULT_DC_CODE,
                                snapshotTime = cleardownTime,
                            ),
                        ),
                    ),
                    inventoryActivities = listOf(
                        InventoryMovement.default().copy(
                            poNumber = pos.first().number,
                            skuId = calculationKey(targetDate).cskuId,
                            activityTime = cleardownTime.plusHours(15).atOffset(UTC), // after cleardown, doesn't matter
                            typeId = InventoryAdjustmentTypeId.USP.name,
                            dcCode = DEFAULT_DC_CODE,
                        ),

                        InventoryMovement.default().copy(
                            poNumber = pos.first().number,
                            skuId = calculationKey(targetDate).cskuId,
                            activityTime = cleardownTime.plusHours(15).atOffset(UTC), // after cleardown, doesn't matter
                            typeId = InventoryAdjustmentTypeId.RCV.name,
                            dcCode = DEFAULT_DC_CODE,
                        ),
                    ),
                ),
            ),
        )

        val qty = BigDecimal(10_000L)
        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        dayCalculation.inventory = listOf(
            CalculationInventory(
                qty = SkuQuantity.fromBigDecimal(qty),
                expiryDate = null,
                locationType = LOCATION_TYPE_STORAGE,
                sourcePoNumber = pos.first().number,
            ),
        )

        val calculations = mapOf(dayCalculation.key to dayCalculation)

        CalculationStep(calculations, dayCalculation).apply(rules).calculate()

        // 1 for each expiry
        assertEquals(3, dayCalculation.inventory.size) // PO become inventory
        assertTrue {
            dayCalculation.inventory.any {
                it.qty == delivery1.quantity && it.expiryDate == delivery1.expiryDate
            }
        }
        assertTrue {
            dayCalculation.inventory.any {
                it.qty == delivery2.quantity && it.expiryDate == delivery2.expiryDate
            }
        }
        assertEquals(
            pos.first().purchaseOrderSkus.first().expectedQuantity,
            dayCalculation.expectedInboundQty,
        ) // POs are added
        assertEquals(delivery1.quantity.plus(delivery2.quantity), dayCalculation.actualInboundQty)
    }

    @ParameterizedTest
    @CsvSource("WMS_SYSTEM_FCMS", "WMS_SYSTEM_WMS_LITE")
    fun `unconsumed Inbounds when present in inventory are not accounted for FCMS and WMSLITE DCs`(
        wmsSystem: WmsSystem
    ) {
        val dc = DistributionCenterConfiguration.default(DEFAULT_DC_CODE).copy(wmsType = wmsSystem)
        val targetDate = LocalDate.now()
        val expiryDate = LocalDate.now().plusDays(100)
        var pos = createPurchaseOrders(
            date = targetDate,
            expectedQuantity = SkuQuantity.fromLong(1000L),
            actualQuantity = SkuQuantity.fromLong(10L),
            expiryDate = expiryDate,
        )

        val delivery = pos.first().purchaseOrderSkus.first().deliveries.first()
            .copy(expiryDate = expiryDate, deliveryTime = cleardownTime.minusHours(14).atZone(dc.zoneId))
        val delivery1 = delivery.copy(
            id = UUID.randomUUID().toString(),
            quantity = SkuQuantity.fromLong(10L),
            expiryDate = expiryDate,
        )
        val delivery2 = delivery.copy(
            id = UUID.randomUUID().toString(),
            quantity = SkuQuantity.fromLong(30L),
            expiryDate = null,
        )

        pos = listOf(
            pos.first().copy(
                purchaseOrderSkus = pos.first().purchaseOrderSkus.map {
                    it.copy(
                        // 2 deliveries have same expiry and 1 has null
                        deliveries = listOf(delivery, delivery1, delivery2),
                    )
                },
            ),
        )

        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                purchaseOrderInbounds = PurchaseOrderInbounds(pos),
                dcConfig = mapOf(
                    DEFAULT_DC_CODE to dc,
                ),
                inventory = InventorySnapshots(
                    inventoryList = listOf(),
                    liveInventoryList = listOf(),
                    cleardownData = listOf(
                        CleardownData(
                            dcCode = DEFAULT_DC_CODE,
                            cleardownTime = cleardownTime,
                            cleardownMode = SCHEDULED,
                            snapshot = InventorySnapshot.default().copy(
                                dcCode = DEFAULT_DC_CODE,
                                snapshotTime = cleardownTime,
                            ),
                        ),
                    ),
                    inventoryActivities = listOf(
                        InventoryMovement.default().copy(
                            poNumber = pos.first().number,
                            skuId = calculationKey(targetDate).cskuId,
                            activityTime = cleardownTime.minusHours(15).atOffset(UTC), // before cleardown
                            typeId = InventoryAdjustmentTypeId.USP.name,
                            dcCode = DEFAULT_DC_CODE,
                        ),

                        InventoryMovement.default().copy(
                            poNumber = pos.first().number,
                            skuId = calculationKey(targetDate).cskuId,
                            activityTime = cleardownTime.minusHours(15).atOffset(UTC), // before cleardown
                            typeId = InventoryAdjustmentTypeId.RCV.name,
                            dcCode = DEFAULT_DC_CODE,
                        ),
                    ),
                ),
            ),
        )

        val qty = BigDecimal(10_000L).setScale(5, HALF_UP)
        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        dayCalculation.inventory = listOf(
            CalculationInventory(
                qty = SkuQuantity.fromBigDecimal(qty),
                expiryDate = null,
                locationType = LOCATION_TYPE_STORAGE,
                sourcePoNumber = pos.first().number,
            ),
        )

        val calculations = mapOf(dayCalculation.key to dayCalculation)

        CalculationStep(calculations, dayCalculation).apply(rules).calculate()
        // 1 for each expiry
        assertEquals(1, dayCalculation.inventory.size) // Po doesn't become inventory
        assertEquals(
            qty.stripTrailingZeros().toPlainString().toBigDecimal(),
            dayCalculation.inventory.first().qty.getValue(),
        )
    }

    @ParameterizedTest
    @CsvSource("WMS_SYSTEM_FCMS", "WMS_SYSTEM_WMS_LITE")
    fun `unconsumed Inbounds when not present in inventory are accounted for FCMS and WMSLITE DCs`(
        wmsSystem: WmsSystem
    ) {
        val dc = DistributionCenterConfiguration.default(DEFAULT_DC_CODE).copy(wmsType = wmsSystem)
        val targetDate = LocalDate.now()
        val expiryDate = LocalDate.now().plusDays(100)
        var pos = createPurchaseOrders(
            date = cleardownTime.minusHours(14).atZone(dc.zoneId).toLocalDate(),
            expectedQuantity = SkuQuantity.fromLong(1000L),
            actualQuantity = SkuQuantity.fromLong(300),
            expiryDate = expiryDate,
        )

        val delivery1 = pos.first().purchaseOrderSkus.first().deliveries.first()
            .copy(
                expiryDate = expiryDate,
                deliveryTime = cleardownTime.minusHours(14).atZone(dc.zoneId),
                state = OPEN,
            )
        val delivery2 = delivery1.copy(
            id = UUID.randomUUID().toString(),
            quantity = SkuQuantity.fromLong(400),
            expiryDate = null,
            state = CLOSED,
        )

        pos = listOf(
            pos.first().copy(
                purchaseOrderSkus = pos.first().purchaseOrderSkus.map {
                    it.copy(
                        deliveries = listOf(delivery1, delivery2),
                    )
                },
            ),
        )

        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                purchaseOrderInbounds = PurchaseOrderInbounds(pos),
                dcConfig = mapOf(
                    DEFAULT_DC_CODE to DistributionCenterConfiguration.default(DEFAULT_DC_CODE).copy(wmsType = wmsSystem),
                ),
                inventory = InventorySnapshots(
                    inventoryList = listOf(),
                    liveInventoryList = listOf(),
                    cleardownData = listOf(
                        CleardownData(
                            dcCode = DEFAULT_DC_CODE,
                            cleardownTime = cleardownTime,
                            cleardownMode = SCHEDULED,
                            snapshot = InventorySnapshot.default().copy(
                                dcCode = DEFAULT_DC_CODE,
                                snapshotTime = cleardownTime,
                            ),
                        ),
                    ),
                    inventoryActivities = listOf(), // No inventory activities
                ),
            ),
        )

        val openingInventoryQty = SkuQuantity.fromLong(10000)
        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )
        dayCalculation.inventory = listOf(
            CalculationInventory(
                qty = openingInventoryQty,
                expiryDate = null,
                locationType = LOCATION_TYPE_STORAGE,
                sourcePoNumber = pos.first().number,
            ),
        )

        val calculations = mapOf(dayCalculation.key to dayCalculation)

        CalculationStep(calculations, dayCalculation).apply(rules).calculate()
        // 1 for each expiry
        assertEquals(3, dayCalculation.inventory.size) // older PO become inventory
        assertTrue {
            dayCalculation.inventory.any {
                it.qty == delivery1.quantity && it.expiryDate == delivery1.expiryDate
            }
        }
        assertTrue {
            dayCalculation.inventory.any {
                it.qty == delivery2.quantity && it.expiryDate == delivery2.expiryDate
            }
        }
        assertEquals(
            openingInventoryQty +
                pos.flatMap { it.purchaseOrderSkus.flatMap { it.deliveries } }.sumOf { it.quantity },
            dayCalculation.openingStock,
        )
        assertEquals(0, dayCalculation.expectedInboundQty.getValue().toLong()) // Older POs are not added
        assertEquals(0, dayCalculation.actualInboundQty.getValue().toLong())
    }

    @Test
    fun `remaining quantity from unconsumed Inbounds in same day are not present in opening stock`() {
        val dc = DistributionCenterConfiguration.default(DEFAULT_DC_CODE).copy(wmsType = WMS_SYSTEM_FCMS)
        val targetDate = LocalDate.now()
        var pos = createPurchaseOrders(
            date = cleardownTime.atZone(dc.zoneId).toLocalDate(),
            expectedQuantity = SkuQuantity.fromLong(1000L),
            actualQuantity = SkuQuantity.fromLong(0),
        )

        val delivery1 = pos.first().purchaseOrderSkus.first().deliveries.first()
            .copy(
                deliveryTime = cleardownTime.minusHours(1).atZone(dc.zoneId),
                state = OPEN,
            )
        val delivery2 = delivery1.copy(
            id = UUID.randomUUID().toString(),
            quantity = SkuQuantity.fromLong(400),
            state = CLOSED,
        )

        pos = listOf(
            pos.first().copy(
                purchaseOrderSkus = pos.first().purchaseOrderSkus.map {
                    it.copy(
                        deliveries = listOf(delivery1, delivery2),
                    )
                },
            ),
        )

        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                purchaseOrderInbounds = PurchaseOrderInbounds(pos),
                dcConfig = mapOf(dc.dcCode to dc),
                inventory = InventorySnapshots(
                    inventoryList = listOf(),
                    liveInventoryList = listOf(),
                    cleardownData = listOf(
                        CleardownData(
                            dcCode = DEFAULT_DC_CODE,
                            cleardownTime = cleardownTime,
                            cleardownMode = SCHEDULED,
                            snapshot = InventorySnapshot.default().copy(
                                dcCode = DEFAULT_DC_CODE,
                                snapshotTime = cleardownTime,
                            ),
                        ),
                    ),
                    inventoryActivities = listOf(), // No inventory activities
                ),
            ),
        )

        val dayCalculation = DayCalculation(
            key = calculationKey(targetDate),
            sku = defaultSku,
        )

        val calculations = mapOf(dayCalculation.key to dayCalculation)

        CalculationStep(calculations, dayCalculation).apply(rules).calculate()

        assertEquals(
            pos.flatMap { it.purchaseOrderSkus.flatMap { it.deliveries } }.sumOf { it.quantity },
            dayCalculation.openingStock,
        )
    }

    @Test
    fun `inbounded snapshot deliveries are not included in cleardown opening stock`() {
        val dc = DistributionCenterConfiguration.default(DEFAULT_DC_CODE).copy(wmsType = WMS_SYSTEM_UNSPECIFIED)
        var pos = createPurchaseOrders(
            date = cleardownTime.minusDays(1).atZone(dc.zoneId).toLocalDate(),
            expectedQuantity = SkuQuantity.fromLong(1000L),
            actualQuantity = SkuQuantity.fromLong(0),
        )

        val delivery1 = pos.first().purchaseOrderSkus.first().deliveries.first()
            .copy(
                deliveryTime = cleardownTime.minusHours(12).atZone(dc.zoneId),
                state = OPEN,
            )
        val delivery2 = delivery1.copy(
            id = UUID.randomUUID().toString(),
            quantity = SkuQuantity.fromLong(400),
            state = CLOSED,
        )

        pos = listOf(
            pos.first().copy(
                purchaseOrderSkus = pos.first().purchaseOrderSkus.map {
                    it.copy(
                        deliveries = listOf(delivery1, delivery2),
                    )
                },
            ),
        )

        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                purchaseOrderInbounds = PurchaseOrderInbounds(pos),
                dcConfig = mapOf(dc.dcCode to dc),
                inventory = InventorySnapshots(
                    inventoryList = listOf(),
                    liveInventoryList = listOf(),
                    cleardownData = listOf(
                        CleardownData(
                            dcCode = DEFAULT_DC_CODE,
                            cleardownTime = cleardownTime,
                            cleardownMode = SCHEDULED,
                            snapshot = InventorySnapshot.default().copy(
                                dcCode = DEFAULT_DC_CODE,
                                snapshotTime = cleardownTime,
                            ),
                        ),
                    ),
                    inventoryActivities = emptyList(),
                ),
            ),
        )

        val dayCalculation = DayCalculation(
            key = calculationKey(cleardownTime.toLocalDate()),
            sku = defaultSku,
        )

        val calculations = mapOf(dayCalculation.key to dayCalculation)

        CalculationStep(calculations, dayCalculation).apply(rules).calculate()

        assertEquals(SkuQuantity.ZERO, dayCalculation.openingStock)
        assertEquals(SkuQuantity.ZERO, dayCalculation.closingStock)
        assertEquals(SkuQuantity.ZERO, dayCalculation.expectedInboundQty)
        assertEquals(SkuQuantity.ZERO, dayCalculation.actualInboundQty)
        assertTrue(dayCalculation.expectedInboundPos.isEmpty())
        assertTrue(dayCalculation.actualInboundPos.isEmpty())
    }

    @Test
    fun `sqr calculation result is not negative`() {
        val dc = DistributionCenterConfiguration.default(DEFAULT_DC_CODE).copy(
            wmsType = WMS_SYSTEM_UNSPECIFIED,
            poCutoffTime = LocalTime.parse("11:00:00")
        )
        val expectedQuantity = SkuQuantity.fromLong(200L)
        val pos = createPurchaseOrders(
            date = LocalDate.now(),
            expectedQuantity = expectedQuantity,
            actualQuantity = SkuQuantity.fromLong(0),
        )

        val rules = selectCalculationsRules(
            calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
                purchaseOrderInbounds = PurchaseOrderInbounds(pos),
                dcConfig = mapOf(dc.dcCode to dc),
                inventory = InventorySnapshots(
                    inventoryList = listOf(),
                    liveInventoryList = listOf(),
                    cleardownData = listOf(
                        CleardownData(
                            dcCode = DEFAULT_DC_CODE,
                            cleardownTime = cleardownTime,
                            cleardownMode = SCHEDULED,
                            snapshot = InventorySnapshot.default().copy(
                                dcCode = DEFAULT_DC_CODE,
                                snapshotTime = cleardownTime,
                            ),
                        ),
                    ),
                    inventoryActivities = emptyList(),
                ),
            ),
        )

        val dayCalculation = DayCalculation(
            key = calculationKey(cleardownTime.toLocalDate()),
            sku = defaultSku,
        ).apply {
            demand = SkuQuantity.fromLong(100)
        }

        val calculations = mapOf(dayCalculation.key to dayCalculation)

        CalculationStep(calculations, dayCalculation).apply(rules).calculate()

        assertEquals(SkuQuantity.ZERO, dayCalculation.openingStock)
        assertEquals(SkuQuantity.ZERO, dayCalculation.closingStock)
        assertEquals(expectedQuantity, dayCalculation.expectedInboundQty)
        assertEquals(SkuQuantity.ZERO, dayCalculation.actualInboundQty)

        // SkuQuantity.max(0, 100 (consumption) - 200 (po before po cut off))
        assertEquals(SkuQuantity.ZERO, dayCalculation.netNeeds)
    }

    private fun calculationInventory(
        qty: BigDecimal,
        expiry: LocalDate? = null,
        locationType: LocationType = LOCATION_TYPE_STAGING
    ) = CalculationInventory(SkuQuantity.fromBigDecimal(qty), expiry, locationType)
}
