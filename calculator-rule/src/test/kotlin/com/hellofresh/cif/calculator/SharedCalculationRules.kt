package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.models.CalculationData
import com.hellofresh.cif.calculator.models.CalculationInventory
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.calculator.models.InputData
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_HIGH_JUMP
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.DeliveryInfo
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.Supplier
import com.hellofresh.cif.models.purchaseorder.TimeRange
import com.hellofresh.cif.transferorder.model.TransferOrderInbounds
import com.hellofresh.cif.transferorder.model.TransferOrderOutbounds
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventoryCleardown
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import com.hellofresh.inventory.models.inventory.live.SkuLiveInventory
import com.hellofresh.sku.models.SkuSpecification
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID

const val DEFAULT_TEST_STRATEGY = "ALGORITHM_FORECASTVARIANCE"

open class SharedCalculationRules(private val mode: CalculatorMode) {

    // TODO at least 1 test has to be common
    internal val defaultInputData = inputData(true, mapOf(DEFAULT_UUID to defaultSku), mode)
    internal val defaultCalculatorData = calculatorData(true, mapOf(DEFAULT_UUID to defaultSku))

    internal fun calculatorData(
        hasCleardown: Boolean,
        skuSpecs: Map<UUID, SkuSpecification>,
    ) = calculatorData(hasCleardown, skuSpecs, mode)

    fun inventorySnapshots(inventorySnapshot: InventorySnapshot, liveInventorySnapshot: LiveInventorySnapshot) =
        inventorySnapshots(listOf(inventorySnapshot), listOf(liveInventorySnapshot))

    fun inventorySnapshots(
        inventorySnapshots: List<InventorySnapshot>,
        liveInventorySnapshots: List<LiveInventorySnapshot>,
    ) = inventorySnapshots(defaultInputData.dcConfig, inventorySnapshots, liveInventorySnapshots)

    companion object {
        val defaultZoneId = DistributionCenterConfiguration.default().zoneId
        val targetDate = LocalDate.of(2020, 11, 11)
        val today = LocalDate.now(defaultZoneId)
        val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())

        val DEFAULT_UUID = UUID.randomUUID()
        const val DEFAULT_DC_CODE = "VE"
        const val DEFAULT_MARKET = "DACH"
        val defaultSku = SkuSpecification(
            skuCode = "PHF-00-00000-00",
            name = "a name",
            category = "PHF",
            coolingType = "type",
            packaging = "packaging",
            acceptableCodeLife = 0,
            market = "dach",
            fumigationAllowed = null,
        )

        fun Inventory.Companion.fromStaging(c: CalculationInventory) = Inventory(
            qty = c.qty,
            expiryDate = c.expiryDate,
            location = Location("", c.locationType, null),
            null,
        )

        fun Inventory.Companion.fromStorage(c: CalculationInventory) = Inventory(
            qty = c.qty,
            expiryDate = c.expiryDate,
            location = Location("", c.locationType, null),
            null,
        )

        fun calculationKey(targetDate: LocalDate) =
            CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, targetDate)

        fun createInventory(
            qty: SkuQuantity,
            expiryDate: LocalDate?,
            location: Location = Location("", LOCATION_TYPE_STAGING, null)
        ) =
            Inventory(qty, expiryDate, location, null)

        fun inventorySnapshot(calculationKey: CalculationKey, inventory: Inventory) =
            inventorySnapshot(calculationKey, listOf(inventory))

        fun inventorySnapshot(calculationKey: CalculationKey, inventory: List<Inventory>) =
            InventorySnapshot(
                calculationKey.dcCode,
                UUID.randomUUID(),
                calculationKey.date.atStartOfDay(),
                listOf(
                    SkuInventory(
                        calculationKey.cskuId,
                        inventory,
                    ),
                ),
            )

        fun liveInventorySnapshot(calculationKey: CalculationKey, liveInventory: List<Inventory>) =
            LiveInventorySnapshot(
                calculationKey.dcCode,
                UUID.randomUUID(),
                calculationKey.date.atStartOfDay(),
                listOf(
                    SkuLiveInventory(
                        calculationKey.cskuId,
                        liveInventory,
                    ),
                ),
            )

        fun inventorySnapshot(targetDate: LocalDate, inventory: List<Inventory>) =
            InventorySnapshot(
                DEFAULT_DC_CODE,
                UUID.randomUUID(),
                targetDate.atStartOfDay(),
                listOf(
                    SkuInventory(
                        DEFAULT_UUID,
                        inventory,
                    ),
                ),
            )

        fun liveInventorySnapshot(calculationKey: CalculationKey, liveInventory: Inventory) =
            liveInventorySnapshot(calculationKey, listOf(liveInventory))

        fun liveInventorySnapshot(targetDate: LocalDate, liveInventory: List<Inventory>): LiveInventorySnapshot =
            LiveInventorySnapshot(
                DEFAULT_DC_CODE,
                UUID.randomUUID(),
                targetDate.atStartOfDay(),
                listOf(
                    SkuLiveInventory(
                        DEFAULT_UUID,
                        liveInventory,
                    ),
                ),
            )

        fun inventorySnapshots(
            dcConfigs: Map<DcCode, DistributionCenterConfiguration>,
            inventorySnapshot: InventorySnapshot,
            liveInventorySnapshot: LiveInventorySnapshot,
        ) = inventorySnapshots(dcConfigs, listOf(inventorySnapshot), listOf(liveInventorySnapshot))

        fun inventorySnapshots(
            dcConfigs: Map<DcCode, DistributionCenterConfiguration>,
            inventorySnapshots: List<InventorySnapshot>,
            liveInventorySnapshots: List<LiveInventorySnapshot> = emptyList(),
        ) = InventoryCleardown.createInventorySnapshotsWithScheduledCleardown(
            dcConfigs,
            inventorySnapshots,
            liveInventorySnapshots,
        )

        internal fun calculatorData(
            hasCleardown: Boolean,
            skuSpecs: Map<UUID, SkuSpecification>,
            calculatorMode: CalculatorMode,
        ) = CalculationData(
            inputData(hasCleardown, skuSpecs, calculatorMode),
            statsigFeatureFlagClient,
        )

        internal fun inputData(
            hasCleardown: Boolean,
            skuSpecs: Map<UUID, SkuSpecification>,
            calculatorMode: CalculatorMode,
        ) = mapOf(
            DEFAULT_DC_CODE to DistributionCenterConfiguration.default(DEFAULT_DC_CODE).copy(hasCleardown = hasCleardown, wmsType = WMS_SYSTEM_HIGH_JUMP),
        ).let { dcConfigs ->
            InputData(
                mode = calculatorMode,
                skuDcCandidates = skuSpecs.flatMap { sku ->
                    dcConfigs.map { dc ->
                        SkuDcCandidate(
                            sku.key,
                            sku.value,
                            dc.value
                        )
                    }
                }.toSet(),
                inventory = InventoryCleardown.createInventorySnapshotsWithScheduledCleardown(
                    dcConfigs,
                    emptyList(),
                    emptyList(),
                ),
                purchaseOrderInbounds = PurchaseOrderInbounds(emptyList()),
                transferOrderInbounds = TransferOrderInbounds(emptyList()),
                transferOrderOutbounds = TransferOrderOutbounds(emptyList()),
                demands = Demands(emptyList()),
                stockUpdates = emptyMap(),
                safetyStocks = emptyMap(),
                supplierSku = emptyMap(),
                preproductionCleardownDcs = emptySet()
            )
        }

        internal fun CalculationData.withPo(
            date: LocalDate,
            expectedQuantity: SkuQuantity,
            deliveredQuantity: SkuQuantity? = null,
            poNumber: String? = null,
            poRef: String? = null
        ) = this.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(
                listOf(
                    PurchaseOrder(
                        poNumber ?: "PO",
                        poRef ?: "POREF",
                        null,
                        DEFAULT_DC_CODE,
                        TimeRange(
                            date.atStartOfDay(UTC),
                            date.atStartOfDay(UTC).plusMinutes(1),
                        ),
                        null,
                        listOf(
                            PurchaseOrderSku(
                                DEFAULT_UUID,
                                expectedQuantity,
                                deliveredQuantity?.let {
                                    listOf(
                                        DeliveryInfo(
                                            UUID.randomUUID().toString(),
                                            date.atStartOfDay(UTC),
                                            CLOSED, it,
                                        ),
                                    )
                                } ?: emptyList(),
                            ),
                        ),
                        poStatus = APPROVED,
                    ),
                ),
            ),
        )

        fun getPurchaseOrderInbounds(
            pOsToday: Set<Long>,
            todayKey: CalculationKey,
            poDeliveryTimeslotToday: TimeRange
        ) = PurchaseOrderInbounds(
            pOsToday.map {
                PurchaseOrder(
                    "TESTPOZ_$it",
                    "TESTPOZ_$it",
                    null,
                    todayKey.dcCode,
                    poDeliveryTimeslotToday,
                    null,
                    listOf(
                        PurchaseOrderSku(todayKey.cskuId, SkuQuantity.fromLong(it), emptyList()),
                    ),
                    poStatus = APPROVED,
                )
            },
        )

        internal fun createPurchaseOrders(
            date: LocalDate,
            expectedQuantity: SkuQuantity?,
            actualQuantity: SkuQuantity? = null,
            expiryDate: LocalDate? = null
        ) = listOf(
            PurchaseOrder(
                "po3",
                "poRef3_1",
                UUID.randomUUID(),
                DEFAULT_DC_CODE,
                TimeRange(date.atStartOfDay(UTC), date.atStartOfDay(UTC).plusMinutes(1)),
                Supplier(UUID.randomUUID(), "test supplier"),
                listOf(
                    PurchaseOrderSku(
                        DEFAULT_UUID,
                        expectedQuantity,
                        actualQuantity?.let {
                            listOf(
                                DeliveryInfo(
                                    UUID.randomUUID().toString(),
                                    date.atStartOfDay(UTC),
                                    CLOSED, it, expiryDate,
                                ),
                            )
                        } ?: emptyList(),
                        expiryDate,
                    ),
                ),
                poStatus = APPROVED,
            ),
        )
    }
}
