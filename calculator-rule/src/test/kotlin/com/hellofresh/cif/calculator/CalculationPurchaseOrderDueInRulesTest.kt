package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.createInventory
import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.inventorySnapshots
import com.hellofresh.cif.calculator.calculations.SupplierSkuPoDueIn
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode.PRE_PRODUCTION
import com.hellofresh.cif.calculator.models.CalculatorMode.PRODUCTION
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.calculator.models.InputData
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.EuBerlinZoneId
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.transferorder.model.TransferOrderInbounds
import com.hellofresh.cif.transferorder.model.TransferOrderOutbounds
import com.hellofresh.demand.models.Demand
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import com.hellofresh.inventory.models.inventory.live.SkuLiveInventory
import com.hellofresh.sku.models.LeadTime
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.SupplierSkuDetail
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull

class CalculationPurchaseOrderDueInRulesTest {
    private val calculatorClient =
        CalculatorClient(StatsigTestFeatureFlagClient(emptySet()))
    private val cskuId = UUID.randomUUID()
    private val defaultSkuSpec = SkuSpecification("", "name", "pack", "SPI-", "", null, 0, "", null)
    private val dcCode = "VE"
    private val distributionCenterConfiguration = DistributionCenterConfiguration.default(dcCode)
    private val dcConfigMap = mapOf(dcCode to distributionCenterConfiguration)
    private val inputData = InputData(
        PRODUCTION,
        skuDcCandidates = setOf(
            SkuDcCandidate(cskuId, defaultSkuSpec, distributionCenterConfiguration)
        ),
        inventorySnapshots(dcConfigMap, emptyList(), emptyList()),
        PurchaseOrderInbounds(emptyList()),
        transferOrderInbounds = TransferOrderInbounds(emptyList()),
        transferOrderOutbounds = TransferOrderOutbounds(emptyList()),
        demands = Demands(emptyList()),
        safetyStocks = emptyMap(),
        stockUpdates = emptyMap(),
        supplierSku = emptyMap(),
        preproductionCleardownDcs = emptySet(),
    )
    private val expectedPoDueIn1 = -9
    private val expectedPoDueIn2 = -14
    private val today = LocalDate.now()

    @Test
    fun `should be able to calculate the 'purchase order due in' for the given inputs - shortage in current week`() {
        val inventoryQty = SkuQuantity.fromBigDecimal(BigDecimal(2L))
        val demandQty = SkuQuantity.fromLong(99)
        val date = LocalDate.now()
        val key = CalculationKey(cskuId, dcCode, date)

        val expectedSupplierId1 = UUID.randomUUID()
        val expectedSupplierId2 = UUID.randomUUID()

        val inputData = inputData.copy(
            supplierSku = mapOf(
                cskuId to listOf(
                    SupplierSkuDetail(
                        supplierId = expectedSupplierId1,
                        mlor = 0,
                        supplierName = "Supplier 1",
                        leadTimes = listOf(
                            LeadTime(
                                leadTime = 10,
                                startDate = today,
                                endDate = today.plusMonths(10),
                            ),
                        ),
                    ),
                    SupplierSkuDetail(
                        supplierId = expectedSupplierId2,
                        mlor = 0,
                        supplierName = "Supplier 1",
                        leadTimes = listOf(
                            LeadTime(
                                leadTime = 15,
                                startDate = today,
                                endDate = today.plusMonths(10),
                            ),
                        ),
                    ),
                ),
            ),
            inventory = inventorySnapshots(
                inputData.dcConfig,
                InventorySnapshot(
                    key.dcCode,
                    UUID.randomUUID(),
                    LocalDateTime.now(),
                    listOf(
                        SkuInventory(
                            key.cskuId,
                            listOf(createInventory(inventoryQty, null)),
                        ),
                    ),
                ),
                LiveInventorySnapshot(
                    key.dcCode,
                    UUID.randomUUID(),
                    LocalDateTime.now(),
                    listOf(
                        SkuLiveInventory(
                            key.cskuId,
                            listOf(
                                Inventory(
                                    inventoryQty,
                                    null,
                                    Location(
                                        "location-id",
                                        LOCATION_TYPE_STAGING,
                                        "transport-module-id",
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
            demands = Demands(listOf(Demand(key.cskuId, key.dcCode, date.plusDays(1), demandQty))),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)
        val resultPreprod = calculatorClient.runDailyCalculations(inputData.copy(mode = PRE_PRODUCTION))

        val purchaseOrderDueInForSuppliers = { out: List<DayCalculationResult> ->
            out.first { it.closingStock < SkuQuantity.fromLong(0) }.purchaseOrderDueInForSuppliers
        }
        val maxPurchaseOrderDueIn = { out: List<DayCalculationResult> ->
            out.first { it.closingStock < SkuQuantity.fromLong(0) }.maxPurchaseOrderDueIn
        }
        assertEquals(
            listOf(
                SupplierSkuPoDueIn(
                    expectedSupplierId1,
                    expectedPoDueIn1,
                ),
                SupplierSkuPoDueIn(
                    expectedSupplierId2,
                    expectedPoDueIn2,
                ),
            ),
            purchaseOrderDueInForSuppliers(resultProd),
        )
        assertEquals(-14, maxPurchaseOrderDueIn(resultProd))
        assertEquals(-15, maxPurchaseOrderDueIn(resultPreprod))
    }

    @Test
    fun `should be able to calculate the 'purchase order due in' for the given inputs - shortage in future weeks and months`() {
        val inventoryQty = SkuQuantity.fromBigDecimal(BigDecimal(5L))
        val demandQty = SkuQuantity.fromLong(9)
        val date = LocalDate.now(EuBerlinZoneId)
        val negativeStockDate1 = date.plusDays(10)
        val negativeStockDate2 = date.plusDays(40)
        val key = CalculationKey(cskuId, dcCode, date)

        val expectedSupplierId1 = UUID.randomUUID()
        val expectedSupplierId2 = UUID.randomUUID()
        val inputData = inputData.copy(
            supplierSku = mapOf(
                cskuId to listOf(
                    SupplierSkuDetail(
                        supplierId = expectedSupplierId1,
                        supplierName = "Supplier 1",
                        mlor = 0,
                        leadTimes = listOf(
                            LeadTime(
                                leadTime = 10,
                                startDate = today,
                                endDate = today.plusMonths(10),
                            ),
                        ),
                    ),
                    SupplierSkuDetail(
                        supplierId = expectedSupplierId2,
                        supplierName = "Supplier 1",
                        mlor = 0,
                        leadTimes = listOf(
                            LeadTime(
                                leadTime = 15,
                                startDate = today,
                                endDate = today.plusMonths(10),
                            ),
                        ),
                    ),
                ),
            ),
            inventory = inventorySnapshots(
                inputData.dcConfig,
                InventorySnapshot(
                    key.dcCode,
                    UUID.randomUUID(),
                    LocalDateTime.now(),
                    listOf(
                        SkuInventory(
                            key.cskuId,
                            listOf(createInventory(inventoryQty, null)),
                        ),
                    ),
                ),
                LiveInventorySnapshot(
                    key.dcCode,
                    UUID.randomUUID(),
                    LocalDateTime.now(),
                    listOf(
                        SkuLiveInventory(
                            key.cskuId,
                            listOf(
                                Inventory(
                                    inventoryQty,
                                    null,
                                    Location(
                                        "location-id",
                                        LOCATION_TYPE_STAGING,
                                        "transport-module-id",
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
            demands = Demands(
                listOf(
                    Demand(key.cskuId, key.dcCode, negativeStockDate1, demandQty),
                    Demand(key.cskuId, key.dcCode, negativeStockDate2, demandQty),
                ),
            ),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)
        val resultPreprod = calculatorClient.runDailyCalculations(inputData.copy(mode = PRE_PRODUCTION))

        val purchaseOrderDueInForSuppliers = { out: List<DayCalculationResult>, localDate: LocalDate ->
            out.first { it.date == localDate && it.closingStock.getValue().toLong() < 0 }.purchaseOrderDueInForSuppliers
        }
        val maxPurchaseOrderDueIn = { out: List<DayCalculationResult>, localDate: LocalDate ->
            out.first { it.date == localDate && it.closingStock.getValue().toLong() < 0 }.maxPurchaseOrderDueIn
        }

        with(purchaseOrderDueInForSuppliers(resultProd, negativeStockDate1)) {
            assertEquals(
                listOf(
                    SupplierSkuPoDueIn(
                        expectedSupplierId1,
                        0,
                    ),
                    SupplierSkuPoDueIn(
                        expectedSupplierId2,
                        -5,
                    ),
                ),
                this,
            )
        }
        assertEquals(-5, maxPurchaseOrderDueIn(resultProd, negativeStockDate1))
        assertEquals(-6, maxPurchaseOrderDueIn(resultPreprod, negativeStockDate1.minusDays(1)))

        with(purchaseOrderDueInForSuppliers(resultProd, negativeStockDate2)) {
            assertEquals(
                listOf(
                    SupplierSkuPoDueIn(
                        expectedSupplierId1,
                        30,
                    ),
                    SupplierSkuPoDueIn(
                        expectedSupplierId2,
                        25,
                    ),
                ),
                this,
            )
        }
        assertEquals(25, maxPurchaseOrderDueIn(resultProd, negativeStockDate2))
        assertEquals(24, maxPurchaseOrderDueIn(resultPreprod, negativeStockDate2.minusDays(1)))
    }

    @Test
    fun `should be able to calculate the 'purchase order due in' for the given inputs - supplier contract date is out of range`() {
        val inventoryQty = SkuQuantity.fromBigDecimal(BigDecimal(5L))
        val demandQty = SkuQuantity.fromLong(9)
        val date = LocalDate.now()
        val key = CalculationKey(cskuId, dcCode, date)

        val expectedSupplierId1 = UUID.randomUUID()
        val expectedSupplierId2 = UUID.randomUUID()
        val inputData = inputData.copy(
            supplierSku = mapOf(
                cskuId to listOf(
                    SupplierSkuDetail(
                        supplierId = expectedSupplierId1,
                        mlor = 0,
                        supplierName = "Supplier 1",
                        leadTimes = listOf(
                            LeadTime(
                                leadTime = 10,
                                startDate = today.minusMonths(10),
                                endDate = today.minusMonths(9),
                            ),
                        ),
                    ),
                    SupplierSkuDetail(
                        supplierId = expectedSupplierId2,
                        supplierName = "Supplier 1",
                        mlor = 0,
                        leadTimes = listOf(
                            LeadTime(
                                leadTime = 15,
                                startDate = today.plusMonths(10),
                                endDate = today.plusMonths(12),
                            ),
                        ),
                    ),
                ),
            ),
            inventory = inventorySnapshots(
                inputData.dcConfig,
                InventorySnapshot(
                    key.dcCode,
                    UUID.randomUUID(),
                    LocalDateTime.now(),
                    listOf(
                        SkuInventory(
                            key.cskuId,
                            listOf(createInventory(inventoryQty, null)),
                        ),
                    ),
                ),
                LiveInventorySnapshot(
                    key.dcCode,
                    UUID.randomUUID(),
                    LocalDateTime.now(),
                    listOf(
                        SkuLiveInventory(
                            key.cskuId,
                            listOf(
                                Inventory(
                                    inventoryQty,
                                    null,
                                    Location(
                                        "location-id",
                                        LOCATION_TYPE_STAGING,
                                        "transport-module-id",
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
            demands = Demands(listOf(Demand(key.cskuId, key.dcCode, date.plusDays(10), demandQty))),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)
        val resultPreprod = calculatorClient.runDailyCalculations(inputData.copy(mode = PRE_PRODUCTION))

        val purchaseOrderDueInForSuppliers = { out: List<DayCalculationResult>, targetDate: LocalDate ->
            out.first { it.date == targetDate }.purchaseOrderDueInForSuppliers
        }
        val maxPurchaseOrderDueIn = { out: List<DayCalculationResult>, targetDate: LocalDate ->
            out.first { it.date == targetDate }.maxPurchaseOrderDueIn
        }
        assertNull(
            purchaseOrderDueInForSuppliers(resultProd, date.plusDays(1)),
        )
        assertNull(maxPurchaseOrderDueIn(resultProd, date))
        assertNull(maxPurchaseOrderDueIn(resultPreprod, date))
    }

    @Test
    fun `should return empty 'purchase order due in' results for the empty supplier sku`() {
        val inventoryQty = SkuQuantity.fromBigDecimal(BigDecimal(2L))
        val demandQty = SkuQuantity.fromLong(99)
        val date = LocalDate.now()
        val key = CalculationKey(cskuId, dcCode, date)
        val inputData = inputData.copy(
            supplierSku = emptyMap(),
            inventory = inventorySnapshots(
                inputData.dcConfig,
                InventorySnapshot(
                    key.dcCode,
                    UUID.randomUUID(),
                    LocalDateTime.now(),
                    listOf(
                        SkuInventory(
                            key.cskuId,
                            listOf(createInventory(inventoryQty, null)),
                        ),
                    ),
                ),
                LiveInventorySnapshot(
                    key.dcCode,
                    UUID.randomUUID(),
                    LocalDateTime.now(),
                    listOf(
                        SkuLiveInventory(
                            key.cskuId,
                            listOf(
                                Inventory(
                                    inventoryQty,
                                    null,
                                    Location(
                                        "location-id",
                                        LOCATION_TYPE_STAGING,
                                        "transport-module-id",
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
            demands = Demands(listOf(Demand(key.cskuId, key.dcCode, date.plusDays(1), demandQty))),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)
        val resultPreprod = calculatorClient.runDailyCalculations(inputData.copy(mode = PRE_PRODUCTION))

        val purchaseOrderDueInForSuppliers = { out: List<DayCalculationResult>, targetDate: LocalDate ->
            out.first { it.date == targetDate }.purchaseOrderDueInForSuppliers
        }
        val maxPurchaseOrderDueIn = { out: List<DayCalculationResult>, targetDate: LocalDate ->
            out.first { it.date == targetDate }.maxPurchaseOrderDueIn
        }
        assertNull(purchaseOrderDueInForSuppliers(resultProd, date.plusDays(1)))
        assertNull(maxPurchaseOrderDueIn(resultPreprod, date))
    }
}
