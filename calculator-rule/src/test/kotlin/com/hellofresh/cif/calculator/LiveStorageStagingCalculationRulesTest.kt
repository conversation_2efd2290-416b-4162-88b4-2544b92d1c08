package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.calculations.CalculationStep
import com.hellofresh.cif.calculator.calculations.DayCalculation
import com.hellofresh.cif.calculator.calculations.nextDay
import com.hellofresh.cif.calculator.calculations.previousDay
import com.hellofresh.cif.calculator.calculations.rules.removeStaging
import com.hellofresh.cif.calculator.calculations.rules.selectCalculationsRules
import com.hellofresh.cif.calculator.models.CalculationData
import com.hellofresh.cif.calculator.models.CalculationInventory
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode.LIVE_INVENTORY_PRODUCTION
import com.hellofresh.cif.calculator.models.random
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.TimeRange
import com.hellofresh.demand.models.Demand
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_QUARANTINE
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import com.hellofresh.inventory.models.random
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.Period
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.time.ZonedDateTime
import kotlin.math.max
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

@Suppress("LargeClass", "DestructuringDeclarationWithTooManyEntries")
class LiveStorageStagingCalculationRulesTest : SharedCalculationRules(LIVE_INVENTORY_PRODUCTION) {

    @AfterEach
    fun afterEachTest() {
        unmockkAll()
    }

    @Test fun `Test the removeStaging() function, should return the difference between two lists of staging stocks`() {
        data class TestCase(
            val name: String,
            val todayStaging: List<CalculationInventory>,
            val yesterdayStaging: List<CalculationInventory>,
            val expected: List<CalculationInventory>
        )

        val c = z.copy(qty = SkuQuantity.fromLong(100))
        val d = z.copy(qty = SkuQuantity.fromLong(150))

        val testCases = listOf(
            TestCase("Inventory is exactly matching", listOf(x, y, z, a, b), listOf(x, y, z), listOf(a, b)),
            TestCase("Today's inventory bigger than the yesterday one", listOf(x, y, z), listOf(x, y, d), listOf(c)),
            TestCase(
                "Today's inventory bigger than the yesterday one and an extra",
                listOf(x, y, z, b),
                listOf(x, y, d),
                listOf(c, b),
            ),
            TestCase("If there was nothing in staging yesterday", listOf(x, y, z), emptyList(), listOf(x, y, z)),
            TestCase("There is nothing in staging today", emptyList(), listOf(x, y, z), emptyList()),
            TestCase("Only two items are removed from staging today", listOf(x, a), listOf(x, y, z), listOf(a)),
            TestCase("New stock has less than yesterdays", listOf(x, y, d, a), listOf(x, y, z), listOf(a)),
            TestCase("Simplified average test case", listOf(x, a), listOf(x), listOf(a)),
        )

        testCases.forEach { (name, today, yesterday, expected) ->
            assertEquals(expected, today.removeStaging(yesterday), "removeStaging Test: $name")
        }
    }

    @Test fun `removeStaging() throws exception, when inventory is not grouped by expiry and state`() {
        val e = x.copy(qty = SkuQuantity.fromLong(200))
        assertThrows<IllegalStateException> { listOf(x).removeStaging(listOf(x, e)) }
    }

    @Test fun `past storage stock should match the calculated storage stocks`() {
        val todayInventory = Inventory.random(false)
        val yesterdayInventory = Inventory.random(false)
        val dayBeforeYesterdayInventory = Inventory.random(false)

        val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId))
        val yesterdayKey = todayKey.previousDay()
        val dayBeforeYesterdayKey = yesterdayKey.previousDay()

        val inventoryMap = mapOf(
            todayKey to inventorySnapshot(todayKey.previousDay(), todayInventory),
            yesterdayKey to inventorySnapshot(yesterdayKey.previousDay(), yesterdayInventory),
            dayBeforeYesterdayKey to inventorySnapshot(dayBeforeYesterdayKey.previousDay(), dayBeforeYesterdayInventory),
        )

        val inputData = defaultCalculatorData.copy(
            inventory = inventorySnapshots(defaultCalculatorData.dcConfig, inventoryMap.values.toList()),
        )

        val calculations = performTestCalculations(inputData, -5L..2L)

        inventoryMap.filterKeys {
            it.date < todayKey.date
        }.forEach { (key, inv) ->
            val calculated = calculations[key]
            assertNotNull(calculated)
            assertEquals(
                inv.skus.first().inventory.first().qty.getValue().toLong(),
                calculated.openingStorageStock.getValue().toLong(),
            )
            assertEquals(inv.skus.first().inventory.first().qty, calculated.storageStock)
        }
    }

    @Test fun `Today's storage stock and staging should match yesterday inventory if today is cleardown`() {
        val todayInventory = Inventory.random()
        val yesterdayInventory = Inventory.random(true)
        val dayBeforeYesterdayInventory = Inventory.random()

        val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId))
        val yesterdayKey = todayKey.previousDay()
        val dayBeforeYesterdayKey = yesterdayKey.previousDay()

        val inventoryMap = mapOf(
            todayKey to inventorySnapshot(todayKey, todayInventory),
            yesterdayKey to inventorySnapshot(yesterdayKey, listOf(yesterdayInventory, yesterdayInventory.toStorage())),
            dayBeforeYesterdayKey to inventorySnapshot(dayBeforeYesterdayKey, dayBeforeYesterdayInventory),
        )

        val dcConfig =
            mapOf(
                DEFAULT_DC_CODE to (defaultCalculatorData.dcConfig[DEFAULT_DC_CODE]!!.copy(cleardown = todayKey.date.dayOfWeek)),
            )
        val inData = defaultCalculatorData.copy(
            inventory = inventorySnapshots(dcConfig, inventoryMap.values.toList()),
            dcConfig = dcConfig,
        )

        val calculations = performTestCalculations(inData, -5L..2L)

        val calculated = calculations[todayKey]
        assertNotNull(calculated)
        assertEquals(yesterdayInventory.qty, calculated.openingStorageStock)
        assertEquals(yesterdayInventory.qty, calculated.storageStock)
        assertEquals(yesterdayInventory.qty, calculated.openingStagingStock)
        assertEquals(yesterdayInventory.qty, calculated.stagingStock)
    }

    @Test fun `Staging stock is opened with leftover from past day and the staging stock added for the given date`() {
        data class TestCase(
            val name: String,
            val yesterdayStaging: List<CalculationInventory>,
            val todayStaging: List<CalculationInventory>,
            val demandYesterday: List<CalculationInventory>, // demand for yesterday the inventory
            val expectedLeftover: List<CalculationInventory>,
            val expectedStaging: List<CalculationInventory>,
        )

        val halfY = y.copy(qty = SkuQuantity.fromLong(y.qty.getValue().toLong() / 2))
        val doubleX = x.copy(qty = SkuQuantity.fromLong(y.qty.getValue().toLong() * 2))

        val testCases = listOf(
            TestCase(
                "Best Average Test: today's staging is greater than yesterday and there is leftover",
                listOf(x, y),
                listOf(a),
                listOf(halfY, x),
                listOf(x, y),
                listOf(halfY),
            ),
            TestCase(
                "Constant stagingStock and no consumption",
                listOf(x, y),
                listOf(x, y),
                emptyList(),
                listOf(x, y),
                listOf(x, y),
            ),
            TestCase(
                "All staging stock was consumed",
                listOf(x, y),
                listOf(x, y),
                listOf(x, y),
                listOf(x, y),
                emptyList(),
            ),
            TestCase(
                "Half of staging stock was consumed",
                listOf(x, y),
                listOf(x, y),
                listOf(x),
                listOf(x, y),
                listOf(y),
            ),
            TestCase(
                "If demand is greater than staging inventory it shout be zero",
                emptyList(),
                listOf(x),
                listOf(doubleX),
                emptyList(),
                emptyList(),
            ),
            TestCase(
                "Staging today is lower than yesterdays",
                listOf(x, y),
                listOf(halfY),
                listOf(halfY),
                listOf(x, y),
                listOf(x, halfY),
            ),
            TestCase(
                "Staging today doesn't match with yesterdays",
                listOf(x, y),
                listOf(a),
                listOf(halfY),
                listOf(x, y),
                listOf(x, halfY),
            ),
        )

        // For each test case set up a calculation and check if the staging stock output is correct
        testCases.forEach { (name, yesterday, today, demand, expectedLeftover, expected) ->

            val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId))
            val yesterdayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId).minusDays(1))
            val dayBeforeYesterdayKey =
                CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId).minusDays(2))

            val dcConfig =
                mapOf(
                    DEFAULT_DC_CODE to (defaultCalculatorData.dcConfig[DEFAULT_DC_CODE]!!.copy(cleardown = todayKey.date.plusDays(1).dayOfWeek)),
                )
            val input = defaultCalculatorData.copy(
                inventory = inventorySnapshots(
                    dcConfig,
                    listOf(
                        inventorySnapshot(dayBeforeYesterdayKey, yesterday.map { Inventory.fromStaging(it) }),
                        inventorySnapshot(yesterdayKey, today.map { Inventory.fromStaging(it) }),
                        inventorySnapshot(todayKey, emptyList()),
                    ),
                ),
                dcConfig = dcConfig,
                demands = getDemands(yesterdayKey, demand),
            )

            val calculations = performTestCalculations(input, -1L..0L)

            val todayCalculated = calculations.filterKeys { it == todayKey }
            val yesterdayCalculated = calculations.filterKeys { it == yesterdayKey }
            assertEquals(1, todayCalculated.size)
            assertEquals(
                expectedLeftover.sumOf {
                    it.qty.getValue().toLong()
                },
                yesterdayCalculated.values.first().openingStock.getValue().toLong(),
                "Check past day leftover -> $name",
            )
            assertEquals(
                expected.sumOf {
                    it.qty.getValue().toLong()
                },
                todayCalculated.values.first().openingStagingStock.getValue().toLong(),
                "Check openingStagingStock -> $name",
            )
            assertEquals(
                expected.sumOf { it.qty.getValue().toLong() },
                todayCalculated.values.first().stagingStock.getValue().toLong(),
                "Check stagingStock -> $name",
            )
        }
    }

    @Test fun `Staging and storage stock is opened with yesterday closing staging and storage if today is not cleardown`() {
        data class TestCase(
            val name: String,
            val yesterdayStaging: List<CalculationInventory>,
            val todayStaging: List<CalculationInventory>,
            val demandYesterday: List<CalculationInventory>, // demand for yesterday the inventory
            val expectedLeftover: List<CalculationInventory>,
            val expectedStaging: List<CalculationInventory>,
        )

        val halfY = y.copy(qty = SkuQuantity.fromLong(y.qty.getValue().toLong() / 2))

        val testCase = TestCase(
            "Today's staging is greater than yesterday and there is leftover",
            listOf(x, y),
            listOf(x, y, a),
            listOf(halfY, x),
            listOf(x, y),
            listOf(halfY),
        )

        val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId))
        val yesterdayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId).minusDays(1))
        val dayBeforeYesterdayKey =
            CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId).minusDays(2))
        val dcConfig =
            mapOf(
                DEFAULT_DC_CODE to (defaultCalculatorData.dcConfig[DEFAULT_DC_CODE]!!.copy(cleardown = todayKey.date.plusDays(1).dayOfWeek)),
            )
        val input = defaultCalculatorData.copy(
            inventory = inventorySnapshots(
                dcConfig,
                listOf(
                    inventorySnapshot(
                        dayBeforeYesterdayKey,
                        testCase.yesterdayStaging.map { Inventory.fromStaging(it) },
                    ),
                    inventorySnapshot(yesterdayKey, testCase.todayStaging.map { Inventory.fromStaging(it) }),
                    inventorySnapshot(todayKey, emptyList()),
                ),
            ),
            dcConfig = dcConfig,
            demands = Demands(
                listOf(
                    Demand(
                        skuId = yesterdayKey.cskuId,
                        dcCode = yesterdayKey.dcCode,
                        date = yesterdayKey.date,
                        forecastedQty = SkuQuantity.fromLong(
                            testCase.demandYesterday.sumOf { it.qty.getValue().toLong() },
                        ),
                    ),
                ),
            ),
        )

        val calculations = performTestCalculations(input, -1L..0L)

        val todayCalculated = calculations.filterKeys { it == todayKey }
        val yesterdayCalculated = calculations.filterKeys { it == yesterdayKey }
        assertEquals(1, todayCalculated.size)
        assertEquals(
            testCase.expectedLeftover.sumOf {
                it.qty.getValue().toLong()
            },
            yesterdayCalculated.values.first().openingStock.getValue().toLong(),
            "Check past day leftover -> ${testCase.name}",
        )
        assertEquals(
            testCase.expectedStaging.sumOf { it.qty.getValue().toLong() },
            todayCalculated.values.first().openingStagingStock.getValue().toLong(),
            "Check openingStagingStock -> ${testCase.name}",
        )
        assertEquals(
            testCase.expectedStaging.sumOf {
                it.qty.getValue().toLong()
            },
            todayCalculated.values.first().stagingStock.getValue().toLong(),
            "Check stagingStock -> ${testCase.name}",
        )
    }

    @Test fun `POs should go into storage stock then the demand is deducted from it if staging was fully consumed`() {
        val poQty = SkuQuantity.fromLong(500L)
        val yesterdayStaging = listOf(x)
        val demandToday = listOf(a)

        val yesterdayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId).minusDays(1))
        val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId))
        val dayBeforeYesterdayKey =
            CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId).minusDays(2))

        val poDeliveryTimeslot =
            TimeRange(todayKey.date.atStartOfDay(UTC), todayKey.date.atStartOfDay(UTC).plusMinutes(1))
        val dcConfig =
            mapOf(
                DEFAULT_DC_CODE to (defaultCalculatorData.dcConfig[DEFAULT_DC_CODE]!!.copy(cleardown = todayKey.date.plusDays(1).dayOfWeek)),
            )
        val input = defaultCalculatorData.copy(
            dcConfig = dcConfig,
            inventory = inventorySnapshots(
                dcConfig,
                listOf(
                    inventorySnapshot(
                        dayBeforeYesterdayKey,
                        yesterdayStaging.map { Inventory.fromStaging(it) },
                    ),
                    inventorySnapshot(yesterdayKey, yesterdayStaging.map { Inventory.fromStaging(it) }),
                ),
            ),
            purchaseOrderInbounds = PurchaseOrderInbounds(
                listOf(
                    PurchaseOrder(
                        "PO",
                        "POREF",
                        null,
                        todayKey.dcCode,
                        poDeliveryTimeslot,
                        null,
                        listOf(
                            PurchaseOrderSku(todayKey.cskuId, poQty, emptyList()),
                        ),
                        poStatus = APPROVED,
                    ),
                ),
            ),
            demands = getDemands(todayKey, demandToday),
        )

        val calculations = performTestCalculations(input, -1L..1L)

        val tomorrowCalculation = calculations[todayKey.nextDay()]
        assertEquals(zeroSkuQuantity, tomorrowCalculation?.stagingStock, "Check if staging was consumed first")
        assertEquals(
            (poQty + (x.qty - a.qty)),
            tomorrowCalculation?.storageStock,
            "Check if PO was added to storage and consumption served",
        )
    }

    @Test fun `closingStagingInventory should have today's demand deducted without affecting stagingInventory and future staging`() {
        val yesterdayStaging = listOf(a, b, y)
        val demandToday = listOf(a)
        val demandYesterday = listOf(b)

        val yesterdayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId).minusDays(1))
        val dayBeforeYesterdayKey =
            CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId).minusDays(2))
        val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId))

        val dcConfig = mapOf(
            DEFAULT_DC_CODE to DistributionCenterConfiguration.default(DEFAULT_DC_CODE).copy(
                hasCleardown = true,
                cleardown = todayKey.date.plusDays(1).dayOfWeek,
            ),
        )
        val inputWithNoCleardown = calculatorData(true, mapOf(DEFAULT_UUID to defaultSku)).copy(
            dcConfig = dcConfig,
            inventory = inventorySnapshots(
                dcConfig,
                listOf(
                    inventorySnapshot(
                        dayBeforeYesterdayKey,
                        yesterdayStaging.map { Inventory.fromStaging(it) },
                    ),
                ),
            ),
            demands = Demands(
                listOf(
                    Demand(
                        skuId = yesterdayKey.cskuId,
                        dcCode = yesterdayKey.dcCode,
                        date = yesterdayKey.date,
                        forecastedQty = SkuQuantity.fromBigDecimal(demandYesterday.sumOf { it.qty.getValue() }),
                    ),
                    Demand(
                        skuId = todayKey.cskuId,
                        dcCode = todayKey.dcCode,
                        date = todayKey.date,
                        forecastedQty = SkuQuantity.fromBigDecimal(demandToday.sumOf { it.qty.getValue() }),
                    ),
                ),
            ),
        )

        val calculations = performTestCalculations(inputWithNoCleardown, -1L..1L)

        assertEquals(y.qty + a.qty, calculations[todayKey]?.openingStock)
        assertEquals(y.qty + a.qty, calculations[todayKey]?.openingStagingStock)
        assertEquals(y.qty, calculations[todayKey]?.stagingStock)
        val tomorrowCalc = calculations[todayKey.nextDay()]
        assertEquals(y.qty, tomorrowCalc?.stagingStock)

        // If cleardown Happens today then closing staging for tomorrow will not have yesterday's demand
        val dcConfigToday = mapOf(
            DEFAULT_DC_CODE to DistributionCenterConfiguration.default(DEFAULT_DC_CODE).copy(
                hasCleardown = true,
                cleardown = LocalDate.now(defaultZoneId).dayOfWeek,
            ),
        )
        val inputCleardown = inputWithNoCleardown.copy(
            dcConfig = dcConfigToday,
            inventory = inventorySnapshots(
                dcConfigToday,
                listOf(
                    inventorySnapshot(
                        yesterdayKey,
                        yesterdayStaging.map {
                            Inventory.fromStaging(it)
                        },
                    ),
                ),
            ),
        )
        val calculationsCleardown = performTestCalculations(inputCleardown, 0L..1L)
        assertEquals(
            y.qty + b.qty,
            calculationsCleardown[todayKey.nextDay()]?.stagingStock,
        )
    }

    @Test fun `future staging stock tests`() {
        data class TestCase(
            val name: String,
            val yesterdayStaging: List<CalculationInventory>,
            val todayStaging: List<CalculationInventory>, // this should be ignored
            val demandToday: List<CalculationInventory>,
            val expectedTodayStaging: List<CalculationInventory>,
            val expectedTomorrowStaging: List<CalculationInventory>,
        )

        val testCases = listOf(
            TestCase(
                "Simple future prediction no demand or new staging today or Demand ",
                listOf(x, y),
                emptyList(),
                emptyList(),
                listOf(x, y),
                listOf(x, y),
            ),
            TestCase(
                "Today's Demand should be removed from tomorrow's staging",
                listOf(x, y),
                emptyList(),
                listOf(y),
                listOf(x, y),
                listOf(x),
            ),
            TestCase(
                "Staging stock should not go lower than zero even if consumption is greater",
                listOf(y),
                emptyList(),
                listOf(z),
                listOf(y),
                listOf(zero),
            ),
            TestCase(
                "Today's demand should served tomorrow. And today staging is yesterday closing stock",
                listOf(x, y),
                listOf(y, a),
                listOf(y),
                listOf(x, y),
                listOf(x),
            ),
            TestCase(
                "If something is removed from today's staging the calculation should still work",
                listOf(y),
                listOf(z),
                listOf(y),
                listOf(y),
                listOf(),
            ),
        )

        testCases.forEach { (name, yesterdayStaging, todayStaging, demandToday, expectedTodayStaging, expectedTomorrowStaging) ->

            val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId))
            val yesterdayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId).minusDays(1))
            val dayBeforeYesterdayKey =
                CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId).minusDays(2))

            val dcConfig = mapOf(
                DEFAULT_DC_CODE to DistributionCenterConfiguration.default(DEFAULT_DC_CODE).copy(
                    hasCleardown = true,
                    cleardown = LocalDate.now(defaultZoneId).plusDays(1).dayOfWeek,
                ),
            )
            val input = defaultCalculatorData.copy(
                inventory = inventorySnapshots(
                    dcConfig,
                    listOf(
                        inventorySnapshot(
                            dayBeforeYesterdayKey,
                            yesterdayStaging.map { Inventory.fromStaging(it) },
                        ),
                        inventorySnapshot(yesterdayKey, todayStaging.map { Inventory.fromStaging(it) }),
                    ),
                ),
                dcConfig = dcConfig,
                demands = getDemands(todayKey, demandToday),
            )

            val calculations = performTestCalculations(input, -1L..1L)

            val todayCalculation = calculations[todayKey]
            assertEquals(
                expectedTodayStaging.sumOf { it.qty.getValue() }.toLong(),
                todayCalculation?.openingStock?.getValue()?.toLong(),
                "$name -> Check today's opening ",
            )
            assertEquals(
                expectedTodayStaging.sumOf {
                    it.qty.getValue()
                }.toLong(),
                todayCalculation?.openingStagingStock?.getValue()?.toLong(),
                "$name -> Check today's staging ",
            )
            assertEquals(
                (
                    yesterdayStaging.sumOf {
                        it.qty.getValue()
                    }.toLong() - demandToday.sumOf { it.qty.getValue() }.toLong()
                    ).coerceAtLeast(
                    0,
                ),
                todayCalculation?.stagingInventory?.sumOf { it.qty.getValue() }?.toLong(),
                "$name -> Check today's closing staging ",
            )
            val tomorrowCalculation = calculations.values.last()
            assertEquals(
                expectedTomorrowStaging.sumOf {
                    it.qty.getValue()
                }.toLong(),
                tomorrowCalculation.stagingStock.getValue().toLong(),
                "$name -> Check tomorrow's staging",
            )
        }
    }

    @Test fun `future & today storage needs to be calculated`() {
        data class TestCase(
            val name: String,
            val yesterdayStorage: List<CalculationInventory>,
            val todayStorage: List<CalculationInventory>,
            val demandToday: List<CalculationInventory>,
            val posToday: Set<Long>,
            val expectedTodayStorage: List<CalculationInventory>,
            val expectedTomorrowStorage: List<CalculationInventory>,
        )

        val poOfZ = z.qty
        val poOfA = a.qty

        // More test cases will be added
        val testCases = listOf(
            TestCase(
                "Simple future prediction; no POS or Demand ",
                listOf(xStorage, yStorage),
                listOf(xStorage, yStorage),
                emptyList(),
                emptySet(),
                listOf(xStorage, yStorage),
                listOf(xStorage, yStorage),
            ),
            TestCase(
                "Today's storageStock Should be considered in today's Storage",
                listOf(xStorage, a.toStorage()),
                listOf(yStorage),
                emptyList(),
                emptySet(),
                listOf(yStorage),
                listOf(yStorage),
            ),
            TestCase(
                "Today's Demand Should be removed from tomorrow's Storage if staging is zero",
                listOf(xStorage, yStorage),
                listOf(xStorage, yStorage),
                listOf(yStorage),
                emptySet(),
                listOf(xStorage, yStorage),
                listOf(xStorage),
            ),
            TestCase(
                "Incoming PO for today should be added to tomorrow's Storage stock",
                emptyList(),
                emptyList(),
                emptyList(),
                setOf(poOfA.getValue().toLong(), poOfZ.getValue().toLong()),
                emptyList(),
                listOf(a, z),
            ),
            TestCase(
                "Today's Storage and today's PO will compose tomorrow's storage stock",
                listOf(xStorage, yStorage),
                listOf(xStorage, yStorage),
                emptyList(),
                setOf(poOfA.getValue().toLong()),
                listOf(xStorage, yStorage),
                listOf(xStorage, yStorage, a),
            ),
            TestCase(
                "Tomorrows storage stock should use today's PO",
                listOf(xStorage, yStorage),
                listOf(xStorage, yStorage),
                emptyList(),
                setOf(poOfA.getValue().toLong()),
                listOf(xStorage, yStorage),
                listOf(xStorage, yStorage, a),
            ),
        )

        testCases.forEach { (name, yesterdayStorage, todayStorage, demandToday, POsToday, expectedTodayStorage, expectedTomorrowStorage) ->

            val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId))
            val poDeliveryTimeslotToday =
                TimeRange(todayKey.date.atStartOfDay(UTC), todayKey.date.atStartOfDay(UTC).plusMinutes(1))
            val yesterdayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId).minusDays(1))

            val dcConfig = mapOf(
                DEFAULT_DC_CODE to DistributionCenterConfiguration.default(DEFAULT_DC_CODE).copy(
                    hasCleardown = true,
                    cleardown = LocalDate.now(defaultZoneId).plusDays(1).dayOfWeek,
                ),
            )
            val input = defaultCalculatorData.copy(
                inventory = inventorySnapshots(
                    dcConfig,
                    listOf(
                        inventorySnapshot(yesterdayKey, yesterdayStorage.map { Inventory.fromStorage(it) }),
                        inventorySnapshot(todayKey, todayStorage.map { Inventory.fromStorage(it) }),
                    ),
                ),
                purchaseOrderInbounds = getPurchaseOrderInbounds(POsToday, todayKey, poDeliveryTimeslotToday),
                dcConfig = dcConfig,
                demands = getDemands(todayKey, demandToday),
            )

            val calculations = performTestCalculations(input, -1L..2L)

            val todayCalculation = calculations[todayKey]!!
            assertEquals(
                expectedTodayStorage.sumOf { it.qty.getValue() }.toLong(),
                todayCalculation.openingStock.getValue().toLong(),
                "$name -> Check today opening",
            )
            assertEquals(
                expectedTodayStorage.sumOf {
                    it.qty.getValue()
                }.toLong(),
                todayCalculation.openingStorageStock.getValue().toLong(),
                "$name -> Check today storage",
            )
            val tomorrowCalculation = calculations[todayKey.nextDay()]!!
            assertEquals(
                expectedTomorrowStorage.sumOf {
                    it.qty.getValue()
                }.toLong(),
                tomorrowCalculation.storageStock.getValue().toLong(),
                "$name -> Check tomorrow storage",
            )
            val dayAfterTomorrowCalculation = calculations[tomorrowCalculation.key.nextDay()]!!
            assertEquals(
                expectedTomorrowStorage.sumOf {
                    it.qty.getValue()
                }.toLong(),
                dayAfterTomorrowCalculation.storageStock.getValue().toLong(),
                "$name -> Check day after tomorrow storage",
            )
        }
    }

    @Test
    fun `opening and closing stock are correctly calculated from storage`() {
        data class TestCase(
            val name: String,
            val yesterdayStorage: List<CalculationInventory>,
            val todayStorage: List<CalculationInventory>,
            val posToday: Set<Long> = emptySet(),
            val expectedOpeningStock: Long,
            val expectedClosingStockStorage: List<CalculationInventory>,
            val isCleardown: Boolean,
        )

        val cases = listOf(true, false).map {
            TestCase(
                "no POs or demand in cleardown",
                yesterdayStorage = listOf(xStorage),
                todayStorage = listOf(xStorage),
                expectedOpeningStock = x.qty.getValue().toLong(),
                expectedClosingStockStorage = listOf(xStorage),
                isCleardown = it,
            )
        }

        // when today is cleardown
        cases.forEach {
            val dcConfig = mapOf(
                DEFAULT_DC_CODE to DistributionCenterConfiguration.default(DEFAULT_DC_CODE)
                    .copy(
                        cleardown = if (it.isCleardown) LocalDate.now(defaultZoneId).dayOfWeek else LocalDate.now(defaultZoneId).plusDays(1).dayOfWeek,
                    ),
            )

            val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId))
            val poDeliveryTimeslotToday =
                TimeRange(todayKey.date.atStartOfDay(UTC), todayKey.date.atStartOfDay(UTC).plusMinutes(1))
            val yesterdayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId).minusDays(1))

            val input = defaultCalculatorData.copy(
                dcConfig = dcConfig,
                inventory = inventorySnapshots(
                    dcConfig,
                    listOf(
                        inventorySnapshot(yesterdayKey, it.yesterdayStorage.map { Inventory.fromStorage(it) }),
                        inventorySnapshot(todayKey, it.todayStorage.map { Inventory.fromStorage(it) }),
                    ),
                ),
                purchaseOrderInbounds = PurchaseOrderInbounds(
                    it.posToday.map { poQty ->
                        PurchaseOrder(
                            "TESTPO_$it",
                            "TESTPO_$it",
                            null,
                            todayKey.dcCode,
                            poDeliveryTimeslotToday,
                            null,
                            listOf(
                                PurchaseOrderSku(todayKey.cskuId, SkuQuantity.fromLong(poQty), emptyList()),
                            ),
                            poStatus = APPROVED,
                        )
                    },
                ),
            )

            val calculations = performTestCalculations(input, -1L..0L)

            // rules regardless of cleardown
            // `live opening stock is equal to staging and storage`
            assertEquals(it.expectedOpeningStock, calculations[todayKey]?.openingStock?.getValue()?.toLong())
            assertEquals(it.expectedOpeningStock, calculations[todayKey]?.openingStorageStock?.getValue()?.toLong())
            // `is calculated for today` then `initialize closing storage stock with yesterdays closing storage inventory stock`
            assertEquals(it.yesterdayStorage, calculations[todayKey]?.storageInventory)
            // !`is calculated for today` then `initialize closing storage stock with the storage inventory stock`
            assertEquals(it.expectedClosingStockStorage, calculations[todayKey]?.storageInventory)
        }
    }

    @Test
    fun `opening and closing stock are correctly calculated from staging`() {
        data class TestCase(
            val name: String,
            val yesterdayStaging: List<CalculationInventory>,
            val todayStaging: List<CalculationInventory>,
            val posToday: Set<Long> = emptySet(),
            val isCleardown: Boolean,
        )

        val cases = listOf(true, false).map {
            TestCase(
                "no POs or demand",
                yesterdayStaging = listOf(x),
                todayStaging = listOf(y),
                isCleardown = it,
            )
        }

        // when today is cleardown
        cases.forEach {
            val dcConfig = mapOf(
                DEFAULT_DC_CODE to DistributionCenterConfiguration.default(DEFAULT_DC_CODE)
                    .copy(
                        cleardown = if (it.isCleardown) LocalDate.now(defaultZoneId).dayOfWeek else LocalDate.now(defaultZoneId).plusDays(1).dayOfWeek,
                    ),
            )

            val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId))
            val poDeliveryTimeslotToday =
                TimeRange(todayKey.date.atStartOfDay(UTC), todayKey.date.atStartOfDay(UTC).plusMinutes(1))
            val yesterdayKey = todayKey.previousDay()

            val input = defaultCalculatorData.copy(
                dcConfig = dcConfig,
                inventory = inventorySnapshots(
                    dcConfig,
                    listOf(
                        inventorySnapshot(
                            yesterdayKey.previousDay(),
                            it.yesterdayStaging.map { Inventory.fromStaging(it) },
                        ),
                        inventorySnapshot(yesterdayKey, it.todayStaging.map { Inventory.fromStaging(it) }),
                    ),
                ),
                purchaseOrderInbounds = PurchaseOrderInbounds(
                    it.posToday.map { poQty ->
                        PurchaseOrder(
                            "TESTPO_$it",
                            "TESTPOZ_$it",
                            null,
                            todayKey.dcCode,
                            poDeliveryTimeslotToday,
                            null,
                            listOf(
                                PurchaseOrderSku(todayKey.cskuId, SkuQuantity.fromLong(poQty), emptyList()),
                            ),
                            poStatus = APPROVED,
                        )
                    },
                ),
            )

            val calculations = performTestCalculations(input, -1L..0L)

            // `live opening stock is equal to staging and storage`
            if (it.isCleardown) {
                val expectedOpeningCleardown = it.todayStaging.sumOf { i -> i.qty.getValue() }.toLong()
                assertEquals(expectedOpeningCleardown, calculations[todayKey]?.openingStock?.getValue()?.toLong())
                assertEquals(
                    expectedOpeningCleardown,
                    calculations[todayKey]?.openingStagingStock?.getValue()?.toLong(),
                )
                assertEquals(expectedOpeningCleardown, calculations[todayKey]?.stagingStock?.getValue()?.toLong())
                assertEquals(it.todayStaging.toSet(), calculations[todayKey]?.stagingInventory?.toSet())
            } else {
                val expectedOpening = it.yesterdayStaging.sumOf { i -> i.qty.getValue() }.toLong()
                assertEquals(expectedOpening, calculations[todayKey]?.openingStock?.getValue()?.toLong())
                assertEquals(expectedOpening, calculations[todayKey]?.openingStagingStock?.getValue()?.toLong())
                assertEquals(expectedOpening, calculations[todayKey]?.stagingStock?.getValue()?.toLong())
                assertEquals(it.yesterdayStaging.toSet(), calculations[todayKey]?.stagingInventory?.toSet())

                // !is Cleardown and `is calculated for today`
                // `initialize closing staging stock with yesterday's staging inventory stock`
                assertEquals(calculations[todayKey]?.stagingInventory, calculations[yesterdayKey]?.stagingInventory)
            }
        }
    }

    @Test
    fun `today live opening stock is equal to staging and storage if it is cleardown day`() {
        val storageStock = BigDecimal(3L)
        val stagingStock = BigDecimal(4L)
        val expectedOpeningStock = SkuQuantity.fromLong(7L)
        val dcConfig = mapOf(
            DEFAULT_DC_CODE to DistributionCenterConfiguration.default(DEFAULT_DC_CODE)
                .copy(cleardown = LocalDate.now(defaultZoneId).dayOfWeek),
        )

        val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId))
        val input = defaultCalculatorData.copy(
            dcConfig = dcConfig,
            inventory = inventorySnapshots(
                dcConfig,
                listOf(
                    inventorySnapshot(
                        todayKey.previousDay(),
                        listOf(
                            Inventory(
                                SkuQuantity.fromBigDecimal(storageStock),
                                expiryDate = null,
                                location = Location("", LOCATION_TYPE_STORAGE, null),
                            ),
                            Inventory(
                                SkuQuantity.fromBigDecimal(stagingStock),
                                expiryDate = null,
                                location = Location("", LOCATION_TYPE_STAGING, null),
                            ),
                        ),
                    ),
                ),
            ),
        )

        val calculations = performTestCalculations(input, -1L..0L)
        // `live opening stock is equal to staging and storage`
        assertEquals(expectedOpeningStock, calculations[todayKey]?.openingStock)
        assertEquals(stagingStock.toLong(), calculations[todayKey]?.openingStagingStock?.getValue()?.toLong())
        assertEquals(storageStock.toLong(), calculations[todayKey]?.openingStorageStock?.getValue()?.toLong())
    }

    @Test fun `Actual inbound needs to be present on live calculations output`() {
        val actualInbound = SkuQuantity.fromLong(234L)
        val expectedActualInboundQty = SkuQuantity.fromLong(0L)
        val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, today)
        val purchaseOrders = createPurchaseOrders(today, null, actualInbound)
        val input = defaultCalculatorData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
        )

        val calculations = performTestCalculations(input, -1L..2L)
        assertEquals(actualInbound, calculations[todayKey]?.actualInboundQty)
        assertEquals(purchaseOrders.first().number, calculations[todayKey]?.actualInboundPos?.first())
        assertEquals(expectedActualInboundQty, calculations[todayKey.nextDay()]?.actualInboundQty)
    }

    @Test
    fun `On the day prior to cleardown the live closing stock should be maxof(0, (Staging Stock - Consumption Same Day)) + Storage Stock)`() {
        val cleardownDate = latestCleardownDate()
        val dayBeforeCleardown = cleardownDate.minusDays(1)
        val twoDaysBeforeCleardown = cleardownDate.minusDays(2)
        val dayBeforeCleardownStagingStock = SkuQuantity.fromBigDecimal(BigDecimal(12L))
        val dayBeforeCleardownStorageStock = 13L
        val dayBeforeCleardownDemand = SkuQuantity.fromLong(15L)
        mockkStatic(LocalDate::class)

        val dayBeforeCleardownCalculationKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, dayBeforeCleardown)
        val twoDaysBeforeCleardownCalculationKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, twoDaysBeforeCleardown)
        val inputDayBeforeCleardown = defaultCalculatorData.copy(
            inventory = inventorySnapshots(
                defaultCalculatorData.dcConfig,
                listOf(
                    inventorySnapshot(
                        twoDaysBeforeCleardownCalculationKey,
                        listOf(
                            Inventory(
                                qty = SkuQuantity.fromLong(dayBeforeCleardownStorageStock),
                                expiryDate = null,
                                location = Location("", LOCATION_TYPE_STORAGE, null),
                            ),
                            Inventory(
                                qty = dayBeforeCleardownStagingStock,
                                expiryDate = null,
                                location = Location("", LOCATION_TYPE_STAGING, null),
                            ),
                        ),
                    ),
                    inventorySnapshot(
                        dayBeforeCleardownCalculationKey,
                        emptyList(),
                    ),
                ),
            ),
            dcConfig = mapOf(
                DEFAULT_DC_CODE to DistributionCenterConfiguration.default(DEFAULT_DC_CODE).copy(
                    hasCleardown = true,
                    cleardown = cleardownDate.dayOfWeek,
                ),
            ),
            demands = Demands(
                listOf(
                    Demand(
                        skuId = dayBeforeCleardownCalculationKey.cskuId,
                        dcCode = dayBeforeCleardownCalculationKey.dcCode,
                        date = dayBeforeCleardownCalculationKey.date,
                        forecastedQty = dayBeforeCleardownDemand,
                    ),
                ),
            ),
        )

        val calculationStartDayIndex = Period.between(LocalDate.now(defaultZoneId), cleardownDate).days.toLong() - 1
        val calculations =
            performTestCalculations(inputDayBeforeCleardown, calculationStartDayIndex..calculationStartDayIndex)

        assertEquals(
            max(0, dayBeforeCleardownStagingStock.getValue().toLong() - dayBeforeCleardownDemand.getValue().toLong()) + dayBeforeCleardownStorageStock,
            calculations[dayBeforeCleardownCalculationKey]?.closingStock?.getValue()?.toLong(),
        )
    }

    @Test
    fun `any inbound becomes closing storage inventory`() {
        fun calculate(todayKey: CalculationKey): MutableMap<CalculationKey, DayCalculation> {
            val purchaseOrders = createPurchaseOrders(todayKey.date, SkuQuantity.fromLong(50))
            val input = defaultCalculatorData.copy(
                purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
            )
            return performTestCalculations(input, 0..0L)
        }

        val today = LocalDate.now(defaultZoneId)
        val before2330 = today.atTime(11, 0)
        val after2330 = today.atTime(23, 31)

        mockkStatic(LocalDate::class)
        mockkStatic(LocalDateTime::class)
        every { LocalDate.now(any<ZoneId>()) } returns today
        val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId))

        every { LocalDateTime.now(UTC) } returns before2330.atZone(UTC).toLocalDateTime()
        var calculations = calculate(todayKey)
        assertEquals(50, calculations[todayKey]?.storageInventory?.sumOf { it.qty.getValue() }?.toLong())

        every { LocalDateTime.now(UTC) } returns after2330.atZone(UTC).toLocalDateTime()
        calculations = calculate(todayKey)
        assertEquals(0, calculations[todayKey]?.storageInventory?.sumOf { it.qty.getValue() }?.toLong())
    }

    @Test
    fun `expired inbounds are not included in the closing storage inventory`() {
        fun calculate(todayKey: CalculationKey): MutableMap<CalculationKey, DayCalculation> {
            val purchaseOrders = createPurchaseOrders(
                todayKey.date,
                SkuQuantity.fromLong(50),
                SkuQuantity.fromLong(50),
                LocalDate.now(),
            )
            val input = defaultCalculatorData.copy(
                purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
            )
            return performTestCalculations(input, 0..0L)
        }

        val today = LocalDate.now(defaultZoneId)
        val before2330 = today.atTime(11, 0)

        mockkStatic(LocalDate::class)
        mockkStatic(ZonedDateTime::class)
        every { LocalDate.now(any<ZoneId>()) } returns today
        val todayKey = CalculationKey(DEFAULT_UUID, DEFAULT_DC_CODE, LocalDate.now(defaultZoneId))

        every { ZonedDateTime.now(UTC) } returns before2330.atZone(UTC)
        val calculations = calculate(todayKey)
        assertEquals(0, calculations[todayKey]?.storageInventory?.sumOf { it.qty.getValue() }?.toLong())
    }

    @ParameterizedTest
    @CsvSource("true, 201", "false, 0")
    fun `consider yesterday's storage & staging unusable inventory for today`(
        todayCleardown: Boolean,
        expectedQty: Long
    ) {
        val yesterdayUnusableStorageQty = BigDecimal(100L)
        val yesterdayUnusableStagingQty = BigDecimal(101L)
        val today = LocalDate.now(defaultZoneId)
        val cskuId = DEFAULT_UUID
        val calcKeyDayYesterday = CalculationKey(cskuId, DEFAULT_DC_CODE, today.minusDays(1))
        val calcKeyToday = CalculationKey(cskuId, DEFAULT_DC_CODE, today)
        val dcConfig =
            mapOf(
                DEFAULT_DC_CODE to DistributionCenterConfiguration.default(DEFAULT_DC_CODE)
                    .copy(cleardown = if (todayCleardown) today.dayOfWeek else today.plusDays(1).dayOfWeek),
            )
        val inputData = defaultCalculatorData.copy(
            dcConfig = dcConfig,
            inventory = inventorySnapshots(
                dcConfig,
                if (todayCleardown) { // Inventory at cleardown, yesterday snapshot by default
                    listOf(
                        inventorySnapshot(
                            calcKeyDayYesterday,
                            listOf(
                                Inventory(
                                    qty = SkuQuantity.fromBigDecimal(yesterdayUnusableStorageQty),
                                    expiryDate = null, location = Location("", LOCATION_TYPE_QUARANTINE, null),
                                ),
                                Inventory(
                                    qty = SkuQuantity.fromBigDecimal(yesterdayUnusableStagingQty),
                                    expiryDate = LocalDate.now().minusWeeks(1),
                                    location = Location("", LOCATION_TYPE_STAGING, null),
                                ),
                            ),
                        ),
                    )
                } else { // storage from yesterday + closing staging from yesterday
                    listOf(
                        inventorySnapshot(
                            calcKeyDayYesterday.previousDay(),
                            Inventory(
                                qty = SkuQuantity.fromBigDecimal(yesterdayUnusableStorageQty),
                                expiryDate = null, location = Location("", LOCATION_TYPE_QUARANTINE, null),
                            ),
                        ),
                        inventorySnapshot(
                            calcKeyDayYesterday,
                            Inventory(
                                qty = SkuQuantity.fromBigDecimal(yesterdayUnusableStorageQty),
                                expiryDate = null, location = Location("", LOCATION_TYPE_QUARANTINE, null),
                            ),
                        ),
                    )
                } + listOf(
                    inventorySnapshot(
                        calcKeyToday,
                        Inventory(
                            qty = SkuQuantity.fromBigDecimal(BigDecimal.ZERO),
                            expiryDate = null, location = Location("", LOCATION_TYPE_QUARANTINE, null),
                        ),
                    ),
                ),
            ),
        )

        // when
        val calculations = performTestCalculations(inputData, -1L..0)

        // then
        assertEquals(expectedQty, calculations[calcKeyToday]?.unusable?.getValue()?.toLong())
    }

    private fun latestCleardownDate() =
        DistributionCenterConfiguration.default().let {
            if (it.isLatestCleardown(LocalDate.now(defaultZoneId))) {
                LocalDate.now(defaultZoneId)
            } else {
                it.getLatestCleardown()
            }
        }

    private fun performTestCalculations(data: CalculationData, range: LongRange): MutableMap<CalculationKey, DayCalculation> {
        val rules = selectCalculationsRules(data)
        val keys = (range).map { calculationKey(LocalDate.now(defaultZoneId).plusDays(it)) }
        val calculations = keys.associateWith { DayCalculation(it, defaultSku) }.toMutableMap()
        keys.forEach {
            val calculated = CalculationStep(calculations, calculations[it]!!).apply(rules)
            calculations[it] = calculated.dayCalculation
        }
        return calculations
    }

    private fun getDemands(
        todayKey: CalculationKey,
        demandToday: List<CalculationInventory>
    ) = Demands(
        listOf(
            Demand(
                skuId = todayKey.cskuId,
                dcCode = todayKey.dcCode,
                date = todayKey.date,
                forecastedQty = SkuQuantity.fromBigDecimal(demandToday.sumOf { it.qty.getValue() }),
            ),
        ),
    )

    companion object {
        private val x = CalculationInventory.random().copy(
            qty = SkuQuantity.fromLong(100),
            locationType = LOCATION_TYPE_STAGING,
            expiryDate = LocalDate.now().plusDays(10),
            location = Location(
                id = "",
                type = LOCATION_TYPE_STAGING,
                transportModuleId = null,
            ),
        )
        private val y = CalculationInventory.random().copy(
            qty = SkuQuantity.fromLong(200),
            locationType = LOCATION_TYPE_STAGING,
            expiryDate = LocalDate.now().plusDays(11),
            location = Location(
                id = "",
                type = LOCATION_TYPE_STAGING,
                transportModuleId = null,
            ),
        )
        private val z = CalculationInventory.random().copy(
            qty = SkuQuantity.fromLong(250),
            expiryDate = LocalDate.now().plusDays(12),
            locationType = LOCATION_TYPE_STAGING,
        )
        private val a = CalculationInventory.random().copy(
            qty = SkuQuantity.fromLong(145),
            expiryDate = LocalDate.now().plusDays(13),
            locationType = LOCATION_TYPE_STAGING,
        )
        private val b = CalculationInventory.random().copy(
            qty = SkuQuantity.fromLong(50),
            expiryDate = LocalDate.now().plusDays(14),
            locationType = LOCATION_TYPE_STAGING,
        )
        private val zero = CalculationInventory.random().copy(
            qty = SkuQuantity.fromLong(0),
            locationType = LOCATION_TYPE_STAGING,
        )

        private val zeroSkuQuantity = SkuQuantity.fromLong(0L)

        private val xStorage = x.toStorage()
        private val yStorage = y.toStorage()

        private fun CalculationInventory.toStorage() = this.copy(
            locationType = LOCATION_TYPE_STORAGE,
            location =
            Location(
                id = "",
                type = LOCATION_TYPE_STORAGE,
                transportModuleId = null,
            ),
        )

        private fun Inventory.toStorage() = this.copy(location = location.copy(type = LOCATION_TYPE_STORAGE))
    }
}
