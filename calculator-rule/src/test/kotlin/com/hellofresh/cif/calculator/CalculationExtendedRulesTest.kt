package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.DEFAULT_DC_CODE
import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.createInventory
import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.inventorySnapshot
import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.inventorySnapshots
import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.liveInventorySnapshot
import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.statsigFeatureFlagClient
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode.PRE_PRODUCTION
import com.hellofresh.cif.calculator.models.CalculatorMode.PRODUCTION
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.calculator.models.InputData
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.EuBerlinZoneId
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.cif.models.purchaseorder.DeliveryInfo
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.TimeRange
import com.hellofresh.cif.transferorder.model.TransferOrderInbounds
import com.hellofresh.cif.transferorder.model.TransferOrderOutbounds
import com.hellofresh.demand.models.ActualConsumption
import com.hellofresh.demand.models.Demand
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import com.hellofresh.inventory.models.inventory.live.SkuLiveInventory
import com.hellofresh.sku.models.DEFAULT_MAX_DAYS_BEFORE_EXPIRY
import com.hellofresh.sku.models.SkuSpecification
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.time.ZonedDateTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.math.absoluteValue
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.MethodSource

class CalculationExtendedRulesTest {
    private val calculatorClient = CalculatorClient(
        statsigFeatureFlagClient,
    )
    private val cskuId = UUID.randomUUID()
    private val defaultSkuSpec = SkuSpecification("", "name", "pack", "SPI-", "", null, 0, "", null)
    private val dcCode = "VE"
    private val poNumber = "**********"
    private val poRef = "**********_23"
    private val distributionCenterConfiguration = DistributionCenterConfiguration.default(dcCode)
    private val dcConfigMap = mapOf(dcCode to distributionCenterConfiguration)
    private val inputData = InputData(
        PRODUCTION,
        setOf(SkuDcCandidate(cskuId, defaultSkuSpec, distributionCenterConfiguration)),
        inventorySnapshots(dcConfigMap, emptyList(), emptyList()),
        PurchaseOrderInbounds(emptyList()),
        transferOrderInbounds = TransferOrderInbounds(emptyList()),
        transferOrderOutbounds = TransferOrderOutbounds(emptyList()),
        demands = Demands(emptyList()),
        safetyStocks = emptyMap(),
        stockUpdates = emptyMap(),
        supplierSku = emptyMap(),
        preproductionCleardownDcs = emptySet(),
    )

    @Test
    fun `should be able to perform daily calculation for the given inputs`() {
        val expectedInboundQty = SkuQuantity.fromLong(1L)
        val actualInboundQty = SkuQuantity.fromLong(1L)
        val inventoryQty = SkuQuantity.fromBigDecimal(BigDecimal(2L))
        val demandQty = SkuQuantity.fromLong(99L)
        val date = LocalDate.now()
        val key = CalculationKey(cskuId, dcCode, date)
        val poDeliveryTime = TimeRange(date.atStartOfDay(UTC), date.atStartOfDay(UTC).plusMinutes(1))

        val inputData = inputData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(
                listOf(
                    PurchaseOrder(
                        poNumber,
                        poRef,
                        null,
                        dcCode,
                        poDeliveryTime,
                        null,
                        listOf(
                            PurchaseOrderSku(
                                key.cskuId,
                                expectedInboundQty,
                                listOf(
                                    DeliveryInfo(
                                        UUID.randomUUID().toString(),
                                        ZonedDateTime.of(
                                            date.atStartOfDay(),
                                            UTC,
                                        ),
                                        CLOSED,
                                        actualInboundQty,
                                    ),
                                ),
                            ),
                        ),
                        poStatus = APPROVED,
                    ),
                ),
            ),
            inventory = inventorySnapshots(
                inputData.dcConfig,
                InventorySnapshot(
                    key.dcCode,
                    UUID.randomUUID(),
                    key.date.atStartOfDay(),
                    listOf(
                        SkuInventory(
                            skuId = key.cskuId,
                            inventory = listOf(createInventory(inventoryQty, null)),
                        ),
                    ),
                ),
                LiveInventorySnapshot(
                    key.dcCode,
                    UUID.randomUUID(),
                    key.date.atStartOfDay(),
                    listOf(
                        SkuLiveInventory(
                            skuId = key.cskuId,
                            inventory = listOf(
                                Inventory(
                                    inventoryQty,
                                    null,
                                    Location(
                                        "location-id",
                                        LOCATION_TYPE_STAGING,
                                        "transport-module-id",
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
            demands = Demands(listOf(Demand(key.cskuId, key.dcCode, date.plusDays(1), demandQty))),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)
        val resultPreprod = calculatorClient.runDailyCalculations(inputData.copy(mode = PRE_PRODUCTION))

        val demanded = { out: List<DayCalculationResult>, targetDate: LocalDate ->
            out.first { it.date == targetDate }.demanded
        }
        assertEquals(99, demanded(resultProd, date.plusDays(1)).getValue().toLong())
        assertEquals(99, demanded(resultPreprod, date).getValue().toLong())
    }

    @ParameterizedTest
    @CsvSource(
        "100, 99,1,100",
        "-100, 99,-99,0",
    )
    fun `should be able to apply calculation experiment`(
        stockUpdate: BigDecimal,
        demanded: Long,
        closingStock: Long,
        openingStock: Long
    ) {
        // given
        val inventoryQty = SkuQuantity.fromBigDecimal(BigDecimal(2L))
        val demandQty = SkuQuantity.fromLong(99L)
        val date = LocalDate.now()
        val key = CalculationKey(cskuId, dcCode, date)
        val inputData = inputData.copy(
            inventory = inventorySnapshots(
                inputData.dcConfig,
                inventorySnapshot(key, createInventory(inventoryQty, null)),
                liveInventorySnapshot(
                    key,
                    Inventory(
                        inventoryQty,
                        null,
                        Location(
                            "location-id",
                            LOCATION_TYPE_STAGING,
                            "transport-module-id",
                        ),
                    ),
                ),
            ),
            stockUpdates = mapOf(key to SkuQuantity.fromBigDecimal(stockUpdate, UOM_UNIT)),
            demands = Demands(listOf(Demand(cskuId, dcCode, date.plusDays(1), demandQty))),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)

        val dayCalculationResult = { out: List<DayCalculationResult>, targetDate: LocalDate ->
            out.first { it.date == targetDate }
        }

        // then
        assertEquals(demanded, dayCalculationResult(resultProd, date.plusDays(1)).demanded.getValue().toLong())
        assertEquals(closingStock, dayCalculationResult(resultProd, date.plusDays(1)).closingStock.getValue().toLong())
        assertEquals(openingStock, dayCalculationResult(resultProd, date.plusDays(1)).openingStock.getValue().toLong())
    }

    @Test
    fun `do not sum inventory from different dcs from the same market`() {
        val inventoryQty = SkuQuantity.fromBigDecimal(BigDecimal(2L))
        val gr = "GR"
        val bv = "BV"
        val dcConfigGR = DistributionCenterConfiguration.default().copy(
            dcCode = gr,
        )
        val dcConfigBV = DistributionCenterConfiguration.default().copy(
            dcCode = bv,
        )

        val inventory = createInventory(inventoryQty, null)
        val date = dcConfigGR.getLatestCleardown().minusDays(1)
        val dcConfigMap = mapOf(gr to dcConfigGR, bv to dcConfigBV)
        val inputData = inputData.copy(
            skuDcCandidates = setOf(
                SkuDcCandidate(cskuId, defaultSkuSpec, dcConfigGR),
                SkuDcCandidate(cskuId, defaultSkuSpec, dcConfigBV),
            ),
            inventory = inventorySnapshots(
                dcConfigMap,
                listOf(
                    inventorySnapshot(
                        CalculationKey(
                            cskuId,
                            bv,
                            date,
                        ),
                        inventory,
                    ),
                    inventorySnapshot(
                        CalculationKey(
                            cskuId,
                            gr,
                            date,
                        ),
                        inventory,
                    ),
                ),
                emptyList(),
            ),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)

        resultProd.forEach {
            assertEquals(it.openingStock, inventoryQty)
        }
    }

    @Test
    fun `do not consider previous demand when starting a calculation`() {
        val demandQty1 = SkuQuantity.fromLong(19)
        val dayAfterCleardown = DistributionCenterConfiguration.default().getLatestCleardown().plusDays(1)
        val demandQty2 = SkuQuantity.fromLong(22)
        val date2 = dayAfterCleardown.minusDays(1)
        val demands = Demands(
            listOf(
                Demand(cskuId, dcCode, dayAfterCleardown, demandQty1),
                Demand(cskuId, dcCode, date2, demandQty2),
            ),
        )
        val inputData = inputData.copy(demands = demands)

        // when
        val result = calculatorClient.runDailyCalculations(inputData)

        assertEquals(demandQty2, result.first().demanded)
        assertEquals(demandQty2.getValue().toLong(), result.first().closingStock.getValue().toLong().absoluteValue)
        assertEquals(
            demandQty1.getValue().toLong(),
            result.drop(1).first().closingStock.getValue().toLong().absoluteValue,
        )
        result.drop(2).forEach { assertEquals(0L, it.closingStock.getValue().toLong()) }
    }

    @Test
    fun `do not use inventory values from after cleardown in calculation`() {
        val inventoryQtyBeforeCleardown = SkuQuantity.fromBigDecimal(BigDecimal(2L))
        val inventoryQty = SkuQuantity.fromBigDecimal(BigDecimal(20L))
        val dcConfig = DistributionCenterConfiguration.default()

        val inventoryMap = mutableMapOf(
            CalculationKey(cskuId, dcCode, dcConfig.getLatestCleardown().minusDays(1)).let {
                it to inventorySnapshot(it, createInventory(inventoryQtyBeforeCleardown, null))
            },
        )

        val input1 = inputData.copy(
            inventory = inventorySnapshots(inputData.dcConfig, inventoryMap.values.toList(), emptyList()),
        )

        // when
        val result1 = calculatorClient.runDailyCalculations(input1)

        // then
        result1.forEach {
            assertEquals(it.closingStock, inventoryQtyBeforeCleardown)
            assertEquals(it.present, inventoryQtyBeforeCleardown)
        }

        // givenn
        CalculationKey(cskuId, dcCode, dcConfig.getLatestCleardown().plusDays(2))
            .also {
                inventoryMap[it] = inventorySnapshot(it, createInventory(inventoryQty, null))
            }
        val input2 = inputData.copy(
            inventory = inventorySnapshots(inputData.dcConfig, inventoryMap.values.toList(), emptyList()),
        )

        // when
        val result2 = calculatorClient.runDailyCalculations(input2)

        // then
        assertEquals(result1, result2)
    }

    @Test
    fun `inventory is used only once`() {
        val cleardownDate = DistributionCenterConfiguration.default().getLatestCleardown()
        val cleardownDayDemand = SkuQuantity.fromLong(2)
        val dayAfterCleardownDemand = SkuQuantity.fromLong(1)

        val calculationKey = CalculationKey(cskuId, dcCode, cleardownDate)
        val inputData = inputData.copy(
            inventory = inventorySnapshots(
                inputData.dcConfig,
                inventorySnapshot(
                    calculationKey.copy(date = cleardownDate.minusDays(1)),
                    createInventory(SkuQuantity.fromBigDecimal(BigDecimal(2)), null),
                ),
                liveInventorySnapshot(
                    calculationKey.copy(date = cleardownDate.minusDays(1)),
                    Inventory(
                        SkuQuantity.fromBigDecimal(BigDecimal(2)),
                        null,
                        Location(
                            "location-id",
                            LOCATION_TYPE_STAGING,
                            "transport-module-id",
                        ),
                    ),
                ),
            ),
            demands = Demands(
                listOf(
                    Demand(calculationKey.cskuId, calculationKey.dcCode, calculationKey.date, cleardownDayDemand),
                    Demand(
                        calculationKey.cskuId,
                        calculationKey.dcCode,
                        calculationKey.date.plusDays(1),
                        dayAfterCleardownDemand,
                    ),
                ),
            ),
        )

        // when
        val result = calculatorClient.runDailyCalculations(inputData)

        val firstDayCalc = result.first()
        val secondDayCalc = result.drop(1).first()

        assertEquals(cleardownDayDemand, firstDayCalc.demanded)
        assertEquals(0, firstDayCalc.closingStock.getValue().toLong())

        assertEquals(dayAfterCleardownDemand, secondDayCalc.demanded)
        assertEquals(0 - dayAfterCleardownDemand.getValue().toLong(), secondDayCalc.closingStock.getValue().toLong())
    }

    @Test
    fun `ignore updates from demand from before cleardown`() {
        // given
        val inputData = inputData.copy(
            demands = Demands(
                listOf(
                    Demand(
                        cskuId,
                        dcCode,
                        DistributionCenterConfiguration.default().getLatestCleardown().minusDays(1),
                        SkuQuantity.fromLong(1),
                    ),
                ),
            ),
        )

        // when
        val result = calculatorClient.runDailyCalculations(inputData)

        // then
        result.forEach {
            assertEquals(0, it.demanded.getValue().toLong())
        }
    }

    @Test
    fun `correctly carry the previous day's demand or inventory having multiple purchase orders for the same cskuId`() {
        val dcConfig = DistributionCenterConfiguration.default()
        // we have to make sure there's enough days in the future to make InventoryForecastCalculator.inbound logic to select the selectedInbound
        val futureDate = dcConfig.getLatestCleardown().plusWeeks(1)
        val qty1 = SkuQuantity.fromLong(2L)
        val qty2 = SkuQuantity.fromLong(3L)

        val key = CalculationKey(cskuId, dcCode, futureDate)
        val poDeliveryTime = TimeRange(key.date.atStartOfDay(UTC), key.date.atStartOfDay(UTC).plusMinutes(1))
        val poDeliveryTimeNextDay = TimeRange(
            key.date.atStartOfDay(UTC).plusDays(1),
            key.date.atStartOfDay(UTC).plusDays(1).plusMinutes(1),
        )

        val inputData2 = inputData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(
                listOf(
                    PurchaseOrder(
                        "2109VE400461",
                        "2109VE400461_01",
                        null,
                        dcCode,
                        poDeliveryTime,
                        null,
                        listOf(
                            PurchaseOrderSku(key.cskuId, qty1, emptyList()),
                        ),
                        poStatus = APPROVED,
                    ),
                    PurchaseOrder(
                        "2109VE400461",
                        "2109VE400461_01",
                        null,
                        dcCode,
                        poDeliveryTimeNextDay,
                        null,
                        listOf(
                            PurchaseOrderSku(key.cskuId, qty2, emptyList()),
                        ),
                        poStatus = APPROVED,
                    ),
                ),
            ),
        )

        // when
        val result2 = calculatorClient.runDailyCalculations(inputData2)

        result2.filter { it.date >= futureDate.plusDays(1) }
            .forEach {
                assertEquals(qty1.plus(qty2).getValue().toLong(), it.closingStock.getValue().toLong())
            }
    }

    @Test fun `don't consider the expired inventory`() {
        val dcConfig = DistributionCenterConfiguration.default()
        val dateBeforeCleardown = dcConfig.getLatestCleardown().minusDays(1)
        val minExpiryDaysAhead = DEFAULT_MAX_DAYS_BEFORE_EXPIRY - 1

        val qty1 = SkuQuantity.fromBigDecimal(BigDecimal(10L))
        val expiredQty = SkuQuantity.fromBigDecimal(BigDecimal(100L))
        val notExpiredDate = dcConfig.getLatestCleardown().plusDays(minExpiryDaysAhead)
        val calculationKey = CalculationKey(cskuId, dcCode, dateBeforeCleardown)
        val inputData = inputData.copy(
            inventory = inventorySnapshots(
                inputData.dcConfig,
                listOf(
                    inventorySnapshot(calculationKey, createInventory(qty1, notExpiredDate)),
                    inventorySnapshot(
                        calculationKey.copy(date = calculationKey.date.minusDays(1)),
                        createInventory(expiredQty, notExpiredDate),
                    ),
                ),
                emptyList(),
            ),
        )

        // when
        val result = calculatorClient.runDailyCalculations(inputData)

        result.forEach {
            if (it.date.isBefore(notExpiredDate.minusDays(minExpiryDaysAhead))) {
                assertEquals(qty1, it.openingStock)
                assertEquals(qty1, it.closingStock)
                assertEquals(0, it.unusable.getValue().toLong())
            } else {
                assertEquals(0, it.openingStock.getValue().toLong())
                // there will be expired inventory on cleardown but ZERO_QTY subsequently. This doesn't influence calculations
                assertEquals(0, it.closingStock.getValue().toLong())
            }
        }
    }

    @Test
    fun `first use inventory that expires sooner`() {
        val inventoryQtyExpiryDateSooner = SkuQuantity.fromBigDecimal(BigDecimal(10L))
        val inventoryQtyExpiryDateLater = SkuQuantity.fromBigDecimal(BigDecimal(19L))
        val expectedUnusableStock = SkuQuantity.fromLong(18L)
        val expectedUnusable = SkuQuantity.fromLong(0L)
        val latestCleardown = DistributionCenterConfiguration.default().getLatestCleardown()
        val expiryDateSooner = latestCleardown.plusDays(DEFAULT_MAX_DAYS_BEFORE_EXPIRY)
        val expiryDateLater = latestCleardown.plusDays(DEFAULT_MAX_DAYS_BEFORE_EXPIRY + 1)
        val inventory = listOf(
            createInventory(inventoryQtyExpiryDateSooner, expiryDateSooner),
            createInventory(inventoryQtyExpiryDateLater, expiryDateLater),
        )

        val calculationKey = CalculationKey(cskuId, dcCode, latestCleardown)
        val inputData = inputData.copy(
            inventory = inventorySnapshots(
                inputData.dcConfig,
                inventorySnapshot(calculationKey.copy(date = latestCleardown.minusDays(1)), inventory),
                liveInventorySnapshot(calculationKey.copy(date = latestCleardown.minusDays(1)), listOf()),
            ),
            demands = Demands(listOf(Demand(cskuId, dcCode, latestCleardown, SkuQuantity.fromLong(11)))),
        )

        // when
        val result = calculatorClient.runDailyCalculations(inputData)

        // then
        assertEquals(expectedUnusable, result.first().unusable)
        assertEquals(expectedUnusable, result.drop(1).first().unusable)
        assertEquals(expectedUnusableStock, result.drop(2).first().unusable)
    }

    @Test
    fun `inventory that doesn't expire should be used last`() {
        val expectedUnusableDayOne = SkuQuantity.fromLong(0L)
        val expectedUnusableDayTwo = SkuQuantity.fromLong(0L)
        val expectedUnusableDayThree = SkuQuantity.fromLong(18L)
        val expectedUnusableDayFour = 20L
        val latestCleardown = DistributionCenterConfiguration.default().getLatestCleardown()
        val expiryDateSooner = latestCleardown.plusDays(DEFAULT_MAX_DAYS_BEFORE_EXPIRY)
        val expiryDateLater = latestCleardown.plusDays(DEFAULT_MAX_DAYS_BEFORE_EXPIRY + 1)
        val inventory = listOf(
            createInventory(SkuQuantity.fromBigDecimal(BigDecimal(10)), expiryDateSooner),
            createInventory(SkuQuantity.fromBigDecimal(BigDecimal(19)), expiryDateLater),
            createInventory(SkuQuantity.fromBigDecimal(BigDecimal(20)), null),
        )

        val calculationKey = CalculationKey(cskuId, dcCode, latestCleardown)
        val inputData = inputData.copy(
            inventory = inventorySnapshots(
                inputData.dcConfig,
                inventorySnapshot(
                    calculationKey.copy(date = latestCleardown.minusDays(1)),
                    inventory,
                ),
                liveInventorySnapshot(
                    calculationKey.copy(date = latestCleardown.minusDays(1)),
                    listOf(),
                ),
            ),
            demands = Demands(listOf(Demand(cskuId, dcCode, latestCleardown, SkuQuantity.fromLong(11)))),
        )

        // when
        val result = calculatorClient.runDailyCalculations(inputData)

        assertEquals(expectedUnusableDayOne, result.first().unusable)
        assertEquals(expectedUnusableDayTwo, result.drop(1).first().unusable)
        assertEquals(expectedUnusableDayThree, result.drop(2).first().unusable)
        assertEquals(expectedUnusableDayFour, result.drop(3).first().openingStock.getValue().toLong())
    }

    @Test
    fun `should use actual consumption for an assembly sku in IT before today and demand afterwards`() {
        // given
        val today = LocalDate.now()
        val sku = defaultSkuSpec.copy(packaging = "coolpouch")
        val dcCodeIT = "IT"

        val dcConfig = DistributionCenterConfiguration.default(dcCodeIT).copy(
            dcCode = dcCodeIT,
            cleardown = today.minusDays(3).dayOfWeek,
        )
        val latestCleardown = dcConfig.getLatestCleardown()

        val inventory = InventorySnapshot(
            dcCodeIT,
            UUID.randomUUID(),
            latestCleardown.minusDays(1).atStartOfDay(),
            listOf(
                SkuInventory(
                    skuId = cskuId,
                    inventory = listOf(createInventory(SkuQuantity.fromBigDecimal(BigDecimal(1000)), null)),
                ),
            ),
        )
        val liveInventory = LiveInventorySnapshot(
            dcCodeIT,
            UUID.randomUUID(),
            latestCleardown.minusDays(1).atStartOfDay(),
            listOf(
                SkuLiveInventory(
                    skuId = cskuId,
                    inventory = listOf(
                        Inventory(
                            SkuQuantity.fromBigDecimal(BigDecimal(1000)),
                            null,
                            Location(
                                "location-id",
                                LOCATION_TYPE_STAGING,
                                "transport-module-id",
                            ),
                        ),
                    ),
                ),
            ),
        )

        val actualConsumptionQty = BigDecimal(5L)
        val endNextWeek = today.plusWeeks(1)
        val actualConsumptions = latestCleardown.plusDays(1).datesUntil(today).toList()
            .map {
                Demand(
                    cskuId,
                    dcCodeIT,
                    it,
                    SkuQuantity.fromLong(0),
                    ActualConsumption(
                        SkuQuantity.fromBigDecimal(actualConsumptionQty),
                        true,
                    ),
                )
            }

        val demandValue = SkuQuantity.fromLong(7)
        val demands = latestCleardown.plusDays(1).datesUntil(endNextWeek).toList()
            .map { Demand(cskuId, dcCodeIT, it, demandValue) }

        val dcConfigMap = mapOf(dcCodeIT to dcConfig)
        val inputData = inputData.copy(
            skuDcCandidates = setOf(SkuDcCandidate(cskuId, sku, dcConfig)),
            inventory = inventorySnapshots(dcConfigMap, inventory, liveInventory),
            demands = Demands(demands + actualConsumptions),
        )

        // when
        val result = calculatorClient.runDailyCalculations(inputData)

        val olderThanToday = result.filter { it.date.isAfter(latestCleardown) && it.date.isBefore(today) }
        val todayAndLater = result.filter { it.date == today || (it.date.isAfter(today) && it.date.isBefore(endNextWeek)) }

        var remainingInventory = inventory.skus.flatMap { it.inventory }.sumOf { it.qty.getValue() }
        olderThanToday.forEach { value ->
            remainingInventory -= actualConsumptionQty
            assertEquals(remainingInventory, value.closingStock.getValue())
            assertEquals(actualConsumptionQty, value.actualConsumption.getValue())
        }

        todayAndLater.forEach { value ->
            remainingInventory -= demandValue.getValue()
            assertEquals(remainingInventory, value.closingStock.getValue())

            // Actual consumption is always 0 for the future dates
            assertEquals(0, value.actualConsumption.getValue().toLong())
        }
    }

    @Test
    fun `should use actual consumption for an assembly sku in IT before today and demand afterwards for Preprod calculations`() {
        // given
        val today = LocalDate.now()
        val sku = defaultSkuSpec.copy(packaging = "coolpouch")
        val dcCodeIT = "IT"

        val dcConfig = DistributionCenterConfiguration.default(dcCodeIT).copy(
            dcCode = dcCodeIT,
            cleardown = today.minusDays(3).dayOfWeek,
        )
        val latestCleardown = dcConfig.getLatestCleardown()

        val inventory = InventorySnapshot(
            dcCodeIT,
            UUID.randomUUID(),
            latestCleardown.minusDays(1).atStartOfDay(),
            listOf(
                SkuInventory(
                    cskuId,
                    listOf(createInventory(SkuQuantity.fromBigDecimal(BigDecimal(1000)), null)),
                ),
            ),
        )

        val liveInventory = LiveInventorySnapshot(
            dcCodeIT,
            UUID.randomUUID(),
            latestCleardown.minusDays(1).atStartOfDay(),
            listOf(
                SkuLiveInventory(
                    cskuId,
                    listOf(
                        Inventory(
                            SkuQuantity.fromBigDecimal(BigDecimal(1000)),
                            null,
                            Location(
                                "location-id",
                                LOCATION_TYPE_STAGING,
                                "transport-module-id",
                            ),
                        ),
                    ),
                ),
            ),
        )

        val actualConsumptionQty = SkuQuantity.fromLong(5L)
        val reducedDemandAfterToday = BigDecimal(19)
        val lastDayDemand = BigDecimal(54)
        val demand = SkuQuantity.fromLong(7)
        val endNextWeek = today.plusWeeks(1)
        val actualConsumptions = latestCleardown.plusDays(1).datesUntil(today).toList()
            .map {
                Demand(
                    cskuId,
                    dcCodeIT,
                    it,
                    demand,
                    ActualConsumption(actualConsumptionQty, true),
                )
            }

        val demands = today.datesUntil(endNextWeek).toList()
            .map { Demand(cskuId, dcCodeIT, it, demand) }

        val dcConfigMap = mapOf(dcCodeIT to dcConfig)
        val inputData = inputData.copy(
            mode = PRE_PRODUCTION,
            skuDcCandidates = setOf(SkuDcCandidate(cskuId, sku, dcConfig)),
            inventory = inventorySnapshots(dcConfigMap, inventory, liveInventory),
            demands = Demands(demands + actualConsumptions),
        )

        // when
        val result = calculatorClient.runDailyCalculations(inputData)

        val olderThanToday = result.filter { it.date.isAfter(latestCleardown) && it.date.isBefore(today) }
        val todayAndLater = result.filter {
            it.date == today || (it.date.isAfter(today) && it.date.isBefore(endNextWeek.minusDays(1)))
        }
        val lastDay = result.find { it.cskuId == cskuId && it.dcCode == dcCodeIT && it.date == endNextWeek.minusDays(1) }

        var remainingInventory = inventory.skus.flatMap {
            it.inventory
        }.sumOf { it.qty.getValue() } - actualConsumptionQty.getValue()
        assertEquals(remainingInventory - actualConsumptionQty.getValue(), olderThanToday[0].closingStock.getValue())
        assertEquals(remainingInventory - reducedDemandAfterToday, todayAndLater[0].closingStock.getValue())

        // After shifting, the last demand is 0
        assertEquals(remainingInventory - lastDayDemand, lastDay!!.closingStock.getValue())
    }

    @Test fun `should add productionWeek to calculations`() {
        // Given
        val inputData = inputData.copy(
            demands = Demands(
                listOf(
                    Demand(
                        cskuId,
                        dcCode,
                        DistributionCenterConfiguration.default().getLatestCleardown().minusDays(1),
                        SkuQuantity.fromLong(1),
                    ),
                ),
            ),
        )

        // when
        val result = calculatorClient.runDailyCalculations(inputData)

        assertFalse(result.isEmpty())
        result.forEach {
            val expectedDcWeek = DcWeek(it.date, DistributionCenterConfiguration.default().productionStart)
            assertEquals(expectedDcWeek.value, it.productionWeek)
        }
    }

    @Test
    fun `should mark inventory usable until its expiry date minus acceptableCodeLife`() {
        // given
        val sku = defaultSkuSpec.copy(acceptableCodeLife = 4)
        val latestCleardownDate = DistributionCenterConfiguration.default().getLatestCleardown()
        val qty = SkuQuantity.fromBigDecimal(BigDecimal(2L))
        val inventoryWithExpiry = createInventory(qty, latestCleardownDate.plusDays(10L))
        val liveInventoryWithExpiry = Inventory(
            qty,
            latestCleardownDate.plusDays(10L),
            Location(
                "location-id",
                LOCATION_TYPE_STAGING,
                "transport-module-id",
            ),
        )

        val inputData = inputData.copy(
            skuDcCandidates = setOf(SkuDcCandidate(cskuId, sku, distributionCenterConfiguration)),
            inventory = inventorySnapshots(
                inputData.dcConfig,
                inventorySnapshot(
                    CalculationKey(
                        cskuId,
                        dcCode,
                        latestCleardownDate.minusDays(1),
                    ),
                    inventoryWithExpiry,
                ),
                liveInventorySnapshot(
                    CalculationKey(
                        cskuId,
                        dcCode,
                        latestCleardownDate.minusDays(1),
                    ),
                    liveInventoryWithExpiry,
                ),
            ),
        )

        // when
        val result = calculatorClient.runDailyCalculations(inputData)

        // then
        val lastUsableDate = inventoryWithExpiry.expiryDate!!.minusDays(sku.acceptableCodeLife.toLong())
        result.filter { it.date <= lastUsableDate }.forEach {
            assertEquals(qty, it.openingStock)
            assertEquals(0, it.unusable.getValue().toLong())
        }
        // inventory is marked expired on lastUsableDate + 1day
        result.find { it.date == lastUsableDate.plusDays(1) }.let {
            assertEquals(0, it?.openingStock?.getValue()?.toLong())
            assertEquals(qty, it?.unusable)
        }
        result.filter { it.date > lastUsableDate.plusDays(1) }.forEach {
            assertEquals(0, it.openingStock.getValue().toLong())
            assertEquals(0, it.unusable.getValue().toLong())
        }
    }

    @Test
    fun `inventory is correctly calculated on cleardown date`() {
        val dc = "TEST"
        val today = LocalDate.now()
        val inventory = listOf(
            createInventory(SkuQuantity.fromBigDecimal(BigDecimal.ONE), null),
            createInventory(SkuQuantity.fromBigDecimal(BigDecimal.TWO), null),
        )

        val distributionCenterConfiguration = DistributionCenterConfiguration(
            dc,
            LocalDate.now().dayOfWeek,
            LocalDate.now().dayOfWeek,
            "DACH",
            EuBerlinZoneId,
            enabled = true,
            wmsType = WmsSystem.WMS_SYSTEM_FCMS,
        )
        val dcConfig = mapOf(dc to distributionCenterConfiguration)
        val inputData = inputData.copy(
            skuDcCandidates = setOf(
                SkuDcCandidate(cskuId, defaultSkuSpec, distributionCenterConfiguration),
            ),
            inventory = inventorySnapshots(
                dcConfig,
                inventorySnapshot(
                    CalculationKey(cskuId, "TEST", today.minusDays(1)),
                    inventory,
                ),
                liveInventorySnapshot(
                    CalculationKey(cskuId, "TEST", today.minusDays(1)),
                    listOf(),
                ),
            ),
            demands = Demands(listOf(Demand(cskuId, dc, today, SkuQuantity.fromLong(10)))),
        )

        // when
        val result = calculatorClient.runDailyCalculations(inputData)

        // proves that memoization doesn't skip updates
        assertEquals(3, result.first { it.date == LocalDate.now() }.openingStock.getValue().toLong())
    }

    @ParameterizedTest
    @MethodSource("preproductionCleardownInputs")
    fun `yesterday's prekitting demand is considered on cleardown day for pre production`(
        dcCode: String,
        preproductionCleardownDc: String?,
        expectedOpeningStock: Long,
        expectedClosingStock: Long,
    ) {
        // given
        val today = LocalDate.now()
        val inventory = listOf(
            createInventory(SkuQuantity.fromBigDecimal(BigDecimal(500)), null),
            createInventory(SkuQuantity.fromBigDecimal(BigDecimal(500)), null),
        )

        val distributionCenterConfiguration = DistributionCenterConfiguration(
            dcCode,
            LocalDate.now().dayOfWeek,
            LocalDate.now().dayOfWeek,
            "DACH",
            EuBerlinZoneId,
            enabled = true,
            wmsType = WmsSystem.UNRECOGNIZED,
        )
        val dcConfig = mapOf(
            dcCode to
                distributionCenterConfiguration,
        )
        val inputData = inputData.copy(
            mode = PRE_PRODUCTION,
            skuDcCandidates = setOf(
                SkuDcCandidate(cskuId, defaultSkuSpec, distributionCenterConfiguration),
            ),
            inventory = inventorySnapshots(
                dcConfig,
                inventorySnapshot(
                    CalculationKey(cskuId, dcCode, today.minusDays(1)),
                    inventory,
                ),
                liveInventorySnapshot(
                    CalculationKey(cskuId, dcCode, today.minusDays(1)),
                    listOf(),
                ),
            ),
            demands = Demands(
                listOf(
                    Demand(cskuId, dcCode, today.minusDays(1), SkuQuantity.fromLong(100)),
                    Demand(cskuId, dcCode, today, SkuQuantity.fromLong(100)),
                    Demand(cskuId, dcCode, today.plusDays(1), SkuQuantity.fromLong(100)),
                ),
            ),
            preproductionCleardownDcs = preproductionCleardownDc?.let { setOf(dcCode) } ?: emptySet(),
        )

        // when
        val result = calculatorClient.runDailyCalculations(inputData)

        // then
        assertEquals(expectedOpeningStock, result.first { it.date == LocalDate.now() }.openingStock.getValue().toLong())
        assertEquals(expectedClosingStock, result.first { it.date == LocalDate.now() }.closingStock.getValue().toLong())
    }

    companion object {
        @JvmStatic
        fun preproductionCleardownInputs(): Stream<Arguments> = Stream.of(
            Arguments.of(DEFAULT_DC_CODE, DEFAULT_DC_CODE, 900, 800),
            Arguments.of("NOT_SUPPORTED_DC_CODE", null, 1000, 900),
        )
    }
}
