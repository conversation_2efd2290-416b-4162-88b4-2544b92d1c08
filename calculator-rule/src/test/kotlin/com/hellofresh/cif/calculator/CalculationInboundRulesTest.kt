package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.createInventory
import com.hellofresh.cif.calculator.SharedCalculationRules.Companion.inventorySnapshots
import com.hellofresh.cif.calculator.calculations.rules.getInventoryTimeFrom
import com.hellofresh.cif.calculator.models.CalculationData
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode.PRE_PRODUCTION
import com.hellofresh.cif.calculator.models.CalculatorMode.PRODUCTION
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.calculator.models.InputData
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_HIGH_JUMP
import com.hellofresh.cif.distributionCenter.models.WmsSystem.WMS_SYSTEM_UNSPECIFIED
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.DeliveryInfo
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.OPEN
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.Supplier
import com.hellofresh.cif.models.purchaseorder.TimeRange
import com.hellofresh.cif.transferorder.model.DeliveryInfo as ToDeliveryInfo
import com.hellofresh.cif.transferorder.model.DeliveryInfoStatus as ToDeliveryInfoStatus
import com.hellofresh.cif.transferorder.model.ToTimeRange
import com.hellofresh.cif.transferorder.model.TransferOrder
import com.hellofresh.cif.transferorder.model.TransferOrderInbounds
import com.hellofresh.cif.transferorder.model.TransferOrderOutbounds
import com.hellofresh.cif.transferorder.model.TransferOrderSku
import com.hellofresh.cif.transferorder.model.TransferOrderStatus
import com.hellofresh.demand.models.Demand
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.CleardownData
import com.hellofresh.inventory.models.CleardownMode.SCHEDULED
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import com.hellofresh.inventory.models.inventory.live.SkuLiveInventory
import com.hellofresh.sku.models.SkuSpecification
import io.mockk.mockk
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneOffset.UTC
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals

class CalculationInboundRulesTest {

    private val calculatorClient = CalculatorClient(
        StatsigTestFeatureFlagClient(emptySet()),
    )
    private val cskuId = UUID.randomUUID()
    private val defaultSkuSpec = SkuSpecification("", "name", "pack", "SPI-", "", null, 0, "", null)
    private val dcCode = "VE"
    private val dcConfigMap =
        mapOf(dcCode to DistributionCenterConfiguration.default(dcCode).copy(wmsType = WMS_SYSTEM_HIGH_JUMP))
    private val supplier = mockk<Supplier>()
    private val inputData = InputData(
        PRODUCTION,
        skuDcCandidates = setOf(SkuDcCandidate(cskuId, defaultSkuSpec, dcConfigMap.values.first())),
        inventorySnapshots(dcConfigMap, emptyList(), emptyList()),
        PurchaseOrderInbounds(emptyList()),
        transferOrderInbounds = TransferOrderInbounds(emptyList()),
        transferOrderOutbounds = TransferOrderOutbounds(emptyList()),
        demands = Demands(emptyList()),
        safetyStocks = emptyMap(),
        stockUpdates = emptyMap(),
        supplierSku = emptyMap(),
        preproductionCleardownDcs = emptySet(),
    )
    private val quantityFunction = { list: List<DayCalculationResult>, localDate: LocalDate,
            block: (DayCalculationResult) -> Long ->
        list.first { it.date == localDate }.let { block.invoke(it) }
    }

    @Test
    fun `actual po values are included in closing stock when are received after cleardown time on cleardown day`() {
        val now = LocalDate.now(UTC).atTime(LocalTime.NOON)
        val today = now.toLocalDate()
        val dcConfig = DistributionCenterConfiguration.default(dcCode)
            .copy(cleardown = now.toLocalDate().dayOfWeek, wmsType = WMS_SYSTEM_UNSPECIFIED)

        val before = now.minusHours(1).atZone(dcConfig.zoneId)
        val after = now.plusMinutes(1).atZone(dcConfig.zoneId)
        val poDeliveryBefore = TimeRange(before, before.plusMinutes(1))
        val poDeliveryAfter = TimeRange(after, after.plusMinutes(1))

        val expectedInboundQty = SkuQuantity.fromLong(15L)
        val actualInboundQty = SkuQuantity.fromLong(10L)
        val purchaseOrders = listOf(
            PurchaseOrder(
                "po1",
                "poRef1_1",
                UUID.randomUUID(),
                dcCode,
                poDeliveryBefore,
                supplier,
                listOf(
                    PurchaseOrderSku(
                        cskuId,
                        expectedInboundQty,
                        listOf(
                            DeliveryInfo(
                                UUID.randomUUID().toString(),
                                before,
                                CLOSED,
                                actualInboundQty,
                            ),
                        ),
                    ),
                ),
                poStatus = APPROVED,
            ),
            PurchaseOrder(
                "po2",
                "poRef1_2",
                UUID.randomUUID(),
                dcCode,
                poDeliveryAfter,
                supplier,
                listOf(
                    PurchaseOrderSku(
                        cskuId,
                        expectedInboundQty,
                        listOf(
                            DeliveryInfo(
                                UUID.randomUUID().toString(),
                                after,
                                OPEN,
                                actualInboundQty,
                            ),
                        ),
                    ),
                ),
                poStatus = APPROVED,
            ),
        )

        val inputData = inputData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
            skuDcCandidates = setOf(SkuDcCandidate(cskuId, defaultSkuSpec, dcConfig)),
            inventory = InventorySnapshots(
                emptyList(),
                emptyList(),
                listOf(
                    CleardownData(
                        dcCode = dcCode,
                        cleardownTime = now,
                        cleardownMode = SCHEDULED,
                        snapshot = null,
                    ),
                ),
            ),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)

        assertEquals(
            expectedInboundQty.times(SkuQuantity.fromLong(2)).getValue().toLong(),
            quantityFunction(resultProd, today) { it.expectedInbound.getValue().toLong() },
        )
        assertEquals(setOf("po1", "po2"), resultProd.first { it.date == today }.expectedInboundPurchaseOrders)
        assertEquals(
            actualInboundQty.times(SkuQuantity.fromLong(2)).getValue().toLong(),
            quantityFunction(resultProd, today) { it.actualInbound.getValue().toLong() },
        )
        assertEquals(setOf("po1", "po2"), resultProd.first { it.date == today }.actualInboundPurchaseOrders)
        assertEquals(
            expectedInboundQty,
            SkuQuantity.fromLong(quantityFunction(resultProd, today) { it.closingStock.getValue().toLong() }),
        )
    }

    @Test
    fun `expected transfer order inbounds without deliveries are included in inbound calculation`() {
        val startDate = ZonedDateTime.now()
        val expectedInboundQty = SkuQuantity.fromLong(20L)
        val dcConfig = DistributionCenterConfiguration.default(dcCode)
            .copy(cleardown = startDate.dayOfWeek, wmsType = WMS_SYSTEM_UNSPECIFIED)
        val transferOrderInbounds = TransferOrderInbounds(
            listOf(
                TransferOrder(
                    transferOrderNumber = "to1001",
                    sourceDc = "GR",
                    destinationDc = dcCode,
                    week = "2023-W40",
                    marketCode = "GB",
                    status = TransferOrderStatus.STATE_RESERVED,
                    expectedDeliveryTimeslot = ToTimeRange(
                        startTime = startDate,
                        endTime = startDate.plusDays(5),
                    ),
                    transferOrderSkus = listOf(
                        TransferOrderSku(
                            toNumber = "to1001",
                            skuId = cskuId,
                            supplierId = UUID.randomUUID(),
                            supplierName = null,
                            expectedQuantity = expectedInboundQty,
                            deliveries = emptyList(),
                        ),
                    ),
                )
            )
        )

        val inputData = inputData.copy(
            transferOrderInbounds = transferOrderInbounds,
            skuDcCandidates = setOf(SkuDcCandidate(cskuId, defaultSkuSpec, dcConfig)),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)
        assertEquals(
            expectedInboundQty.getValue().toLong(),
            quantityFunction(
                resultProd,
                startDate.toLocalDate()
            ) { it.expectedInboundTransferOrdersQuantity.getValue().toLong() },
        )
        // Closing stock should equal expected inbound
        assertEquals(
            expectedInboundQty.getValue().toLong(),
            quantityFunction(resultProd, startDate.toLocalDate()) { it.closingStock.getValue().toLong() },
        )
    }

    @Test
    fun `expected transfer order inbounds with deliveries are included calculation`() {
        val startDate = ZonedDateTime.now()
        val deliveryDate = LocalDateTime.now()
        val expectedActualInboundQty = SkuQuantity.fromLong(50L)
        val transferOrderInbounds = TransferOrderInbounds(
            listOf(
                TransferOrder(
                    transferOrderNumber = "to1002",
                    sourceDc = "GR",
                    destinationDc = dcCode,
                    week = "2023-W40",
                    marketCode = "GB",
                    status = TransferOrderStatus.STATE_RESERVED,
                    expectedDeliveryTimeslot = ToTimeRange(
                        startTime = startDate,
                        endTime = startDate.plusDays(2),
                    ),
                    transferOrderSkus = listOf(
                        TransferOrderSku(
                            toNumber = "to1002",
                            skuId = cskuId,
                            supplierId = UUID.randomUUID(),
                            supplierName = null,
                            expectedQuantity = expectedActualInboundQty,
                            deliveries = listOf(
                                ToDeliveryInfo(
                                    id = UUID.randomUUID().toString(),
                                    deliveryTime = deliveryDate,
                                    state = ToDeliveryInfoStatus.CLOSED,
                                    quantity = SkuQuantity.fromLong(expectedActualInboundQty.getValue().toLong()),
                                    expiryDate = null,
                                )
                            ), // add delivery
                        ),
                    ),
                )
            )
        )

        val inputData = inputData.copy(
            transferOrderInbounds = transferOrderInbounds
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)

        assertEquals(
            expectedActualInboundQty.getValue().toLong(),
            quantityFunction(
                resultProd,
                deliveryDate.toLocalDate()
            ) { it.actualInboundTransferOrdersQuantity.getValue().toLong() },
        )
        // Closing stock should equal expected actual inbound
        assertEquals(
            expectedActualInboundQty.getValue().toLong(),
            quantityFunction(resultProd, deliveryDate.toLocalDate()) { it.closingStock.getValue().toLong() },
        )
    }

    @Test
    fun `expected transfer order outbounds without deliveries are included in inbound calculation`() {
        val startDate = ZonedDateTime.now()
        val expectedInboundQty = SkuQuantity.fromLong(120L)
        val expectedOutboundQty = SkuQuantity.fromLong(50L)
        val transferOrderInbounds = TransferOrderInbounds(
            listOf(
                TransferOrder(
                    transferOrderNumber = "to1001",
                    sourceDc = "GR",
                    destinationDc = dcCode,
                    week = "2023-W40",
                    marketCode = "GB",
                    status = TransferOrderStatus.STATE_RESERVED,
                    expectedDeliveryTimeslot = ToTimeRange(
                        startTime = startDate,
                        endTime = startDate.plusDays(5),
                    ),
                    transferOrderSkus = listOf(
                        TransferOrderSku(
                            toNumber = "to1001",
                            skuId = cskuId,
                            supplierId = UUID.randomUUID(),
                            supplierName = null,
                            expectedQuantity = expectedInboundQty,
                            deliveries = emptyList(),
                        ),
                    ),
                )
            )
        )

        val transferOrderOutbounds = TransferOrderOutbounds(
            listOf(
                TransferOrder(
                    transferOrderNumber = "to2001",
                    sourceDc = dcCode,
                    destinationDc = "GR",
                    week = "2023-W40",
                    marketCode = "GB",
                    status = TransferOrderStatus.STATE_RESERVED,
                    expectedDeliveryTimeslot = ToTimeRange(
                        startTime = startDate,
                        endTime = startDate.plusDays(5),
                    ),
                    transferOrderSkus = listOf(
                        TransferOrderSku(
                            toNumber = "to2001",
                            skuId = cskuId,
                            supplierId = UUID.randomUUID(),
                            supplierName = null,
                            expectedQuantity = expectedOutboundQty,
                            deliveries = emptyList(),
                        ),
                    ),
                )
            )
        )

        val inputData = inputData.copy(
            transferOrderInbounds = transferOrderInbounds,
            transferOrderOutbounds = transferOrderOutbounds
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)
        assertEquals(
            expectedOutboundQty.getValue().toLong(),
            quantityFunction(
                resultProd,
                startDate.toLocalDate()
            ) { it.expectedOutboundTransferOrdersQuantity.getValue().toLong() },
        )

        assertEquals(
            expectedInboundQty.minus(expectedOutboundQty).getValue().toLong(),
            quantityFunction(resultProd, startDate.toLocalDate()) { it.closingStock.getValue().toLong() },
        )
    }

    @Test
    fun `expected transfer order outbound with deliveries are included calculation`() {
        val startDate = ZonedDateTime.now()
        val deliveryDate = LocalDateTime.now()
        val expectedActualInboundQty = SkuQuantity.fromLong(150L)
        val expectedActualOutboundQty = SkuQuantity.fromLong(50L)

        val transferOrderInbounds = TransferOrderInbounds(
            listOf(
                TransferOrder(
                    transferOrderNumber = "to1002",
                    sourceDc = "GR",
                    destinationDc = dcCode,
                    week = "2023-W40",
                    marketCode = "GB",
                    status = TransferOrderStatus.STATE_RESERVED,
                    expectedDeliveryTimeslot = ToTimeRange(
                        startTime = startDate,
                        endTime = startDate.plusDays(2),
                    ),
                    transferOrderSkus = listOf(
                        TransferOrderSku(
                            toNumber = "to1002",
                            skuId = cskuId,
                            supplierId = UUID.randomUUID(),
                            supplierName = null,
                            expectedQuantity = expectedActualInboundQty,
                            deliveries = listOf(
                                ToDeliveryInfo(
                                    id = UUID.randomUUID().toString(),
                                    deliveryTime = deliveryDate,
                                    state = ToDeliveryInfoStatus.CLOSED,
                                    quantity = SkuQuantity.fromLong(expectedActualInboundQty.getValue().toLong()),
                                    expiryDate = null,
                                )
                            ), // add delivery
                        ),
                    ),
                )
            )
        )

        val transferOrderOutbounds = TransferOrderOutbounds(
            listOf(
                TransferOrder(
                    transferOrderNumber = "to2001",
                    sourceDc = dcCode,
                    destinationDc = "GB",
                    week = "2023-W40",
                    marketCode = "GR",
                    status = TransferOrderStatus.STATE_RESERVED,
                    expectedDeliveryTimeslot = ToTimeRange(
                        startTime = startDate,
                        endTime = startDate.plusDays(2),
                    ),
                    transferOrderSkus = listOf(
                        TransferOrderSku(
                            toNumber = "to2001",
                            skuId = cskuId,
                            supplierId = UUID.randomUUID(),
                            supplierName = null,
                            expectedQuantity = expectedActualOutboundQty,
                            deliveries = listOf(
                                ToDeliveryInfo(
                                    id = UUID.randomUUID().toString(),
                                    deliveryTime = deliveryDate,
                                    state = ToDeliveryInfoStatus.CLOSED,
                                    quantity = SkuQuantity.fromLong(expectedActualOutboundQty.getValue().toLong()),
                                    expiryDate = null,
                                )
                            ), // add delivery
                        ),
                    ),
                )
            )
        )

        val inputData = inputData.copy(
            transferOrderInbounds = transferOrderInbounds,
            transferOrderOutbounds = transferOrderOutbounds
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)

        assertEquals(
            expectedActualOutboundQty.getValue().toLong(),
            quantityFunction(
                resultProd,
                deliveryDate.toLocalDate()
            ) { it.expectedOutboundTransferOrdersQuantity.getValue().toLong() },
        )

        assertEquals(
            expectedActualInboundQty.minus(expectedActualOutboundQty).getValue().toLong(),
            quantityFunction(resultProd, deliveryDate.toLocalDate()) { it.closingStock.getValue().toLong() },
        )
    }

    @Test
    fun `inbound deliveries valid time is the minimum time between snapshot time and cleardown time for that day`() {
        val cleardownTime = LocalDateTime.now(UTC)
        val snapshotTime = cleardownTime.minusHours(1)
        val calculationKey = CalculationKey(UUID.randomUUID(), dcCode, LocalDateTime.now(UTC).toLocalDate())
        val inputData = inputData.copy(
            inventory = InventorySnapshots(
                emptyList(),
                emptyList(),
                listOf(
                    CleardownData(
                        dcCode,
                        cleardownTime,
                        SCHEDULED,
                        InventorySnapshot(
                            dcCode,
                            UUID.randomUUID(),
                            snapshotTime,
                            emptyList(),
                        ),
                    ),
                ),
            ),
        )
        assertEquals(
            snapshotTime,
            getInventoryTimeFrom(
                calculationKey,
                CalculationData(inputData, StatsigTestFeatureFlagClient(emptySet())),
            ),
        )
    }

    @Test
    fun `minimum inbound deliveries valid time is cleardown day at midnight`() {
        val cleardownTime = LocalDateTime.now(UTC)
        val snapshotTime = LocalDateTime.now(UTC).minusDays(1)
        val calculationKey = CalculationKey(UUID.randomUUID(), dcCode, LocalDateTime.now(UTC).toLocalDate())
        val inputData = inputData.copy(
            inventory = InventorySnapshots(
                emptyList(),
                emptyList(),
                listOf(
                    CleardownData(
                        dcCode,
                        cleardownTime,
                        SCHEDULED,
                        InventorySnapshot(
                            dcCode,
                            UUID.randomUUID(),
                            snapshotTime,
                            emptyList(),
                        ),
                    ),
                ),
            ),
        )
        assertEquals(
            cleardownTime.toLocalDate().atStartOfDay(),
            getInventoryTimeFrom(
                calculationKey,
                CalculationData(inputData, StatsigTestFeatureFlagClient(emptySet())),
            ),
        )
    }

    @Test
    fun `default inbound deliveries valid time is cleardown time`() {
        val cleardownTime = LocalDateTime.now(UTC)
        val calculationKey = CalculationKey(UUID.randomUUID(), dcCode, cleardownTime.toLocalDate())
        val inputData = inputData.copy(
            inventory = InventorySnapshots(
                emptyList(),
                emptyList(),
                listOf(
                    CleardownData(
                        dcCode,
                        cleardownTime,
                        SCHEDULED,
                        null,
                    ),
                ),
            ),
        )
        assertEquals(
            cleardownTime,
            getInventoryTimeFrom(
                calculationKey,
                CalculationData(inputData, StatsigTestFeatureFlagClient(emptySet())),
            ),
        )
    }

    @Test
    fun `actual and expected po values are used for today daily calculation for cleardown dc`() {
        val today = LocalDate.now()
        val poDeliveryToday = TimeRange(today.atStartOfDay(UTC), today.atStartOfDay(UTC).plusMinutes(1))

        val expectedInboundQty = SkuQuantity.fromLong(15L)
        val actualInboundQty = SkuQuantity.fromLong(10L)
        val purchaseOrders = listOf(
            PurchaseOrder(
                "po1",
                "poRef2_1",
                UUID.randomUUID(),
                dcCode,
                poDeliveryToday,
                supplier,
                listOf(
                    PurchaseOrderSku(
                        cskuId,
                        expectedInboundQty,
                        listOf(
                            DeliveryInfo(
                                UUID.randomUUID().toString(),
                                today.atStartOfDay(UTC),
                                OPEN,
                                actualInboundQty,
                            ),
                        ),
                    ),
                ),
                poStatus = APPROVED,
            ),
            PurchaseOrder(
                "po2",
                "poRef2_2",
                UUID.randomUUID(),
                dcCode,
                poDeliveryToday,
                supplier,
                listOf(
                    PurchaseOrderSku(
                        cskuId,
                        expectedInboundQty,
                        listOf(
                            DeliveryInfo(
                                UUID.randomUUID().toString(),
                                today.atStartOfDay(UTC),
                                CLOSED,
                                actualInboundQty,
                            ),
                        ),
                    ),
                ),
                poStatus = APPROVED,
            ),
        )

        val inputData = inputData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)

        assertEquals(
            expectedInboundQty.times(SkuQuantity.fromLong(2)).getValue().toLong(),
            quantityFunction(resultProd, today) { it.expectedInbound.getValue().toLong() },
        )
        assertEquals(setOf("po1", "po2"), resultProd.first { it.date == today }.expectedInboundPurchaseOrders)
        assertEquals(
            actualInboundQty.times(SkuQuantity.fromLong(2)).getValue().toLong(),
            quantityFunction(resultProd, today) { it.actualInbound.getValue().toLong() },
        )
        assertEquals(setOf("po1", "po2"), resultProd.first { it.date == today }.actualInboundPurchaseOrders)
        assertEquals(
            actualInboundQty.times(SkuQuantity.fromLong(2))
                .plus(expectedInboundQty.minus(actualInboundQty))
                .getValue()
                .toLong(),
            quantityFunction(resultProd, today) { it.closingStock.getValue().toLong() },
        )
    }

    @Test
    fun `unexpired inbounds should be included to the inventory stocks for cleardown dc`() {
        val today = LocalDate.now()
        val poDeliveryToday = TimeRange(today.atStartOfDay(UTC), today.atStartOfDay(UTC).plusMinutes(1))
        val expectedInboundQty = SkuQuantity.fromLong(15L)
        val actualInboundQty = SkuQuantity.fromLong(10L)
        val purchaseOrders = listOf(
            PurchaseOrder(
                "po1",
                "poRef2_1",
                UUID.randomUUID(),
                dcCode,
                poDeliveryToday,
                supplier,
                listOf(
                    PurchaseOrderSku(
                        cskuId,
                        expectedInboundQty,
                        listOf(
                            DeliveryInfo(
                                UUID.randomUUID().toString(),
                                today.atStartOfDay(UTC),
                                OPEN,
                                actualInboundQty,
                            ),
                        ),
                    ),
                ),
                poStatus = APPROVED,
            ),
            PurchaseOrder(
                "po2",
                "poRef2_2",
                UUID.randomUUID(),
                dcCode,
                poDeliveryToday,
                supplier,
                listOf(
                    PurchaseOrderSku(
                        cskuId,
                        expectedInboundQty,
                        listOf(
                            DeliveryInfo(
                                UUID.randomUUID().toString(),
                                today.atStartOfDay(UTC),
                                CLOSED,
                                actualInboundQty,
                            ),
                        ),
                    ),
                ),
                poStatus = APPROVED,
            ),
        )

        val inputData = inputData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)
        assertEquals(
            expectedInboundQty
                .times(SkuQuantity.fromLong(2))
                .getValue().toLong(),
            quantityFunction(resultProd, today) { it.expectedInbound.getValue().toLong() },
        )
        assertEquals(setOf("po1", "po2"), resultProd.first { it.date == today }.expectedInboundPurchaseOrders)
        assertEquals(
            actualInboundQty
                .times(SkuQuantity.fromLong(2))
                .getValue()
                .toLong(),
            quantityFunction(resultProd, today) { it.actualInbound.getValue().toLong() },
        )
        assertEquals(setOf("po1", "po2"), resultProd.first { it.date == today }.actualInboundPurchaseOrders)
        assertEquals(
            25,
            quantityFunction(resultProd, today) { it.closingStock.getValue().toLong() },
        )
    }

    @Test
    fun `expired inbounds should not be included to the inventory stocks for cleardown dc`() {
        val today = LocalDate.now()
        val poDeliveryToday = TimeRange(today.atStartOfDay(UTC), today.atStartOfDay(UTC).plusMinutes(1))
        val expectedInboundQty = SkuQuantity.fromLong(15L)
        val actualInboundQty = SkuQuantity.fromLong(10L)
        val purchaseOrders = listOf(
            PurchaseOrder(
                "po1",
                "poRef2_1",
                UUID.randomUUID(),
                dcCode,
                poDeliveryToday,
                supplier,
                listOf(
                    PurchaseOrderSku(
                        cskuId,
                        expectedInboundQty,
                        listOf(
                            DeliveryInfo(
                                UUID.randomUUID().toString(),
                                today.atStartOfDay(UTC),
                                OPEN,
                                actualInboundQty,
                                today,
                            ),
                        ),
                        today,
                    ),
                ),
                poStatus = APPROVED,
            ),
            PurchaseOrder(
                "po2",
                "poRef2_2",
                UUID.randomUUID(),
                dcCode,
                poDeliveryToday,
                supplier,
                listOf(
                    PurchaseOrderSku(
                        cskuId,
                        expectedInboundQty,
                        listOf(
                            DeliveryInfo(
                                UUID.randomUUID().toString(),
                                today.atStartOfDay(UTC),
                                CLOSED,
                                actualInboundQty,
                                today,
                            ),
                        ),
                        today,
                    ),
                ),
                poStatus = APPROVED,
            ),
        )

        val inputData = inputData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)
        assertEquals(
            expectedInboundQty
                .times(SkuQuantity.fromLong(2))
                .getValue().toLong(),
            quantityFunction(resultProd, today) { it.expectedInbound.getValue().toLong() },
        )
        assertEquals(setOf("po1", "po2"), resultProd.first { it.date == today }.expectedInboundPurchaseOrders)
        assertEquals(
            actualInboundQty
                .times(SkuQuantity.fromLong(2))
                .getValue()
                .toLong(),
            quantityFunction(resultProd, today) { it.actualInbound.getValue().toLong() },
        )
        assertEquals(setOf("po1", "po2"), resultProd.first { it.date == today }.actualInboundPurchaseOrders)
        assertEquals(
            0,
            quantityFunction(resultProd, today) { it.closingStock.getValue().toLong() },
        )
    }

    @Test
    fun `purchase orders are used for tomorrow daily calculation for cleardown dc`() {
        val today = LocalDate.now()
        val tomorrow = today.plusDays(1)
        val poDeliveryTomorrow = TimeRange(tomorrow.atStartOfDay(UTC), tomorrow.atStartOfDay(UTC))

        val expectedInboundQty = SkuQuantity.fromLong(9L)
        val actualInboundQty = SkuQuantity.fromLong(10L)
        val purchaseOrders = listOf(
            PurchaseOrder(
                "po1",
                "poRef3_1",
                UUID.randomUUID(),
                dcCode,
                poDeliveryTomorrow,
                supplier,
                listOf(
                    PurchaseOrderSku(
                        cskuId,
                        expectedInboundQty,
                        listOf(
                            DeliveryInfo(
                                UUID.randomUUID().toString(),
                                tomorrow.atStartOfDay(UTC),
                                CLOSED,
                                actualInboundQty,
                            ),
                        ),
                    ),
                ),
                poStatus = APPROVED,
            ),
            PurchaseOrder(
                "po2",
                "poRef3_2",
                UUID.randomUUID(),
                dcCode,
                poDeliveryTomorrow,
                supplier,
                listOf(PurchaseOrderSku(cskuId, expectedInboundQty, emptyList())),
                poStatus = APPROVED,
            ),
        )

        val inputData = inputData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)

        assertEquals(
            expectedInboundQty.times(SkuQuantity.fromLong(2)).getValue().toLong(),
            quantityFunction(resultProd, tomorrow) { it.expectedInbound.getValue().toLong() },
        )
        assertEquals(
            setOf("po1", "po2"),
            resultProd.first {
                it.date == tomorrow
            }.expectedInboundPurchaseOrders,
        )
        assertEquals(
            actualInboundQty.getValue().toLong(),
            quantityFunction(resultProd, tomorrow) { it.actualInbound.getValue().toLong() },
        )
        assertEquals(setOf("po1"), resultProd.first { it.date == tomorrow }.actualInboundPurchaseOrders)
        assertEquals(
            actualInboundQty.plus(expectedInboundQty).getValue().toLong(),
            quantityFunction(resultProd, tomorrow) { it.closingStock.getValue().toLong() },
        )
    }

    @Test
    fun `expected purchase orders are used for today if we dont have actual inbounds for no cleardown dc`() {
        val today = LocalDate.now()
        val poDeliveryToday = TimeRange(today.atStartOfDay(UTC), today.atStartOfDay(UTC).plusMinutes(1))

        val expectedInboundQty = SkuQuantity.fromLong(9L)
        val purchaseOrders = listOf(
            PurchaseOrder(
                "po1",
                "poRef1_1",
                UUID.randomUUID(),
                dcCode,
                poDeliveryToday,
                supplier,
                listOf(PurchaseOrderSku(cskuId, expectedInboundQty, emptyList())),
                poStatus = APPROVED,
            ),
            PurchaseOrder(
                "po2",
                "poRef1_2",
                UUID.randomUUID(),
                dcCode,
                poDeliveryToday,
                supplier,
                listOf(PurchaseOrderSku(cskuId, expectedInboundQty, emptyList())),
                poStatus = APPROVED,
            ),
        )

        val inputData = inputData.copy(
            skuDcCandidates = setOf(
                SkuDcCandidate(
                    cskuId,
                    defaultSkuSpec,
                    DistributionCenterConfiguration.default(dcCode).copy(hasCleardown = false),
                ),
            ),
            purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)

        assertEquals(
            expectedInboundQty.times(SkuQuantity.fromLong(2)).getValue().toLong(),
            quantityFunction(resultProd, today) { it.expectedInbound.getValue().toLong() },
        )
        assertEquals(setOf("po1", "po2"), resultProd.first { it.date == today }.expectedInboundPurchaseOrders)
        assertEquals(0, quantityFunction(resultProd, today) { it.actualInbound.getValue().toLong() })
        assertEquals(0, resultProd.first { it.date == today }.actualInboundPurchaseOrders?.size)
        assertEquals(
            expectedInboundQty.times(SkuQuantity.fromLong(2)).getValue().toLong(),
            quantityFunction(resultProd, today) { it.closingStock.getValue().toLong() },
        )
    }

    @Test
    fun `no purchase orders are used for today if we have actual inbounds for no cleardown dc`() {
        val today = LocalDate.now()
        val poDeliveryToday = TimeRange(today.atStartOfDay(UTC), today.atStartOfDay(UTC).plusMinutes(1))

        val expectedInboundQty = SkuQuantity.fromLong(9L)
        val actualInboundQty = SkuQuantity.fromLong(10L)
        val purchaseOrders = listOf(
            PurchaseOrder(
                "po1",
                "poRef1_1",
                UUID.randomUUID(),
                dcCode,
                poDeliveryToday,
                supplier,
                listOf(
                    PurchaseOrderSku(
                        cskuId,
                        expectedInboundQty,
                        listOf(
                            DeliveryInfo(
                                UUID.randomUUID().toString(),
                                today.atStartOfDay(UTC),
                                CLOSED,
                                actualInboundQty,
                            ),
                        ),
                    ),
                ),
                poStatus = APPROVED,
            ),
            PurchaseOrder(
                "po2",
                "poRef1_2",
                UUID.randomUUID(),
                dcCode,
                poDeliveryToday,
                supplier,
                listOf(
                    PurchaseOrderSku(
                        cskuId,
                        expectedInboundQty,
                        listOf(
                            DeliveryInfo(
                                UUID.randomUUID().toString(),
                                today.atStartOfDay(UTC),
                                CLOSED,
                                actualInboundQty,
                            ),
                        ),
                    ),
                ),
                poStatus = APPROVED,
            ),
        )

        val inputData = inputData.copy(
            skuDcCandidates = setOf(
                SkuDcCandidate(
                    cskuId,
                    defaultSkuSpec,
                    DistributionCenterConfiguration.default(dcCode).copy(hasCleardown = false),
                ),
            ),
            purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)

        assertEquals(
            expectedInboundQty.times(SkuQuantity.fromLong(2)).getValue().toLong(),
            quantityFunction(resultProd, today) { it.expectedInbound.getValue().toLong() },
        )
        assertEquals(setOf("po1", "po2"), resultProd.first { it.date == today }.expectedInboundPurchaseOrders)
        assertEquals(
            actualInboundQty.times(SkuQuantity.fromLong(2)).getValue().toLong(),
            quantityFunction(resultProd, today) { it.actualInbound.getValue().toLong() },
        )
        assertEquals(setOf("po1", "po2"), resultProd.first { it.date == today }.actualInboundPurchaseOrders)
        assertEquals(0, quantityFunction(resultProd, today) { it.closingStock.getValue().toLong() })
    }

    @Test
    fun `expected and actual purchase orders are used for tomorrow for no cleardown dc`() {
        val today = LocalDate.now()
        val tomorrow = today.plusDays(1)
        val poDeliveryTomorrow = TimeRange(tomorrow.atStartOfDay(UTC), tomorrow.atStartOfDay(UTC).plusMinutes(1))

        val expectedInboundQty = SkuQuantity.fromLong(15L)
        val actualInboundQty = SkuQuantity.fromLong(10L)
        val purchaseOrders = listOf(
            PurchaseOrder(
                "po1",
                "poRef1_1",
                UUID.randomUUID(),
                dcCode,
                poDeliveryTomorrow,
                supplier,
                listOf(
                    PurchaseOrderSku(
                        cskuId,
                        expectedInboundQty,
                        listOf(
                            DeliveryInfo(
                                UUID.randomUUID().toString(),
                                tomorrow.atStartOfDay(UTC),
                                CLOSED,
                                actualInboundQty,
                            ),
                        ),
                    ),
                ),
                poStatus = APPROVED,
            ),
            PurchaseOrder(
                "po2",
                "poRef1_2",
                UUID.randomUUID(),
                dcCode,
                poDeliveryTomorrow,
                supplier,
                listOf(
                    PurchaseOrderSku(
                        cskuId,
                        expectedInboundQty,
                        listOf(
                            DeliveryInfo(
                                UUID.randomUUID().toString(),
                                tomorrow.atStartOfDay(UTC),
                                OPEN,
                                actualInboundQty,
                            ),
                        ),
                    ),
                ),
                poStatus = APPROVED,
            ),
        )

        val inputData = inputData.copy(
            skuDcCandidates = setOf(
                SkuDcCandidate(
                    cskuId,
                    defaultSkuSpec,
                    DistributionCenterConfiguration.default(
                        dcCode
                    ).copy(hasCleardown = false, wmsType = WMS_SYSTEM_HIGH_JUMP),
                ),
            ),
            purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)

        assertEquals(
            expectedInboundQty.times(SkuQuantity.fromLong(2)).getValue().toLong(),
            quantityFunction(resultProd, tomorrow) { it.expectedInbound.getValue().toLong() },
        )
        assertEquals(setOf("po1", "po2"), resultProd.first { it.date == tomorrow }.expectedInboundPurchaseOrders)
        assertEquals(
            actualInboundQty.times(SkuQuantity.fromLong(2)).getValue().toLong(),
            quantityFunction(resultProd, tomorrow) { it.actualInbound.getValue().toLong() },
        )
        assertEquals(setOf("po1", "po2"), resultProd.first { it.date == tomorrow }.actualInboundPurchaseOrders)
        assertEquals(
            actualInboundQty
                .times(SkuQuantity.fromLong(2))
                .plus(expectedInboundQty.minus(actualInboundQty))
                .getValue().toLong(),
            quantityFunction(
                resultProd,
                tomorrow,
            ) {
                it.closingStock.getValue().toLong()
            },
        )
    }

    @Test
    fun `calculation should not be affected if poRef in actualInbound is null`() {
        val date = LocalDate.now()
        val key = CalculationKey(cskuId, dcCode, date)
        val poDeliveryTime = TimeRange(key.date.atStartOfDay(UTC), key.date.atStartOfDay(UTC).plusMinutes(1))

        val expectedInboundQty = SkuQuantity.fromLong(1L)
        val inventoryQty = SkuQuantity.fromLong(2L)
        val demandQty = 99L

        val inputData = inputData.copy(
            purchaseOrderInbounds = PurchaseOrderInbounds(
                listOf(
                    PurchaseOrder(
                        "po1",
                        "POREF_01",
                        null,
                        dcCode,
                        poDeliveryTime,
                        null,
                        listOf(
                            PurchaseOrderSku(key.cskuId, expectedInboundQty, emptyList()),
                        ),
                        poStatus = APPROVED,
                    ),
                ),
            ),
            inventory = inventorySnapshots(
                inputData.dcConfig,
                InventorySnapshot(
                    key.dcCode,
                    UUID.randomUUID(),
                    LocalDateTime.now(),
                    listOf(
                        SkuInventory(
                            key.cskuId,
                            listOf(createInventory(inventoryQty, null)),
                        ),
                    ),
                ),
                LiveInventorySnapshot(
                    key.dcCode,
                    UUID.randomUUID(),
                    LocalDateTime.now(),
                    listOf(
                        SkuLiveInventory(
                            key.cskuId,
                            listOf(
                                Inventory(
                                    inventoryQty,
                                    null,
                                    Location(
                                        "location-id",
                                        LOCATION_TYPE_STAGING,
                                        "transport-module-id",
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
            demands = Demands(
                listOf(
                    Demand(
                        key.cskuId,
                        key.dcCode,
                        key.date.plusDays(1),
                        SkuQuantity.fromLong(demandQty),
                    ),
                ),
            ),
        )

        // when
        val resultProd = calculatorClient.runDailyCalculations(inputData)
        val resultPreprod = calculatorClient.runDailyCalculations(inputData.copy(mode = PRE_PRODUCTION))

        val demanded = { out: List<DayCalculationResult>, targetDate: LocalDate ->
            out.first { it.date == targetDate }.demanded
        }
        assertEquals(99, demanded(resultProd, date.plusDays(1)).getValue().toLong())
        assertEquals(99, demanded(resultPreprod, date).getValue().toLong())
    }
}
